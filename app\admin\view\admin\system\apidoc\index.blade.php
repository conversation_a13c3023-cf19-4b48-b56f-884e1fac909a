@extends('admin.layouts.app')

@section('content')
<div class="container-fluid">
    <!-- 页面标题 -->
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <h4 class="page-title">{{ $page_title }}</h4>
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        @foreach($breadcrumb as $item)
                            <li class="breadcrumb-item">
                                @if($item['url'])
                                    <a href="{{ $item['url'] }}">{{ $item['title'] }}</a>
                                @else
                                    {{ $item['title'] }}
                                @endif
                            </li>
                        @endforeach
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- API统计卡片 -->
    <div class="row">
        <div class="col-xl-3 col-md-6">
            <div class="card">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-6">
                            <h5 class="text-muted fw-normal mt-0 text-truncate" title="总表数">总表数</h5>
                            <h3 class="my-2 py-1">{{ $api_stats['total_tables'] }}</h3>
                            <p class="mb-0 text-muted">
                                <span class="text-success me-2"><i class="mdi mdi-arrow-up-bold"></i></span>
                                已生成API文档
                            </p>
                        </div>
                        <div class="col-6">
                            <div class="text-end">
                                <div id="total-tables-chart" data-colors="#00acc1"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-6">
                            <h5 class="text-muted fw-normal mt-0 text-truncate" title="总接口数">总接口数</h5>
                            <h3 class="my-2 py-1">{{ $api_stats['total_apis'] }}</h3>
                            <p class="mb-0 text-muted">
                                <span class="text-success me-2"><i class="mdi mdi-arrow-up-bold"></i></span>
                                RESTful接口
                            </p>
                        </div>
                        <div class="col-6">
                            <div class="text-end">
                                <div id="total-apis-chart" data-colors="#ffab00"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-6">
                            <h5 class="text-muted fw-normal mt-0 text-truncate" title="文档大小">文档大小</h5>
                            <h3 class="my-2 py-1">{{ $api_stats['total_size'] }}</h3>
                            <p class="mb-0 text-muted">
                                <span class="text-success me-2"><i class="mdi mdi-arrow-up-bold"></i></span>
                                总文档大小
                            </p>
                        </div>
                        <div class="col-6">
                            <div class="text-end">
                                <div id="total-size-chart" data-colors="#f672a7"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-6">
                            <h5 class="text-muted fw-normal mt-0 text-truncate" title="平均接口数">平均接口数</h5>
                            <h3 class="my-2 py-1">{{ $api_stats['avg_apis_per_table'] }}</h3>
                            <p class="mb-0 text-muted">
                                <span class="text-success me-2"><i class="mdi mdi-arrow-up-bold"></i></span>
                                每表平均
                            </p>
                        </div>
                        <div class="col-6">
                            <div class="text-end">
                                <div id="avg-apis-chart" data-colors="#6c757d"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 操作按钮 -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <button type="button" class="btn btn-primary" onclick="generateApiDoc()">
                                <i class="mdi mdi-plus"></i> 生成新的API文档
                            </button>
                            <button type="button" class="btn btn-success" onclick="batchExport()">
                                <i class="mdi mdi-download"></i> 批量导出
                            </button>
                        </div>
                        <div class="col-md-6">
                            <div class="input-group">
                                <input type="text" class="form-control" placeholder="搜索表名或API..." id="search-input">
                                <button class="btn btn-outline-secondary" type="button" onclick="searchApiDocs()">
                                    <i class="mdi mdi-magnify"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- API文档列表 -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <h4 class="header-title mb-3">
                        <i class="mdi mdi-api"></i> API文档列表
                        <span class="badge badge-soft-primary ms-2">{{ count($api_docs) }}个</span>
                    </h4>

                    <div class="table-responsive">
                        <table class="table table-centered table-nowrap table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th style="width: 20px;">
                                        <div class="form-check">
                                            <input type="checkbox" class="form-check-input" id="customCheck1">
                                            <label class="form-check-label" for="customCheck1">&nbsp;</label>
                                        </div>
                                    </th>
                                    <th>表名</th>
                                    <th>显示名称</th>
                                    <th>接口数量</th>
                                    <th>文档大小</th>
                                    <th>生成时间</th>
                                    <th>状态</th>
                                    <th style="width: 125px;">操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($api_docs as $doc)
                                <tr>
                                    <td>
                                        <div class="form-check">
                                            <input type="checkbox" class="form-check-input" id="customCheck{{ $loop->iteration }}">
                                            <label class="form-check-label" for="customCheck{{ $loop->iteration }}">&nbsp;</label>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge badge-soft-info">{{ $doc['table_name'] }}</span>
                                    </td>
                                    <td>
                                        <h5 class="font-14 my-1">{{ $doc['display_name'] }}</h5>
                                        <span class="text-muted font-13">{{ $doc['table_name'] }}</span>
                                    </td>
                                    <td>
                                        <span class="badge badge-soft-primary">{{ $doc['api_count'] }}个接口</span>
                                    </td>
                                    <td>{{ $doc['doc_size'] }}</td>
                                    <td>{{ $doc['generated_at'] }}</td>
                                    <td>
                                        @if($doc['status'] == 'active')
                                            <span class="badge badge-soft-success">正常</span>
                                        @else
                                            <span class="badge badge-soft-warning">待更新</span>
                                        @endif
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <button type="button" class="btn btn-soft-primary btn-sm" 
                                                    onclick="viewApiDoc('{{ $doc['table_name'] }}')" 
                                                    title="查看文档">
                                                <i class="mdi mdi-eye"></i>
                                            </button>
                                            <button type="button" class="btn btn-soft-success btn-sm" 
                                                    onclick="testApi('{{ $doc['table_name'] }}')" 
                                                    title="测试接口">
                                                <i class="mdi mdi-play"></i>
                                            </button>
                                            <div class="btn-group" role="group">
                                                <button type="button" class="btn btn-soft-info btn-sm dropdown-toggle" 
                                                        data-bs-toggle="dropdown" aria-expanded="false" title="导出">
                                                    <i class="mdi mdi-download"></i>
                                                </button>
                                                <ul class="dropdown-menu">
                                                    <li><a class="dropdown-item" href="javascript:void(0)" onclick="exportDoc('{{ $doc['table_name'] }}', 'html')">HTML格式</a></li>
                                                    <li><a class="dropdown-item" href="javascript:void(0)" onclick="exportDoc('{{ $doc['table_name'] }}', 'markdown')">Markdown格式</a></li>
                                                    <li><a class="dropdown-item" href="javascript:void(0)" onclick="exportDoc('{{ $doc['table_name'] }}', 'json')">JSON格式</a></li>
                                                    <li><a class="dropdown-item" href="javascript:void(0)" onclick="exportDoc('{{ $doc['table_name'] }}', 'pdf')">PDF格式</a></li>
                                                </ul>
                                            </div>
                                            <button type="button" class="btn btn-soft-warning btn-sm" 
                                                    onclick="regenerateDoc('{{ $doc['table_name'] }}')" 
                                                    title="重新生成">
                                                <i class="mdi mdi-refresh"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="8" class="text-center py-4">
                                        <div class="text-center">
                                            <i class="mdi mdi-api mdi-48px text-muted"></i>
                                            <h5 class="mt-2">暂无API文档</h5>
                                            <p class="text-muted">点击"生成新的API文档"开始创建</p>
                                            <button type="button" class="btn btn-primary" onclick="generateApiDoc()">
                                                <i class="mdi mdi-plus"></i> 生成API文档
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 生成API文档模态框 -->
<div class="modal fade" id="generateApiModal" tabindex="-1" aria-labelledby="generateApiModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="generateApiModalLabel">
                    <i class="mdi mdi-api"></i> 生成API文档
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="generateApiForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="table_name" class="form-label">选择数据表 <span class="text-danger">*</span></label>
                                <select class="form-select" id="table_name" name="table_name" required>
                                    <option value="">请选择数据表</option>
                                    <option value="users">users - 用户表</option>
                                    <option value="articles">articles - 文章表</option>
                                    <option value="categories">categories - 分类表</option>
                                    <option value="tags">tags - 标签表</option>
                                    <option value="comments">comments - 评论表</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="api_version" class="form-label">API版本</label>
                                <input type="text" class="form-control" id="api_version" name="api_version" value="1.0.0">
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="base_url" class="form-label">基础URL</label>
                                <input type="text" class="form-control" id="base_url" name="base_url" value="/api">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="auth_type" class="form-label">认证方式</label>
                                <select class="form-select" id="auth_type" name="auth_type">
                                    <option value="none">无认证</option>
                                    <option value="jwt" selected>JWT Token</option>
                                    <option value="oauth2">OAuth2</option>
                                    <option value="api_key">API Key</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">生成选项</label>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="include_validation" name="options[]" value="validation" checked>
                                    <label class="form-check-label" for="include_validation">
                                        包含参数验证
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="include_pagination" name="options[]" value="pagination" checked>
                                    <label class="form-check-label" for="include_pagination">
                                        包含分页功能
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="include_search" name="options[]" value="search" checked>
                                    <label class="form-check-label" for="include_search">
                                        包含搜索功能
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="include_filter" name="options[]" value="filter" checked>
                                    <label class="form-check-label" for="include_filter">
                                        包含筛选功能
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="include_sort" name="options[]" value="sort" checked>
                                    <label class="form-check-label" for="include_sort">
                                        包含排序功能
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="include_export" name="options[]" value="export">
                                    <label class="form-check-label" for="include_export">
                                        包含导出功能
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="description" class="form-label">API描述</label>
                        <textarea class="form-control" id="description" name="description" rows="3" placeholder="请输入API的详细描述..."></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="submitGenerateApi()">
                    <i class="mdi mdi-check"></i> 开始生成
                </button>
            </div>
        </div>
    </div>
</div>

<!-- API测试模态框 -->
<div class="modal fade" id="apiTestModal" tabindex="-1" aria-labelledby="apiTestModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="apiTestModalLabel">
                    <i class="mdi mdi-play"></i> API接口测试
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div id="apiTestContent">
                    <!-- API测试内容将通过AJAX加载 -->
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('script')
<script>
// 生成API文档
function generateApiDoc() {
    $('#generateApiModal').modal('show');
}

// 提交生成API文档
function submitGenerateApi() {
    const formData = new FormData(document.getElementById('generateApiForm'));
    
    // 显示加载状态
    const submitBtn = event.target;
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="mdi mdi-loading mdi-spin"></i> 生成中...';
    submitBtn.disabled = true;
    
    fetch('/admin/system/apidoc/generate', {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.code === 200) {
            toastr.success(data.msg);
            $('#generateApiModal').modal('hide');
            setTimeout(() => {
                location.reload();
            }, 1000);
        } else {
            toastr.error(data.msg);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        toastr.error('生成失败，请重试');
    })
    .finally(() => {
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    });
}

// 查看API文档
function viewApiDoc(tableName) {
    window.open(`/admin/system/apidoc/view?table=${tableName}`, '_blank');
}

// 测试API
function testApi(tableName) {
    $('#apiTestModal').modal('show');
    
    // 加载API测试界面
    fetch(`/admin/system/apidoc/test-ui?table=${tableName}`)
    .then(response => response.text())
    .then(html => {
        document.getElementById('apiTestContent').innerHTML = html;
    })
    .catch(error => {
        console.error('Error:', error);
        document.getElementById('apiTestContent').innerHTML = '<div class="text-center py-4"><p class="text-danger">加载失败，请重试</p></div>';
    });
}

// 导出文档
function exportDoc(tableName, format) {
    const url = `/admin/system/apidoc/export?table=${tableName}&format=${format}`;
    window.open(url, '_blank');
}

// 重新生成文档
function regenerateDoc(tableName) {
    if (confirm('确定要重新生成API文档吗？这将覆盖现有文档。')) {
        const formData = new FormData();
        formData.append('table_name', tableName);
        
        fetch('/admin/system/apidoc/generate', {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.code === 200) {
                toastr.success(data.msg);
                setTimeout(() => {
                    location.reload();
                }, 1000);
            } else {
                toastr.error(data.msg);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            toastr.error('重新生成失败，请重试');
        });
    }
}

// 批量导出
function batchExport() {
    const checkedBoxes = document.querySelectorAll('input[type="checkbox"]:checked');
    if (checkedBoxes.length === 0) {
        toastr.warning('请先选择要导出的API文档');
        return;
    }
    
    // 这里可以实现批量导出逻辑
    toastr.info('批量导出功能开发中...');
}

// 搜索API文档
function searchApiDocs() {
    const keyword = document.getElementById('search-input').value;
    if (keyword.trim() === '') {
        location.reload();
        return;
    }
    
    // 这里可以实现搜索逻辑
    toastr.info('搜索功能开发中...');
}

// 页面加载完成后的初始化
document.addEventListener('DOMContentLoaded', function() {
    // 初始化工具提示
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // 搜索框回车事件
    document.getElementById('search-input').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            searchApiDocs();
        }
    });
});
</script>
@endsection
