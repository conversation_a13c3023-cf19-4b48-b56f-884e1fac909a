<?php
/**
 * 多数据库 CURD 生成器测试脚本
 */

require_once __DIR__ . '/vendor/autoload.php';

echo "=== 多数据库 CURD 生成器测试 ===\n\n";

// 测试数据库连接配置
$connections = [
    'mysql' => '默认连接',
    'mysql_read' => '读库连接',
    'mysql_second' => '第二数据库',
    'mysql_log' => '日志数据库',
    'mysql_cache' => '缓存数据库',
    'mysql_without_prefix' => '无前缀连接',
];

echo "1. 测试数据库连接\n";
foreach ($connections as $connection => $description) {
    try {
        $config = config("database.connections.{$connection}");
        if ($config) {
            echo "   ✅ {$description} ({$connection}): 配置存在\n";
            
            // 测试连接
            $conn = \support\Db::connection($connection);
            $result = $conn->select('SELECT 1 as test');
            if ($result) {
                echo "      ✅ 连接测试成功\n";
            }
        } else {
            echo "   ❌ {$description} ({$connection}): 配置不存在\n";
        }
    } catch (\Exception $e) {
        echo "   ❌ {$description} ({$connection}): 连接失败 - " . $e->getMessage() . "\n";
    }
}

echo "\n2. 测试 TableAnalyzer 多数据库支持\n";
try {
    $analyzer = new \app\common\services\curd\v2\analyzers\TableAnalyzer();
    
    foreach ($connections as $connection => $description) {
        try {
            echo "   测试 {$description} ({$connection}):\n";
            
            // 测试获取表列表
            $tables = $analyzer->getAllTables($connection);
            echo "     ✅ 获取表列表成功: " . count($tables) . " 个表\n";
            
            // 如果有表，测试第一个表的分析
            if (!empty($tables)) {
                $firstTable = $tables[0]['name'];
                echo "     测试分析表: {$firstTable}\n";
                
                // 测试表是否存在
                $exists = $analyzer->tableExists($firstTable, '', $connection);
                if ($exists) {
                    echo "       ✅ 表存在检查通过\n";
                    
                    // 测试表分析（这里只测试不抛异常）
                    try {
                        $tableInfo = $analyzer->analyze($firstTable, '', $connection);
                        echo "       ✅ 表分析成功\n";
                    } catch (\Exception $e) {
                        echo "       ❌ 表分析失败: " . $e->getMessage() . "\n";
                    }
                } else {
                    echo "       ❌ 表存在检查失败\n";
                }
            } else {
                echo "     ⚠️  数据库中没有表\n";
            }
            
        } catch (\Exception $e) {
            echo "     ❌ 测试失败: " . $e->getMessage() . "\n";
        }
        echo "\n";
    }
    
} catch (\Exception $e) {
    echo "   ❌ TableAnalyzer 初始化失败: " . $e->getMessage() . "\n";
}

echo "3. 测试 CurdGenerator 多数据库支持\n";
try {
    $generator = new \app\common\services\curd\v2\CurdGenerator();
    
    foreach ($connections as $connection => $description) {
        try {
            echo "   测试 {$description} ({$connection}):\n";
            
            // 测试获取表列表
            $tables = $generator->getAllTables($connection);
            echo "     ✅ 获取表列表成功: " . count($tables) . " 个表\n";
            
            // 如果有表，测试第一个表
            if (!empty($tables)) {
                $firstTable = $tables[0]['name'];
                echo "     测试表: {$firstTable}\n";
                
                // 测试表验证
                $isValid = $generator->validateTable($firstTable, '', $connection);
                if ($isValid) {
                    echo "       ✅ 表验证通过\n";
                    
                    // 测试获取表信息
                    try {
                        $tableInfo = $generator->getTableInfo($firstTable, '', $connection);
                        echo "       ✅ 获取表信息成功\n";
                        echo "         表名: " . $tableInfo['name'] . "\n";
                        echo "         字段数: " . count($tableInfo['fields']) . "\n";
                    } catch (\Exception $e) {
                        echo "       ❌ 获取表信息失败: " . $e->getMessage() . "\n";
                    }
                } else {
                    echo "       ❌ 表验证失败\n";
                }
            } else {
                echo "     ⚠️  数据库中没有表\n";
            }
            
        } catch (\Exception $e) {
            echo "     ❌ 测试失败: " . $e->getMessage() . "\n";
        }
        echo "\n";
    }
    
} catch (\Exception $e) {
    echo "   ❌ CurdGenerator 初始化失败: " . $e->getMessage() . "\n";
}

echo "4. 前端集成测试建议\n";
echo "   访问以下 URL 进行前端测试:\n";
echo "   • CURD 生成器 V2: http://localhost:8787/admin/system/curdgeneratev2\n";
echo "   • 数据库连接测试: http://localhost:8787/admin/system/databasetest\n";
echo "\n";

echo "5. 测试步骤\n";
echo "   1. 在 CURD 生成器页面选择不同的数据库连接\n";
echo "   2. 点击刷新按钮加载对应数据库的表列表\n";
echo "   3. 选择表进行分析和代码生成\n";
echo "   4. 在数据库测试页面验证连接状态\n";
echo "\n";

echo "=== 测试完成 ===\n";
echo "如果所有测试都显示 ✅，说明多数据库 CURD 功能已正常工作\n";
?>