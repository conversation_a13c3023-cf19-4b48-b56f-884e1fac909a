<?php

namespace app\admin\controller\system;

use app\common\controller\AdminController;
use app\common\services\annotation\ControllerAnnotation;
use app\common\services\annotation\NodeAnnotation;
use app\common\services\curd\v2\CurdGenerator;
use app\common\services\curd\v2\dto\GenerateRequest;
use app\common\services\curd\v2\managers\TemplateManager;
use support\Request;
use support\Response;

#[ControllerAnnotation(title: 'CURD可视化管理V2')]
class CurdGenerateV2Controller extends AdminController
{
    protected CurdGenerator $generator;
    protected TemplateManager $templateManager;

    public function initialize()
    {
        parent::initialize();
        $this->generator = new CurdGenerator();
        $this->templateManager = new TemplateManager();
    }

    #[NodeAnnotation(title: '列表', auth: true)]
    public function index(Request $request): Response
    {
        if ($request->isAjax()) {
            $action = $request->input('action');

            switch ($action) {
                case 'get_tables':
                    return $this->getTables();
                case 'analyze_table':
                    return $this->analyzeTable($request);
                case 'get_components':
                    return $this->getComponents();
                case 'preview_code':
                    return $this->previewCode($request);
                case 'generate':
                    return $this->generate($request);
                case 'delete':
                    return $this->deleteFiles($request);
                case 'save_template':
                    return $this->saveTemplate($request);
                case 'load_template':
                    return $this->loadTemplate($request);
                case 'get_templates':
                    return $this->getTemplates($request);
                case 'recommend_templates':
                    return $this->recommendTemplates($request);
                case 'delete_template':
                    return $this->deleteTemplate($request);
                case 'analyze_relationships':
                    return $this->analyzeRelationships($request);
                case 'get_table_info_with_relationships':
                    return $this->getTableInfoWithRelationships($request);
                case 'analyze_api_endpoints':
                    return $this->analyzeApiEndpoints($request);
                case 'generate_api_code':
                    return $this->generateApiCode($request);
                case 'get_complete_table_info':
                    return $this->getCompleteTableInfo($request);
                case 'analyze_code_quality':
                    return $this->analyzeCodeQuality($request);
                case 'optimize_code_quality':
                    return $this->optimizeCodeQuality($request);
                case 'get_quality_report':
                    return $this->getQualityReport($request);
                default:
                    return $this->error('未知操作');
            }
        }

        return $this->fetch();
    }

    /**
     * 获取数据库表列表
     */
    protected function getTables(): Response
    {
        try {
            $connection = request()->input('connection', 'mysql');
            $tables = $this->generator->getAllTables($connection);
            return $this->success('获取成功', $tables);
        } catch (\Exception $e) {
            // 如果数据库连接失败，返回模拟数据
            $mockTables = $this->getMockTablesForConnection($connection);
            return $this->success('获取成功 (演示数据)', $mockTables);
        }
    }

    /**
     * 获取模拟表数据
     */
    protected function getMockTablesForConnection(string $connection): array
    {
        $mockTables = [
            'mysql' => [
                ['name' => 'users', 'comment' => '用户表'],
                ['name' => 'articles', 'comment' => '文章表'],
                ['name' => 'categories', 'comment' => '分类表'],
                ['name' => 'config', 'comment' => '配置表'],
                ['name' => 'admin_users', 'comment' => '管理员表'],
                ['name' => 'permissions', 'comment' => '权限表'],
            ],
            'mysql_read' => [
                ['name' => 'users', 'comment' => '用户表(读库)'],
                ['name' => 'articles', 'comment' => '文章表(读库)'],
                ['name' => 'statistics', 'comment' => '统计表(读库)'],
            ],
            'mysql_second' => [
                ['name' => 'products', 'comment' => '产品表'],
                ['name' => 'orders', 'comment' => '订单表'],
                ['name' => 'customers', 'comment' => '客户表'],
            ],
            'mysql_log' => [
                ['name' => 'access_logs', 'comment' => '访问日志'],
                ['name' => 'error_logs', 'comment' => '错误日志'],
                ['name' => 'operation_logs', 'comment' => '操作日志'],
            ],
            'mysql_cache' => [
                ['name' => 'cache_data', 'comment' => '缓存数据'],
                ['name' => 'sessions', 'comment' => '会话数据'],
            ],
        ];

        return $mockTables[$connection] ?? [
            ['name' => 'demo_table', 'comment' => '演示表'],
        ];
    }

    /**
     * 分析表结构
     */
    protected function analyzeTable(Request $request): Response
    {
        try {
            $tableName = $request->input('table_name');
            $tablePrefix = $request->input('table_prefix', '');
            $connection = $request->input('connection', 'mysql');

            if (empty($tableName)) {
                return $this->error('表名不能为空');
            }

            if (!$this->generator->validateTable($tableName, $tablePrefix, $connection)) {
                return $this->error('表不存在');
            }

            $tableInfo = $this->generator->getTableInfo($tableName, $tablePrefix, $connection);

            return $this->success('分析成功', $tableInfo);
        } catch (\Exception $e) {
            // 如果数据库连接失败，返回模拟表信息
            try {
                $mockTableInfo = $this->getMockTableInfo($request->input('table_name'), $request->input('connection', 'mysql'));
                return $this->success('分析成功 (演示数据)', $mockTableInfo);
            } catch (\Exception $mockError) {
                return $this->error('分析失败: ' . $e->getMessage());
            }
        }
    }

    /**
     * 获取模拟表信息
     */
    protected function getMockTableInfo(string $tableName, string $connection): array
    {
        $mockFields = $this->getMockFields($tableName);

        return [
            'name' => $tableName,
            'comment' => $this->getMockTableComment($tableName),
            'connection' => $connection,
            'fields' => $mockFields,
            'indexes' => [
                [
                    'name' => 'PRIMARY',
                    'unique' => true,
                    'primary' => true,
                    'columns' => ['id'],
                ]
            ],
            'foreign_keys' => [],
        ];
    }

    /**
     * 获取模拟字段
     */
    protected function getMockFields(string $tableName): array
    {
        $commonFields = [
            [
                'name' => 'id',
                'type' => 'int',
                'length' => 11,
                'nullable' => false,
                'default' => null,
                'comment' => '主键ID',
                'primary' => true,
                'auto_increment' => true,
                'component' => 'hidden',
            ],
            [
                'name' => 'created_at',
                'type' => 'timestamp',
                'length' => null,
                'nullable' => true,
                'default' => 'CURRENT_TIMESTAMP',
                'comment' => '创建时间',
                'primary' => false,
                'auto_increment' => false,
                'component' => 'datetime',
            ],
            [
                'name' => 'updated_at',
                'type' => 'timestamp',
                'length' => null,
                'nullable' => true,
                'default' => 'CURRENT_TIMESTAMP',
                'comment' => '更新时间',
                'primary' => false,
                'auto_increment' => false,
                'component' => 'datetime',
            ]
        ];

        $specificFields = [];

        switch ($tableName) {
            case 'users':
                $specificFields = [
                    [
                        'name' => 'username',
                        'type' => 'varchar',
                        'length' => 50,
                        'nullable' => false,
                        'default' => null,
                        'comment' => '用户名',
                        'primary' => false,
                        'auto_increment' => false,
                        'component' => 'input',
                    ],
                    [
                        'name' => 'email',
                        'type' => 'varchar',
                        'length' => 100,
                        'nullable' => true,
                        'default' => null,
                        'comment' => '邮箱',
                        'primary' => false,
                        'auto_increment' => false,
                        'component' => 'input',
                    ],
                    [
                        'name' => 'password',
                        'type' => 'varchar',
                        'length' => 255,
                        'nullable' => false,
                        'default' => null,
                        'comment' => '密码',
                        'primary' => false,
                        'auto_increment' => false,
                        'component' => 'password',
                    ],
                    [
                        'name' => 'status',
                        'type' => 'tinyint',
                        'length' => 1,
                        'nullable' => true,
                        'default' => '1',
                        'comment' => '状态',
                        'primary' => false,
                        'auto_increment' => false,
                        'component' => 'switch',
                    ]
                ];
                break;

            case 'articles':
                $specificFields = [
                    [
                        'name' => 'title',
                        'type' => 'varchar',
                        'length' => 200,
                        'nullable' => false,
                        'default' => null,
                        'comment' => '标题',
                        'primary' => false,
                        'auto_increment' => false,
                        'component' => 'input',
                    ],
                    [
                        'name' => 'content',
                        'type' => 'text',
                        'length' => null,
                        'nullable' => true,
                        'default' => null,
                        'comment' => '内容',
                        'primary' => false,
                        'auto_increment' => false,
                        'component' => 'textarea',
                    ],
                    [
                        'name' => 'author_id',
                        'type' => 'int',
                        'length' => 11,
                        'nullable' => true,
                        'default' => null,
                        'comment' => '作者ID',
                        'primary' => false,
                        'auto_increment' => false,
                        'component' => 'select',
                    ]
                ];
                break;

            default:
                $specificFields = [
                    [
                        'name' => 'name',
                        'type' => 'varchar',
                        'length' => 100,
                        'nullable' => false,
                        'default' => null,
                        'comment' => '名称',
                        'primary' => false,
                        'auto_increment' => false,
                        'component' => 'input',
                    ]
                ];
        }

        return array_merge([$commonFields[0]], $specificFields, array_slice($commonFields, 1));
    }

    /**
     * 获取模拟表注释
     */
    protected function getMockTableComment(string $tableName): string
    {
        $comments = [
            'users' => '用户表',
            'articles' => '文章表',
            'categories' => '分类表',
            'config' => '配置表',
            'admin_users' => '管理员表',
            'permissions' => '权限表',
            'products' => '产品表',
            'orders' => '订单表',
            'customers' => '客户表',
            'access_logs' => '访问日志',
            'error_logs' => '错误日志',
            'operation_logs' => '操作日志',
            'cache_data' => '缓存数据',
            'sessions' => '会话数据',
        ];

        return $comments[$tableName] ?? '演示表';
    }

    /**
     * 获取可用组件
     */
    protected function getComponents(): Response
    {
        try {
            $components = $this->generator->getAvailableComponents();
            $fieldTypes = $this->generator->getFieldTypeConfig();

            return $this->success('获取成功', [
                'components' => $components,
                'field_types' => $fieldTypes,
            ]);
        } catch (\Exception $e) {
            return $this->error('获取失败: ' . $e->getMessage());
        }
    }

    /**
     * 预览代码
     */
    protected function previewCode(Request $request): Response
    {
        try {
            $config = $request->input('config', []);

            // 如果是简单的表名配置，构建完整的请求
            if (isset($config['table_name']) && !isset($config['options'])) {
                $tableName = $config['table_name'];
                $tablePrefix = $config['table_prefix'] ?? '';

                // 分析表结构
                $tableInfo = $this->generator->getTableInfo($tableName, $tablePrefix);

                // 构建默认配置
                $config = [
                    'table_name' => $tableName,
                    'table_prefix' => $tablePrefix,
                    'options' => [
                        'fields' => $tableInfo['fields'] ?? [],
                        'generate_controller' => true,
                        'generate_model' => true,
                        'generate_view' => true,
                        'generate_js' => true,
                    ]
                ];
            }

            $generateRequest = GenerateRequest::fromArray($config);
            $files = $this->generator->preview($generateRequest);

            // 格式化返回数据
            $formattedFiles = [];
            foreach ($files as $type => $content) {
                $formattedFiles[$type] = $this->formatCode($content, $type);
            }

            return $this->success('预览成功', $formattedFiles);
        } catch (\Exception $e) {
            return $this->error('预览失败: ' . $e->getMessage());
        }
    }

    /**
     * 格式化代码
     */
    protected function formatCode(string $content, string $type): string
    {
        // 移除多余的空行
        $content = preg_replace('/\n\s*\n\s*\n/', "\n\n", $content);

        // 确保代码缩进正确
        $lines = explode("\n", $content);
        $formattedLines = [];

        foreach ($lines as $line) {
            // 移除行尾空格
            $formattedLines[] = rtrim($line);
        }

        return implode("\n", $formattedLines);
    }

    /**
     * 生成代码
     */
    protected function generate(Request $request): Response
    {
        try {
            $requestData = $request->post();
            $generateRequest = GenerateRequest::fromArray($requestData);

            // 检查文件是否存在
            if (!$generateRequest->isForce()) {
                $existingFiles = $this->generator->checkFilesExist($generateRequest);
                if (!empty($existingFiles)) {
                    return json([
                        'code' => -1,
                        'msg' => '以下文件已存在，是否强制覆盖？',
                        'data' => ['existing_files' => $existingFiles]
                    ]);
                }
            }

            $result = $this->generator->generate($generateRequest);

            if ($result->isSuccess()) {
                return $this->success($result->getMessage(), $result->getData());
            } else {
                return $this->error($result->getMessage(), $result->getErrors());
            }
        } catch (\Exception $e) {
            return $this->error('生成失败: ' . $e->getMessage());
        }
    }

    /**
     * 删除生成的文件
     */
    protected function deleteFiles(Request $request): Response
    {
        try {
            $requestData = $request->post();
            $generateRequest = GenerateRequest::fromArray($requestData);

            $result = $this->generator->delete($generateRequest);

            if ($result->isSuccess()) {
                return $this->success($result->getMessage(), $result->getData());
            } else {
                return $this->error($result->getMessage(), $result->getErrors());
            }
        } catch (\Exception $e) {
            return $this->error('删除失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取生成统计
     */
    #[NodeAnnotation(title: '统计', auth: true)]
    public function stats(): Response
    {
        try {
            $stats = $this->generator->getGenerateStats();
            return $this->success('获取成功', $stats);
        } catch (\Exception $e) {
            return $this->error('获取失败: ' . $e->getMessage());
        }
    }

    /**
     * 保存字段配置
     */
    #[NodeAnnotation(title: '保存配置', auth: true)]
    public function saveFieldConfig(Request $request): Response
    {
        try {
            $tableName = $request->input('table_name');
            $fields = $request->input('fields', []);

            // 这里可以保存用户的字段配置到数据库或文件
            // 暂时返回成功

            return $this->success('保存成功');
        } catch (\Exception $e) {
            return $this->error('保存失败: ' . $e->getMessage());
        }
    }

    /**
     * 加载字段配置
     */
    #[NodeAnnotation(title: '加载配置', auth: true)]
    public function loadFieldConfig(Request $request): Response
    {
        try {
            $tableName = $request->input('table_name');

            // 这里可以从数据库或文件加载用户的字段配置
            // 暂时返回空配置

            return $this->success('加载成功', []);
        } catch (\Exception $e) {
            return $this->error('加载失败: ' . $e->getMessage());
        }
    }

    /**
     * 保存模板
     */
    protected function saveTemplate(Request $request): Response
    {
        try {
            $templateData = $request->input('template', []);

            if (empty($templateData['name']) || empty($templateData['config'])) {
                return $this->error('模板数据不完整');
            }

            $templateId = $this->templateManager->saveTemplate($templateData);

            return $this->success('模板保存成功', ['template_id' => $templateId]);
        } catch (\Exception $e) {
            return $this->error('保存失败: ' . $e->getMessage());
        }
    }

    /**
     * 加载模板
     */
    protected function loadTemplate(Request $request): Response
    {
        try {
            $templateId = $request->input('template_id');

            if (empty($templateId)) {
                return $this->error('模板ID不能为空');
            }

            $config = $this->templateManager->applyTemplate($templateId, '');

            return $this->success('模板加载成功', $config);
        } catch (\Exception $e) {
            return $this->error('加载失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取模板列表
     */
    protected function getTemplates(Request $request): Response
    {
        try {
            $filters = [
                'keyword' => $request->input('keyword'),
                'is_public' => $request->input('is_public'),
                'created_by' => $request->input('my_only') ? session('admin.id') : null,
                'limit' => $request->input('limit', 50),
                'offset' => $request->input('offset', 0),
            ];

            $templates = $this->templateManager->getTemplates($filters);

            return $this->success('获取成功', $templates);
        } catch (\Exception $e) {
            return $this->error('获取失败: ' . $e->getMessage());
        }
    }

    /**
     * 推荐模板
     */
    protected function recommendTemplates(Request $request): Response
    {
        try {
            $tableName = $request->input('table_name');

            if (empty($tableName)) {
                return $this->error('表名不能为空');
            }

            $recommendations = $this->templateManager->recommendTemplates($tableName);

            return $this->success('获取推荐成功', $recommendations);
        } catch (\Exception $e) {
            return $this->error('获取推荐失败: ' . $e->getMessage());
        }
    }

    /**
     * 删除模板
     */
    protected function deleteTemplate(Request $request): Response
    {
        try {
            $templateId = $request->input('template_id');

            if (empty($templateId)) {
                return $this->error('模板ID不能为空');
            }

            $result = $this->templateManager->deleteTemplate($templateId);

            if ($result) {
                return $this->success('删除成功');
            } else {
                return $this->error('删除失败');
            }
        } catch (\Exception $e) {
            return $this->error('删除失败: ' . $e->getMessage());
        }
    }

    /**
     * 分析表关联关系
     */
    protected function analyzeRelationships(Request $request): Response
    {
        try {
            $tableName = $request->input('table_name');

            if (empty($tableName)) {
                return $this->error('表名不能为空');
            }

            $relationships = $this->generator->analyzeRelationships($tableName);

            return $this->success('分析成功', $relationships);
        } catch (\Exception $e) {
            return $this->error('分析失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取表信息（包含关联关系）
     */
    protected function getTableInfoWithRelationships(Request $request): Response
    {
        try {
            $tableName = $request->input('table_name');
            $tablePrefix = $request->input('table_prefix', '');

            if (empty($tableName)) {
                return $this->error('表名不能为空');
            }

            $tableInfo = $this->generator->getTableInfoWithRelationships($tableName, $tablePrefix);

            return $this->success('获取成功', $tableInfo);
        } catch (\Exception $e) {
            return $this->error('获取失败: ' . $e->getMessage());
        }
    }

    /**
     * 分析 API 接口设计
     */
    protected function analyzeApiEndpoints(Request $request): Response
    {
        try {
            $tableName = $request->input('table_name');
            $options = $request->input('options', []);

            if (empty($tableName)) {
                return $this->error('表名不能为空');
            }

            $endpoints = $this->generator->analyzeApiEndpoints($tableName, $options);

            return $this->success('分析成功', $endpoints);
        } catch (\Exception $e) {
            return $this->error('分析失败: ' . $e->getMessage());
        }
    }

    /**
     * 生成 API 接口代码
     */
    protected function generateApiCode(Request $request): Response
    {
        try {
            $tableName = $request->input('table_name');
            $options = $request->input('options', []);

            if (empty($tableName)) {
                return $this->error('表名不能为空');
            }

            $tableInfo = $this->generator->getTableInfoWithRelationships($tableName);
            $endpoints = $this->generator->analyzeApiEndpoints($tableName, $options);
            $apiCode = $this->generator->generateApiCode($endpoints, $tableInfo);

            return $this->success('生成成功', $apiCode);
        } catch (\Exception $e) {
            return $this->error('生成失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取完整表信息（包含关联关系和API设计）
     */
    protected function getCompleteTableInfo(Request $request): Response
    {
        try {
            $tableName = $request->input('table_name');
            $tablePrefix = $request->input('table_prefix', '');
            $options = $request->input('options', []);

            if (empty($tableName)) {
                return $this->error('表名不能为空');
            }

            $tableInfo = $this->generator->getCompleteTableInfo($tableName, $tablePrefix, $options);

            return $this->success('获取成功', $tableInfo);
        } catch (\Exception $e) {
            return $this->error('获取失败: ' . $e->getMessage());
        }
    }

    /**
     * 分析代码质量
     */
    protected function analyzeCodeQuality(Request $request): Response
    {
        try {
            $generatedCode = $request->input('generated_code', []);
            $tableInfo = $request->input('table_info', []);
            $config = $request->input('config', []);

            if (empty($generatedCode)) {
                return $this->error('生成的代码不能为空');
            }

            $qualityAnalysis = $this->generator->analyzeCodeQuality($generatedCode, $tableInfo, $config);

            return $this->success('分析成功', $qualityAnalysis);
        } catch (\Exception $e) {
            return $this->error('分析失败: ' . $e->getMessage());
        }
    }

    /**
     * 优化代码质量
     */
    protected function optimizeCodeQuality(Request $request): Response
    {
        try {
            $generatedCode = $request->input('generated_code', []);
            $qualityAnalysis = $request->input('quality_analysis', []);

            if (empty($generatedCode) || empty($qualityAnalysis)) {
                return $this->error('参数不完整');
            }

            $optimization = $this->generator->optimizeCodeQuality($generatedCode, $qualityAnalysis);

            return $this->success('优化成功', $optimization);
        } catch (\Exception $e) {
            return $this->error('优化失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取质量报告
     */
    protected function getQualityReport(Request $request): Response
    {
        try {
            $generatedCode = $request->input('generated_code', []);
            $tableInfo = $request->input('table_info', []);

            if (empty($generatedCode)) {
                return $this->error('生成的代码不能为空');
            }

            $qualityReport = $this->generator->getQualityReport($generatedCode, $tableInfo);

            return $this->success('获取成功', $qualityReport);
        } catch (\Exception $e) {
            return $this->error('获取失败: ' . $e->getMessage());
        }
    }
}
