<?php

namespace app\common\services;

use support\Db;
use support\Cache;
use Illuminate\Support\Str;
use app\common\services\CacheHelper;

/**
 * 优化后的权限验证服务
 */
class AuthServiceOptimized
{
    protected int $adminId;
    protected array $config;
    protected ?array $adminInfo = null;
    protected ?array $nodeList = null;
    protected ?array $adminNodes = null;

    public function __construct(int $adminId)
    {
        $this->adminId = $adminId;
        $this->config = config('auth', []);
    }

    /**
     * 检查权限节点
     */
    public function checkNode(?string $node = null): bool
    {
        // 超级管理员检查
        if ($this->isSuperAdmin()) {
            return true;
        }

        // 权限开关检查
        if (!$this->config['auth_on']) {
            return true;
        }

        $node = $node ?: $this->getCurrentNode();
        $node = $this->parseNodeStr($node);

        // 检查节点是否存在
        if (!$this->nodeExists($node)) {
            return false;
        }

        // 检查节点是否需要权限验证
        if (!$this->nodeRequiresAuth($node)) {
            return true;
        }

        // 检查管理员权限
        return $this->adminHasNode($node);
    }

    /**
     * 是否为超级管理员
     */
    public function isSuperAdmin(): bool
    {
        return $this->adminId === $this->config['super_admin']['id']
            && $this->config['super_admin']['bypass_auth'];
    }

    /**
     * 获取当前节点
     */
    public function getCurrentNode(): string
    {
        $request = request();
        $controller = $request->controller ?? '';
        $action = $request->action ?? 'index';

        // 解析控制器路径
        $controllerPath = str_replace(['app\\admin\\controller\\', 'Controller'], '', $controller);
        $controllerPath = str_replace('\\', '/', $controllerPath);
        $controllerPath = Str::snake(lcfirst($controllerPath));

        return strtolower($controllerPath . '/' . $action);
    }

    /**
     * 检查节点是否存在
     */
    protected function nodeExists(string $node): bool
    {
        $nodeList = $this->getNodeList();
        return isset($nodeList[$node]);
    }

    /**
     * 检查节点是否需要权限验证
     */
    protected function nodeRequiresAuth(string $node): bool
    {
        $nodeList = $this->getNodeList();
        $nodeInfo = $nodeList[$node] ?? null;

        return $nodeInfo && ($nodeInfo['is_auth'] ?? false);
    }

    /**
     * 检查管理员是否拥有节点权限
     */
    protected function adminHasNode(string $node): bool
    {
        $adminInfo = $this->getAdminInfo();

        if (empty($adminInfo) || $adminInfo['status'] != 1 || empty($adminInfo['auth_ids'])) {
            return false;
        }

        $adminNodes = $this->getAdminNodes();
        return isset($adminNodes[$node]);
    }

    /**
     * 获取管理员信息（带缓存）
     */
    public function getAdminInfo(): array
    {
        if ($this->adminInfo !== null) {
            return $this->adminInfo;
        }

        $cacheKey = $this->config['cache']['prefix'] . $this->config['cache']['keys']['admin_info'] . $this->adminId;

        $this->adminInfo = CacheHelper::remember($cacheKey, $this->config['cache']['ttl'], function() {
            $result = Db::table($this->config['tables']['admin'])
                ->where('id', $this->adminId)
                ->first();

            return $result ? (array)$result : [];
        });

        return $this->adminInfo;
    }

    /**
     * 获取所有节点列表（带缓存）
     */
    public function getNodeList(): array
    {
        if ($this->nodeList !== null) {
            return $this->nodeList;
        }

        $cacheKey = $this->config['cache']['prefix'] . $this->config['cache']['keys']['node_list'];

        $this->nodeList = CacheHelper::remember($cacheKey, $this->config['cache']['ttl'], function() {
            return Db::table($this->config['tables']['node'])
                ->select(['id', 'node', 'title', 'type', 'is_auth'])
                ->get()
                ->keyBy('node')
                ->map(function($item) {
                    return (array)$item;
                })
                ->toArray();
        });

        return $this->nodeList;
    }

    /**
     * 获取管理员权限节点（带缓存）
     */
    public function getAdminNodes(): array
    {
        if ($this->adminNodes !== null) {
            return $this->adminNodes;
        }

        $cacheKey = $this->config['cache']['prefix'] . $this->config['cache']['keys']['admin_nodes'] . $this->adminId;

        $this->adminNodes = CacheHelper::remember($cacheKey, $this->config['cache']['ttl'], function() {
            $adminInfo = $this->getAdminInfo();

            if (empty($adminInfo['auth_ids'])) {
                return [];
            }

            $authIds = explode(',', $adminInfo['auth_ids']);

            // 获取权限对应的节点ID
            $nodeIds = Db::table($this->config['tables']['auth_node'])
                ->whereIn('auth_id', $authIds)
                ->pluck('node_id')
                ->toArray();

            if (empty($nodeIds)) {
                return [];
            }

            // 获取节点详情
            return Db::table($this->config['tables']['node'])
                ->whereIn('id', $nodeIds)
                ->get()
                ->keyBy('node')
                ->map(function($item) {
                    return (array)$item;
                })
                ->toArray();
        });

        return $this->adminNodes;
    }

    /**
     * 解析节点字符串
     */
    public function parseNodeStr(string $node): string
    {
        $parts = explode('/', $node);

        foreach ($parts as $key => $part) {
            if ($key === 0) {
                $subParts = explode('.', $part);
                foreach ($subParts as &$subPart) {
                    $subPart = Str::snake(lcfirst($subPart));
                }
                $parts[$key] = implode('.', $subParts);
            }
        }

        return implode('/', $parts);
    }

    /**
     * 清除权限缓存
     */
    public function clearCache(?int $adminId = null): void
    {
        $prefix = $this->config['cache']['prefix'];

        // 清除节点列表缓存
        CacheHelper::forget($prefix . $this->config['cache']['keys']['node_list']);

        // 清除指定管理员或当前管理员的缓存
        $targetAdminId = $adminId ?: $this->adminId;
        CacheHelper::forget($prefix . $this->config['cache']['keys']['admin_info'] . $targetAdminId);
        CacheHelper::forget($prefix . $this->config['cache']['keys']['admin_nodes'] . $targetAdminId);
    }

    /**
     * 批量清除所有管理员权限缓存
     */
    public static function clearAllCache(): void
    {
        $config = config('auth', []);
        $prefix = $config['cache']['prefix'];

        // 清除节点列表缓存
        CacheHelper::forget($prefix . $config['cache']['keys']['node_list']);

        // 尝试清除所有权限相关缓存
        CacheHelper::forgetByPrefix($prefix);
    }
}
