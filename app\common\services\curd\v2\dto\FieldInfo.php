<?php

namespace app\common\services\curd\v2\dto;

/**
 * 字段信息数据传输对象
 */
class FieldInfo
{
    protected string $name;
    protected string $type;
    protected string $comment;
    protected bool $nullable;
    protected $default;
    protected bool $primary;
    protected bool $autoIncrement;
    
    // 表单配置
    protected string $component = 'input';
    protected array $options = [];
    protected bool $showInList = true;
    protected bool $showInForm = true;
    protected bool $searchable = false;
    protected bool $sortable = false;
    protected bool $required = false;
    protected array $validation = [];

    public function __construct(
        string $name,
        string $type,
        string $comment,
        bool $nullable = true,
        $default = null,
        bool $primary = false,
        bool $autoIncrement = false
    ) {
        $this->name = $name;
        $this->type = $type;
        $this->comment = $comment;
        $this->nullable = $nullable;
        $this->default = $default;
        $this->primary = $primary;
        $this->autoIncrement = $autoIncrement;
        $this->required = !$nullable && $default === null && !$primary;
    }

    // Getters
    public function getName(): string
    {
        return $this->name;
    }

    public function getType(): string
    {
        return $this->type;
    }

    public function getComment(): string
    {
        return $this->comment;
    }

    public function isNullable(): bool
    {
        return $this->nullable;
    }

    public function getDefault()
    {
        return $this->default;
    }

    public function isPrimary(): bool
    {
        return $this->primary;
    }

    public function isAutoIncrement(): bool
    {
        return $this->autoIncrement;
    }

    public function getComponent(): string
    {
        return $this->component;
    }

    public function getOptions(): array
    {
        return $this->options;
    }

    public function isShowInList(): bool
    {
        return $this->showInList;
    }

    public function isShowInForm(): bool
    {
        return $this->showInForm;
    }

    public function isSearchable(): bool
    {
        return $this->searchable;
    }

    public function isSortable(): bool
    {
        return $this->sortable;
    }

    public function isRequired(): bool
    {
        return $this->required;
    }

    public function getValidation(): array
    {
        return $this->validation;
    }

    // Setters
    public function setComponent(string $component): self
    {
        $this->component = $component;
        return $this;
    }

    public function setOptions(array $options): self
    {
        $this->options = $options;
        return $this;
    }

    public function setShowInList(bool $showInList): self
    {
        $this->showInList = $showInList;
        return $this;
    }

    public function setShowInForm(bool $showInForm): self
    {
        $this->showInForm = $showInForm;
        return $this;
    }

    public function setSearchable(bool $searchable): self
    {
        $this->searchable = $searchable;
        return $this;
    }

    public function setSortable(bool $sortable): self
    {
        $this->sortable = $sortable;
        return $this;
    }

    public function setRequired(bool $required): self
    {
        $this->required = $required;
        return $this;
    }

    public function setValidation(array $validation): self
    {
        $this->validation = $validation;
        return $this;
    }

    public function setComment(string $comment): self
    {
        $this->comment = $comment;
        return $this;
    }

    /**
     * 获取字段的 PHP 类型
     */
    public function getPhpType(): string
    {
        $type = strtolower($this->type);
        
        if (strpos($type, 'int') !== false) {
            return 'int';
        }
        
        if (strpos($type, 'float') !== false || strpos($type, 'double') !== false || strpos($type, 'decimal') !== false) {
            return 'float';
        }
        
        if (strpos($type, 'bool') !== false || $type === 'tinyint(1)') {
            return 'bool';
        }
        
        if (in_array($type, ['datetime', 'timestamp', 'date', 'time'])) {
            return 'string'; // 或者 Carbon 类型
        }
        
        return 'string';
    }

    /**
     * 获取字段的验证规则
     */
    public function getValidationRules(): array
    {
        $rules = [];
        
        if ($this->required) {
            $rules[] = 'required';
        }
        
        switch ($this->component) {
            case 'email':
                $rules[] = 'email';
                break;
            case 'number':
                $rules[] = 'numeric';
                break;
            case 'url':
                $rules[] = 'url';
                break;
            case 'date':
                $rules[] = 'date';
                break;
            case 'datetime':
                $rules[] = 'date_format:Y-m-d H:i:s';
                break;
        }
        
        return array_merge($rules, $this->validation);
    }

    /**
     * 转换为数组
     */
    public function toArray(): array
    {
        return [
            'name' => $this->name,
            'type' => $this->type,
            'comment' => $this->comment,
            'nullable' => $this->nullable,
            'default' => $this->default,
            'primary' => $this->primary,
            'auto_increment' => $this->autoIncrement,
            'component' => $this->component,
            'options' => $this->options,
            'show_in_list' => $this->showInList,
            'show_in_form' => $this->showInForm,
            'searchable' => $this->searchable,
            'sortable' => $this->sortable,
            'required' => $this->required,
            'validation' => $this->validation,
            'php_type' => $this->getPhpType(),
            'validation_rules' => $this->getValidationRules(),
        ];
    }
}
