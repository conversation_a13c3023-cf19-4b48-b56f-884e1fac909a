<?php

namespace app\common\services\curd\v2\generators;

/**
 * 云端部署生成器
 * 根据云端部署需求分析结果生成部署配置和脚本
 */
class CloudDeploymentGenerator
{
    /**
     * 生成云端部署配置
     */
    public function generateCloudDeploymentConfig(array $cloudAnalysis, array $tableInfo, array $options = []): array
    {
        $configs = [];

        // 生成 Docker 配置
        $configs['docker'] = $this->generateDockerConfigs($cloudAnalysis, $tableInfo);

        // 生成 Kubernetes 配置
        $configs['kubernetes'] = $this->generateKubernetesConfigs($cloudAnalysis, $tableInfo);

        // 生成云平台特定配置
        foreach ($cloudAnalysis['cloud_recommendations'] as $provider => $recommendation) {
            $configs[$provider] = $this->generateCloudProviderConfigs($provider, $cloudAnalysis, $tableInfo);
        }

        // 生成部署脚本
        $configs['scripts'] = $this->generateDeploymentScripts($cloudAnalysis, $tableInfo);

        // 生成监控配置
        $configs['monitoring'] = $this->generateMonitoringConfigs($cloudAnalysis, $tableInfo);

        return $configs;
    }

    /**
     * 生成 Docker 配置
     */
    protected function generateDockerConfigs(array $cloudAnalysis, array $tableInfo): array
    {
        return [
            'Dockerfile' => $this->generateDockerfile($cloudAnalysis, $tableInfo),
            'docker-compose.yml' => $this->generateDockerCompose($cloudAnalysis, $tableInfo),
            'docker-compose.prod.yml' => $this->generateProductionDockerCompose($cloudAnalysis, $tableInfo),
            '.dockerignore' => $this->generateDockerIgnore(),
        ];
    }

    /**
     * 生成 Dockerfile
     */
    protected function generateDockerfile(array $cloudAnalysis, array $tableInfo): string
    {
        $appName = $tableInfo['name'] ?? 'app';
        
        return <<<EOT
# Multi-stage build for production optimization
FROM php:8.1-fpm-alpine AS base

# Install system dependencies
RUN apk add --no-cache \\
    git \\
    curl \\
    libpng-dev \\
    libxml2-dev \\
    zip \\
    unzip \\
    mysql-client

# Install PHP extensions
RUN docker-php-ext-install pdo pdo_mysql mysqli gd xml

# Install Composer
COPY --from=composer:latest /usr/bin/composer /usr/bin/composer

# Set working directory
WORKDIR /var/www/html

# Development stage
FROM base AS development

# Copy composer files
COPY composer.json composer.lock ./

# Install dependencies
RUN composer install --no-scripts --no-autoloader

# Copy application code
COPY . .

# Generate autoloader
RUN composer dump-autoload --optimize

# Set permissions
RUN chown -R www-data:www-data /var/www/html \\
    && chmod -R 755 /var/www/html/storage

# Expose port
EXPOSE 9000

CMD ["php-fpm"]

# Production stage
FROM base AS production

# Copy composer files
COPY composer.json composer.lock ./

# Install production dependencies
RUN composer install --no-dev --optimize-autoloader --no-scripts

# Copy application code
COPY . .

# Generate optimized autoloader
RUN composer dump-autoload --optimize --classmap-authoritative

# Set permissions
RUN chown -R www-data:www-data /var/www/html \\
    && chmod -R 755 /var/www/html/storage

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \\
    CMD curl -f http://localhost:9000/health || exit 1

# Expose port
EXPOSE 9000

CMD ["php-fpm"]
EOT;
    }

    /**
     * 生成 Docker Compose
     */
    protected function generateDockerCompose(array $cloudAnalysis, array $tableInfo): string
    {
        $appName = $tableInfo['name'] ?? 'app';
        
        return <<<EOT
version: '3.8'

services:
  app:
    build:
      context: .
      target: development
    container_name: {$appName}_app
    restart: unless-stopped
    working_dir: /var/www/html
    volumes:
      - ./:/var/www/html
      - ./storage:/var/www/html/storage
    networks:
      - {$appName}_network
    depends_on:
      - database
      - redis
    environment:
      - DB_HOST=database
      - DB_DATABASE={$appName}
      - DB_USERNAME=root
      - DB_PASSWORD=password
      - REDIS_HOST=redis

  nginx:
    image: nginx:alpine
    container_name: {$appName}_nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./:/var/www/html
      - ./docker/nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./docker/nginx/sites/:/etc/nginx/sites-available
      - ./docker/nginx/ssl/:/etc/ssl/certs
    networks:
      - {$appName}_network
    depends_on:
      - app

  database:
    image: mysql:8.0
    container_name: {$appName}_database
    restart: unless-stopped
    environment:
      MYSQL_DATABASE: {$appName}
      MYSQL_ROOT_PASSWORD: password
      MYSQL_PASSWORD: password
      MYSQL_USER: {$appName}
    volumes:
      - {$appName}_mysql_data:/var/lib/mysql
      - ./docker/mysql/init:/docker-entrypoint-initdb.d
    ports:
      - "3306:3306"
    networks:
      - {$appName}_network

  redis:
    image: redis:alpine
    container_name: {$appName}_redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - {$appName}_redis_data:/data
    networks:
      - {$appName}_network

volumes:
  {$appName}_mysql_data:
    driver: local
  {$appName}_redis_data:
    driver: local

networks:
  {$appName}_network:
    driver: bridge
EOT;
    }

    /**
     * 生成 Kubernetes 配置
     */
    protected function generateKubernetesConfigs(array $cloudAnalysis, array $tableInfo): array
    {
        return [
            'namespace.yml' => $this->generateKubernetesNamespace($tableInfo),
            'deployment.yml' => $this->generateKubernetesDeployment($cloudAnalysis, $tableInfo),
            'service.yml' => $this->generateKubernetesService($tableInfo),
            'ingress.yml' => $this->generateKubernetesIngress($tableInfo),
            'configmap.yml' => $this->generateKubernetesConfigMap($tableInfo),
            'secret.yml' => $this->generateKubernetesSecret($tableInfo),
            'hpa.yml' => $this->generateKubernetesHPA($cloudAnalysis, $tableInfo),
        ];
    }

    /**
     * 生成 Kubernetes Deployment
     */
    protected function generateKubernetesDeployment(array $cloudAnalysis, array $tableInfo): string
    {
        $appName = $tableInfo['name'] ?? 'app';
        $replicas = $cloudAnalysis['requirements']['scalability']['horizontal_scaling']['min_instances'] ?? 2;
        
        return <<<EOT
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {$appName}-deployment
  namespace: {$appName}
  labels:
    app: {$appName}
spec:
  replicas: {$replicas}
  selector:
    matchLabels:
      app: {$appName}
  template:
    metadata:
      labels:
        app: {$appName}
    spec:
      containers:
      - name: {$appName}
        image: {$appName}:latest
        ports:
        - containerPort: 9000
        env:
        - name: DB_HOST
          valueFrom:
            configMapKeyRef:
              name: {$appName}-config
              key: db_host
        - name: DB_DATABASE
          valueFrom:
            configMapKeyRef:
              name: {$appName}-config
              key: db_database
        - name: DB_USERNAME
          valueFrom:
            secretKeyRef:
              name: {$appName}-secret
              key: db_username
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: {$appName}-secret
              key: db_password
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 9000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /ready
            port: 9000
          initialDelaySeconds: 5
          periodSeconds: 5
        volumeMounts:
        - name: storage
          mountPath: /var/www/html/storage
      volumes:
      - name: storage
        persistentVolumeClaim:
          claimName: {$appName}-storage-pvc
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: {$appName}-storage-pvc
  namespace: {$appName}
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 5Gi
EOT;
    }

    /**
     * 生成云平台特定配置
     */
    protected function generateCloudProviderConfigs(string $provider, array $cloudAnalysis, array $tableInfo): array
    {
        switch ($provider) {
            case 'aws':
                return $this->generateAWSConfigs($cloudAnalysis, $tableInfo);
            case 'azure':
                return $this->generateAzureConfigs($cloudAnalysis, $tableInfo);
            case 'alibaba':
                return $this->generateAlibabaConfigs($cloudAnalysis, $tableInfo);
            default:
                return [];
        }
    }

    /**
     * 生成 AWS 配置
     */
    protected function generateAWSConfigs(array $cloudAnalysis, array $tableInfo): array
    {
        return [
            'cloudformation.yml' => $this->generateCloudFormationTemplate($cloudAnalysis, $tableInfo),
            'ecs-task-definition.json' => $this->generateECSTaskDefinition($cloudAnalysis, $tableInfo),
            'buildspec.yml' => $this->generateCodeBuildSpec($cloudAnalysis, $tableInfo),
            'appspec.yml' => $this->generateCodeDeploySpec($cloudAnalysis, $tableInfo),
        ];
    }

    /**
     * 生成 CloudFormation 模板
     */
    protected function generateCloudFormationTemplate(array $cloudAnalysis, array $tableInfo): string
    {
        $appName = $tableInfo['name'] ?? 'app';
        
        return <<<EOT
AWSTemplateFormatVersion: '2010-09-09'
Description: 'CloudFormation template for {$appName} application'

Parameters:
  Environment:
    Type: String
    Default: production
    AllowedValues: [development, staging, production]
  
  InstanceType:
    Type: String
    Default: t3.micro
    AllowedValues: [t3.micro, t3.small, t3.medium]

Resources:
  # VPC Configuration
  VPC:
    Type: AWS::EC2::VPC
    Properties:
      CidrBlock: 10.0.0.0/16
      EnableDnsHostnames: true
      EnableDnsSupport: true
      Tags:
        - Key: Name
          Value: !Sub '{$appName}-vpc-\${Environment}'

  # Internet Gateway
  InternetGateway:
    Type: AWS::EC2::InternetGateway
    Properties:
      Tags:
        - Key: Name
          Value: !Sub '{$appName}-igw-\${Environment}'

  # Attach Internet Gateway to VPC
  InternetGatewayAttachment:
    Type: AWS::EC2::VPCGatewayAttachment
    Properties:
      InternetGatewayId: !Ref InternetGateway
      VpcId: !Ref VPC

  # Public Subnet
  PublicSubnet1:
    Type: AWS::EC2::Subnet
    Properties:
      VpcId: !Ref VPC
      AvailabilityZone: !Select [0, !GetAZs '']
      CidrBlock: ********/24
      MapPublicIpOnLaunch: true
      Tags:
        - Key: Name
          Value: !Sub '{$appName}-public-subnet-1-\${Environment}'

  # Route Table
  PublicRouteTable:
    Type: AWS::EC2::RouteTable
    Properties:
      VpcId: !Ref VPC
      Tags:
        - Key: Name
          Value: !Sub '{$appName}-public-routes-\${Environment}'

  # Default Public Route
  DefaultPublicRoute:
    Type: AWS::EC2::Route
    DependsOn: InternetGatewayAttachment
    Properties:
      RouteTableId: !Ref PublicRouteTable
      DestinationCidrBlock: 0.0.0.0/0
      GatewayId: !Ref InternetGateway

  # Associate Route Table with Subnet
  PublicSubnet1RouteTableAssociation:
    Type: AWS::EC2::SubnetRouteTableAssociation
    Properties:
      RouteTableId: !Ref PublicRouteTable
      SubnetId: !Ref PublicSubnet1

  # Security Group
  WebServerSecurityGroup:
    Type: AWS::EC2::SecurityGroup
    Properties:
      GroupName: !Sub '{$appName}-web-sg-\${Environment}'
      GroupDescription: Security group for web servers
      VpcId: !Ref VPC
      SecurityGroupIngress:
        - IpProtocol: tcp
          FromPort: 80
          ToPort: 80
          CidrIp: 0.0.0.0/0
        - IpProtocol: tcp
          FromPort: 443
          ToPort: 443
          CidrIp: 0.0.0.0/0
        - IpProtocol: tcp
          FromPort: 22
          ToPort: 22
          CidrIp: 0.0.0.0/0

  # RDS Subnet Group
  DBSubnetGroup:
    Type: AWS::RDS::DBSubnetGroup
    Properties:
      DBSubnetGroupDescription: Subnet group for RDS database
      SubnetIds:
        - !Ref PublicSubnet1
        - !Ref PublicSubnet2
      Tags:
        - Key: Name
          Value: !Sub '{$appName}-db-subnet-group-\${Environment}'

  # RDS Instance
  Database:
    Type: AWS::RDS::DBInstance
    Properties:
      DBInstanceIdentifier: !Sub '{$appName}-db-\${Environment}'
      DBInstanceClass: db.t3.micro
      Engine: mysql
      EngineVersion: '8.0'
      MasterUsername: admin
      MasterUserPassword: !Ref DBPassword
      AllocatedStorage: 20
      DBSubnetGroupName: !Ref DBSubnetGroup
      VPCSecurityGroups:
        - !Ref DatabaseSecurityGroup
      BackupRetentionPeriod: 7
      MultiAZ: false
      StorageType: gp2

Outputs:
  VPCId:
    Description: VPC ID
    Value: !Ref VPC
    Export:
      Name: !Sub '{$appName}-vpc-id-\${Environment}'

  DatabaseEndpoint:
    Description: RDS instance endpoint
    Value: !GetAtt Database.Endpoint.Address
    Export:
      Name: !Sub '{$appName}-db-endpoint-\${Environment}'
EOT;
    }

    /**
     * 生成部署脚本
     */
    protected function generateDeploymentScripts(array $cloudAnalysis, array $tableInfo): array
    {
        return [
            'deploy.sh' => $this->generateDeployScript($cloudAnalysis, $tableInfo),
            'rollback.sh' => $this->generateRollbackScript($cloudAnalysis, $tableInfo),
            'health-check.sh' => $this->generateHealthCheckScript($cloudAnalysis, $tableInfo),
            'backup.sh' => $this->generateBackupScript($cloudAnalysis, $tableInfo),
        ];
    }

    /**
     * 生成部署脚本
     */
    protected function generateDeployScript(array $cloudAnalysis, array $tableInfo): string
    {
        $appName = $tableInfo['name'] ?? 'app';
        
        return <<<EOT
#!/bin/bash

# {$appName} Deployment Script
set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Configuration
APP_NAME="{$appName}"
ENVIRONMENT=\${ENVIRONMENT:-production}
DOCKER_REGISTRY=\${DOCKER_REGISTRY:-your-registry.com}
IMAGE_TAG=\${IMAGE_TAG:-latest}

log_info() {
    echo -e "\${GREEN}[INFO]\${NC} \$1"
}

log_warn() {
    echo -e "\${YELLOW}[WARN]\${NC} \$1"
}

log_error() {
    echo -e "\${RED}[ERROR]\${NC} \$1"
}

# Check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed"
        exit 1
    fi
    
    if ! command -v kubectl &> /dev/null; then
        log_error "kubectl is not installed"
        exit 1
    fi
    
    log_info "Prerequisites check passed"
}

# Build Docker image
build_image() {
    log_info "Building Docker image..."
    docker build -t \$DOCKER_REGISTRY/\$APP_NAME:\$IMAGE_TAG .
    
    if [ \$? -eq 0 ]; then
        log_info "Docker image built successfully"
    else
        log_error "Failed to build Docker image"
        exit 1
    fi
}

# Push Docker image
push_image() {
    log_info "Pushing Docker image to registry..."
    docker push \$DOCKER_REGISTRY/\$APP_NAME:\$IMAGE_TAG
    
    if [ \$? -eq 0 ]; then
        log_info "Docker image pushed successfully"
    else
        log_error "Failed to push Docker image"
        exit 1
    fi
}

# Deploy to Kubernetes
deploy_to_kubernetes() {
    log_info "Deploying to Kubernetes..."
    
    # Apply configurations
    kubectl apply -f k8s/namespace.yml
    kubectl apply -f k8s/configmap.yml
    kubectl apply -f k8s/secret.yml
    kubectl apply -f k8s/deployment.yml
    kubectl apply -f k8s/service.yml
    kubectl apply -f k8s/ingress.yml
    
    # Wait for deployment to be ready
    kubectl rollout status deployment/\$APP_NAME-deployment -n \$APP_NAME
    
    if [ \$? -eq 0 ]; then
        log_info "Deployment completed successfully"
    else
        log_error "Deployment failed"
        exit 1
    fi
}

# Health check
health_check() {
    log_info "Performing health check..."
    
    # Wait for pods to be ready
    sleep 30
    
    # Get service URL
    SERVICE_URL=\$(kubectl get service \$APP_NAME-service -n \$APP_NAME -o jsonpath='{.status.loadBalancer.ingress[0].ip}')
    
    if [ -z "\$SERVICE_URL" ]; then
        SERVICE_URL="localhost"
    fi
    
    # Perform health check
    for i in {1..10}; do
        if curl -f http://\$SERVICE_URL/health > /dev/null 2>&1; then
            log_info "Health check passed"
            return 0
        fi
        log_warn "Health check attempt \$i failed, retrying..."
        sleep 10
    done
    
    log_error "Health check failed after 10 attempts"
    return 1
}

# Main deployment process
main() {
    log_info "Starting deployment of \$APP_NAME to \$ENVIRONMENT environment"
    
    check_prerequisites
    build_image
    push_image
    deploy_to_kubernetes
    
    if health_check; then
        log_info "Deployment completed successfully!"
    else
        log_error "Deployment failed health check"
        exit 1
    fi
}

# Run main function
main "\$@"
EOT;
    }

    /**
     * 生成监控配置
     */
    protected function generateMonitoringConfigs(array $cloudAnalysis, array $tableInfo): array
    {
        return [
            'prometheus.yml' => $this->generatePrometheusConfig($cloudAnalysis, $tableInfo),
            'grafana-dashboard.json' => $this->generateGrafanaDashboard($cloudAnalysis, $tableInfo),
            'alertmanager.yml' => $this->generateAlertManagerConfig($cloudAnalysis, $tableInfo),
        ];
    }

    // 其他生成方法的占位实现
    protected function generateDockerIgnore(): string { return "# Docker ignore file"; }
    protected function generateProductionDockerCompose(array $cloudAnalysis, array $tableInfo): string { return "# Production Docker Compose"; }
    protected function generateKubernetesNamespace(array $tableInfo): string { return "# Kubernetes Namespace"; }
    protected function generateKubernetesService(array $tableInfo): string { return "# Kubernetes Service"; }
    protected function generateKubernetesIngress(array $tableInfo): string { return "# Kubernetes Ingress"; }
    protected function generateKubernetesConfigMap(array $tableInfo): string { return "# Kubernetes ConfigMap"; }
    protected function generateKubernetesSecret(array $tableInfo): string { return "# Kubernetes Secret"; }
    protected function generateKubernetesHPA(array $cloudAnalysis, array $tableInfo): string { return "# Kubernetes HPA"; }
    protected function generateAzureConfigs(array $cloudAnalysis, array $tableInfo): array { return []; }
    protected function generateAlibabaConfigs(array $cloudAnalysis, array $tableInfo): array { return []; }
    protected function generateECSTaskDefinition(array $cloudAnalysis, array $tableInfo): string { return "// ECS Task Definition"; }
    protected function generateCodeBuildSpec(array $cloudAnalysis, array $tableInfo): string { return "# CodeBuild Spec"; }
    protected function generateCodeDeploySpec(array $cloudAnalysis, array $tableInfo): string { return "# CodeDeploy Spec"; }
    protected function generateRollbackScript(array $cloudAnalysis, array $tableInfo): string { return "#!/bin/bash\n# Rollback script"; }
    protected function generateHealthCheckScript(array $cloudAnalysis, array $tableInfo): string { return "#!/bin/bash\n# Health check script"; }
    protected function generateBackupScript(array $cloudAnalysis, array $tableInfo): string { return "#!/bin/bash\n# Backup script"; }
    protected function generatePrometheusConfig(array $cloudAnalysis, array $tableInfo): string { return "# Prometheus Config"; }
    protected function generateGrafanaDashboard(array $cloudAnalysis, array $tableInfo): string { return "// Grafana Dashboard"; }
    protected function generateAlertManagerConfig(array $cloudAnalysis, array $tableInfo): string { return "# AlertManager Config"; }
}
