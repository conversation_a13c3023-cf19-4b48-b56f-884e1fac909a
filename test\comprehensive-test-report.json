{"timestamp": "2025-07-30 22:44:43", "environment": {"php_version": "8.2.20", "os": "WINNT", "working_directory": "D:\\wwwroot\\127.0.0.1\\EasyAdmin8-webman"}, "summary": {"total_tests": 2, "passed_tests": 1, "failed_tests": 1, "success_rate": 50}, "results": {"mysql": {"status": "api_error", "message": "请先登录后台"}, "mysql_read": {"status": "api_error", "message": "请先登录后台"}, "mysql_second": {"status": "api_error", "message": "请先登录后台"}, "mysql_log": {"status": "api_error", "message": "请先登录后台"}, "mysql_cache": {"status": "api_error", "message": "请先登录后台"}, "简化API接口测试": {"status": "failed", "duration": 2.4, "output": "🔧 CURD生成器V2 - 简化API测试\n==================================================\n\n🌐 测试1: 检查服务器状态\n------------------------------\n✅ 服务器运行正常 (HTTP 200)\n\n🗄️  测试2: 测试各数据库连接\n------------------------------\n测试连接: mysql\n  ❌ API错误: 请先登录后台\n\n测试连接: mysql_read\n  ❌ API错误: 请先登录后台\n\n测试连接: mysql_second\n  ❌ API错误: 请先登录后台\n\n测试连接: mysql_log\n  ❌ API错误: 请先登录后台\n\n测试连接: mysql_cache\n  ❌ API错误: 请先登录后台\n\n🎯 测试3: 重点测试mysql_second连接\n------------------------------\n❌ mysql_second 连接测试失败\n错误详情: {\n    \"status\": \"api_error\",\n    \"message\": \"请先登录后台\"\n}\n\n🔍 测试4: 测试表分析功能\n------------------------------\n❌ 表分析失败: 请先登录后台\n📄 原始响应: {\"code\":0,\"msg\":\"请先登录后台\",\"data\":[],\"url\":\"\\/admin\\/login\",\"wait\":3,\"__token__\":\"\"}...\n\n📊 测试总结\n==================================================\n📈 连接测试结果:\n  总连接数: 5\n  成功连接: 0\n  失败连接: 5\n  成功率: 0%\n\n⚠️  发现API问题，需要修复:\n   - mysql: api_error\n   - mysql_read: api_error\n   - mysql_second: api_error\n   - mysql_log: api_error\n   - mysql_cache: api_error\n\n💡 下一步:\n   - 访问简化测试页面: http://localhost:8787/test/simple-connection-test.html\n   - 访问调试页面: http://localhost:8787/test/debug-api.html\n   - 访问实际页面: http://localhost:8787/admin/system/curdgeneratev2\n\n==================================================\n简化API测试完成！\n"}}}