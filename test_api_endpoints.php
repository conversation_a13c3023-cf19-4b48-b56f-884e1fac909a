<?php
/**
 * API 接口测试脚本
 */

echo "=== API 接口测试 ===\n\n";

$baseUrl = 'http://localhost:8787';

// 测试函数
function testApi($url, $data = null, $description = '') {
    echo "测试: {$description}\n";
    echo "URL: {$url}\n";
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    
    if ($data) {
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/x-www-form-urlencoded',
        ]);
        echo "数据: " . json_encode($data) . "\n";
    }
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($error) {
        echo "❌ 请求失败: {$error}\n";
        return false;
    }
    
    echo "状态码: {$httpCode}\n";
    
    if ($httpCode === 200) {
        // 尝试解析 JSON 响应
        $json = json_decode($response, true);
        if ($json !== null) {
            echo "✅ JSON 响应: " . json_encode($json, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT) . "\n";
            return $json;
        } else {
            // 如果不是 JSON，显示前 200 个字符
            $preview = substr($response, 0, 200);
            if (strlen($response) > 200) {
                $preview .= '...';
            }
            echo "✅ HTML 响应: {$preview}\n";
            return true;
        }
    } else {
        echo "❌ HTTP 错误: {$httpCode}\n";
        echo "响应: " . substr($response, 0, 200) . "\n";
        return false;
    }
}

// 测试 1: 访问 CURD 生成器页面
echo "1. 测试 CURD 生成器页面访问\n";
$result = testApi("{$baseUrl}/admin/system/curdgeneratev2", null, "CURD 生成器页面");
echo "\n" . str_repeat("-", 80) . "\n\n";

// 测试 2: 访问数据库测试页面
echo "2. 测试数据库测试页面访问\n";
$result = testApi("{$baseUrl}/admin/system/databasetest", null, "数据库测试页面");
echo "\n" . str_repeat("-", 80) . "\n\n";

// 测试 3: 测试获取表列表 API
echo "3. 测试获取表列表 API\n";
$result = testApi("{$baseUrl}/admin/system/curdgeneratev2", [
    'action' => 'get_tables',
    'connection' => 'mysql'
], "获取默认数据库表列表");
echo "\n" . str_repeat("-", 80) . "\n\n";

// 测试 4: 测试数据库连接测试 API
echo "4. 测试数据库连接测试 API\n";
$result = testApi("{$baseUrl}/admin/system/databasetest", [
    'connection' => 'mysql'
], "测试默认数据库连接");
echo "\n" . str_repeat("-", 80) . "\n\n";

// 测试 5: 测试不同数据库连接的表列表
echo "5. 测试不同数据库连接\n";
$connections = ['mysql', 'mysql_read', 'mysql_second'];
foreach ($connections as $conn) {
    echo "测试连接: {$conn}\n";
    $result = testApi("{$baseUrl}/admin/system/curdgeneratev2", [
        'action' => 'get_tables',
        'connection' => $conn
    ], "获取 {$conn} 数据库表列表");
    echo "\n";
}
echo str_repeat("-", 80) . "\n\n";

echo "=== API 测试完成 ===\n";
echo "如果所有测试都显示 ✅，说明多数据库 CURD 生成器功能正常\n";
echo "\n访问地址:\n";
echo "• CURD 生成器: {$baseUrl}/admin/system/curdgeneratev2\n";
echo "• 数据库测试: {$baseUrl}/admin/system/databasetest\n";
?>
