<?php

namespace {{namespace}};

use support\Model;
@if(enableSoftDelete)
use Illuminate\Database\Eloquent\SoftDeletes;
@endif

/**
 * {{className}} 模型
 */
class {{className}} extends Model
{
@if(enableSoftDelete)
    use SoftDeletes;
@endif

    /**
     * 数据表名
     */
    protected $table = '{{tableName}}';

    /**
     * 主键
     */
    protected $primaryKey = '{{primaryKey}}';

@if(enableTimestamps)
    /**
     * 时间戳
     */
    public $timestamps = true;
@else
    /**
     * 时间戳
     */
    public $timestamps = false;
@endif

    /**
     * 可批量赋值的属性
     */
    protected $fillable = {{fillable}};

    /**
     * 属性类型转换
     */
    protected $casts = {{casts}};

@if(dates)
    /**
     * 日期字段
     */
    protected $dates = {{dates}};
@endif

@if(enableSoftDelete)
    /**
     * 软删除字段
     */
    protected $deleteTime = 'deleted_at';
@endif

    /**
     * 获取器示例
     */
    public function getStatusTextAttribute()
    {
        $statusMap = [
            0 => '禁用',
            1 => '启用',
        ];
        
        return $statusMap[$this->status] ?? '未知';
    }

    /**
     * 修改器示例
     */
    public function setPasswordAttribute($value)
    {
        if (!empty($value)) {
            $this->attributes['password'] = password_hash($value, PASSWORD_DEFAULT);
        }
    }

    /**
     * 作用域：启用状态
     */
    public function scopeEnabled($query)
    {
        return $query->where('status', 1);
    }

    /**
     * 作用域：禁用状态
     */
    public function scopeDisabled($query)
    {
        return $query->where('status', 0);
    }

    /**
     * 关联关系示例
     * 
     * 一对一关系
     * public function profile()
     * {
     *     return $this->hasOne(UserProfile::class);
     * }
     * 
     * 一对多关系
     * public function posts()
     * {
     *     return $this->hasMany(Post::class);
     * }
     * 
     * 多对多关系
     * public function roles()
     * {
     *     return $this->belongsToMany(Role::class);
     * }
     * 
     * 反向关联
     * public function user()
     * {
     *     return $this->belongsTo(User::class);
     * }
     */
}
