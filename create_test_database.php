<?php
/**
 * 创建测试数据库和表的脚本
 */

echo "=== 创建测试数据库和表 ===\n\n";

// 尝试不同的连接方式
$connectionAttempts = [
    ['host' => '127.0.0.1', 'username' => 'root', 'password' => ''],
    ['host' => '127.0.0.1', 'username' => 'root', 'password' => 'root'],
    ['host' => '127.0.0.1', 'username' => 'root', 'password' => '123456'],
    ['host' => 'localhost', 'username' => 'root', 'password' => ''],
];

$pdo = null;
$workingConnection = null;

foreach ($connectionAttempts as $conn) {
    try {
        echo "🔍 尝试连接: {$conn['username']}@{$conn['host']} (密码: " . ($conn['password'] ?: '空') . ")\n";
        
        $dsn = "mysql:host={$conn['host']};charset=utf8mb4";
        $pdo = new PDO($dsn, $conn['username'], $conn['password'], [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_TIMEOUT => 5
        ]);
        
        echo "   ✅ 连接成功！\n";
        $workingConnection = $conn;
        break;
        
    } catch (PDOException $e) {
        echo "   ❌ 连接失败: " . $e->getMessage() . "\n";
    }
}

if (!$pdo) {
    echo "\n❌ 无法连接到 MySQL 数据库\n";
    echo "请检查:\n";
    echo "1. MySQL 服务是否启动\n";
    echo "2. 用户名和密码是否正确\n";
    echo "3. 防火墙设置\n";
    exit(1);
}

echo "\n🎉 成功连接到 MySQL！\n";
echo "使用连接: {$workingConnection['username']}@{$workingConnection['host']}\n\n";

try {
    // 创建 easyadmin8 数据库
    echo "📋 创建数据库 easyadmin8...\n";
    $pdo->exec("CREATE DATABASE IF NOT EXISTS easyadmin8 CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci");
    echo "   ✅ 数据库创建成功\n";
    
    // 选择数据库
    $pdo->exec("USE easyadmin8");
    
    // 创建测试表
    echo "\n📋 创建测试表...\n";
    
    // 用户表
    $userTableSQL = "
    CREATE TABLE IF NOT EXISTS ea8_users (
        id INT AUTO_INCREMENT PRIMARY KEY,
        username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
        email VARCHAR(100) COMMENT '邮箱',
        password VARCHAR(255) NOT NULL COMMENT '密码',
        status TINYINT DEFAULT 1 COMMENT '状态:1=正常,0=禁用',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
    ) ENGINE=InnoDB COMMENT='用户表'";
    
    $pdo->exec($userTableSQL);
    echo "   ✅ ea8_users 表创建成功\n";
    
    // 文章表
    $articleTableSQL = "
    CREATE TABLE IF NOT EXISTS ea8_articles (
        id INT AUTO_INCREMENT PRIMARY KEY,
        title VARCHAR(200) NOT NULL COMMENT '标题',
        content TEXT COMMENT '内容',
        author_id INT COMMENT '作者ID',
        category_id INT COMMENT '分类ID',
        status TINYINT DEFAULT 1 COMMENT '状态:1=发布,0=草稿',
        views INT DEFAULT 0 COMMENT '浏览量',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
        INDEX idx_author (author_id),
        INDEX idx_category (category_id),
        INDEX idx_status (status)
    ) ENGINE=InnoDB COMMENT='文章表'";
    
    $pdo->exec($articleTableSQL);
    echo "   ✅ ea8_articles 表创建成功\n";
    
    // 分类表
    $categoryTableSQL = "
    CREATE TABLE IF NOT EXISTS ea8_categories (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(100) NOT NULL COMMENT '分类名称',
        description TEXT COMMENT '分类描述',
        parent_id INT DEFAULT 0 COMMENT '父分类ID',
        sort_order INT DEFAULT 0 COMMENT '排序',
        status TINYINT DEFAULT 1 COMMENT '状态:1=启用,0=禁用',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
        INDEX idx_parent (parent_id),
        INDEX idx_status (status)
    ) ENGINE=InnoDB COMMENT='分类表'";
    
    $pdo->exec($categoryTableSQL);
    echo "   ✅ ea8_categories 表创建成功\n";
    
    // 配置表
    $configTableSQL = "
    CREATE TABLE IF NOT EXISTS ea8_config (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(100) NOT NULL UNIQUE COMMENT '配置名称',
        value TEXT COMMENT '配置值',
        description VARCHAR(255) COMMENT '配置描述',
        group_name VARCHAR(50) DEFAULT 'system' COMMENT '配置分组',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
        INDEX idx_group (group_name)
    ) ENGINE=InnoDB COMMENT='配置表'";
    
    $pdo->exec($configTableSQL);
    echo "   ✅ ea8_config 表创建成功\n";
    
    // 插入一些测试数据
    echo "\n📋 插入测试数据...\n";
    
    // 插入用户数据
    $pdo->exec("INSERT IGNORE INTO ea8_users (username, email, password) VALUES 
        ('admin', '<EMAIL>', '" . password_hash('admin123', PASSWORD_DEFAULT) . "'),
        ('user1', '<EMAIL>', '" . password_hash('123456', PASSWORD_DEFAULT) . "'),
        ('user2', '<EMAIL>', '" . password_hash('123456', PASSWORD_DEFAULT) . "')");
    echo "   ✅ 用户数据插入成功\n";
    
    // 插入分类数据
    $pdo->exec("INSERT IGNORE INTO ea8_categories (id, name, description) VALUES 
        (1, '技术', '技术相关文章'),
        (2, '生活', '生活相关文章'),
        (3, '学习', '学习相关文章')");
    echo "   ✅ 分类数据插入成功\n";
    
    // 插入文章数据
    $pdo->exec("INSERT IGNORE INTO ea8_articles (title, content, author_id, category_id) VALUES 
        ('测试文章1', '这是第一篇测试文章的内容', 1, 1),
        ('测试文章2', '这是第二篇测试文章的内容', 1, 2),
        ('测试文章3', '这是第三篇测试文章的内容', 2, 1)");
    echo "   ✅ 文章数据插入成功\n";
    
    // 插入配置数据
    $pdo->exec("INSERT IGNORE INTO ea8_config (name, value, description, group_name) VALUES 
        ('site_name', 'EasyAdmin8', '网站名称', 'basic'),
        ('site_url', 'http://localhost:8787', '网站地址', 'basic'),
        ('admin_email', '<EMAIL>', '管理员邮箱', 'basic')");
    echo "   ✅ 配置数据插入成功\n";
    
    // 验证表创建
    echo "\n📊 验证表创建结果...\n";
    $stmt = $pdo->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    echo "   创建的表数量: " . count($tables) . "\n";
    foreach ($tables as $table) {
        // 获取表的记录数
        $countStmt = $pdo->query("SELECT COUNT(*) FROM {$table}");
        $count = $countStmt->fetchColumn();
        echo "   - {$table}: {$count} 条记录\n";
    }
    
    echo "\n🔧 更新数据库配置建议:\n";
    echo "请确保 config/database.php 中的配置为:\n";
    echo "DB_HOST={$workingConnection['host']}\n";
    echo "DB_PORT=3306\n";
    echo "DB_DATABASE=easyadmin8\n";
    echo "DB_USERNAME={$workingConnection['username']}\n";
    echo "DB_PASSWORD={$workingConnection['password']}\n";
    echo "DB_PREFIX=ea8_\n";
    
} catch (PDOException $e) {
    echo "❌ 创建数据库或表失败: " . $e->getMessage() . "\n";
    exit(1);
}

echo "\n🎉 测试数据库和表创建完成！\n";
echo "\n现在可以测试多数据库 CURD 生成器功能了:\n";
echo "1. 访问: http://localhost:8787/admin/system/curdgeneratev2\n";
echo "2. 选择数据库连接: mysql\n";
echo "3. 点击刷新表列表\n";
echo "4. 应该能看到 4 个表: users, articles, categories, config\n";

echo "\n=== 创建完成 ===\n";
?>
