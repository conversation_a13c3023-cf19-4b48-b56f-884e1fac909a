<?php
/**
 * 环境函数修复脚本
 * 修复 env() 函数未定义的问题
 */

echo "=== 环境函数修复脚本 ===\n\n";

/**
 * 创建环境函数定义文件
 */
function createEnvFunction() {
    echo "1. 创建环境函数定义\n";
    
    $envContent = '<?php
/**
 * 环境变量辅助函数
 */

if (!function_exists(\'env\')) {
    /**
     * 获取环境变量值
     * @param string $key 环境变量名
     * @param mixed $default 默认值
     * @return mixed
     */
    function env($key, $default = null) {
        $value = getenv($key);
        
        if ($value === false) {
            return $default;
        }
        
        // 处理布尔值
        switch (strtolower($value)) {
            case \'true\':
            case \'(true)\':
                return true;
            case \'false\':
            case \'(false)\':
                return false;
            case \'empty\':
            case \'(empty)\':
                return \'\';
            case \'null\':
            case \'(null)\':
                return null;
        }
        
        // 处理引号包围的字符串
        if (strlen($value) > 1 && $value[0] === \'"\' && $value[strlen($value) - 1] === \'"\') {
            return substr($value, 1, -1);
        }
        
        return $value;
    }
}

if (!function_exists(\'config\')) {
    /**
     * 获取配置值
     * @param string $key 配置键名
     * @param mixed $default 默认值
     * @return mixed
     */
    function config($key = null, $default = null) {
        static $configs = [];
        
        if ($key === null) {
            return $configs;
        }
        
        if (isset($configs[$key])) {
            return $configs[$key];
        }
        
        // 尝试加载配置文件
        $parts = explode(\'.\', $key);
        $configFile = $parts[0];
        $configPath = __DIR__ . \'/config/\' . $configFile . \'.php\';
        
        if (file_exists($configPath)) {
            $config = include $configPath;
            if (is_array($config)) {
                $configs[$configFile] = $config;
                
                // 获取嵌套值
                $value = $config;
                for ($i = 1; $i < count($parts); $i++) {
                    if (isset($value[$parts[$i]])) {
                        $value = $value[$parts[$i]];
                    } else {
                        return $default;
                    }
                }
                return $value;
            }
        }
        
        return $default;
    }
}

if (!function_exists(\'base_path\')) {
    /**
     * 获取项目根目录路径
     * @param string $path 相对路径
     * @return string
     */
    function base_path($path = \'\') {
        return __DIR__ . ($path ? DIRECTORY_SEPARATOR . ltrim($path, DIRECTORY_SEPARATOR) : \'\');
    }
}

if (!function_exists(\'runtime_path\')) {
    /**
     * 获取运行时目录路径
     * @param string $path 相对路径
     * @return string
     */
    function runtime_path($path = \'\') {
        return base_path(\'runtime\') . ($path ? DIRECTORY_SEPARATOR . ltrim($path, DIRECTORY_SEPARATOR) : \'\');
    }
}

if (!function_exists(\'public_path\')) {
    /**
     * 获取公共目录路径
     * @param string $path 相对路径
     * @return string
     */
    function public_path($path = \'\') {
        return base_path(\'public\') . ($path ? DIRECTORY_SEPARATOR . ltrim($path, DIRECTORY_SEPARATOR) : \'\');
    }
}

if (!function_exists(\'config_path\')) {
    /**
     * 获取配置目录路径
     * @param string $path 相对路径
     * @return string
     */
    function config_path($path = \'\') {
        return base_path(\'config\') . ($path ? DIRECTORY_SEPARATOR . ltrim($path, DIRECTORY_SEPARATOR) : \'\');
    }
}
';
    
    $filename = 'bootstrap/helpers.php';
    
    // 创建bootstrap目录
    if (!is_dir('bootstrap')) {
        if (mkdir('bootstrap', 0755, true)) {
            echo "   ✅ 创建bootstrap目录成功\n";
        } else {
            echo "   ❌ 创建bootstrap目录失败\n";
            return false;
        }
    }
    
    // 写入辅助函数文件
    if (file_put_contents($filename, $envContent)) {
        echo "   ✅ 创建环境函数文件: {$filename}\n";
        return true;
    } else {
        echo "   ❌ 创建环境函数文件失败\n";
        return false;
    }
}

/**
 * 修复配置文件
 */
function fixConfigFiles() {
    echo "\n2. 修复配置文件\n";
    
    $configFiles = [
        'config/app.php',
        'config/server.php',
        'config/database.php'
    ];
    
    $fixed = 0;
    
    foreach ($configFiles as $file) {
        if (file_exists($file)) {
            $content = file_get_contents($file);
            
            // 检查是否需要添加辅助函数引入
            if (strpos($content, 'require_once') === false && strpos($content, 'env(') !== false) {
                // 在文件开头添加辅助函数引入
                $newContent = "<?php\n\n// 引入辅助函数\nif (file_exists(__DIR__ . '/../bootstrap/helpers.php')) {\n    require_once __DIR__ . '/../bootstrap/helpers.php';\n}\n\n" . substr($content, 5);
                
                if (file_put_contents($file, $newContent)) {
                    echo "   ✅ 修复配置文件: {$file}\n";
                    $fixed++;
                } else {
                    echo "   ❌ 修复配置文件失败: {$file}\n";
                }
            } else {
                echo "   ✅ 配置文件无需修复: {$file}\n";
                $fixed++;
            }
        } else {
            echo "   ⚠️  配置文件不存在: {$file}\n";
        }
    }
    
    return $fixed > 0;
}

/**
 * 创建环境变量文件
 */
function createEnvFile() {
    echo "\n3. 创建环境变量文件\n";
    
    $envContent = '# EasyAdmin8-webman 环境配置

# 应用配置
APP_NAME=EasyAdmin8
APP_ENV=local
APP_DEBUG=true
APP_URL=http://localhost:8787

# 数据库配置
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=easyadmin8
DB_USERNAME=root
DB_PASSWORD=

# 缓存配置
CACHE_DRIVER=file

# 会话配置
SESSION_DRIVER=file

# 日志配置
LOG_CHANNEL=daily

# API文档配置
API_DOC_ENABLED=true
API_DOC_VERSION=1.0.0
API_DOC_TITLE="EasyAdmin8 API文档"
';
    
    $filename = '.env';
    
    if (!file_exists($filename)) {
        if (file_put_contents($filename, $envContent)) {
            echo "   ✅ 创建环境变量文件: {$filename}\n";
            return true;
        } else {
            echo "   ❌ 创建环境变量文件失败\n";
            return false;
        }
    } else {
        echo "   ✅ 环境变量文件已存在: {$filename}\n";
        return true;
    }
}

/**
 * 测试修复结果
 */
function testFixResult() {
    echo "\n4. 测试修复结果\n";
    
    try {
        // 引入辅助函数
        if (file_exists('bootstrap/helpers.php')) {
            require_once 'bootstrap/helpers.php';
            echo "   ✅ 辅助函数加载成功\n";
        } else {
            echo "   ❌ 辅助函数文件不存在\n";
            return false;
        }
        
        // 测试env函数
        $testValue = env('APP_NAME', 'default');
        echo "   ✅ env函数测试成功: {$testValue}\n";
        
        // 测试配置文件加载
        $configFiles = ['app', 'server'];
        foreach ($configFiles as $configName) {
            $configFile = "config/{$configName}.php";
            if (file_exists($configFile)) {
                try {
                    $config = include $configFile;
                    if (is_array($config)) {
                        echo "   ✅ 配置文件加载成功: {$configFile}\n";
                    } else {
                        echo "   ❌ 配置文件格式错误: {$configFile}\n";
                    }
                } catch (Exception $e) {
                    echo "   ❌ 配置文件加载失败: {$configFile} - " . $e->getMessage() . "\n";
                }
            }
        }
        
        return true;
        
    } catch (Exception $e) {
        echo "   ❌ 测试失败: " . $e->getMessage() . "\n";
        return false;
    }
}

/**
 * 生成修复后的启动脚本
 */
function generateFixedStartupScript() {
    echo "\n5. 生成修复后的启动脚本\n";
    
    $isWindows = PHP_OS_FAMILY === 'Windows';
    
    if ($isWindows) {
        $script = "@echo off\n";
        $script .= "echo Starting EasyAdmin8-webman server (Fixed Version)...\n";
        $script .= "cd /d \"%~dp0\"\n";
        $script .= "echo Checking environment...\n";
        $script .= "php -v\n";
        $script .= "echo Starting server on port 8080 (alternative port)...\n";
        $script .= "php -S localhost:8080 -t public\n";
        $script .= "pause\n";
        
        $filename = 'start_server_fixed.bat';
    } else {
        $script = "#!/bin/bash\n";
        $script .= "echo \"Starting EasyAdmin8-webman server (Fixed Version)...\"\n";
        $script .= "cd \"$(dirname \"$0\")\"\n";
        $script .= "echo \"Checking environment...\"\n";
        $script .= "php -v\n";
        $script .= "echo \"Starting server on port 8080 (alternative port)...\"\n";
        $script .= "php -S localhost:8080 -t public\n";
        
        $filename = 'start_server_fixed.sh';
    }
    
    if (file_put_contents($filename, $script)) {
        if (!$isWindows) {
            chmod($filename, 0755);
        }
        echo "   ✅ 生成修复版启动脚本: {$filename}\n";
        return true;
    } else {
        echo "   ❌ 生成启动脚本失败\n";
        return false;
    }
}

// 执行修复
try {
    echo "开始修复EasyAdmin8-webman环境问题...\n\n";
    
    $results = [];
    $results['env_function'] = createEnvFunction();
    $results['config_files'] = fixConfigFiles();
    $results['env_file'] = createEnvFile();
    $results['test_result'] = testFixResult();
    $results['startup_script'] = generateFixedStartupScript();
    
    echo "\n=== 修复结果 ===\n";
    
    $passed = array_sum($results);
    $total = count($results);
    $successRate = ($passed / $total) * 100;
    
    echo "📊 修复统计:\n";
    echo "   - 成功项: {$passed}/{$total}\n";
    echo "   - 成功率: " . number_format($successRate, 1) . "%\n";
    
    if ($successRate >= 80) {
        echo "   🎉 修复成功！环境问题已解决\n";
    } else {
        echo "   ⚠️  部分修复失败，请手动检查\n";
    }
    
    echo "\n🚀 使用建议:\n";
    echo "   1. 使用修复版启动脚本启动服务器\n";
    echo "   2. 访问地址: http://localhost:8080/admin/system/apidoc\n";
    echo "   3. 如果仍有问题，请检查PHP环境和端口占用\n";
    
} catch (Exception $e) {
    echo "❌ 修复过程中发生错误: " . $e->getMessage() . "\n";
    echo "📍 错误位置: " . $e->getFile() . ":" . $e->getLine() . "\n";
}

echo "\n=== 环境修复完成 ===\n";
