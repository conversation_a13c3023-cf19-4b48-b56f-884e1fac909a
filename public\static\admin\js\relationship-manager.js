/**
 * CURD 关联关系管理组件
 * 支持关联关系分析、可视化展示和代码生成
 */
define(['jquery', 'layui'], function($, layui) {
    'use strict';
    
    var RelationshipManager = function(options) {
        this.options = $.extend({
            apiUrl: '/admin/system/curd_generate_v2',
            container: '#relationship-manager-container',
            enableAutoAnalyze: true,
            confidenceThreshold: 70 // 置信度阈值
        }, options);
        
        this.container = $(this.options.container);
        this.currentTable = null;
        this.relationships = [];
        this.relationshipTypes = {
            'belongsTo': { name: '属于', icon: '↗', color: '#1890ff' },
            'hasOne': { name: '拥有一个', icon: '↘', color: '#52c41a' },
            'hasMany': { name: '拥有多个', icon: '↓', color: '#fa8c16' },
            'belongsToMany': { name: '多对多', icon: '↔', color: '#722ed1' }
        };
        
        this.init();
    };
    
    RelationshipManager.prototype = {
        
        /**
         * 初始化关联关系管理器
         */
        init: function() {
            this.bindEvents();
            this.initUI();
        },
        
        /**
         * 绑定事件
         */
        bindEvents: function() {
            var self = this;
            
            // 分析关联关系
            $(document).on('click', '.analyze-relationships-btn', function() {
                self.analyzeRelationships();
            });
            
            // 查看关联详情
            $(document).on('click', '.view-relationship-detail', function() {
                var relationshipId = $(this).data('id');
                self.showRelationshipDetail(relationshipId);
            });
            
            // 生成关联代码
            $(document).on('click', '.generate-relationship-code', function() {
                self.generateRelationshipCode();
            });
            
            // 切换关联关系启用状态
            $(document).on('change', '.relationship-enabled', function() {
                var relationshipId = $(this).data('id');
                var enabled = $(this).prop('checked');
                self.toggleRelationship(relationshipId, enabled);
            });
        },
        
        /**
         * 初始化UI
         */
        initUI: function() {
            if (this.container.length === 0) {
                return;
            }
            
            var html = `
                <div class="relationship-manager">
                    <div class="relationship-header">
                        <h3>关联关系分析</h3>
                        <div class="relationship-actions">
                            <button type="button" class="layui-btn layui-btn-sm analyze-relationships-btn">
                                <i class="layui-icon layui-icon-search"></i> 分析关联关系
                            </button>
                            <button type="button" class="layui-btn layui-btn-sm layui-btn-normal generate-relationship-code">
                                <i class="layui-icon layui-icon-code"></i> 生成关联代码
                            </button>
                        </div>
                    </div>
                    <div class="relationship-content">
                        <div class="relationship-stats" id="relationship-stats"></div>
                        <div class="relationship-list" id="relationship-list"></div>
                        <div class="relationship-preview" id="relationship-preview"></div>
                    </div>
                </div>
            `;
            
            this.container.html(html);
        },
        
        /**
         * 分析关联关系
         */
        analyzeRelationships: function() {
            var self = this;
            
            if (!this.currentTable) {
                layer.msg('请先选择数据表');
                return;
            }
            
            var loading = layer.load(2, { content: '正在分析关联关系...' });
            
            $.ajax({
                url: this.options.apiUrl,
                type: 'POST',
                data: {
                    action: 'analyze_relationships',
                    table_name: this.currentTable
                },
                success: function(response) {
                    layer.close(loading);
                    
                    if (response.code === 0) {
                        self.relationships = response.data;
                        self.renderRelationships();
                        self.updateStats();
                        layer.msg('关联关系分析完成', { icon: 1 });
                    } else {
                        layer.msg(response.msg || '分析失败');
                    }
                },
                error: function() {
                    layer.close(loading);
                    layer.msg('网络错误');
                }
            });
        },
        
        /**
         * 渲染关联关系列表
         */
        renderRelationships: function() {
            var self = this;
            var container = $('#relationship-list');
            
            if (this.relationships.length === 0) {
                container.html('<div class="no-relationships">未发现关联关系</div>');
                return;
            }
            
            var html = '<div class="relationships-grid">';
            
            this.relationships.forEach(function(relationship, index) {
                var typeInfo = self.relationshipTypes[relationship.type];
                var confidenceClass = self.getConfidenceClass(relationship.confidence);
                
                html += `
                    <div class="relationship-card ${confidenceClass}" data-id="${index}">
                        <div class="relationship-card-header">
                            <div class="relationship-type">
                                <span class="type-icon" style="color: ${typeInfo.color}">${typeInfo.icon}</span>
                                <span class="type-name">${typeInfo.name}</span>
                            </div>
                            <div class="relationship-confidence">
                                <span class="confidence-value">${relationship.confidence}%</span>
                                <div class="confidence-bar">
                                    <div class="confidence-fill" style="width: ${relationship.confidence}%"></div>
                                </div>
                            </div>
                        </div>
                        <div class="relationship-card-body">
                            <div class="relationship-info">
                                <div class="related-table">${relationship.related_table}</div>
                                <div class="relationship-description">${relationship.description}</div>
                            </div>
                            <div class="relationship-details">
                                <div class="detail-item">
                                    <span class="detail-label">方法名:</span>
                                    <span class="detail-value">${relationship.method_name}</span>
                                </div>
                                ${self.renderRelationshipSpecificDetails(relationship)}
                            </div>
                        </div>
                        <div class="relationship-card-footer">
                            <label class="layui-form-switch">
                                <input type="checkbox" class="relationship-enabled" data-id="${index}" 
                                       ${relationship.confidence >= self.options.confidenceThreshold ? 'checked' : ''}>
                                <div class="layui-form-switch-text">启用|禁用</div>
                            </label>
                            <button type="button" class="layui-btn layui-btn-xs view-relationship-detail" data-id="${index}">
                                查看详情
                            </button>
                        </div>
                    </div>
                `;
            });
            
            html += '</div>';
            container.html(html);
            
            // 重新渲染表单组件
            layui.form.render();
        },
        
        /**
         * 渲染关联关系特定详情
         */
        renderRelationshipSpecificDetails: function(relationship) {
            var html = '';
            
            switch (relationship.type) {
                case 'belongsTo':
                    html += `
                        <div class="detail-item">
                            <span class="detail-label">外键:</span>
                            <span class="detail-value">${relationship.foreign_key}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">关联键:</span>
                            <span class="detail-value">${relationship.owner_key}</span>
                        </div>
                    `;
                    break;
                case 'hasOne':
                case 'hasMany':
                    html += `
                        <div class="detail-item">
                            <span class="detail-label">外键:</span>
                            <span class="detail-value">${relationship.foreign_key}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">本地键:</span>
                            <span class="detail-value">${relationship.local_key}</span>
                        </div>
                    `;
                    break;
                case 'belongsToMany':
                    html += `
                        <div class="detail-item">
                            <span class="detail-label">中间表:</span>
                            <span class="detail-value">${relationship.pivot_table}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">外键:</span>
                            <span class="detail-value">${relationship.foreign_pivot_key}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">关联键:</span>
                            <span class="detail-value">${relationship.related_pivot_key}</span>
                        </div>
                    `;
                    break;
            }
            
            return html;
        },
        
        /**
         * 获取置信度样式类
         */
        getConfidenceClass: function(confidence) {
            if (confidence >= 90) return 'confidence-high';
            if (confidence >= 70) return 'confidence-medium';
            return 'confidence-low';
        },
        
        /**
         * 更新统计信息
         */
        updateStats: function() {
            var container = $('#relationship-stats');
            var stats = this.calculateStats();
            
            var html = `
                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-value">${stats.total}</div>
                        <div class="stat-label">总关联数</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">${stats.highConfidence}</div>
                        <div class="stat-label">高置信度</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">${stats.enabled}</div>
                        <div class="stat-label">已启用</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">${stats.avgConfidence}%</div>
                        <div class="stat-label">平均置信度</div>
                    </div>
                </div>
            `;
            
            container.html(html);
        },
        
        /**
         * 计算统计信息
         */
        calculateStats: function() {
            var total = this.relationships.length;
            var highConfidence = this.relationships.filter(r => r.confidence >= 90).length;
            var enabled = this.relationships.filter(r => r.confidence >= this.options.confidenceThreshold).length;
            var avgConfidence = total > 0 ? Math.round(this.relationships.reduce((sum, r) => sum + r.confidence, 0) / total) : 0;
            
            return {
                total: total,
                highConfidence: highConfidence,
                enabled: enabled,
                avgConfidence: avgConfidence
            };
        },
        
        /**
         * 显示关联关系详情
         */
        showRelationshipDetail: function(relationshipId) {
            var relationship = this.relationships[relationshipId];
            if (!relationship) return;
            
            var typeInfo = this.relationshipTypes[relationship.type];
            
            var html = `
                <div class="relationship-detail">
                    <div class="detail-header">
                        <h4>${typeInfo.name} - ${relationship.related_table}</h4>
                        <div class="confidence-badge confidence-${this.getConfidenceClass(relationship.confidence)}">
                            置信度: ${relationship.confidence}%
                        </div>
                    </div>
                    <div class="detail-content">
                        <div class="detail-section">
                            <h5>基本信息</h5>
                            <table class="layui-table">
                                <tr><td>关联类型</td><td>${typeInfo.name}</td></tr>
                                <tr><td>关联表</td><td>${relationship.related_table}</td></tr>
                                <tr><td>关联模型</td><td>${relationship.related_model}</td></tr>
                                <tr><td>方法名</td><td>${relationship.method_name}</td></tr>
                                <tr><td>描述</td><td>${relationship.description}</td></tr>
                            </table>
                        </div>
                        ${this.generateDetailSpecificInfo(relationship)}
                        <div class="detail-section">
                            <h5>生成的代码预览</h5>
                            <pre><code class="language-php">${this.generateCodePreview(relationship)}</code></pre>
                        </div>
                    </div>
                </div>
            `;
            
            layer.open({
                type: 1,
                title: '关联关系详情',
                content: html,
                area: ['800px', '600px'],
                maxmin: true
            });
        },
        
        /**
         * 生成详情特定信息
         */
        generateDetailSpecificInfo: function(relationship) {
            var html = '<div class="detail-section"><h5>关联参数</h5><table class="layui-table">';
            
            switch (relationship.type) {
                case 'belongsTo':
                    html += `
                        <tr><td>外键</td><td>${relationship.foreign_key}</td></tr>
                        <tr><td>关联键</td><td>${relationship.owner_key}</td></tr>
                    `;
                    break;
                case 'hasOne':
                case 'hasMany':
                    html += `
                        <tr><td>外键</td><td>${relationship.foreign_key}</td></tr>
                        <tr><td>本地键</td><td>${relationship.local_key}</td></tr>
                    `;
                    break;
                case 'belongsToMany':
                    html += `
                        <tr><td>中间表</td><td>${relationship.pivot_table}</td></tr>
                        <tr><td>外键</td><td>${relationship.foreign_pivot_key}</td></tr>
                        <tr><td>关联键</td><td>${relationship.related_pivot_key}</td></tr>
                        <tr><td>父键</td><td>${relationship.parent_key}</td></tr>
                        <tr><td>关联键</td><td>${relationship.related_key}</td></tr>
                    `;
                    break;
            }
            
            html += '</table></div>';
            return html;
        },
        
        /**
         * 生成代码预览
         */
        generateCodePreview: function(relationship) {
            var code = `public function ${relationship.method_name}()\n{\n`;
            
            switch (relationship.type) {
                case 'belongsTo':
                    code += `    return $this->belongsTo(${relationship.related_model}::class, '${relationship.foreign_key}', '${relationship.owner_key}');`;
                    break;
                case 'hasOne':
                    code += `    return $this->hasOne(${relationship.related_model}::class, '${relationship.foreign_key}', '${relationship.local_key}');`;
                    break;
                case 'hasMany':
                    code += `    return $this->hasMany(${relationship.related_model}::class, '${relationship.foreign_key}', '${relationship.local_key}');`;
                    break;
                case 'belongsToMany':
                    code += `    return $this->belongsToMany(${relationship.related_model}::class, '${relationship.pivot_table}', '${relationship.foreign_pivot_key}', '${relationship.related_pivot_key}');`;
                    break;
            }
            
            code += '\n}';
            return code;
        },
        
        /**
         * 切换关联关系启用状态
         */
        toggleRelationship: function(relationshipId, enabled) {
            if (this.relationships[relationshipId]) {
                this.relationships[relationshipId].enabled = enabled;
                
                // 触发关联关系变更事件
                if (window.onRelationshipChange) {
                    window.onRelationshipChange(this.relationships);
                }
            }
        },
        
        /**
         * 生成关联关系代码
         */
        generateRelationshipCode: function() {
            var enabledRelationships = this.relationships.filter(r => r.enabled !== false && r.confidence >= this.options.confidenceThreshold);
            
            if (enabledRelationships.length === 0) {
                layer.msg('没有启用的关联关系');
                return;
            }
            
            var self = this;
            var loading = layer.load(2, { content: '正在生成关联代码...' });
            
            // 这里可以调用后端API生成完整的关联代码
            setTimeout(function() {
                layer.close(loading);
                self.showGeneratedCode(enabledRelationships);
            }, 1000);
        },
        
        /**
         * 显示生成的代码
         */
        showGeneratedCode: function(relationships) {
            var modelCode = this.generateModelCode(relationships);
            var controllerCode = this.generateControllerCode(relationships);
            
            var html = `
                <div class="generated-code">
                    <div class="layui-tab layui-tab-brief">
                        <ul class="layui-tab-title">
                            <li class="layui-this">模型代码</li>
                            <li>控制器代码</li>
                        </ul>
                        <div class="layui-tab-content">
                            <div class="layui-tab-item layui-show">
                                <pre><code class="language-php">${modelCode}</code></pre>
                            </div>
                            <div class="layui-tab-item">
                                <pre><code class="language-php">${controllerCode}</code></pre>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            
            layer.open({
                type: 1,
                title: '生成的关联代码',
                content: html,
                area: ['900px', '700px'],
                maxmin: true,
                success: function() {
                    layui.element.render('tab');
                }
            });
        },
        
        /**
         * 生成模型代码
         */
        generateModelCode: function(relationships) {
            var code = "// 关联关系方法\n\n";
            
            relationships.forEach(function(relationship) {
                code += `/**\n * ${relationship.description}\n */\n`;
                code += `public function ${relationship.method_name}()\n{\n`;
                
                switch (relationship.type) {
                    case 'belongsTo':
                        code += `    return $this->belongsTo(${relationship.related_model}::class, '${relationship.foreign_key}', '${relationship.owner_key}');\n`;
                        break;
                    case 'hasOne':
                        code += `    return $this->hasOne(${relationship.related_model}::class, '${relationship.foreign_key}', '${relationship.local_key}');\n`;
                        break;
                    case 'hasMany':
                        code += `    return $this->hasMany(${relationship.related_model}::class, '${relationship.foreign_key}', '${relationship.local_key}');\n`;
                        break;
                    case 'belongsToMany':
                        code += `    return $this->belongsToMany(${relationship.related_model}::class, '${relationship.pivot_table}', '${relationship.foreign_pivot_key}', '${relationship.related_pivot_key}');\n`;
                        break;
                }
                
                code += "}\n\n";
            });
            
            return code;
        },
        
        /**
         * 生成控制器代码
         */
        generateControllerCode: function(relationships) {
            var code = "// 关联查询方法\n\n";
            
            relationships.forEach(function(relationship) {
                code += `/**\n * 获取${relationship.description}\n */\n`;
                code += `public function get${relationship.method_name.charAt(0).toUpperCase() + relationship.method_name.slice(1)}(Request $request): Response\n{\n`;
                code += `    $id = $request->input('id');\n`;
                code += `    $model = $this->model->find($id);\n\n`;
                code += `    if (!$model) {\n`;
                code += `        return $this->error('数据不存在');\n`;
                code += `    }\n\n`;
                
                if (relationship.type === 'hasMany' || relationship.type === 'belongsToMany') {
                    code += `    $related = $model->${relationship.method_name}()->paginate(15);\n`;
                } else {
                    code += `    $related = $model->${relationship.method_name};\n`;
                }
                
                code += `    return $this->success('获取成功', $related);\n`;
                code += "}\n\n";
            });
            
            return code;
        },
        
        /**
         * 设置当前表
         */
        setCurrentTable: function(tableName) {
            this.currentTable = tableName;
            
            if (this.options.enableAutoAnalyze && tableName) {
                this.analyzeRelationships();
            }
        },
        
        /**
         * 获取当前关联关系
         */
        getRelationships: function() {
            return this.relationships.filter(r => r.enabled !== false && r.confidence >= this.options.confidenceThreshold);
        },
        
        /**
         * 销毁组件
         */
        destroy: function() {
            this.container.empty();
            $(document).off('.relationship-manager');
        }
    };
    
    return RelationshipManager;
});
