# 开发环境和工具

## 系统要求

### 基础环境
- **操作系统**: Windows 10/11, Linux, macOS
- **PHP 版本**: >= 8.1.0
- **数据库**: MySQL >= 5.7 或 MariaDB >= 10.2
- **Web 服务器**: Nginx (推荐) 或 Apache
- **内存**: 最低 512MB，推荐 1GB+

### PHP 扩展要求
```
必需扩展:
- pdo              # PDO 数据库抽象层
- pdo_mysql        # MySQL PDO 驱动
- mysqli           # MySQL 扩展
- sockets          # Socket 扩展
- curl             # cURL 扩展
- json             # JSON 扩展
- mbstring         # 多字节字符串扩展
- openssl          # OpenSSL 扩展
- fileinfo         # 文件信息扩展
- gd               # GD 图像处理扩展

推荐扩展:
- redis            # Redis 扩展
- opcache          # OPcache 扩展
- event            # Event 扩展 (提升性能)
```

## 开发工具推荐

### IDE/编辑器
- **PhpStorm**: 专业 PHP IDE，功能强大
- **VS Code**: 轻量级编辑器，插件丰富
- **Sublime Text**: 快速响应的文本编辑器

### VS Code 推荐插件
```
PHP 相关:
- PHP Intelephense
- PHP Debug
- PHP DocBlocker
- PHP Namespace Resolver

前端相关:
- HTML CSS Support
- JavaScript (ES6) code snippets
- Auto Rename Tag
- Bracket Pair Colorizer

通用工具:
- GitLens
- Chinese Language Pack
- Material Icon Theme
- Prettier - Code formatter
```

### 数据库管理工具
- **Navicat**: 功能全面的数据库管理工具
- **phpMyAdmin**: Web 界面的 MySQL 管理工具
- **DBeaver**: 免费的通用数据库工具
- **MySQL Workbench**: MySQL 官方工具

### API 测试工具
- **Postman**: 功能强大的 API 测试工具
- **Insomnia**: 简洁的 REST 客户端
- **curl**: 命令行 HTTP 客户端

## 环境配置

### Windows 开发环境搭建
```bash
# 1. 安装 PHP (推荐使用 XAMPP 或 WampServer)
# 下载地址: https://www.apachefriends.org/

# 2. 安装 Composer
# 下载地址: https://getcomposer.org/

# 3. 安装 Git
# 下载地址: https://git-scm.com/

# 4. 克隆项目
git clone https://github.com/EasyAdmin8/EasyAdmin8-webman

# 5. 安装依赖
cd EasyAdmin8-webman
composer install

# 6. 配置环境
copy .example.env .env

# 7. 启动服务器
start_server.bat
```

### Linux 开发环境搭建
```bash
# 1. 安装 PHP 8.1+
sudo apt update
sudo apt install php8.1 php8.1-cli php8.1-fpm php8.1-mysql php8.1-curl php8.1-gd php8.1-mbstring php8.1-xml php8.1-zip

# 2. 安装 Composer
curl -sS https://getcomposer.org/installer | php
sudo mv composer.phar /usr/local/bin/composer

# 3. 安装 MySQL
sudo apt install mysql-server

# 4. 克隆项目
git clone https://github.com/EasyAdmin8/EasyAdmin8-webman

# 5. 安装依赖
cd EasyAdmin8-webman
composer install

# 6. 配置环境
cp .example.env .env

# 7. 启动服务器
php start.php start
```

## 调试工具

### 日志调试
```php
// 使用 webman 日志系统
use support\Log;

// 记录调试信息
Log::debug('调试信息', ['data' => $data]);
Log::info('信息日志', ['user_id' => $userId]);
Log::warning('警告信息', ['message' => $message]);
Log::error('错误信息', ['exception' => $e->getMessage()]);
```

### 变量调试
```php
// 使用 dump 函数 (开发环境)
dump($variable);

// 使用 var_dump
var_dump($variable);

// 使用 print_r
print_r($variable);

// 记录到日志
Log::debug('变量内容', ['variable' => $variable]);
```

### 性能调试
```php
// 记录执行时间
$startTime = microtime(true);
// ... 执行代码 ...
$endTime = microtime(true);
$executionTime = $endTime - $startTime;
Log::info('执行时间', ['time' => $executionTime . 's']);

// 记录内存使用
$memoryUsage = memory_get_usage(true);
Log::info('内存使用', ['memory' => $memoryUsage . ' bytes']);
```

## 版本控制

### Git 配置
```bash
# 配置用户信息
git config --global user.name "Your Name"
git config --global user.email "<EMAIL>"

# 配置编辑器
git config --global core.editor "code --wait"

# 配置换行符 (Windows)
git config --global core.autocrlf true

# 配置换行符 (Linux/Mac)
git config --global core.autocrlf input
```

### 分支管理策略
```bash
# 主分支
main                # 生产环境分支
develop            # 开发分支

# 功能分支
feature/功能名称    # 新功能开发
bugfix/问题描述     # 问题修复
hotfix/紧急修复     # 紧急修复
```

## 性能优化工具

### PHP 性能优化
```bash
# 启用 OPcache
# 在 php.ini 中配置:
opcache.enable=1
opcache.memory_consumption=128
opcache.interned_strings_buffer=8
opcache.max_accelerated_files=4000
opcache.revalidate_freq=60
opcache.fast_shutdown=1
```

### 数据库性能监控
```sql
-- 查看慢查询
SHOW VARIABLES LIKE 'slow_query_log';
SHOW VARIABLES LIKE 'long_query_time';

-- 分析查询性能
EXPLAIN SELECT * FROM ea8_admin_user WHERE username = 'admin';
```

### 缓存配置
```php
// Redis 缓存配置
'redis' => [
    'host' => '127.0.0.1',
    'port' => 6379,
    'auth' => '',
    'db' => 0,
    'max_connections' => 1024,
]
```

## 测试环境

### 单元测试
```bash
# 安装 PHPUnit
composer require --dev phpunit/phpunit

# 运行测试
./vendor/bin/phpunit
```

### 功能测试
```bash
# 运行项目自带的测试脚本
php 测试脚本.php
php 功能验证测试脚本.php
php 完整环境修复脚本.php
```

### 压力测试
```bash
# 使用 Apache Bench
ab -n 1000 -c 10 http://localhost:8787/

# 使用 wrk
wrk -t12 -c400 -d30s http://localhost:8787/
```