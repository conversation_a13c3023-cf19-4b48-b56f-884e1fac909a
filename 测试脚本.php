<?php
/**
 * EasyAdmin8-webman 项目测试脚本
 * 用于检查项目环境和配置是否正确
 */

echo "=== EasyAdmin8-webman 项目测试脚本 ===\n\n";

// 1. PHP 环境检查
echo "1. PHP 环境检查\n";
echo "   PHP 版本: " . PHP_VERSION . "\n";
echo "   PHP SAPI: " . php_sapi_name() . "\n";
echo "   操作系统: " . PHP_OS . "\n";

// 检查 PHP 版本
if (version_compare(PHP_VERSION, '8.1.0', '<')) {
    echo "   ❌ PHP 版本过低，需要 >= 8.1.0\n";
} else {
    echo "   ✅ PHP 版本符合要求\n";
}

// 2. 必要扩展检查
echo "\n2. PHP 扩展检查\n";
$required_extensions = [
    'pdo' => 'PDO 数据库抽象层',
    'pdo_mysql' => 'MySQL PDO 驱动',
    'mysqli' => 'MySQL 扩展',
    'sockets' => 'Socket 扩展',
    'curl' => 'cURL 扩展',
    'json' => 'JSON 扩展',
    'mbstring' => '多字节字符串扩展',
    'openssl' => 'OpenSSL 扩展'
];

foreach ($required_extensions as $ext => $desc) {
    if (extension_loaded($ext)) {
        echo "   ✅ {$desc} ({$ext})\n";
    } else {
        echo "   ❌ {$desc} ({$ext}) - 未安装\n";
    }
}

// 3. 项目文件检查
echo "\n3. 项目文件检查\n";
$required_files = [
    'vendor/autoload.php' => 'Composer 自动加载器',
    '.env' => '环境配置文件',
    'config/app.php' => '应用配置',
    'config/database.php' => '数据库配置',
    'start.php' => '启动文件',
    'windows.php' => 'Windows 启动文件',
    'app/controller/IndexController.php' => '首页控制器',
    'app/controller/InstallController.php' => '安装控制器'
];

foreach ($required_files as $file => $desc) {
    if (file_exists($file)) {
        echo "   ✅ {$desc} ({$file})\n";
    } else {
        echo "   ❌ {$desc} ({$file}) - 文件不存在\n";
    }
}

// 4. 目录权限检查
echo "\n4. 目录权限检查\n";
$writable_dirs = [
    'runtime' => '运行时目录',
    'runtime/logs' => '日志目录',
    'runtime/views' => '视图缓存目录'
];

foreach ($writable_dirs as $dir => $desc) {
    if (!is_dir($dir)) {
        mkdir($dir, 0755, true);
    }
    
    if (is_writable($dir)) {
        echo "   ✅ {$desc} ({$dir}) - 可写\n";
    } else {
        echo "   ❌ {$desc} ({$dir}) - 不可写\n";
    }
}

// 5. 环境变量检查
echo "\n5. 环境变量检查\n";
if (file_exists('.env')) {
    // 加载环境变量
    require_once 'vendor/autoload.php';
    
    if (class_exists('Dotenv\Dotenv')) {
        try {
            if (method_exists('Dotenv\Dotenv', 'createUnsafeImmutable')) {
                Dotenv\Dotenv::createUnsafeImmutable(__DIR__)->load();
            } else {
                Dotenv\Dotenv::createMutable(__DIR__)->load();
            }
            echo "   ✅ 环境变量加载成功\n";
            
            // 检查关键配置
            $env_vars = [
                'APP_PORT' => '应用端口',
                'DB_HOST' => '数据库主机',
                'DB_DATABASE' => '数据库名称',
                'DB_USERNAME' => '数据库用户名'
            ];
            
            foreach ($env_vars as $var => $desc) {
                $value = $_ENV[$var] ?? getenv($var) ?? '未设置';
                echo "   - {$desc}: {$value}\n";
            }
            
        } catch (Exception $e) {
            echo "   ❌ 环境变量加载失败: " . $e->getMessage() . "\n";
        }
    } else {
        echo "   ❌ Dotenv 类不存在\n";
    }
} else {
    echo "   ❌ .env 文件不存在\n";
}

// 6. 安装状态检查
echo "\n6. 安装状态检查\n";
$install_lock = 'config/install/lock/install.lock';
if (file_exists($install_lock)) {
    echo "   ✅ 系统已安装\n";
} else {
    echo "   ❌ 系统尚未安装，需要访问 /install 页面完成安装\n";
}

// 7. 端口检查
echo "\n7. 端口检查\n";
$port = $_ENV['APP_PORT'] ?? getenv('APP_PORT') ?? 8787;
$socket = @fsockopen('127.0.0.1', $port, $errno, $errstr, 1);
if ($socket) {
    echo "   ✅ 端口 {$port} 可访问\n";
    fclose($socket);
} else {
    echo "   ❌ 端口 {$port} 不可访问 (错误: {$errstr})\n";
}

// 8. 启动测试
echo "\n8. 启动测试建议\n";
echo "   推荐启动命令:\n";
if (PHP_OS_FAMILY === 'Windows') {
    echo "   - php windows.php\n";
    echo "   - windows.bat\n";
} else {
    echo "   - php start.php start\n";
    echo "   - php start.php start -d\n";
}

echo "\n   访问地址:\n";
echo "   - 安装页面: http://127.0.0.1:{$port}/install\n";
echo "   - 管理后台: http://127.0.0.1:{$port}/admin\n";
echo "   - 前台首页: http://127.0.0.1:{$port}/\n";

echo "\n=== 测试完成 ===\n";

// 9. 生成启动脚本
echo "\n9. 生成启动脚本\n";
$start_script = "start_server.bat";
$script_content = "@echo off\n";
$script_content .= "chcp 65001\n";
$script_content .= "cd /d \"%~dp0\"\n";
$script_content .= "echo 启动 EasyAdmin8-webman 服务器...\n";
$script_content .= "php windows.php\n";
$script_content .= "pause\n";

file_put_contents($start_script, $script_content);
echo "   ✅ 已生成启动脚本: {$start_script}\n";

echo "\n使用方法: 双击 {$start_script} 文件启动服务器\n";
