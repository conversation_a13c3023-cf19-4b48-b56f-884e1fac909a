<?php
/**
 * 简单数据库连接测试
 */

echo "=== 简单数据库连接测试 ===\n\n";

// 直接使用 PDO 测试连接
$connections = [
    'mysql' => [
        'host' => '127.0.0.1',
        'port' => 3306,
        'database' => 'easyadmin8',
        'username' => 'root',
        'password' => 'root',
        'prefix' => 'ea8_'
    ],
    'mysql_test' => [
        'host' => '127.0.0.1',
        'port' => 3306,
        'database' => 'test',
        'username' => 'root',
        'password' => 'root',
        'prefix' => ''
    ],
    'mysql_information_schema' => [
        'host' => '127.0.0.1',
        'port' => 3306,
        'database' => 'information_schema',
        'username' => 'root',
        'password' => 'root',
        'prefix' => ''
    ]
];

foreach ($connections as $name => $config) {
    echo "🔍 测试连接: {$name}\n";
    echo "   数据库: {$config['database']}\n";
    
    try {
        $dsn = "mysql:host={$config['host']};port={$config['port']};dbname={$config['database']};charset=utf8mb4";
        $pdo = new PDO($dsn, $config['username'], $config['password'], [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_TIMEOUT => 5
        ]);
        
        echo "   ✅ 连接成功\n";
        
        // 测试查询表列表
        if ($config['database'] === 'information_schema') {
            // 查询所有数据库
            $stmt = $pdo->query("SELECT SCHEMA_NAME FROM SCHEMATA WHERE SCHEMA_NAME NOT IN ('information_schema', 'performance_schema', 'mysql', 'sys')");
            $databases = $stmt->fetchAll(PDO::FETCH_COLUMN);
            echo "   📊 可用数据库: " . implode(', ', $databases) . "\n";
        } else {
            // 查询表列表
            $stmt = $pdo->query("SHOW TABLES");
            $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
            echo "   📊 表数量: " . count($tables) . "\n";
            
            if (count($tables) > 0) {
                echo "   📋 前5个表:\n";
                for ($i = 0; $i < min(5, count($tables)); $i++) {
                    $tableName = $tables[$i];
                    
                    // 检查是否有前缀
                    $displayName = $tableName;
                    if ($config['prefix'] && strpos($tableName, $config['prefix']) === 0) {
                        $displayName = substr($tableName, strlen($config['prefix']));
                    }
                    
                    echo "      - {$tableName}";
                    if ($displayName !== $tableName) {
                        echo " (显示为: {$displayName})";
                    }
                    echo "\n";
                }
                
                // 如果有前缀，测试前缀过滤
                if ($config['prefix']) {
                    $prefixTables = array_filter($tables, function($table) use ($config) {
                        return strpos($table, $config['prefix']) === 0;
                    });
                    echo "   🔍 前缀匹配表数: " . count($prefixTables) . "\n";
                }
            } else {
                echo "   ⚠️  数据库中没有表\n";
            }
        }
        
    } catch (PDOException $e) {
        echo "   ❌ 连接失败: " . $e->getMessage() . "\n";
    }
    
    echo "\n";
}

// 检查 easyadmin8 数据库是否存在
echo "🔍 检查 easyadmin8 数据库\n";
try {
    $dsn = "mysql:host=127.0.0.1;port=3306;charset=utf8mb4";
    $pdo = new PDO($dsn, 'root', 'root', [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
    ]);
    
    $stmt = $pdo->query("SHOW DATABASES LIKE 'easyadmin8'");
    $exists = $stmt->fetch();
    
    if ($exists) {
        echo "   ✅ easyadmin8 数据库存在\n";
        
        // 检查是否有表
        $pdo->exec("USE easyadmin8");
        $stmt = $pdo->query("SHOW TABLES");
        $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
        echo "   📊 表数量: " . count($tables) . "\n";
        
        if (count($tables) === 0) {
            echo "   ⚠️  数据库存在但没有表，可能需要:\n";
            echo "      1. 导入数据库结构\n";
            echo "      2. 运行安装脚本\n";
            echo "      3. 创建测试表\n";
        }
    } else {
        echo "   ❌ easyadmin8 数据库不存在\n";
        echo "   💡 建议:\n";
        echo "      1. 创建数据库: CREATE DATABASE easyadmin8;\n";
        echo "      2. 导入初始数据\n";
        echo "      3. 或使用现有数据库\n";
    }
    
} catch (PDOException $e) {
    echo "   ❌ 检查失败: " . $e->getMessage() . "\n";
}

echo "\n=== 解决方案建议 ===\n";
echo "如果没有看到表列表，可能的解决方案:\n\n";

echo "1. 创建测试数据库和表:\n";
echo "   CREATE DATABASE IF NOT EXISTS easyadmin8;\n";
echo "   USE easyadmin8;\n";
echo "   CREATE TABLE ea8_users (\n";
echo "       id INT AUTO_INCREMENT PRIMARY KEY,\n";
echo "       username VARCHAR(50) NOT NULL,\n";
echo "       email VARCHAR(100),\n";
echo "       created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP\n";
echo "   ) COMMENT='用户表';\n\n";

echo "2. 修改数据库配置使用现有数据库:\n";
echo "   编辑 .env 文件或 config/database.php\n";
echo "   将 DB_DATABASE 改为现有的数据库名\n\n";

echo "3. 检查数据库服务:\n";
echo "   确保 MySQL 服务正在运行\n";
echo "   检查用户名密码是否正确\n\n";

echo "=== 测试完成 ===\n";
?>
