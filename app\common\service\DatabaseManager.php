<?php

namespace app\common\service;

use support\Db;
use support\Log;
use Exception;

/**
 * 数据库连接管理器
 * 提供多数据库连接管理、读写分离、连接池等功能
 */
class DatabaseManager
{
    /**
     * 连接实例缓存
     * @var array
     */
    private static $connections = [];

    /**
     * 读写分离配置
     * @var array
     */
    private static $readWriteConfig = null;

    /**
     * 连接池配置
     * @var array
     */
    private static $poolConfig = null;

    /**
     * 获取数据库连接
     * @param string $connection 连接名称
     * @return \Illuminate\Database\Connection
     */
    public static function connection(string $connection = null)
    {
        if ($connection === null) {
            $connection = config('database.default', 'mysql');
        }

        // 检查连接是否已缓存
        if (isset(self::$connections[$connection])) {
            return self::$connections[$connection];
        }

        try {
            $conn = Db::connection($connection);
            self::$connections[$connection] = $conn;
            
            Log::info("数据库连接建立成功", ['connection' => $connection]);
            return $conn;
        } catch (Exception $e) {
            Log::error("数据库连接失败", [
                'connection' => $connection,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * 获取读库连接（读写分离）
     * @return \Illuminate\Database\Connection
     */
    public static function readConnection()
    {
        $config = self::getReadWriteConfig();
        
        if (!$config['enable']) {
            return self::connection();
        }

        $readConnections = $config['read_connections'];
        
        // 随机选择一个读库连接
        $connectionName = $readConnections[array_rand($readConnections)];
        
        return self::connection($connectionName);
    }

    /**
     * 获取写库连接（读写分离）
     * @return \Illuminate\Database\Connection
     */
    public static function writeConnection()
    {
        $config = self::getReadWriteConfig();
        
        if (!$config['enable']) {
            return self::connection();
        }

        return self::connection($config['write_connection']);
    }

    /**
     * 获取日志数据库连接
     * @return \Illuminate\Database\Connection
     */
    public static function logConnection()
    {
        return self::connection('mysql_log');
    }

    /**
     * 获取缓存数据库连接
     * @return \Illuminate\Database\Connection
     */
    public static function cacheConnection()
    {
        return self::connection('mysql_cache');
    }

    /**
     * 获取第二数据库连接
     * @return \Illuminate\Database\Connection
     */
    public static function secondConnection()
    {
        return self::connection('mysql_second');
    }

    /**
     * 获取无前缀数据库连接
     * @return \Illuminate\Database\Connection
     */
    public static function withoutPrefixConnection()
    {
        return self::connection('mysql_without_prefix');
    }

    /**
     * 测试数据库连接
     * @param string $connection 连接名称
     * @return array
     */
    public static function testConnection(string $connection = null): array
    {
        try {
            $conn = self::connection($connection);
            $result = $conn->select('SELECT 1 as test');
            
            return [
                'success' => true,
                'message' => '连接成功',
                'connection' => $connection ?: config('database.default'),
                'result' => $result
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage(),
                'connection' => $connection ?: config('database.default'),
                'error' => $e->getTraceAsString()
            ];
        }
    }

    /**
     * 测试所有数据库连接
     * @return array
     */
    public static function testAllConnections(): array
    {
        $connections = config('database.connections', []);
        $results = [];

        foreach ($connections as $name => $config) {
            $results[$name] = self::testConnection($name);
        }

        return $results;
    }

    /**
     * 获取连接信息
     * @param string $connection 连接名称
     * @return array
     */
    public static function getConnectionInfo(string $connection = null): array
    {
        $connectionName = $connection ?: config('database.default');
        $config = config("database.connections.{$connectionName}", []);

        return [
            'name' => $connectionName,
            'driver' => $config['driver'] ?? 'unknown',
            'host' => $config['host'] ?? 'unknown',
            'port' => $config['port'] ?? 'unknown',
            'database' => $config['database'] ?? 'unknown',
            'username' => $config['username'] ?? 'unknown',
            'prefix' => $config['prefix'] ?? '',
            'charset' => $config['charset'] ?? 'unknown',
        ];
    }

    /**
     * 获取所有连接信息
     * @return array
     */
    public static function getAllConnectionsInfo(): array
    {
        $connections = config('database.connections', []);
        $info = [];

        foreach ($connections as $name => $config) {
            $info[$name] = self::getConnectionInfo($name);
        }

        return $info;
    }

    /**
     * 执行事务（支持多数据库）
     * @param callable $callback 回调函数
     * @param string $connection 连接名称
     * @return mixed
     * @throws Exception
     */
    public static function transaction(callable $callback, string $connection = null)
    {
        $conn = self::connection($connection);
        
        return $conn->transaction($callback);
    }

    /**
     * 执行跨数据库事务
     * @param callable $callback 回调函数
     * @param array $connections 连接名称数组
     * @return mixed
     * @throws Exception
     */
    public static function multiTransaction(callable $callback, array $connections = [])
    {
        $conns = [];
        
        // 开始所有事务
        foreach ($connections as $connectionName) {
            $conn = self::connection($connectionName);
            $conn->beginTransaction();
            $conns[] = $conn;
        }

        try {
            $result = $callback();
            
            // 提交所有事务
            foreach ($conns as $conn) {
                $conn->commit();
            }
            
            return $result;
        } catch (Exception $e) {
            // 回滚所有事务
            foreach ($conns as $conn) {
                $conn->rollBack();
            }
            
            throw $e;
        }
    }

    /**
     * 关闭连接
     * @param string $connection 连接名称
     */
    public static function disconnect(string $connection = null)
    {
        $connectionName = $connection ?: config('database.default');
        
        if (isset(self::$connections[$connectionName])) {
            unset(self::$connections[$connectionName]);
        }
        
        Db::purge($connectionName);
        Log::info("数据库连接已关闭", ['connection' => $connectionName]);
    }

    /**
     * 关闭所有连接
     */
    public static function disconnectAll()
    {
        foreach (self::$connections as $name => $connection) {
            self::disconnect($name);
        }
        
        self::$connections = [];
        Log::info("所有数据库连接已关闭");
    }

    /**
     * 获取读写分离配置
     * @return array
     */
    private static function getReadWriteConfig(): array
    {
        if (self::$readWriteConfig === null) {
            self::$readWriteConfig = config('database.read_write_separation', [
                'enable' => false,
                'write_connection' => 'mysql',
                'read_connections' => ['mysql_read'],
                'sticky' => false,
            ]);
        }

        return self::$readWriteConfig;
    }

    /**
     * 获取连接池配置
     * @return array
     */
    private static function getPoolConfig(): array
    {
        if (self::$poolConfig === null) {
            self::$poolConfig = config('database.pool', [
                'enable' => false,
                'max_connections' => 10,
                'min_connections' => 1,
                'max_idle_time' => 60,
                'max_wait_time' => 3,
            ]);
        }

        return self::$poolConfig;
    }

    /**
     * 获取连接统计信息
     * @return array
     */
    public static function getConnectionStats(): array
    {
        return [
            'active_connections' => count(self::$connections),
            'connection_names' => array_keys(self::$connections),
            'read_write_separation' => self::getReadWriteConfig(),
            'pool_config' => self::getPoolConfig(),
        ];
    }

    /**
     * 清理空闲连接
     */
    public static function cleanIdleConnections()
    {
        // 这里可以实现连接池的空闲连接清理逻辑
        Log::info("清理空闲数据库连接");
    }
}