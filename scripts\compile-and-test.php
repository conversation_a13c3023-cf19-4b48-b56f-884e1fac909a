<?php
/**
 * CURD生成器V2 编译和测试脚本
 * 用于验证修复后的数据库连接功能
 */

echo "🚀 CURD生成器V2 编译和测试脚本\n";
echo "=" . str_repeat("=", 50) . "\n\n";

// 1. 检查文件完整性
echo "📁 检查文件完整性...\n";
$requiredFiles = [
    'config/route.php' => '路由配置文件',
    'app/admin/controller/system/CurdTestController.php' => 'CURD测试控制器',
    'app/common/services/curd/v2/CurdGenerator.php' => 'CURD生成器服务',
    'app/common/services/curd/v2/analyzers/TableAnalyzer.php' => '表分析器',
    'public/static/admin/js/curd-generator-v2.js' => '前端JavaScript',
    'app/admin/view/admin/system/curdgeneratev2/index.blade.php' => '前端模板',
    'test/database-connection-test.html' => '数据库连接测试页面'
];

$missingFiles = [];
foreach ($requiredFiles as $file => $description) {
    if (file_exists($file)) {
        echo "✅ {$description}: {$file}\n";
    } else {
        echo "❌ {$description}: {$file} (缺失)\n";
        $missingFiles[] = $file;
    }
}

if (!empty($missingFiles)) {
    echo "\n⚠️  发现缺失文件，请检查:\n";
    foreach ($missingFiles as $file) {
        echo "   - {$file}\n";
    }
    exit(1);
}

echo "\n";

// 2. 检查路由配置
echo "🛣️  检查路由配置...\n";
$routeContent = file_get_contents('config/route.php');

$routeChecks = [
    'app\\admin\\controller\\system\\CurdTestController::class' => 'CURD测试控制器路由',
    '/curdtest/tables' => '表列表获取路由',
    '/curdtest/analyze' => '表分析路由'
];

foreach ($routeChecks as $pattern => $description) {
    if (strpos($routeContent, $pattern) !== false) {
        echo "✅ {$description}\n";
    } else {
        echo "❌ {$description} (未找到: {$pattern})\n";
    }
}

echo "\n";

// 3. 检查JavaScript事件监听器
echo "⚡ 检查JavaScript事件监听器...\n";
$jsContent = file_get_contents('public/static/admin/js/curd-generator-v2.js');

$jsChecks = [
    "form.on('select(connectionSelect)" => '数据库连接选择事件',
    "form.on('select(tableSelect)" => '数据表选择事件',
    'loadTables()' => '表列表加载函数'
];

foreach ($jsChecks as $pattern => $description) {
    if (strpos($jsContent, $pattern) !== false) {
        echo "✅ {$description}\n";
    } else {
        echo "❌ {$description} (未找到: {$pattern})\n";
    }
}

echo "\n";

// 4. 检查HTML模板
echo "🎨 检查HTML模板...\n";
$htmlContent = file_get_contents('app/admin/view/admin/system/curdgeneratev2/index.blade.php');

$htmlChecks = [
    'lay-filter="connectionSelect"' => '数据库连接选择框过滤器',
    'lay-filter="tableSelect"' => '数据表选择框过滤器',
    'mysql_second' => '第二数据库选项'
];

foreach ($htmlChecks as $pattern => $description) {
    if (strpos($htmlContent, $pattern) !== false) {
        echo "✅ {$description}\n";
    } else {
        echo "❌ {$description} (未找到: {$pattern})\n";
    }
}

echo "\n";

// 5. 检查模拟数据一致性
echo "🎭 检查模拟数据一致性...\n";
$controllerContent = file_get_contents('app/admin/controller/system/CurdTestController.php');
$analyzerContent = file_get_contents('app/common/services/curd/v2/analyzers/TableAnalyzer.php');

// 检查mysql_second的模拟表数据
$expectedTables = ['admin', 'member', 'shop_product', 'shop_order', 'article', 'business'];
$controllerHasAll = true;
$analyzerHasAll = true;

foreach ($expectedTables as $table) {
    if (strpos($controllerContent, "'{$table}'") === false) {
        $controllerHasAll = false;
        echo "❌ 控制器缺少表: {$table}\n";
    }
    if (strpos($analyzerContent, "'{$table}'") === false) {
        $analyzerHasAll = false;
        echo "❌ 分析器缺少表: {$table}\n";
    }
}

if ($controllerHasAll && $analyzerHasAll) {
    echo "✅ mysql_second 模拟数据一致性检查通过\n";
} else {
    echo "❌ mysql_second 模拟数据不一致\n";
}

echo "\n";

// 6. 生成测试报告
echo "📊 生成测试报告...\n";

$testReport = [
    'timestamp' => date('Y-m-d H:i:s'),
    'files_checked' => count($requiredFiles),
    'missing_files' => count($missingFiles),
    'route_config' => strpos($routeContent, 'CurdTestController') !== false,
    'js_events' => strpos($jsContent, 'connectionSelect') !== false,
    'html_filters' => strpos($htmlContent, 'lay-filter="connectionSelect"') !== false,
    'mock_data_consistent' => $controllerHasAll && $analyzerHasAll
];

$reportFile = 'test/compile-test-report.json';
file_put_contents($reportFile, json_encode($testReport, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
echo "✅ 测试报告已生成: {$reportFile}\n";

echo "\n";

// 7. 输出测试指令
echo "🧪 测试指令:\n";
echo "=" . str_repeat("=", 30) . "\n";
echo "1. 启动Web服务器:\n";
echo "   php start.php start\n\n";
echo "2. 访问数据库连接测试页面:\n";
echo "   http://localhost:8787/test/database-connection-test.html\n\n";
echo "3. 访问CURD生成器V2:\n";
echo "   http://localhost:8787/admin/system/curdgeneratev2\n\n";
echo "4. 测试步骤:\n";
echo "   a) 选择'第二数据库 (mysql_second)'\n";
echo "   b) 检查表列表是否正确显示\n";
echo "   c) 验证表前缀是否自动设置为 'ddwx_'\n";
echo "   d) 测试表结构分析功能\n\n";

// 8. 检查数据库配置
echo "🗄️  检查数据库配置...\n";
if (file_exists('config/database.php')) {
    $dbConfig = include 'config/database.php';
    if (isset($dbConfig['connections']['mysql_second'])) {
        $secondDb = $dbConfig['connections']['mysql_second'];
        echo "✅ mysql_second 连接配置存在\n";
        echo "   - 数据库: {$secondDb['database']}\n";
        echo "   - 前缀: {$secondDb['prefix']}\n";
        echo "   - 主机: {$secondDb['host']}:{$secondDb['port']}\n";
    } else {
        echo "❌ mysql_second 连接配置不存在\n";
    }
} else {
    echo "❌ 数据库配置文件不存在\n";
}

echo "\n";

// 9. 最终状态
$allPassed = empty($missingFiles) && 
             strpos($routeContent, 'CurdTestController') !== false &&
             strpos($jsContent, 'connectionSelect') !== false &&
             strpos($htmlContent, 'lay-filter="connectionSelect"') !== false &&
             $controllerHasAll && $analyzerHasAll;

if ($allPassed) {
    echo "🎉 编译检查完成！所有检查项都通过了。\n";
    echo "✅ 数据库连接功能修复成功，可以开始测试。\n\n";
    echo "🚀 下一步: 启动服务器并访问测试页面验证功能\n";
} else {
    echo "⚠️  编译检查发现问题，请修复后重新运行。\n";
    exit(1);
}

echo "\n" . str_repeat("=", 60) . "\n";
echo "编译和检查完成！\n";
?>
