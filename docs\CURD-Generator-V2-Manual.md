# CURD 可视化生成器 V2 使用手册

## 📖 概述

CURD 可视化生成器 V2 是一个功能强大的代码生成工具，能够根据数据库表结构自动生成完整的 CURD（增删改查）代码，包括控制器、模型、视图、JavaScript 等文件。

## 🚀 主要特性

### 核心功能
- 🎯 **智能表结构分析**：自动解析数据库表结构和字段信息
- 🎨 **可视化字段配置**：直观的字段配置界面，支持拖拽排序
- 📝 **多文件类型生成**：支持生成控制器、模型、视图、JavaScript、路由等
- 🔍 **实时代码预览**：支持语法高亮的代码预览功能
- 📊 **质量检查**：内置代码质量检查器，确保生成代码的质量

### 高级功能
- 🛠️ **批量字段配置**：支持批量设置字段属性
- 📋 **模板管理**：保存和加载字段配置模板
- 🤖 **智能推荐**：基于字段特征智能推荐最佳配置
- 📊 **质量报告**：生成详细的代码质量分析报告
- 💾 **数据导入导出**：支持配置的导入导出功能

## 🎯 使用流程

### 步骤 1：选择数据表

1. **选择数据库连接**
   - 从下拉列表中选择合适的数据库连接
   - 支持 MySQL、PostgreSQL、SQLite、SQL Server 等

2. **选择数据表**
   - 系统会自动加载所选连接下的所有表
   - 支持搜索功能快速定位目标表

3. **设置表前缀**（可选）
   - 根据需要设置表前缀
   - 系统会根据连接类型自动推荐前缀

4. **分析表结构**
   - 点击"分析表结构"按钮
   - 系统会解析表的字段信息、索引、约束等

### 步骤 2：配置字段

1. **字段基础配置**
   - 设置字段标签（显示名称）
   - 选择表单组件类型
   - 配置显示选项（列表、表单、搜索）
   - 设置验证规则（必填、排序等）

2. **批量操作**
   - 使用批量配置功能快速设置多个字段
   - 支持按字段类型、选中状态等条件批量操作

3. **模板管理**
   - 保存当前配置为模板，便于复用
   - 加载已保存的模板快速应用配置

4. **智能推荐**
   - 使用智能推荐功能自动优化字段配置
   - 基于字段名称和类型推荐最佳实践

5. **质量检查**
   - 运行质量检查发现配置问题
   - 使用自动修复功能快速解决常见问题

### 步骤 3：生成选项

1. **选择生成文件**
   - 基础文件：控制器、模型、视图、JavaScript、路由
   - 扩展文件：数据库迁移、数据填充、单元测试、API文档

2. **配置功能特性**
   - 基础功能：分页、搜索、排序、导入导出
   - 高级功能：批量操作、缓存优化、操作日志、权限控制、软删除

3. **设置代码风格**
   - 命名规范：类命名、方法命名风格
   - 代码规范：注释、类型提示、数据验证、异常处理
   - 输出设置：输出目录、备份选项、覆盖策略

### 步骤 4：预览代码

1. **文件导航**
   - 左侧文件树显示所有将要生成的文件
   - 点击文件名切换预览内容

2. **代码预览**
   - 支持语法高亮显示
   - 实时显示代码行数和文件大小
   - 支持全屏预览模式

3. **代码操作**
   - 一键复制代码到剪贴板
   - 下载单个文件
   - 格式化代码（开发中）

### 步骤 5：生成文件

1. **生成选项**
   - 选择要生成的文件类型
   - 设置输出格式（ZIP压缩包或独立文件）
   - 配置覆盖和备份选项

2. **执行生成**
   - 点击"开始生成"按钮
   - 查看生成进度和结果
   - 下载生成的文件

## 🛠️ 高级功能详解

### 数据导入导出

#### 导入功能
- **配置文件导入**：支持 JSON、YAML 格式
- **SQL 结构导入**：从 CREATE TABLE 语句解析表结构
- **Excel 数据导入**：从 Excel 文件导入字段配置
- **JSON 数据导入**：直接粘贴 JSON 数据导入

#### 导出功能
- **多格式导出**：JSON、YAML、Excel、SQL
- **自定义内容**：选择导出的内容类型
- **压缩选项**：支持压缩输出和注释包含

### 质量检查器

#### 检查维度
- **表结构检查**：主键、时间戳、表注释等
- **字段配置检查**：列表字段、搜索字段、表单字段等
- **命名规范检查**：表名、字段名命名规范
- **性能优化检查**：索引、TEXT字段显示等

#### 问题分级
- **错误**：必须修复的严重问题
- **警告**：建议修复的重要问题
- **建议**：可选的优化建议

#### 自动修复
- 支持一键修复常见问题
- 智能推荐最佳配置
- 生成详细的修复报告

### 模板管理

#### 模板保存
- 保存当前字段配置为模板
- 支持自定义模板名称
- 本地存储，持久化保存

#### 模板加载
- 快速加载已保存的模板
- 支持替换或合并模式
- 预览模板内容

## 🎨 界面说明

### 步骤导航
- 圆形进度指示器显示当前步骤
- 进度条显示整体完成度
- 支持点击跳转到已完成的步骤

### 响应式设计
- 完美适配桌面、平板、手机设备
- 移动端优化的触摸交互
- 自适应布局和字体大小

### 快捷键支持
- `Ctrl + ←/→`：切换步骤
- `Ctrl + Enter`：执行生成（在最后一步）
- `Esc`：关闭弹窗

## 🔧 开发者功能

### 测试套件
在开发环境下，系统会自动加载测试套件，支持以下测试：

```javascript
// 运行所有测试
CurdTestSuite.run()

// 运行特定测试套件
CurdTestSuite.runSuite('basic')

// 运行性能测试
CurdTestSuite.runPerformance()

// 查看内存使用情况
CurdTestSuite.runMemory()
```

### 调试功能
- 详细的控制台日志输出
- 错误信息和堆栈跟踪
- 性能监控和内存使用统计

## 📊 技术架构

### 前端技术栈
- **UI框架**：Layui
- **JavaScript**：ES5+ 模块化设计
- **CSS**：现代化响应式设计
- **第三方库**：
  - Sortable.js（拖拽排序）
  - Prism.js（代码高亮）

### 模块结构
- `curd-generator-v2.js`：核心功能模块
- `curd-code-preview.js`：代码预览模块
- `curd-field-config.js`：字段配置模块
- `curd-quality-checker.js`：质量检查模块
- `curd-data-manager.js`：数据管理模块
- `curd-test-suite.js`：测试套件模块

### 设计模式
- **模块化设计**：功能分离，便于维护
- **事件驱动**：基于事件的交互模式
- **观察者模式**：状态变化自动更新UI
- **策略模式**：不同生成策略的灵活切换

## 🚨 注意事项

### 使用建议
1. **数据备份**：生成代码前建议备份原有文件
2. **权限检查**：确保有足够的文件写入权限
3. **配置验证**：使用质量检查器验证配置的合理性
4. **测试验证**：生成代码后进行充分测试

### 常见问题
1. **表结构分析失败**：检查数据库连接和表权限
2. **代码生成失败**：检查输出目录权限和磁盘空间
3. **预览显示异常**：检查浏览器兼容性和JavaScript错误
4. **性能问题**：大表建议分批处理或优化字段配置

## 📈 版本更新

### V2.0 新特性
- 全新的现代化界面设计
- 完整的模块化架构重构
- 新增质量检查和自动修复功能
- 支持数据导入导出
- 增强的代码预览功能
- 完善的测试套件

### 后续规划
- 支持更多数据库类型
- 增加更多代码模板
- 集成AI辅助生成
- 支持团队协作功能
- 云端配置同步

## 📞 技术支持

如有问题或建议，请通过以下方式联系：
- 提交 Issue 到项目仓库
- 发送邮件到技术支持邮箱
- 加入技术交流群讨论

---

*本手册持续更新中，最新版本请查看项目文档。*
