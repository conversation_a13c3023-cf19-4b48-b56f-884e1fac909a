<?php
/**
 * 调试表列表显示问题
 */

echo "=== 调试 CURD 页面表列表显示问题 ===\n\n";

// 1. 测试直接数据库连接
echo "1. 测试直接数据库连接\n";
echo str_repeat("-", 50) . "\n";

$connections = [
    'mysql_second' => [
        'host' => '127.0.0.1',
        'port' => 3306,
        'database' => 'hejiang',
        'username' => 'root',
        'password' => '5GeNi1v7P7Xcur5W',
        'prefix' => 'ddwx_'
    ],
    'mysql' => [
        'host' => '127.0.0.1',
        'port' => 3306,
        'database' => 'easyadmin8',
        'username' => 'root',
        'password' => 'root',
        'prefix' => 'ea8_'
    ]
];

foreach ($connections as $connName => $config) {
    echo "🔍 测试连接: {$connName}\n";
    
    try {
        $dsn = "mysql:host={$config['host']};port={$config['port']};dbname={$config['database']};charset=utf8mb4";
        $pdo = new PDO($dsn, $config['username'], $config['password'], [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_TIMEOUT => 5
        ]);
        
        echo "   ✅ 连接成功\n";
        
        // 获取表列表
        $stmt = $pdo->query("SHOW TABLES");
        $allTables = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        // 筛选带前缀的表
        $prefixTables = array_filter($allTables, function($table) use ($config) {
            return strpos($table, $config['prefix']) === 0;
        });
        
        echo "   📊 总表数: " . count($allTables) . "\n";
        echo "   🎯 前缀表数: " . count($prefixTables) . "\n";
        
        if (count($prefixTables) > 0) {
            echo "   📋 前5个表:\n";
            $sampleTables = array_slice($prefixTables, 0, 5);
            foreach ($sampleTables as $table) {
                $displayName = substr($table, strlen($config['prefix']));
                echo "      - {$displayName} ({$table})\n";
            }
        } else {
            echo "   ⚠️  没有找到带前缀的表\n";
        }
        
    } catch (PDOException $e) {
        echo "   ❌ 连接失败: " . $e->getMessage() . "\n";
    }
    
    echo "\n";
}

// 2. 测试 webman API 响应
echo "2. 测试 webman API 响应\n";
echo str_repeat("-", 50) . "\n";

function testCurdAPI($connection, $action = 'get_tables', $extraData = []) {
    $url = 'http://localhost:8787/admin/system/curdgeneratev2';
    
    $postData = array_merge([
        'action' => $action,
        'connection' => $connection
    ], $extraData);
    
    echo "🧪 测试 API: {$action} (连接: {$connection})\n";
    echo "   请求数据: " . json_encode($postData, JSON_UNESCAPED_UNICODE) . "\n";
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 15);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($postData));
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/x-www-form-urlencoded',
    ]);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($error) {
        echo "   ❌ 请求失败: {$error}\n";
        return false;
    }
    
    echo "   ✅ HTTP 状态: {$httpCode}\n";
    
    if ($httpCode !== 200) {
        echo "   ❌ HTTP 错误\n";
        return false;
    }
    
    $json = json_decode($response, true);
    if ($json === null) {
        echo "   ❌ 响应不是有效的 JSON\n";
        echo "   📄 原始响应: " . substr($response, 0, 200) . "...\n";
        return false;
    }
    
    echo "   📊 响应码: {$json['code']}\n";
    echo "   📝 消息: {$json['msg']}\n";
    
    if (isset($json['data'])) {
        if (is_array($json['data'])) {
            echo "   📈 数据量: " . count($json['data']) . " 项\n";
            
            if (count($json['data']) > 0) {
                echo "   📋 数据示例:\n";
                for ($i = 0; $i < min(3, count($json['data'])); $i++) {
                    $item = $json['data'][$i];
                    if (is_array($item)) {
                        $display = json_encode($item, JSON_UNESCAPED_UNICODE);
                        if (strlen($display) > 100) {
                            $display = substr($display, 0, 100) . '...';
                        }
                        echo "      - {$display}\n";
                    } else {
                        echo "      - {$item}\n";
                    }
                }
            }
        } else {
            echo "   📄 数据类型: " . gettype($json['data']) . "\n";
            echo "   📄 数据内容: " . json_encode($json['data'], JSON_UNESCAPED_UNICODE) . "\n";
        }
    }
    
    echo "\n";
    return $json;
}

// 测试不同连接的表列表
$testConnections = ['mysql', 'mysql_second', 'mysql_read'];

foreach ($testConnections as $conn) {
    $result = testCurdAPI($conn, 'get_tables');
    
    // 如果需要登录，尝试模拟登录状态
    if ($result && $result['code'] === 0 && $result['msg'] === '请先登录后台') {
        echo "   ℹ️  需要登录，尝试直接调用服务类...\n";
        
        // 尝试直接调用服务类
        try {
            if (file_exists('vendor/autoload.php')) {
                require_once 'vendor/autoload.php';
                
                // 模拟调用 CurdGenerator 服务
                echo "   🔧 直接调用 CurdGenerator 服务...\n";
                
                // 这里需要模拟 webman 环境
                if (class_exists('\\app\\common\\services\\curd\\v2\\CurdGenerator')) {
                    $generator = new \app\common\services\curd\v2\CurdGenerator();
                    $tables = $generator->getAllTables($conn);
                    
                    echo "   ✅ 直接调用成功\n";
                    echo "   📊 表数量: " . count($tables) . "\n";
                    
                    if (count($tables) > 0) {
                        echo "   📋 表列表:\n";
                        for ($i = 0; $i < min(5, count($tables)); $i++) {
                            $table = $tables[$i];
                            echo "      - {$table['name']} ({$table['comment']})\n";
                        }
                    }
                } else {
                    echo "   ❌ CurdGenerator 类不存在\n";
                }
            }
        } catch (Exception $e) {
            echo "   ❌ 直接调用失败: " . $e->getMessage() . "\n";
        }
    }
    
    echo str_repeat("-", 30) . "\n";
}

// 3. 检查控制器和路由
echo "3. 检查控制器和路由配置\n";
echo str_repeat("-", 50) . "\n";

// 检查控制器文件
$controllerFile = 'app/admin/controller/system/CurdGenerateV2Controller.php';
if (file_exists($controllerFile)) {
    echo "✅ 控制器文件存在: {$controllerFile}\n";
    
    // 检查关键方法
    $content = file_get_contents($controllerFile);
    $methods = ['getTables', 'analyzeTable', 'generateCode'];
    
    foreach ($methods as $method) {
        if (strpos($content, "function {$method}") !== false || strpos($content, "{$method}(") !== false) {
            echo "   ✅ 方法存在: {$method}\n";
        } else {
            echo "   ❌ 方法缺失: {$method}\n";
        }
    }
} else {
    echo "❌ 控制器文件不存在: {$controllerFile}\n";
}

// 检查路由配置
$routeFiles = ['config/route.php', 'app/admin/route.php'];
foreach ($routeFiles as $routeFile) {
    if (file_exists($routeFile)) {
        echo "✅ 路由文件存在: {$routeFile}\n";
        
        $content = file_get_contents($routeFile);
        if (strpos($content, 'curdgeneratev2') !== false) {
            echo "   ✅ 包含 curdgeneratev2 路由\n";
        } else {
            echo "   ⚠️  未找到 curdgeneratev2 路由\n";
        }
    }
}

// 4. 检查前端页面
echo "\n4. 检查前端页面\n";
echo str_repeat("-", 50) . "\n";

$viewFiles = [
    'app/admin/view/system/curd_generate_v2.blade.php',
    'app/admin/view/system/curdgeneratev2.blade.php',
    'app/view/admin/system/curd_generate_v2.blade.php'
];

foreach ($viewFiles as $viewFile) {
    if (file_exists($viewFile)) {
        echo "✅ 视图文件存在: {$viewFile}\n";
        
        $content = file_get_contents($viewFile);
        if (strpos($content, 'get_tables') !== false) {
            echo "   ✅ 包含 get_tables 调用\n";
        }
        if (strpos($content, 'connection') !== false) {
            echo "   ✅ 包含 connection 参数\n";
        }
        break;
    }
}

echo "\n=== 问题诊断建议 ===\n";

echo "🔍 可能的问题原因:\n";
echo "1. 需要登录后台才能获取表列表\n";
echo "2. 前端 JavaScript 请求参数不正确\n";
echo "3. 控制器方法路由配置问题\n";
echo "4. 数据库连接配置问题\n";
echo "5. 权限验证阻止了请求\n\n";

echo "🔧 解决方案:\n";
echo "1. 确保已登录后台管理系统\n";
echo "2. 检查浏览器开发者工具的网络请求\n";
echo "3. 检查 JavaScript 控制台是否有错误\n";
echo "4. 验证 AJAX 请求的参数和响应\n";
echo "5. 检查后台权限设置\n\n";

echo "🧪 下一步测试:\n";
echo "1. 手动登录后台: http://localhost:8787/admin/login\n";
echo "2. 访问 CURD 页面: http://localhost:8787/admin/system/curdgeneratev2\n";
echo "3. 打开浏览器开发者工具 (F12)\n";
echo "4. 选择数据库连接并点击刷新\n";
echo "5. 查看网络请求和响应\n";

echo "\n=== 调试完成 ===\n";
?>
