<?php
/**
 * 数据库表列表调试脚本
 */

// 引入 webman 框架
require_once __DIR__ . '/vendor/autoload.php';

echo "=== 数据库表列表调试 ===\n\n";

// 测试连接
$connections = ['mysql', 'mysql_read', 'mysql_second'];

foreach ($connections as $connection) {
    echo "🔍 测试连接: {$connection}\n";
    
    try {
        // 获取连接配置
        $config = config("database.connections.{$connection}");
        if (!$config) {
            echo "   ❌ 连接配置不存在\n\n";
            continue;
        }
        
        echo "   📋 配置信息:\n";
        echo "      主机: {$config['host']}\n";
        echo "      端口: {$config['port']}\n";
        echo "      数据库: {$config['database']}\n";
        echo "      用户名: {$config['username']}\n";
        echo "      前缀: " . ($config['prefix'] ?? '无') . "\n";
        
        // 测试连接
        $db = \support\Db::connection($connection);
        echo "   ✅ 连接成功\n";
        
        // 获取数据库名
        $database = $config['database'];
        $prefix = $config['prefix'] ?? '';
        
        echo "   🔍 查询数据库: {$database}\n";
        echo "   🔍 使用前缀: " . ($prefix ?: '无') . "\n";
        
        // 方法1: 查询所有表（不使用前缀过滤）
        echo "   📊 方法1: 查询所有表\n";
        $allTables = $db->select(
            "SELECT table_name, table_comment FROM information_schema.tables WHERE table_schema = ?",
            [$database]
        );
        echo "      总表数: " . count($allTables) . "\n";
        
        if (count($allTables) > 0) {
            echo "      前5个表:\n";
            for ($i = 0; $i < min(5, count($allTables)); $i++) {
                $table = $allTables[$i];
                echo "        - {$table->table_name} ({$table->table_comment})\n";
            }
        }
        
        // 方法2: 使用前缀过滤
        if ($prefix) {
            echo "   📊 方法2: 使用前缀过滤 ({$prefix})\n";
            $prefixTables = $db->select(
                "SELECT table_name, table_comment FROM information_schema.tables WHERE table_schema = ? AND table_name LIKE ?",
                [$database, $prefix . '%']
            );
            echo "      匹配表数: " . count($prefixTables) . "\n";
            
            if (count($prefixTables) > 0) {
                echo "      匹配的表:\n";
                foreach ($prefixTables as $table) {
                    echo "        - {$table->table_name} ({$table->table_comment})\n";
                }
            }
        }
        
        // 方法3: 使用 SHOW TABLES
        echo "   📊 方法3: 使用 SHOW TABLES\n";
        $showTables = $db->select("SHOW TABLES");
        echo "      SHOW TABLES 结果: " . count($showTables) . "\n";
        
        if (count($showTables) > 0) {
            echo "      前5个表:\n";
            for ($i = 0; $i < min(5, count($showTables)); $i++) {
                $table = $showTables[$i];
                $tableName = array_values((array)$table)[0];
                echo "        - {$tableName}\n";
            }
        }
        
    } catch (\Exception $e) {
        echo "   ❌ 错误: " . $e->getMessage() . "\n";
    }
    
    echo "\n" . str_repeat("-", 60) . "\n\n";
}

// 测试 TableAnalyzer
echo "🧪 测试 TableAnalyzer 类\n";
try {
    $analyzer = new \app\common\services\curd\v2\analyzers\TableAnalyzer();
    
    foreach ($connections as $connection) {
        echo "   测试连接: {$connection}\n";
        try {
            $tables = $analyzer->getAllTables($connection);
            echo "     ✅ 获取到 " . count($tables) . " 个表\n";
            
            if (count($tables) > 0) {
                echo "     前3个表:\n";
                for ($i = 0; $i < min(3, count($tables)); $i++) {
                    $table = $tables[$i];
                    echo "       - {$table['name']} (完整名: {$table['full_name']})\n";
                }
            }
        } catch (\Exception $e) {
            echo "     ❌ 错误: " . $e->getMessage() . "\n";
        }
        echo "\n";
    }
} catch (\Exception $e) {
    echo "   ❌ TableAnalyzer 初始化失败: " . $e->getMessage() . "\n";
}

// 测试 CurdGenerator
echo "🧪 测试 CurdGenerator 类\n";
try {
    $generator = new \app\common\services\curd\v2\CurdGenerator();
    
    foreach ($connections as $connection) {
        echo "   测试连接: {$connection}\n";
        try {
            $tables = $generator->getAllTables($connection);
            echo "     ✅ 获取到 " . count($tables) . " 个表\n";
            
            if (count($tables) > 0) {
                echo "     前3个表:\n";
                for ($i = 0; $i < min(3, count($tables)); $i++) {
                    $table = $tables[$i];
                    echo "       - {$table['name']} (注释: {$table['comment']})\n";
                }
            }
        } catch (\Exception $e) {
            echo "     ❌ 错误: " . $e->getMessage() . "\n";
        }
        echo "\n";
    }
} catch (\Exception $e) {
    echo "   ❌ CurdGenerator 初始化失败: " . $e->getMessage() . "\n";
}

echo "=== 调试完成 ===\n";
echo "如果所有连接都显示 0 个表，可能的原因:\n";
echo "1. 数据库中确实没有表\n";
echo "2. 数据库连接配置错误\n";
echo "3. 用户权限不足\n";
echo "4. 前缀过滤过于严格\n";
echo "5. 数据库服务未启动\n";
?>
