<?php
/**
 * 简化的 EasyAdmin8 启动器
 * 用于在 Windows 环境下启动 Webman 服务器
 */

// 设置错误报告
ini_set('display_errors', 1);
error_reporting(E_ALL);

echo "正在启动 EasyAdmin8-webman 服务器...\n";

// 切换到项目目录
chdir(__DIR__);

// 检查必要文件
if (!file_exists('vendor/autoload.php')) {
    die("错误: vendor/autoload.php 不存在，请运行 composer install\n");
}

if (!file_exists('.env')) {
    die("错误: .env 文件不存在，请复制 .example.env 为 .env\n");
}

// 加载自动加载器
require_once __DIR__ . '/vendor/autoload.php';

// 加载环境变量
use Dotenv\Dotenv;
use support\App;
use Workerman\Worker;

try {
    if (class_exists('Dotenv\Dotenv') && file_exists(base_path() . '/.env')) {
        if (method_exists('Dotenv\Dotenv', 'createUnsafeImmutable')) {
            Dotenv::createUnsafeImmutable(base_path())->load();
        } else {
            Dotenv::createMutable(base_path())->load();
        }
    }
    
    echo "环境变量加载成功\n";
    
    // 加载应用配置
    App::loadAllConfig(['route']);
    echo "应用配置加载成功\n";
    
    // 获取端口
    $port = env('APP_PORT', 8787);
    echo "准备在端口 {$port} 启动服务器\n";
    
    // 创建 HTTP Worker
    $worker = new Worker("http://0.0.0.0:{$port}");
    $worker->count = 1; // 在 Windows 下使用单进程
    $worker->name = 'EasyAdmin8-webman';
    
    // 设置回调
    $worker->onWorkerStart = function($worker) {
        echo "EasyAdmin8-webman 服务器启动成功!\n";
        echo "访问地址: http://127.0.0.1:" . env('APP_PORT', 8787) . "\n";
        echo "安装页面: http://127.0.0.1:" . env('APP_PORT', 8787) . "/install\n";
        echo "管理后台: http://127.0.0.1:" . env('APP_PORT', 8787) . "/admin\n";
        echo "按 Ctrl+C 停止服务器\n";
    };
    
    $worker->onMessage = function($connection, $request) {
        try {
            // 创建 Webman 请求对象
            $webman_request = new \support\Request();
            
            // 处理请求
            $app = new \Webman\App($webman_request, new \support\Response());
            $response = $app->process($webman_request);
            
            // 发送响应
            $connection->send($response);
            
        } catch (Exception $e) {
            // 发送错误响应
            $connection->send("HTTP/1.1 500 Internal Server Error\r\n\r\n" . $e->getMessage());
        }
    };
    
    // 启动 Worker
    Worker::runAll();
    
} catch (Exception $e) {
    echo "启动失败: " . $e->getMessage() . "\n";
    echo "错误详情: " . $e->getTraceAsString() . "\n";
}
