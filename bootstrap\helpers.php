<?php
/**
 * 环境变量辅助函数
 */

if (!function_exists('env')) {
    /**
     * 获取环境变量值
     * @param string $key 环境变量名
     * @param mixed $default 默认值
     * @return mixed
     */
    function env($key, $default = null) {
        $value = getenv($key);

        if ($value === false) {
            return $default;
        }

        // 处理布尔值
        switch (strtolower($value)) {
            case 'true':
            case '(true)':
                return true;
            case 'false':
            case '(false)':
                return false;
            case 'empty':
            case '(empty)':
                return '';
            case 'null':
            case '(null)':
                return null;
        }

        // 处理引号包围的字符串
        if (strlen($value) > 1 && $value[0] === '"' && $value[strlen($value) - 1] === '"') {
            return substr($value, 1, -1);
        }

        return $value;
    }
}

if (!function_exists('config')) {
    /**
     * 获取配置值
     * @param string $key 配置键名
     * @param mixed $default 默认值
     * @return mixed
     */
    function config($key = null, $default = null) {
        static $configs = [];

        if ($key === null) {
            return $configs;
        }

        if (isset($configs[$key])) {
            return $configs[$key];
        }

        // 尝试加载配置文件
        $parts = explode('.', $key);
        $configFile = $parts[0];
        $configPath = __DIR__ . '/config/' . $configFile . '.php';

        if (file_exists($configPath)) {
            $config = include $configPath;
            if (is_array($config)) {
                $configs[$configFile] = $config;

                // 获取嵌套值
                $value = $config;
                for ($i = 1; $i < count($parts); $i++) {
                    if (isset($value[$parts[$i]])) {
                        $value = $value[$parts[$i]];
                    } else {
                        return $default;
                    }
                }
                return $value;
            }
        }

        return $default;
    }
}

if (!function_exists('base_path')) {
    /**
     * 获取项目根目录路径
     * @param string $path 相对路径
     * @return string
     */
    function base_path($path = '') {
        return __DIR__ . ($path ? DIRECTORY_SEPARATOR . ltrim($path, DIRECTORY_SEPARATOR) : '');
    }
}

if (!function_exists('runtime_path')) {
    /**
     * 获取运行时目录路径
     * @param string $path 相对路径
     * @return string
     */
    function runtime_path($path = '') {
        return base_path('runtime') . ($path ? DIRECTORY_SEPARATOR . ltrim($path, DIRECTORY_SEPARATOR) : '');
    }
}

if (!function_exists('public_path')) {
    /**
     * 获取公共目录路径
     * @param string $path 相对路径
     * @return string
     */
    function public_path($path = '') {
        return base_path('public') . ($path ? DIRECTORY_SEPARATOR . ltrim($path, DIRECTORY_SEPARATOR) : '');
    }
}

if (!function_exists('config_path')) {
    /**
     * 获取配置目录路径
     * @param string $path 相对路径
     * @return string
     */
    function config_path($path = '') {
        return base_path('config') . ($path ? DIRECTORY_SEPARATOR . ltrim($path, DIRECTORY_SEPARATOR) : '');
    }
}

// 引入Webman模拟类
if (file_exists(__DIR__ . '/webman_mock.php')) {
    require_once __DIR__ . '/webman_mock.php';
}

// 自动加载控制器类
spl_autoload_register(function ($class) {
    // 将命名空间转换为文件路径
    $file = str_replace('\\', '/', $class) . '.php';

    // 检查文件是否存在
    if (file_exists($file)) {
        require_once $file;
        return;
    }

    // 如果直接路径不存在，尝试相对路径
    $relativePaths = [
        '',
        './',
        '../',
        '../../'
    ];

    foreach ($relativePaths as $basePath) {
        $fullPath = $basePath . $file;
        if (file_exists($fullPath)) {
            require_once $fullPath;
            return;
        }
    }
});

// 手动加载新增的控制器类
$controllerFiles = [
    'app/admin/controller/system/ApiVersionController.php',
    'app/admin/controller/system/ApiTestController.php',
    'app/admin/controller/system/CacheController.php',
    'app/admin/controller/system/SearchController.php',
    'app/admin/controller/system/MonitorController.php'
];

foreach ($controllerFiles as $file) {
    if (file_exists($file)) {
        require_once $file;
    }
}

// 避免加载原生的support类，防止冲突
if (file_exists('support/Request.php')) {
    // 如果存在原生support类，不要自动加载
    echo "<!-- 检测到原生support类，跳过自动加载 -->\n";
}
