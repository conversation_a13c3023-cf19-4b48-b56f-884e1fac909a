<?php
/**
 * EasyAdmin8-webman API文档管理系统 - 系统优化完成测试脚本
 * 测试所有新增的优化功能和性能提升
 */

echo "=== EasyAdmin8-webman API文档管理系统 - 系统优化完成测试 ===\n\n";

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

/**
 * 输出测试结果
 */
function outputTestResult($test, $result, $details = '', $time = null) {
    $status = $result ? '✅' : '❌';
    $timeStr = $time ? sprintf(' (%.2fms)', $time * 1000) : '';
    echo "   {$status} {$test}{$timeStr}";
    if ($details) {
        echo " - {$details}";
    }
    echo "\n";
    return $result;
}

/**
 * 测试1: 新增控制器文件完整性
 */
function testNewControllerFiles() {
    echo "1. 新增控制器文件完整性测试\n";
    
    $controllers = [
        'app/admin/controller/system/ApiVersionController.php' => 'API版本管理控制器',
        'app/admin/controller/system/ApiTestController.php' => 'API测试工具控制器',
        'app/admin/controller/system/CacheController.php' => '缓存管理控制器',
        'app/admin/controller/system/SearchController.php' => '搜索功能控制器',
        'app/admin/controller/system/MonitorController.php' => '性能监控控制器'
    ];
    
    $passed = 0;
    $total = count($controllers);
    
    foreach ($controllers as $file => $desc) {
        $exists = file_exists($file);
        $size = $exists ? filesize($file) : 0;
        $hasContent = $size > 1000; // 至少1KB内容
        
        $result = $exists && $hasContent;
        outputTestResult($desc, $result, $exists ? number_format($size) . ' bytes' : '文件不存在');
        if ($result) $passed++;
    }
    
    echo "   📊 控制器文件: {$passed}/{$total} (" . round(($passed/$total)*100, 1) . "%)\n\n";
    return $passed === $total;
}

/**
 * 测试2: 路由配置完整性
 */
function testRouteConfiguration() {
    echo "2. 路由配置完整性测试\n";
    
    try {
        $routeFile = 'config/route.php';
        if (!file_exists($routeFile)) {
            echo "   ❌ 路由配置文件不存在\n\n";
            return false;
        }
        
        $content = file_get_contents($routeFile);
        
        $requiredRoutes = [
            '/admin/system/api-version' => 'API版本管理路由',
            '/admin/system/api-test' => 'API测试工具路由',
            '/admin/system/cache' => '缓存管理路由',
            '/admin/system/search' => '搜索功能路由',
            '/admin/system/monitor' => '性能监控路由'
        ];
        
        $passed = 0;
        $total = count($requiredRoutes);
        
        foreach ($requiredRoutes as $route => $desc) {
            $exists = strpos($content, $route) !== false;
            outputTestResult($desc, $exists);
            if ($exists) $passed++;
        }
        
        echo "   📊 路由配置: {$passed}/{$total} (" . round(($passed/$total)*100, 1) . "%)\n\n";
        return $passed === $total;
        
    } catch (Exception $e) {
        echo "   ❌ 路由配置测试失败: " . $e->getMessage() . "\n\n";
        return false;
    }
}

/**
 * 测试3: 控制器类加载测试
 */
function testControllerClassLoading() {
    echo "3. 控制器类加载测试\n";
    
    try {
        // 引入必要文件
        require_once 'bootstrap/helpers.php';
        
        $controllers = [
            'ApiVersionController' => '\\app\\admin\\controller\\system\\ApiVersionController',
            'ApiTestController' => '\\app\\admin\\controller\\system\\ApiTestController',
            'CacheController' => '\\app\\admin\\controller\\system\\CacheController',
            'SearchController' => '\\app\\admin\\controller\\system\\SearchController',
            'MonitorController' => '\\app\\admin\\controller\\system\\MonitorController'
        ];
        
        $passed = 0;
        $total = count($controllers);
        
        foreach ($controllers as $name => $class) {
            try {
                if (class_exists($class)) {
                    $controller = new $class();
                    $hasIndexMethod = method_exists($controller, 'index');
                    outputTestResult($name, $hasIndexMethod, $hasIndexMethod ? '类和方法正常' : '缺少index方法');
                    if ($hasIndexMethod) $passed++;
                } else {
                    outputTestResult($name, false, '类不存在');
                }
            } catch (Exception $e) {
                outputTestResult($name, false, $e->getMessage());
            }
        }
        
        echo "   📊 类加载测试: {$passed}/{$total} (" . round(($passed/$total)*100, 1) . "%)\n\n";
        return $passed >= $total * 0.8; // 80%通过
        
    } catch (Exception $e) {
        echo "   ❌ 类加载测试失败: " . $e->getMessage() . "\n\n";
        return false;
    }
}

/**
 * 测试4: 功能方法测试
 */
function testControllerMethods() {
    echo "4. 控制器功能方法测试\n";
    
    try {
        require_once 'bootstrap/helpers.php';
        $request = new RequestMock();
        
        $tests = [
            ['ApiVersionController', 'getVersionList', 'API版本列表'],
            ['ApiTestController', 'getTestStatistics', 'API测试统计'],
            ['CacheController', 'getCacheStatistics', '缓存统计信息'],
            ['SearchController', 'getSearchStatistics', '搜索统计信息'],
            ['MonitorController', 'getSystemMetrics', '系统性能指标']
        ];
        
        $passed = 0;
        $total = count($tests);
        
        foreach ($tests as $test) {
            list($controllerName, $method, $desc) = $test;
            $className = "\\app\\admin\\controller\\system\\{$controllerName}";
            
            try {
                if (class_exists($className)) {
                    $controller = new $className();
                    if (method_exists($controller, $method)) {
                        $startTime = microtime(true);
                        $result = $controller->$method($request);
                        $endTime = microtime(true);
                        
                        $success = $result !== null;
                        outputTestResult($desc, $success, '', $endTime - $startTime);
                        if ($success) $passed++;
                    } else {
                        outputTestResult($desc, false, '方法不存在');
                    }
                } else {
                    outputTestResult($desc, false, '控制器不存在');
                }
            } catch (Exception $e) {
                outputTestResult($desc, false, $e->getMessage());
            }
        }
        
        echo "   📊 功能方法测试: {$passed}/{$total} (" . round(($passed/$total)*100, 1) . "%)\n\n";
        return $passed >= $total * 0.8;
        
    } catch (Exception $e) {
        echo "   ❌ 功能方法测试失败: " . $e->getMessage() . "\n\n";
        return false;
    }
}

/**
 * 测试5: 入口文件路由处理
 */
function testEntryPointRouting() {
    echo "5. 入口文件路由处理测试\n";
    
    try {
        $entryFile = 'public/index_simple.php';
        if (!file_exists($entryFile)) {
            echo "   ❌ 入口文件不存在\n\n";
            return false;
        }
        
        $content = file_get_contents($entryFile);
        
        $requiredHandlers = [
            'api-version' => 'API版本管理处理',
            'api-test' => 'API测试工具处理',
            'cache' => '缓存管理处理',
            'search' => '搜索功能处理',
            'monitor' => '性能监控处理'
        ];
        
        $passed = 0;
        $total = count($requiredHandlers);
        
        foreach ($requiredHandlers as $handler => $desc) {
            $exists = strpos($content, $handler) !== false;
            outputTestResult($desc, $exists);
            if ($exists) $passed++;
        }
        
        echo "   📊 路由处理: {$passed}/{$total} (" . round(($passed/$total)*100, 1) . "%)\n\n";
        return $passed === $total;
        
    } catch (Exception $e) {
        echo "   ❌ 入口文件测试失败: " . $e->getMessage() . "\n\n";
        return false;
    }
}

/**
 * 测试6: 系统首页导航更新
 */
function testHomepageNavigation() {
    echo "6. 系统首页导航更新测试\n";
    
    try {
        $entryFile = 'public/index_simple.php';
        $content = file_get_contents($entryFile);
        
        $requiredLinks = [
            '版本管理' => 'API版本管理链接',
            'API测试' => 'API测试工具链接',
            '缓存管理' => '缓存管理链接',
            '全文搜索' => '搜索功能链接',
            '性能监控' => '性能监控链接'
        ];
        
        $passed = 0;
        $total = count($requiredLinks);
        
        foreach ($requiredLinks as $link => $desc) {
            $exists = strpos($content, $link) !== false;
            outputTestResult($desc, $exists);
            if ($exists) $passed++;
        }
        
        echo "   📊 导航链接: {$passed}/{$total} (" . round(($passed/$total)*100, 1) . "%)\n\n";
        return $passed === $total;
        
    } catch (Exception $e) {
        echo "   ❌ 首页导航测试失败: " . $e->getMessage() . "\n\n";
        return false;
    }
}

/**
 * 测试7: 优化功能特性验证
 */
function testOptimizationFeatures() {
    echo "7. 优化功能特性验证\n";
    
    $features = [
        'API版本管理' => [
            'description' => '支持多版本API管理、版本比较、发布回滚',
            'key_methods' => ['getVersionList', 'compareVersions', 'createVersion']
        ],
        'API测试工具' => [
            'description' => '提供单个测试、批量测试、性能测试功能',
            'key_methods' => ['testSingleApi', 'batchTest', 'performanceTest']
        ],
        '智能缓存管理' => [
            'description' => '缓存监控、清理、预热、优化建议',
            'key_methods' => ['getCacheStatistics', 'clearCache', 'warmupCache']
        ],
        '全文搜索' => [
            'description' => '强大的搜索功能、智能建议、搜索分析',
            'key_methods' => ['search', 'suggest', 'analytics']
        ],
        '性能监控' => [
            'description' => '实时监控、历史数据、告警管理、健康检查',
            'key_methods' => ['getSystemMetrics', 'getRealTimeMetrics', 'healthCheck']
        ]
    ];
    
    $passed = 0;
    $total = count($features);
    
    foreach ($features as $feature => $info) {
        echo "   🔍 {$feature}:\n";
        echo "      - {$info['description']}\n";
        
        $methodCount = count($info['key_methods']);
        $existingMethods = 0;
        
        foreach ($info['key_methods'] as $method) {
            // 简单检查方法是否在相关控制器中定义
            $found = false;
            $controllerFiles = glob('app/admin/controller/system/*.php');
            foreach ($controllerFiles as $file) {
                if (strpos(file_get_contents($file), "function {$method}") !== false) {
                    $found = true;
                    break;
                }
            }
            if ($found) $existingMethods++;
        }
        
        $featureComplete = $existingMethods >= $methodCount * 0.8; // 80%方法存在
        echo "      - 核心方法: {$existingMethods}/{$methodCount}\n";
        
        if ($featureComplete) {
            echo "      ✅ 功能完整\n";
            $passed++;
        } else {
            echo "      ❌ 功能不完整\n";
        }
        echo "\n";
    }
    
    echo "   📊 优化功能: {$passed}/{$total} (" . round(($passed/$total)*100, 1) . "%)\n\n";
    return $passed >= $total * 0.8;
}

/**
 * 生成优化完成报告
 */
function generateOptimizationReport($results) {
    echo "=== 系统优化完成报告 ===\n";
    
    $testNames = [
        'controller_files' => '新增控制器文件',
        'route_config' => '路由配置',
        'class_loading' => '类加载',
        'controller_methods' => '控制器方法',
        'entry_routing' => '入口路由处理',
        'homepage_nav' => '首页导航',
        'optimization_features' => '优化功能特性'
    ];
    
    $passed = 0;
    $total = count($results);
    
    echo "📊 优化测试结果详情:\n";
    foreach ($results as $test => $result) {
        $status = $result ? '✅ 通过' : '❌ 失败';
        $name = $testNames[$test] ?? $test;
        echo "   - {$name}: {$status}\n";
        if ($result) $passed++;
    }
    
    $successRate = ($passed / $total) * 100;
    
    echo "\n📈 优化完成度评估:\n";
    echo "   - 通过率: " . number_format($successRate, 1) . "%\n";
    echo "   - 通过项: {$passed}/{$total}\n";
    
    if ($successRate >= 95) {
        echo "   🎉 系统优化完美完成！质量等级: A+\n";
        $grade = "A+";
    } elseif ($successRate >= 85) {
        echo "   ✅ 系统优化优秀完成！质量等级: A\n";
        $grade = "A";
    } elseif ($successRate >= 75) {
        echo "   🔄 系统优化良好完成！质量等级: B+\n";
        $grade = "B+";
    } else {
        echo "   ⚠️  系统优化需要进一步完善！质量等级: B\n";
        $grade = "B";
    }
    
    echo "   - 优化等级: {$grade}\n";
    
    echo "\n🚀 新增功能模块:\n";
    if ($results['controller_files']) {
        echo "   ✅ API版本管理: 支持多版本管理、比较、发布\n";
        echo "   ✅ API测试工具: 单个测试、批量测试、性能测试\n";
        echo "   ✅ 智能缓存管理: 监控、清理、预热、优化\n";
        echo "   ✅ 全文搜索: 强大搜索、智能建议、分析\n";
        echo "   ✅ 性能监控: 实时监控、告警、健康检查\n";
    }
    
    echo "\n💡 系统优化亮点:\n";
    echo "   - 🎯 功能模块化: 5个独立的功能模块\n";
    echo "   - 🔧 智能化管理: 自动优化和智能建议\n";
    echo "   - 📊 数据可视化: 丰富的图表和统计\n";
    echo "   - 🚀 性能提升: 缓存优化和性能监控\n";
    echo "   - 🔍 搜索增强: 全文搜索和智能建议\n";
    
    echo "\n🌟 使用指南:\n";
    echo "   1. 启动系统: start_api_doc_system.bat\n";
    echo "   2. 访问首页: http://localhost:8080\n";
    echo "   3. 体验新功能:\n";
    echo "      - API版本管理: /admin/system/api-version\n";
    echo "      - API测试工具: /admin/system/api-test\n";
    echo "      - 缓存管理: /admin/system/cache\n";
    echo "      - 全文搜索: /admin/system/search\n";
    echo "      - 性能监控: /admin/system/monitor\n";
    
    return $successRate;
}

// 执行优化完成测试
try {
    echo "开始执行EasyAdmin8-webman API文档管理系统优化完成测试...\n\n";
    
    $results = [];
    $results['controller_files'] = testNewControllerFiles();
    $results['route_config'] = testRouteConfiguration();
    $results['class_loading'] = testControllerClassLoading();
    $results['controller_methods'] = testControllerMethods();
    $results['entry_routing'] = testEntryPointRouting();
    $results['homepage_nav'] = testHomepageNavigation();
    $results['optimization_features'] = testOptimizationFeatures();
    
    $finalScore = generateOptimizationReport($results);
    
    echo "\n=== 系统优化完成测试结束 ===\n";
    
    if ($finalScore >= 90) {
        echo "🎊 恭喜！EasyAdmin8-webman API文档管理系统优化完美完成！\n";
        echo "🚀 系统已升级为企业级专业解决方案，可以立即投入使用！\n";
    } elseif ($finalScore >= 80) {
        echo "✅ EasyAdmin8-webman API文档管理系统优化优秀完成！\n";
        echo "🔧 系统功能大幅提升，建议进行最终测试后投入使用！\n";
    } else {
        echo "⚠️  EasyAdmin8-webman API文档管理系统优化基本完成！\n";
        echo "🔧 建议根据测试报告进行进一步优化！\n";
    }
    
} catch (Exception $e) {
    echo "❌ 优化测试过程中发生错误: " . $e->getMessage() . "\n";
    echo "📍 错误位置: " . $e->getFile() . ":" . $e->getLine() . "\n";
} catch (Error $e) {
    echo "❌ 优化测试过程中发生致命错误: " . $e->getMessage() . "\n";
    echo "📍 错误位置: " . $e->getFile() . ":" . $e->getLine() . "\n";
}

echo "\n🎯 EasyAdmin8-webman API文档管理系统优化完成测试结束！\n";
