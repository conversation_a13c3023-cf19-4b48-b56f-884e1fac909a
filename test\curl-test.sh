#!/bin/bash

# CURD生成器V2 - CURL API测试脚本
# 使用curl命令直接测试API接口

echo "🔧 CURD生成器V2 - CURL API测试"
echo "=================================================="
echo ""

# 配置
BASE_URL="http://localhost:8787"
API_URL="$BASE_URL/curdtest/tables"
ANALYZE_URL="$BASE_URL/curdtest/analyze"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 测试1: 检查服务器状态
echo -e "${BLUE}🌐 测试1: 检查服务器状态${NC}"
echo "------------------------------"

echo "测试URL: $BASE_URL"
HTTP_CODE=$(curl -s -o /dev/null -w "%{http_code}" --connect-timeout 5 "$BASE_URL")

if [ "$HTTP_CODE" = "000" ]; then
    echo -e "${RED}❌ 服务器连接失败${NC}"
    echo -e "${YELLOW}💡 请确保服务器已启动: php start.php start${NC}"
    exit 1
else
    echo -e "${GREEN}✅ 服务器运行正常 (HTTP $HTTP_CODE)${NC}"
fi
echo ""

# 测试2: 测试API路由
echo -e "${BLUE}🛣️  测试2: 测试API路由${NC}"
echo "------------------------------"

echo "测试URL: $API_URL"
RESPONSE=$(curl -s -w "\n%{http_code}" -X POST \
    -H "Content-Type: application/x-www-form-urlencoded" \
    -H "User-Agent: CURD-CURL-Test/1.0" \
    -d "connection=mysql" \
    "$API_URL")

# 分离响应体和状态码
HTTP_CODE=$(echo "$RESPONSE" | tail -n1)
BODY=$(echo "$RESPONSE" | head -n -1)

echo "HTTP状态码: $HTTP_CODE"

if [ "$HTTP_CODE" = "200" ]; then
    echo -e "${GREEN}✅ API路由可访问${NC}"
    
    # 检查JSON格式
    if echo "$BODY" | jq . > /dev/null 2>&1; then
        echo -e "${GREEN}✅ 响应为有效JSON格式${NC}"
        echo "响应内容:"
        echo "$BODY" | jq .
    else
        echo -e "${YELLOW}⚠️  响应不是有效JSON格式${NC}"
        echo "原始响应: $BODY"
    fi
else
    echo -e "${RED}❌ API路由访问失败${NC}"
    echo "响应内容: $BODY"
fi
echo ""

# 测试3: 测试mysql_second连接
echo -e "${BLUE}🎯 测试3: 重点测试mysql_second连接${NC}"
echo "------------------------------"

echo "发送请求到: $API_URL"
echo "参数: connection=mysql_second"

RESPONSE=$(curl -s -w "\n%{http_code}\n%{time_total}" -X POST \
    -H "Content-Type: application/x-www-form-urlencoded" \
    -H "X-Requested-With: XMLHttpRequest" \
    -H "X-Debug-Mode: true" \
    -d "connection=mysql_second&debug=true" \
    "$API_URL")

# 分离响应体、状态码和时间
HTTP_CODE=$(echo "$RESPONSE" | tail -n2 | head -n1)
TOTAL_TIME=$(echo "$RESPONSE" | tail -n1)
BODY=$(echo "$RESPONSE" | head -n -2)

echo "HTTP状态码: $HTTP_CODE"
echo "响应时间: ${TOTAL_TIME}s"

if [ "$HTTP_CODE" = "200" ]; then
    echo -e "${GREEN}✅ mysql_second 请求成功${NC}"
    
    if echo "$BODY" | jq . > /dev/null 2>&1; then
        # 解析JSON响应
        CODE=$(echo "$BODY" | jq -r '.code')
        MSG=$(echo "$BODY" | jq -r '.msg')
        DATA_COUNT=$(echo "$BODY" | jq '.data | length')
        
        echo "API返回码: $CODE"
        echo "API消息: $MSG"
        echo "表数量: $DATA_COUNT"
        
        if [ "$CODE" = "1" ]; then
            echo -e "${GREEN}🎯 mysql_second 连接测试成功！${NC}"
            
            if [ "$DATA_COUNT" -gt 0 ]; then
                echo "📋 表列表:"
                echo "$BODY" | jq -r '.data[] | "  - \(.name) (\(.comment // "无注释"))"'
                
                # 检查期望的表
                echo ""
                echo "🔍 检查期望的表:"
                EXPECTED_TABLES=("admin" "member" "shop_product" "shop_order" "article" "business")
                
                for table in "${EXPECTED_TABLES[@]}"; do
                    if echo "$BODY" | jq -e ".data[] | select(.name == \"$table\")" > /dev/null; then
                        echo -e "  ${GREEN}✅ $table${NC}"
                    else
                        echo -e "  ${RED}❌ $table (缺失)${NC}"
                    fi
                done
            fi
            
            if echo "$MSG" | grep -q "演示数据"; then
                echo -e "${YELLOW}⚠️  注意: 使用演示数据模式${NC}"
            fi
        else
            echo -e "${RED}❌ API返回错误: $MSG${NC}"
        fi
    else
        echo -e "${RED}❌ 响应不是有效JSON${NC}"
        echo "原始响应: $BODY"
    fi
else
    echo -e "${RED}❌ mysql_second 请求失败 (HTTP $HTTP_CODE)${NC}"
    echo "响应内容: $BODY"
fi
echo ""

# 测试4: 测试所有连接
echo -e "${BLUE}🗄️  测试4: 测试所有数据库连接${NC}"
echo "------------------------------"

CONNECTIONS=("mysql" "mysql_read" "mysql_second" "mysql_log" "mysql_cache")

for conn in "${CONNECTIONS[@]}"; do
    echo -n "测试 $conn: "
    
    RESPONSE=$(curl -s -w "%{http_code}" -X POST \
        -H "Content-Type: application/x-www-form-urlencoded" \
        -d "connection=$conn" \
        --max-time 10 \
        "$API_URL")
    
    HTTP_CODE=$(echo "$RESPONSE" | tail -c 4)
    BODY=$(echo "$RESPONSE" | head -c -4)
    
    if [ "$HTTP_CODE" = "200" ]; then
        if echo "$BODY" | jq . > /dev/null 2>&1; then
            CODE=$(echo "$BODY" | jq -r '.code')
            if [ "$CODE" = "1" ]; then
                COUNT=$(echo "$BODY" | jq '.data | length')
                echo -e "${GREEN}✅ 成功 ($COUNT 个表)${NC}"
            else
                MSG=$(echo "$BODY" | jq -r '.msg')
                echo -e "${RED}❌ API错误: $MSG${NC}"
            fi
        else
            echo -e "${RED}❌ 响应格式错误${NC}"
        fi
    else
        echo -e "${RED}❌ HTTP错误: $HTTP_CODE${NC}"
    fi
done
echo ""

# 测试5: 测试表分析功能
echo -e "${BLUE}🔍 测试5: 测试表分析功能${NC}"
echo "------------------------------"

echo "测试URL: $ANALYZE_URL"
echo "参数: table_name=admin, connection=mysql_second"

RESPONSE=$(curl -s -w "\n%{http_code}" -X POST \
    -H "Content-Type: application/x-www-form-urlencoded" \
    -d "table_name=admin&table_prefix=&connection=mysql_second" \
    "$ANALYZE_URL")

HTTP_CODE=$(echo "$RESPONSE" | tail -n1)
BODY=$(echo "$RESPONSE" | head -n -1)

echo "HTTP状态码: $HTTP_CODE"

if [ "$HTTP_CODE" = "200" ]; then
    if echo "$BODY" | jq . > /dev/null 2>&1; then
        CODE=$(echo "$BODY" | jq -r '.code')
        if [ "$CODE" = "1" ]; then
            echo -e "${GREEN}✅ 表分析成功${NC}"
            
            TABLE_NAME=$(echo "$BODY" | jq -r '.data.name')
            TABLE_COMMENT=$(echo "$BODY" | jq -r '.data.comment')
            FIELD_COUNT=$(echo "$BODY" | jq '.data.fields | length')
            
            echo "表名: $TABLE_NAME"
            echo "注释: $TABLE_COMMENT"
            echo "字段数: $FIELD_COUNT"
            
            echo "字段列表:"
            echo "$BODY" | jq -r '.data.fields[0:3][] | "  - \(.name) (\(.type)) - \(.comment)"'
        else
            MSG=$(echo "$BODY" | jq -r '.msg')
            echo -e "${RED}❌ 表分析失败: $MSG${NC}"
        fi
    else
        echo -e "${RED}❌ 响应格式错误${NC}"
        echo "原始响应: $BODY"
    fi
else
    echo -e "${RED}❌ 表分析请求失败 (HTTP $HTTP_CODE)${NC}"
    echo "响应内容: $BODY"
fi
echo ""

# 总结
echo -e "${BLUE}📊 测试总结${NC}"
echo "=================================================="
echo -e "${GREEN}✅ CURL API测试完成${NC}"
echo ""
echo "🎯 关键检查点:"
echo "  1. 服务器是否运行正常"
echo "  2. API路由是否可访问"
echo "  3. mysql_second是否返回6个表"
echo "  4. 表分析功能是否正常"
echo ""
echo "💡 下一步:"
echo "  - 如果API测试全部通过，问题在前端JavaScript"
echo "  - 如果API测试失败，检查服务器配置和路由"
echo "  - 访问简化测试页面: http://localhost:8787/test/simple-connection-test.html"
echo ""
