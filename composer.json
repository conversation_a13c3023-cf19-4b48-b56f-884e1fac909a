{"name": "wolfcode/easyadmin8", "type": "project", "url": "https://github.com/EasyAdmin8/EasyAdmin8-webman", "description": "基于webman和Layui的快速开发的后台管理系统。", "keywords": ["webman", "<PERSON><PERSON><PERSON>", "admin"], "homepage": "https://easyadmin8.top/", "license": "MIT", "authors": [{"name": "wolfcode"}], "require": {"php": ">=8.1.0", "workerman/webman-framework": "^2.1", "monolog/monolog": "^2.0", "illuminate/pagination": "^11.0 || ^9.52", "illuminate/events": "^11.0 || ^9.52", "illuminate/view": "^11.0 || ^9.52", "illuminate/filesystem": "^11.0 || ^9.52", "symfony/var-dumper": "^7.0 || ^6.4", "symfony/finder": "^7.0 || ^6.4", "vlucas/phpdotenv": "^5.5", "psr/container": "^2.0", "workerman/validation": "^3.0", "doctrine/annotations": "^2.0", "phpoffice/phpspreadsheet": "^1.29", "webman/database": "^2.1", "webman/console": "^2.1", "webman/blade": "^1.5", "webman/captcha": "^1.0", "webman/cache": "^2.1", "webman/log": "^2.1", "webman/domain": "^2.1", "aliyuncs/oss-sdk-php": "^2.6", "qcloud/cos-sdk-v5": "^2.6", "qiniu/php-sdk": "^7.11", "wolf-leo/phplogviewer": "^0.11.3", "wolfcode/authenticator": "^0.0.6", "ext-pdo": "*"}, "suggest": {"ext-event": "For better performance. "}, "autoload": {"psr-4": {"": "./", "app\\": "./app", "App\\": "./app", "app\\View\\Components\\": "./app/view/components"}}, "scripts": {"post-package-install": ["support\\Plugin::install"], "post-package-update": ["support\\Plugin::install"], "pre-package-uninstall": ["support\\Plugin::uninstall"]}}