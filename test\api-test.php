<?php
/**
 * API接口直接测试脚本
 * 绕过前端，直接测试后端API接口
 */

echo "🔧 CURD生成器V2 - API接口直接测试\n";
echo str_repeat("=", 50) . "\n\n";

// 测试配置
$baseUrl = 'http://localhost:8787';
$testConnections = ['mysql', 'mysql_second', 'mysql_read', 'mysql_log', 'mysql_cache'];

// 测试1: 检查服务器是否运行
echo "🌐 测试1: 检查服务器状态\n";
echo str_repeat("-", 30) . "\n";

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $baseUrl);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 5);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, false);
curl_setopt($ch, CURLOPT_HEADER, true);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

if ($error) {
    echo "❌ 服务器连接失败: $error\n";
    echo "💡 请确保服务器已启动: php start.php start\n\n";
    exit(1);
} else {
    echo "✅ 服务器运行正常 (HTTP $httpCode)\n\n";
}

// 测试2: 检查路由是否存在
echo "🛣️  测试2: 检查CURD测试路由\n";
echo str_repeat("-", 30) . "\n";

$testUrl = $baseUrl . '/curdtest/tables';
echo "测试URL: $testUrl\n";

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $testUrl);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 10);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, 'connection=mysql');
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/x-www-form-urlencoded',
    'User-Agent: CURD-API-Test/1.0'
]);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$contentType = curl_getinfo($ch, CURLINFO_CONTENT_TYPE);
$error = curl_error($ch);
curl_close($ch);

if ($error) {
    echo "❌ 路由请求失败: $error\n\n";
} else {
    echo "📊 HTTP状态码: $httpCode\n";
    echo "📄 内容类型: $contentType\n";
    
    if ($httpCode == 200) {
        echo "✅ 路由存在且可访问\n";
        
        // 尝试解析JSON响应
        $data = json_decode($response, true);
        if ($data !== null) {
            echo "✅ 响应格式为有效JSON\n";
            echo "📋 响应内容: " . json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n";
        } else {
            echo "⚠️  响应不是有效JSON格式\n";
            echo "📄 原始响应: " . substr($response, 0, 200) . "...\n";
        }
    } else {
        echo "❌ 路由访问异常\n";
        echo "📄 响应内容: " . substr($response, 0, 200) . "...\n";
    }
}

echo "\n";

// 测试3: 测试所有数据库连接
echo "🗄️  测试3: 测试各数据库连接\n";
echo str_repeat("-", 30) . "\n";

foreach ($testConnections as $connection) {
    echo "测试连接: $connection\n";
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $testUrl);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 15);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query([
        'connection' => $connection,
        'debug' => true
    ]));
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/x-www-form-urlencoded',
        'X-Requested-With: XMLHttpRequest'
    ]);
    
    $startTime = microtime(true);
    $response = curl_exec($ch);
    $duration = round((microtime(true) - $startTime) * 1000, 2);
    
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($error) {
        echo "  ❌ 请求失败: $error\n";
    } else if ($httpCode != 200) {
        echo "  ❌ HTTP错误: $httpCode\n";
        echo "  📄 响应: " . substr($response, 0, 100) . "...\n";
    } else {
        $data = json_decode($response, true);
        if ($data && isset($data['code'])) {
            if ($data['code'] == 1) {
                $tableCount = count($data['data'] ?? []);
                echo "  ✅ 成功 ({$duration}ms) - 获取到 $tableCount 个表\n";
                
                if (strpos($data['msg'], '演示数据') !== false) {
                    echo "  ⚠️  使用演示数据模式\n";
                }
                
                // 显示前3个表
                if (!empty($data['data'])) {
                    $tables = array_slice($data['data'], 0, 3);
                    foreach ($tables as $table) {
                        echo "    - {$table['name']} ({$table['comment']})\n";
                    }
                    if (count($data['data']) > 3) {
                        echo "    - ... 还有 " . (count($data['data']) - 3) . " 个表\n";
                    }
                }
            } else {
                echo "  ❌ API错误: {$data['msg']}\n";
            }
        } else {
            echo "  ❌ 响应格式错误\n";
            echo "  📄 原始响应: " . substr($response, 0, 100) . "...\n";
        }
    }
    echo "\n";
}

// 测试4: 重点测试mysql_second
echo "🎯 测试4: 重点测试mysql_second连接\n";
echo str_repeat("-", 30) . "\n";

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $testUrl);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 20);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query([
    'connection' => 'mysql_second',
    'debug' => true,
    'timestamp' => date('Y-m-d H:i:s')
]));
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/x-www-form-urlencoded',
    'X-Requested-With: XMLHttpRequest',
    'X-Debug-Mode: true'
]);
curl_setopt($ch, CURLOPT_VERBOSE, true);

$startTime = microtime(true);
$response = curl_exec($ch);
$duration = round((microtime(true) - $startTime) * 1000, 2);

$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$totalTime = curl_getinfo($ch, CURLINFO_TOTAL_TIME);
$connectTime = curl_getinfo($ch, CURLINFO_CONNECT_TIME);
$error = curl_error($ch);
curl_close($ch);

echo "📊 详细性能信息:\n";
echo "  - HTTP状态: $httpCode\n";
echo "  - 总耗时: {$duration}ms\n";
echo "  - 连接时间: " . round($connectTime * 1000, 2) . "ms\n";
echo "  - 传输时间: " . round($totalTime * 1000, 2) . "ms\n";

if ($error) {
    echo "❌ 请求失败: $error\n";
} else {
    $data = json_decode($response, true);
    if ($data) {
        echo "✅ JSON解析成功\n";
        echo "📋 响应结构:\n";
        echo "  - code: {$data['code']}\n";
        echo "  - msg: {$data['msg']}\n";
        echo "  - data count: " . count($data['data'] ?? []) . "\n";
        
        if ($data['code'] == 1) {
            echo "🎯 mysql_second 测试成功！\n";
            echo "📋 期望的表列表:\n";
            $expectedTables = ['admin', 'member', 'shop_product', 'shop_order', 'article', 'business'];
            $actualTables = array_column($data['data'] ?? [], 'name');
            
            foreach ($expectedTables as $expected) {
                if (in_array($expected, $actualTables)) {
                    echo "  ✅ $expected\n";
                } else {
                    echo "  ❌ $expected (缺失)\n";
                }
            }
            
            $extra = array_diff($actualTables, $expectedTables);
            if (!empty($extra)) {
                echo "  ⚠️  额外的表: " . implode(', ', $extra) . "\n";
            }
        } else {
            echo "❌ mysql_second 测试失败: {$data['msg']}\n";
        }
    } else {
        echo "❌ JSON解析失败\n";
        echo "📄 原始响应: $response\n";
    }
}

echo "\n";

// 测试5: 测试表分析功能
echo "🔍 测试5: 测试表分析功能\n";
echo str_repeat("-", 30) . "\n";

$analyzeUrl = $baseUrl . '/curdtest/analyze';
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $analyzeUrl);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 15);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query([
    'table_name' => 'admin',
    'table_prefix' => '',
    'connection' => 'mysql_second'
]));

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

if ($error) {
    echo "❌ 表分析请求失败: $error\n";
} else if ($httpCode != 200) {
    echo "❌ 表分析HTTP错误: $httpCode\n";
} else {
    $data = json_decode($response, true);
    if ($data && $data['code'] == 1) {
        echo "✅ 表分析成功\n";
        $tableInfo = $data['data'];
        echo "📋 表信息:\n";
        echo "  - 表名: {$tableInfo['name']}\n";
        echo "  - 注释: {$tableInfo['comment']}\n";
        echo "  - 字段数: " . count($tableInfo['fields']) . "\n";
        
        echo "📋 字段列表:\n";
        foreach (array_slice($tableInfo['fields'], 0, 5) as $field) {
            echo "  - {$field['name']} ({$field['type']}) - {$field['comment']}\n";
        }
    } else {
        echo "❌ 表分析失败: " . ($data['msg'] ?? '未知错误') . "\n";
    }
}

echo "\n";

// 总结
echo "📊 测试总结\n";
echo str_repeat("=", 50) . "\n";
echo "✅ API接口直接测试完成\n";
echo "🎯 如果mysql_second显示6个表，说明后端API工作正常\n";
echo "🔍 如果API正常但前端不工作，问题在于JavaScript事件绑定\n";
echo "💡 下一步: 使用简化测试页面检查前端问题\n";
echo "   访问: http://localhost:8787/test/simple-connection-test.html\n";
echo "\n";
?>
