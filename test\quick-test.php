<?php
/**
 * 快速测试脚本 - 验证数据库连接修复
 */

require_once __DIR__ . '/../vendor/autoload.php';

echo "🔧 CURD生成器V2 - 数据库连接快速测试\n";
echo str_repeat("=", 50) . "\n\n";

// 测试1: 模拟前端请求测试表列表获取
echo "📡 测试1: 模拟表列表获取请求\n";
echo str_repeat("-", 30) . "\n";

$connections = ['mysql', 'mysql_second', 'mysql_log'];

foreach ($connections as $connection) {
    echo "测试连接: {$connection}\n";

    try {
        // 模拟控制器调用
        $controller = new \app\admin\controller\system\CurdTestController();

        // 创建模拟请求对象
        $request = new class($connection) extends \support\Request {
            private $connectionValue;

            public function __construct($connection) {
                $this->connectionValue = $connection;
                // 不调用父类构造函数，避免依赖问题
            }

            public function input($key, $default = null) {
                return $key === 'connection' ? $this->connectionValue : $default;
            }
        };

        $response = $controller->getTables($request);
        $data = json_decode($response->rawBody(), true);

        if ($data['code'] === 1) {
            echo "✅ 成功获取 " . count($data['data']) . " 个表\n";

            // 显示前3个表
            $tables = array_slice($data['data'], 0, 3);
            foreach ($tables as $table) {
                echo "   - {$table['name']} ({$table['comment']})\n";
            }

            if (strpos($data['msg'], '演示数据') !== false) {
                echo "   [演示模式]\n";
            }
        } else {
            echo "❌ 获取失败: {$data['msg']}\n";
        }

    } catch (Exception $e) {
        echo "💥 异常: " . $e->getMessage() . "\n";
    }

    echo "\n";
}

// 测试2: 验证mysql_second的特定表
echo "🎯 测试2: 验证mysql_second连接的表列表\n";
echo str_repeat("-", 30) . "\n";

try {
    $controller = new \app\admin\controller\system\CurdTestController();
    $request = new class('mysql_second') extends \support\Request {
        private $connectionValue = 'mysql_second';

        public function __construct($connection) {
            $this->connectionValue = $connection;
        }

        public function input($key, $default = null) {
            return $key === 'connection' ? $this->connectionValue : $default;
        }
    };

    $response = $controller->getTables($request);
    $data = json_decode($response->rawBody(), true);

    if ($data['code'] === 1) {
        $expectedTables = ['admin', 'member', 'shop_product', 'shop_order', 'article', 'business'];
        $actualTables = array_column($data['data'], 'name');

        echo "期望的表: " . implode(', ', $expectedTables) . "\n";
        echo "实际的表: " . implode(', ', $actualTables) . "\n";

        $missing = array_diff($expectedTables, $actualTables);
        $extra = array_diff($actualTables, $expectedTables);

        if (empty($missing) && empty($extra)) {
            echo "✅ mysql_second 表列表完全匹配\n";
        } else {
            if (!empty($missing)) {
                echo "❌ 缺少表: " . implode(', ', $missing) . "\n";
            }
            if (!empty($extra)) {
                echo "⚠️  额外表: " . implode(', ', $extra) . "\n";
            }
        }
    } else {
        echo "❌ 获取mysql_second表列表失败: {$data['msg']}\n";
    }

} catch (Exception $e) {
    echo "💥 异常: " . $e->getMessage() . "\n";
}

echo "\n";

// 测试3: 验证表分析功能
echo "🔍 测试3: 验证表分析功能\n";
echo str_repeat("-", 30) . "\n";

try {
    $controller = new \app\admin\controller\system\CurdTestController();
    $request = new class() extends \support\Request {
        public function __construct() {
            // 不调用父类构造函数
        }

        public function input($key, $default = null) {
            $data = [
                'table_name' => 'admin',
                'table_prefix' => '',
                'connection' => 'mysql_second'
            ];
            return $data[$key] ?? $default;
        }
    };

    $response = $controller->analyzeTable($request);
    $data = json_decode($response->rawBody(), true);

    if ($data['code'] === 1) {
        $tableInfo = $data['data'];
        echo "✅ 表分析成功\n";
        echo "   表名: {$tableInfo['name']}\n";
        echo "   注释: {$tableInfo['comment']}\n";
        echo "   字段数: " . count($tableInfo['fields']) . "\n";

        // 显示前3个字段
        $fields = array_slice($tableInfo['fields'], 0, 3);
        foreach ($fields as $field) {
            echo "   - {$field['name']} ({$field['type']}) - {$field['comment']}\n";
        }

    } else {
        echo "❌ 表分析失败: {$data['msg']}\n";
    }

} catch (Exception $e) {
    echo "💥 异常: " . $e->getMessage() . "\n";
}

echo "\n";

// 测试4: 检查配置文件
echo "⚙️  测试4: 检查配置文件\n";
echo str_repeat("-", 30) . "\n";

// 检查路由配置
if (file_exists('config/route.php')) {
    $routeContent = file_get_contents('config/route.php');
    if (strpos($routeContent, 'app\\admin\\controller\\system\\CurdTestController::class') !== false) {
        echo "✅ 路由配置正确\n";
    } else {
        echo "❌ 路由配置错误\n";
    }
} else {
    echo "❌ 路由配置文件不存在\n";
}

// 检查数据库配置
if (file_exists('config/database.php')) {
    $dbConfig = include 'config/database.php';
    if (isset($dbConfig['connections']['mysql_second'])) {
        echo "✅ mysql_second 数据库配置存在\n";
        $config = $dbConfig['connections']['mysql_second'];
        echo "   数据库: {$config['database']}\n";
        echo "   前缀: {$config['prefix']}\n";
    } else {
        echo "❌ mysql_second 数据库配置不存在\n";
    }
} else {
    echo "❌ 数据库配置文件不存在\n";
}

echo "\n";

// 测试5: JavaScript和HTML检查
echo "🎨 测试5: 前端文件检查\n";
echo str_repeat("-", 30) . "\n";

// 检查JavaScript文件
if (file_exists('public/static/admin/js/curd-generator-v2.js')) {
    $jsContent = file_get_contents('public/static/admin/js/curd-generator-v2.js');

    $jsChecks = [
        "form.on('select(connectionSelect)" => '连接选择事件监听器',
        "form.on('select(tableSelect)" => '表选择事件监听器',
        'loadTables()' => '表加载函数'
    ];

    foreach ($jsChecks as $pattern => $description) {
        if (strpos($jsContent, $pattern) !== false) {
            echo "✅ {$description}\n";
        } else {
            echo "❌ {$description} 未找到\n";
        }
    }
} else {
    echo "❌ JavaScript文件不存在\n";
}

// 检查HTML模板
if (file_exists('app/admin/view/admin/system/curdgeneratev2/index.blade.php')) {
    $htmlContent = file_get_contents('app/admin/view/admin/system/curdgeneratev2/index.blade.php');

    $htmlChecks = [
        'lay-filter="connectionSelect"' => '连接选择框过滤器',
        'lay-filter="tableSelect"' => '表选择框过滤器',
        'mysql_second' => '第二数据库选项'
    ];

    foreach ($htmlChecks as $pattern => $description) {
        if (strpos($htmlContent, $pattern) !== false) {
            echo "✅ {$description}\n";
        } else {
            echo "❌ {$description} 未找到\n";
        }
    }
} else {
    echo "❌ HTML模板文件不存在\n";
}

echo "\n";

// 总结
echo "📊 测试总结\n";
echo str_repeat("=", 50) . "\n";
echo "✅ 所有核心功能测试完成\n";
echo "🎯 重点验证项目:\n";
echo "   - mysql_second 连接的表列表获取\n";
echo "   - 模拟数据的一致性\n";
echo "   - 路由配置的正确性\n";
echo "   - 前端事件监听器的修复\n";
echo "\n";
echo "🚀 下一步: 启动Web服务器进行实际测试\n";
echo "   php start.php start\n";
echo "   访问: http://localhost:8787/test/database-connection-test.html\n";
echo "\n";
?>
