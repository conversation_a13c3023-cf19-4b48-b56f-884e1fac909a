<?php
/**
 * 自动数据库设置脚本
 */

echo "=== 自动数据库连接设置 ===\n\n";

// 常见的数据库连接配置
$connectionConfigs = [
    [
        'host' => '127.0.0.1',
        'port' => 3306,
        'username' => 'root',
        'password' => '',
        'database' => 'easyadmin8'
    ],
    [
        'host' => '127.0.0.1',
        'port' => 3306,
        'username' => 'root',
        'password' => 'root',
        'database' => 'easyadmin8'
    ],
    [
        'host' => '127.0.0.1',
        'port' => 3306,
        'username' => 'root',
        'password' => '123456',
        'database' => 'easyadmin8'
    ],
    [
        'host' => 'localhost',
        'port' => 3306,
        'username' => 'root',
        'password' => '',
        'database' => 'easyadmin8'
    ],
];

// 测试数据库连接
function testConnection($config) {
    try {
        $dsn = "mysql:host={$config['host']};port={$config['port']};charset=utf8mb4";
        $pdo = new PDO($dsn, $config['username'], $config['password'], [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_TIMEOUT => 3
        ]);
        
        return ['success' => true, 'pdo' => $pdo];
    } catch (PDOException $e) {
        return ['success' => false, 'error' => $e->getMessage()];
    }
}

$workingConfig = null;

echo "🔍 自动检测数据库连接...\n";

foreach ($connectionConfigs as $i => $config) {
    $passwordDisplay = $config['password'] === '' ? '(空密码)' : $config['password'];
    echo "   尝试: {$config['username']}@{$config['host']}:{$config['port']} / {$passwordDisplay}\n";
    
    $result = testConnection($config);
    
    if ($result['success']) {
        echo "   ✅ 连接成功!\n";
        $workingConfig = $config;
        $workingConfig['pdo'] = $result['pdo'];
        break;
    } else {
        echo "   ❌ 连接失败: " . substr($result['error'], 0, 50) . "...\n";
    }
}

if (!$workingConfig) {
    echo "\n❌ 无法连接到 MySQL 数据库\n";
    echo "请检查:\n";
    echo "1. MySQL 服务是否启动\n";
    echo "2. 用户名和密码是否正确\n";
    echo "3. 手动运行: php setup_real_database.php\n";
    exit(1);
}

echo "\n🎉 找到可用连接!\n";
echo "   主机: {$workingConfig['host']}:{$workingConfig['port']}\n";
echo "   用户名: {$workingConfig['username']}\n";
echo "   密码: " . ($workingConfig['password'] === '' ? '(空密码)' : $workingConfig['password']) . "\n";

$pdo = $workingConfig['pdo'];

// 检查数据库是否存在
echo "\n🔍 检查数据库 '{$workingConfig['database']}'...\n";

try {
    $stmt = $pdo->query("SHOW DATABASES LIKE '{$workingConfig['database']}'");
    $exists = $stmt->fetch();
    
    if (!$exists) {
        echo "   数据库不存在，正在创建...\n";
        $pdo->exec("CREATE DATABASE IF NOT EXISTS `{$workingConfig['database']}` CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci");
        echo "   ✅ 数据库创建成功\n";
    } else {
        echo "   ✅ 数据库已存在\n";
    }
    
    // 连接到具体数据库
    $dsn = "mysql:host={$workingConfig['host']};port={$workingConfig['port']};dbname={$workingConfig['database']};charset=utf8mb4";
    $dbPdo = new PDO($dsn, $workingConfig['username'], $workingConfig['password'], [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_TIMEOUT => 5
    ]);
    
    // 检查表
    $stmt = $dbPdo->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    echo "   📊 当前表数量: " . count($tables) . "\n";
    
    if (count($tables) === 0) {
        echo "   🔧 创建示例表...\n";
        
        // 创建用户表
        $dbPdo->exec("
        CREATE TABLE IF NOT EXISTS `ea8_users` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `username` varchar(50) NOT NULL COMMENT '用户名',
            `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
            `password` varchar(255) NOT NULL COMMENT '密码',
            `status` tinyint(1) DEFAULT '1' COMMENT '状态:1=正常,0=禁用',
            `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
            PRIMARY KEY (`id`),
            UNIQUE KEY `username` (`username`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表'
        ");
        
        // 创建文章表
        $dbPdo->exec("
        CREATE TABLE IF NOT EXISTS `ea8_articles` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `title` varchar(200) NOT NULL COMMENT '标题',
            `content` text COMMENT '内容',
            `author_id` int(11) DEFAULT NULL COMMENT '作者ID',
            `category_id` int(11) DEFAULT NULL COMMENT '分类ID',
            `status` tinyint(1) DEFAULT '1' COMMENT '状态:1=发布,0=草稿',
            `views` int(11) DEFAULT '0' COMMENT '浏览量',
            `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
            PRIMARY KEY (`id`),
            KEY `idx_author` (`author_id`),
            KEY `idx_category` (`category_id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='文章表'
        ");
        
        // 创建分类表
        $dbPdo->exec("
        CREATE TABLE IF NOT EXISTS `ea8_categories` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `name` varchar(100) NOT NULL COMMENT '分类名称',
            `description` text COMMENT '分类描述',
            `parent_id` int(11) DEFAULT '0' COMMENT '父分类ID',
            `sort_order` int(11) DEFAULT '0' COMMENT '排序',
            `status` tinyint(1) DEFAULT '1' COMMENT '状态:1=启用,0=禁用',
            `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
            PRIMARY KEY (`id`),
            KEY `idx_parent` (`parent_id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分类表'
        ");
        
        // 创建配置表
        $dbPdo->exec("
        CREATE TABLE IF NOT EXISTS `ea8_config` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `name` varchar(100) NOT NULL COMMENT '配置名称',
            `value` text COMMENT '配置值',
            `description` varchar(255) COMMENT '配置描述',
            `group_name` varchar(50) DEFAULT 'system' COMMENT '配置分组',
            `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
            PRIMARY KEY (`id`),
            UNIQUE KEY `name` (`name`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='配置表'
        ");
        
        echo "      ✅ 示例表创建成功\n";
        
        // 插入示例数据
        echo "   📋 插入示例数据...\n";
        
        $dbPdo->exec("INSERT IGNORE INTO ea8_users (username, email, password) VALUES 
            ('admin', '<EMAIL>', '" . password_hash('admin123', PASSWORD_DEFAULT) . "'),
            ('user1', '<EMAIL>', '" . password_hash('123456', PASSWORD_DEFAULT) . "'),
            ('user2', '<EMAIL>', '" . password_hash('123456', PASSWORD_DEFAULT) . "')");
        
        $dbPdo->exec("INSERT IGNORE INTO ea8_categories (id, name, description) VALUES 
            (1, '技术', '技术相关文章'),
            (2, '生活', '生活相关文章'),
            (3, '学习', '学习相关文章')");
        
        $dbPdo->exec("INSERT IGNORE INTO ea8_articles (title, content, author_id, category_id) VALUES 
            ('PHP 开发技巧', 'PHP 是一种广泛使用的服务器端脚本语言...', 1, 1),
            ('MySQL 优化指南', 'MySQL 数据库优化是提高应用性能的关键...', 1, 1),
            ('生活小贴士', '分享一些日常生活中的实用小技巧...', 2, 2),
            ('学习方法论', '如何高效学习新技术和新知识...', 1, 3)");
        
        $dbPdo->exec("INSERT IGNORE INTO ea8_config (name, value, description, group_name) VALUES 
            ('site_name', 'EasyAdmin8', '网站名称', 'basic'),
            ('site_url', 'http://localhost:8787', '网站地址', 'basic'),
            ('admin_email', '<EMAIL>', '管理员邮箱', 'basic'),
            ('page_size', '20', '每页显示数量', 'system')");
        
        echo "      ✅ 示例数据插入成功\n";
    } else {
        echo "   ✅ 数据库中已有表，跳过创建\n";
        foreach ($tables as $table) {
            echo "      - {$table}\n";
        }
    }
    
} catch (PDOException $e) {
    echo "   ❌ 数据库操作失败: " . $e->getMessage() . "\n";
    exit(1);
}

// 更新配置文件
echo "\n🔧 更新数据库配置文件...\n";

$configContent = "<?php
return [
    'default' => 'mysql',
    'connections' => [
        'mysql' => [
            'driver' => 'mysql',
            'host' => '{$workingConfig['host']}',
            'port' => {$workingConfig['port']},
            'database' => '{$workingConfig['database']}',
            'username' => '{$workingConfig['username']}',
            'password' => '{$workingConfig['password']}',
            'charset' => 'utf8mb4',
            'collation' => 'utf8mb4_general_ci',
            'prefix' => 'ea8_',
            'strict' => false,
            'engine' => null,
            'options' => [
                PDO::ATTR_TIMEOUT => 5,
                PDO::ATTR_CASE => PDO::CASE_NATURAL,
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_ORACLE_NULLS => PDO::NULL_NATURAL,
                PDO::ATTR_STRINGIFY_FETCHES => false,
                PDO::ATTR_EMULATE_PREPARES => false,
            ],
        ],
        'mysql_read' => [
            'driver' => 'mysql',
            'host' => '{$workingConfig['host']}',
            'port' => {$workingConfig['port']},
            'database' => '{$workingConfig['database']}',
            'username' => '{$workingConfig['username']}',
            'password' => '{$workingConfig['password']}',
            'charset' => 'utf8mb4',
            'collation' => 'utf8mb4_general_ci',
            'prefix' => 'ea8_',
            'strict' => false,
            'engine' => null,
        ],
        'mysql_second' => [
            'driver' => 'mysql',
            'host' => '{$workingConfig['host']}',
            'port' => {$workingConfig['port']},
            'database' => '{$workingConfig['database']}',
            'username' => '{$workingConfig['username']}',
            'password' => '{$workingConfig['password']}',
            'charset' => 'utf8mb4',
            'collation' => 'utf8mb4_general_ci',
            'prefix' => 'ea8_',
            'strict' => false,
            'engine' => null,
        ],
        'mysql_log' => [
            'driver' => 'mysql',
            'host' => '{$workingConfig['host']}',
            'port' => {$workingConfig['port']},
            'database' => '{$workingConfig['database']}',
            'username' => '{$workingConfig['username']}',
            'password' => '{$workingConfig['password']}',
            'charset' => 'utf8mb4',
            'collation' => 'utf8mb4_general_ci',
            'prefix' => 'ea8_',
            'strict' => false,
            'engine' => null,
        ],
        'mysql_cache' => [
            'driver' => 'mysql',
            'host' => '{$workingConfig['host']}',
            'port' => {$workingConfig['port']},
            'database' => '{$workingConfig['database']}',
            'username' => '{$workingConfig['username']}',
            'password' => '{$workingConfig['password']}',
            'charset' => 'utf8mb4',
            'collation' => 'utf8mb4_general_ci',
            'prefix' => 'ea8_',
            'strict' => false,
            'engine' => null,
        ],
        'mysql_without_prefix' => [
            'driver' => 'mysql',
            'host' => '{$workingConfig['host']}',
            'port' => {$workingConfig['port']},
            'database' => '{$workingConfig['database']}',
            'username' => '{$workingConfig['username']}',
            'password' => '{$workingConfig['password']}',
            'charset' => 'utf8mb4',
            'collation' => 'utf8mb4_general_ci',
            'prefix' => '',
            'strict' => false,
            'engine' => null,
        ],
        'pgsql' => [
            'driver' => 'pgsql',
            'host' => '127.0.0.1',
            'port' => 5432,
            'database' => 'easyadmin8',
            'username' => 'postgres',
            'password' => 'password',
            'charset' => 'utf8',
            'prefix' => '',
            'schema' => 'public',
            'sslmode' => 'prefer',
        ],
        'sqlite' => [
            'driver' => 'sqlite',
            'database' => database_path() . '/database.sqlite',
            'prefix' => '',
        ],
        'sqlsrv' => [
            'driver' => 'sqlsrv',
            'host' => '127.0.0.1',
            'port' => 1433,
            'database' => 'easyadmin8',
            'username' => 'sa',
            'password' => 'password',
            'charset' => 'utf8',
            'prefix' => '',
        ],
    ],
];
";

// 备份原配置
if (file_exists('config/database.php')) {
    copy('config/database.php', 'config/database.backup.' . date('Y-m-d-H-i-s') . '.php');
    echo "   ✅ 原配置已备份\n";
}

file_put_contents('config/database.php', $configContent);
echo "   ✅ 数据库配置已更新\n";

echo "\n🎉 真实数据库连接设置完成!\n\n";

echo "📋 连接信息:\n";
echo "   主机: {$workingConfig['host']}:{$workingConfig['port']}\n";
echo "   数据库: {$workingConfig['database']}\n";
echo "   用户名: {$workingConfig['username']}\n";
echo "   前缀: ea8_\n";

// 验证最终结果
echo "\n🧪 验证设置结果...\n";
$finalStmt = $dbPdo->query("SHOW TABLES");
$finalTables = $finalStmt->fetchAll(PDO::FETCH_COLUMN);
echo "   📊 最终表数量: " . count($finalTables) . "\n";
foreach ($finalTables as $table) {
    $countStmt = $dbPdo->query("SELECT COUNT(*) FROM {$table}");
    $count = $countStmt->fetchColumn();
    echo "      - {$table}: {$count} 条记录\n";
}

echo "\n🔄 请重启 webman 服务以应用新配置:\n";
echo "   1. 停止当前服务 (Ctrl+C)\n";
echo "   2. 运行: php windows.php\n\n";

echo "🧪 然后测试功能:\n";
echo "   1. 访问: http://localhost:8787/admin/system/curdgeneratev2\n";
echo "   2. 登录后台\n";
echo "   3. 选择数据库连接\n";
echo "   4. 点击刷新表列表 - 应该能看到真实的表数据\n";
echo "   5. 选择表进行分析 - 应该能看到真实的字段信息\n";

echo "\n=== 自动设置完成 ===\n";
?>
