<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EasyAdmin8-webman API文档管理系统演示</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .hero-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 4rem 0;
        }
        .feature-card {
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            transition: transform 0.3s;
            border: none;
            height: 100%;
        }
        .feature-card:hover {
            transform: translateY(-5px);
        }
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 2rem;
            text-align: center;
        }
        .code-block {
            background-color: #2d3748;
            color: #e2e8f0;
            border-radius: 8px;
            padding: 1rem;
            font-family: 'Courier New', monospace;
            font-size: 0.875rem;
            overflow-x: auto;
        }
        .badge-method-get { background: #28a745; }
        .badge-method-post { background: #007bff; }
        .badge-method-put { background: #ffc107; color: #000; }
        .badge-method-delete { background: #dc3545; }
        .screenshot {
            border-radius: 10px;
            box-shadow: 0 8px 30px rgba(0,0,0,0.12);
            max-width: 100%;
            height: auto;
        }
    </style>
</head>
<body>
    <!-- 英雄区域 -->
    <section class="hero-section">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-6">
                    <h1 class="display-4 fw-bold mb-4">
                        <i class="bi bi-api me-3"></i>
                        API文档管理系统
                    </h1>
                    <p class="lead mb-4">
                        为EasyAdmin8-webman框架打造的企业级API文档管理解决方案
                    </p>
                    <div class="d-flex gap-3">
                        <a href="#features" class="btn btn-light btn-lg">
                            <i class="bi bi-eye me-2"></i>查看功能
                        </a>
                        <a href="#demo" class="btn btn-outline-light btn-lg">
                            <i class="bi bi-play-circle me-2"></i>在线演示
                        </a>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="text-center">
                        <i class="bi bi-diagram-3 display-1 opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 统计数据 -->
    <section class="py-5">
        <div class="container">
            <div class="row g-4">
                <div class="col-md-3">
                    <div class="stat-card">
                        <i class="bi bi-file-earmark-code display-4 mb-3"></i>
                        <h3>15+</h3>
                        <p class="mb-0">核心功能</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <i class="bi bi-speedometer2 display-4 mb-3"></i>
                        <h3>100%</h3>
                        <p class="mb-0">测试通过</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <i class="bi bi-code-slash display-4 mb-3"></i>
                        <h3>157KB</h3>
                        <p class="mb-0">代码总量</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <i class="bi bi-star display-4 mb-3"></i>
                        <h3>企业级</h3>
                        <p class="mb-0">专业品质</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 核心功能 -->
    <section id="features" class="py-5 bg-light">
        <div class="container">
            <div class="text-center mb-5">
                <h2 class="display-5 fw-bold">核心功能特性</h2>
                <p class="lead text-muted">完整的API文档管理解决方案</p>
            </div>
            
            <div class="row g-4">
                <div class="col-lg-4 col-md-6">
                    <div class="card feature-card">
                        <div class="card-body p-4">
                            <div class="text-primary mb-3">
                                <i class="bi bi-magic display-4"></i>
                            </div>
                            <h5 class="card-title">自动生成</h5>
                            <p class="card-text">基于数据表结构自动生成完整的RESTful API文档，支持批量生成和自定义配置。</p>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-4 col-md-6">
                    <div class="card feature-card">
                        <div class="card-body p-4">
                            <div class="text-success mb-3">
                                <i class="bi bi-eye display-4"></i>
                            </div>
                            <h5 class="card-title">美观展示</h5>
                            <p class="card-text">现代化的Bootstrap 5界面设计，响应式布局，支持桌面和移动设备访问。</p>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-4 col-md-6">
                    <div class="card feature-card">
                        <div class="card-body p-4">
                            <div class="text-info mb-3">
                                <i class="bi bi-play-circle display-4"></i>
                            </div>
                            <h5 class="card-title">在线测试</h5>
                            <p class="card-text">内置API接口在线测试功能，支持参数验证、结果展示和测试历史记录。</p>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-4 col-md-6">
                    <div class="card feature-card">
                        <div class="card-body p-4">
                            <div class="text-warning mb-3">
                                <i class="bi bi-download display-4"></i>
                            </div>
                            <h5 class="card-title">多格式导出</h5>
                            <p class="card-text">支持HTML、JSON、Markdown、Postman、Swagger等多种格式导出。</p>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-4 col-md-6">
                    <div class="card feature-card">
                        <div class="card-body p-4">
                            <div class="text-danger mb-3">
                                <i class="bi bi-graph-up display-4"></i>
                            </div>
                            <h5 class="card-title">数据分析</h5>
                            <p class="card-text">完整的使用统计、性能分析和趋势预测，提供详细的数据仪表板。</p>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-4 col-md-6">
                    <div class="card feature-card">
                        <div class="card-body p-4">
                            <div class="text-purple mb-3">
                                <i class="bi bi-search display-4"></i>
                            </div>
                            <h5 class="card-title">高级搜索</h5>
                            <p class="card-text">多维度搜索和筛选功能，支持表名、接口、描述等多种搜索方式。</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- API示例 -->
    <section class="py-5">
        <div class="container">
            <div class="text-center mb-5">
                <h2 class="display-5 fw-bold">API文档示例</h2>
                <p class="lead text-muted">自动生成的API接口文档展示</p>
            </div>
            
            <div class="row">
                <div class="col-lg-6">
                    <h4 class="mb-3">用户管理 API</h4>
                    <div class="mb-3">
                        <span class="badge badge-method-get me-2">GET</span>
                        <code>/api/users</code>
                        <p class="mt-2 text-muted">获取用户列表，支持分页和搜索</p>
                    </div>
                    <div class="mb-3">
                        <span class="badge badge-method-get me-2">GET</span>
                        <code>/api/users/{id}</code>
                        <p class="mt-2 text-muted">根据ID获取用户详情</p>
                    </div>
                    <div class="mb-3">
                        <span class="badge badge-method-post me-2">POST</span>
                        <code>/api/users</code>
                        <p class="mt-2 text-muted">创建新用户</p>
                    </div>
                    <div class="mb-3">
                        <span class="badge badge-method-put me-2">PUT</span>
                        <code>/api/users/{id}</code>
                        <p class="mt-2 text-muted">更新用户信息</p>
                    </div>
                    <div class="mb-3">
                        <span class="badge badge-method-delete me-2">DELETE</span>
                        <code>/api/users/{id}</code>
                        <p class="mt-2 text-muted">删除用户</p>
                    </div>
                </div>
                <div class="col-lg-6">
                    <h4 class="mb-3">响应示例</h4>
                    <div class="code-block">
{
  "code": 200,
  "msg": "success",
  "data": {
    "id": 1,
    "name": "张三",
    "email": "<EMAIL>",
    "status": 1,
    "created_at": "2025-01-20 10:30:00",
    "updated_at": "2025-01-20 10:30:00"
  }
}
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 技术特色 -->
    <section class="py-5 bg-light">
        <div class="container">
            <div class="text-center mb-5">
                <h2 class="display-5 fw-bold">技术特色</h2>
                <p class="lead text-muted">现代化的技术架构和设计理念</p>
            </div>
            
            <div class="row g-4">
                <div class="col-lg-3 col-md-6">
                    <div class="text-center">
                        <i class="bi bi-bootstrap text-primary display-4 mb-3"></i>
                        <h5>Bootstrap 5</h5>
                        <p class="text-muted">现代化CSS框架</p>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="text-center">
                        <i class="bi bi-code-square text-success display-4 mb-3"></i>
                        <h5>MVC架构</h5>
                        <p class="text-muted">清晰的代码结构</p>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="text-center">
                        <i class="bi bi-phone text-info display-4 mb-3"></i>
                        <h5>响应式设计</h5>
                        <p class="text-muted">完美移动适配</p>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="text-center">
                        <i class="bi bi-lightning text-warning display-4 mb-3"></i>
                        <h5>高性能</h5>
                        <p class="text-muted">优化的性能表现</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 在线演示 -->
    <section id="demo" class="py-5">
        <div class="container">
            <div class="text-center mb-5">
                <h2 class="display-5 fw-bold">功能演示</h2>
                <p class="lead text-muted">体验完整的API文档管理功能</p>
            </div>
            
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-body p-4">
                            <h5 class="card-title mb-4">
                                <i class="bi bi-play-circle text-primary me-2"></i>
                                功能演示链接
                            </h5>
                            
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <a href="http://localhost:8787/admin/system/apidoc" class="btn btn-primary w-100" target="_blank">
                                        <i class="bi bi-star me-2"></i>增强版首页
                                    </a>
                                </div>
                                <div class="col-md-6">
                                    <a href="http://localhost:8787/admin/system/apidoc-simple" class="btn btn-outline-primary w-100" target="_blank">
                                        <i class="bi bi-gear me-2"></i>简化版首页
                                    </a>
                                </div>
                                <div class="col-md-6">
                                    <a href="http://localhost:8787/admin/system/apidoc/dashboard" class="btn btn-success w-100" target="_blank">
                                        <i class="bi bi-graph-up me-2"></i>数据仪表板
                                    </a>
                                </div>
                                <div class="col-md-6">
                                    <a href="http://localhost:8787/admin/system/apidoc/analytics" class="btn btn-info w-100" target="_blank">
                                        <i class="bi bi-bar-chart me-2"></i>数据分析
                                    </a>
                                </div>
                            </div>
                            
                            <div class="alert alert-info mt-4">
                                <i class="bi bi-info-circle me-2"></i>
                                <strong>注意：</strong>请确保Webman服务器已启动 (php windows.php)
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 安装指南 -->
    <section class="py-5 bg-light">
        <div class="container">
            <div class="text-center mb-5">
                <h2 class="display-5 fw-bold">快速开始</h2>
                <p class="lead text-muted">简单几步即可开始使用</p>
            </div>
            
            <div class="row">
                <div class="col-lg-6">
                    <h4 class="mb-3">1. 启动服务器</h4>
                    <div class="code-block mb-4">
cd EasyAdmin8-webman/EasyAdmin8
php windows.php  # Windows
php start.php start  # Linux/Mac
                    </div>
                    
                    <h4 class="mb-3">3. 生成API文档</h4>
                    <ul class="list-unstyled">
                        <li class="mb-2"><i class="bi bi-check-circle text-success me-2"></i>选择数据表</li>
                        <li class="mb-2"><i class="bi bi-check-circle text-success me-2"></i>配置生成选项</li>
                        <li class="mb-2"><i class="bi bi-check-circle text-success me-2"></i>点击生成按钮</li>
                        <li class="mb-2"><i class="bi bi-check-circle text-success me-2"></i>查看和测试API</li>
                    </ul>
                </div>
                <div class="col-lg-6">
                    <h4 class="mb-3">2. 访问系统</h4>
                    <div class="code-block mb-4">
http://localhost:8787/admin/system/apidoc
                    </div>
                    
                    <h4 class="mb-3">4. 导出文档</h4>
                    <ul class="list-unstyled">
                        <li class="mb-2"><i class="bi bi-file-earmark-text text-primary me-2"></i>HTML格式</li>
                        <li class="mb-2"><i class="bi bi-file-earmark-code text-success me-2"></i>JSON格式</li>
                        <li class="mb-2"><i class="bi bi-file-earmark-richtext text-info me-2"></i>Markdown格式</li>
                        <li class="mb-2"><i class="bi bi-file-earmark-arrow-down text-warning me-2"></i>Postman集合</li>
                        <li class="mb-2"><i class="bi bi-file-earmark-medical text-danger me-2"></i>Swagger文档</li>
                    </ul>
                </div>
            </div>
        </div>
    </section>

    <!-- 页脚 -->
    <footer class="bg-dark text-light py-4">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h5 class="mb-0">
                        <i class="bi bi-api me-2"></i>
                        EasyAdmin8-webman API文档管理系统
                    </h5>
                    <p class="text-muted mb-0">企业级API文档管理解决方案</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="mb-0">
                        <i class="bi bi-code-slash me-2"></i>
                        基于 EasyAdmin8-webman 框架开发
                    </p>
                    <p class="text-muted mb-0">版本 2.0.0 | 2025年1月</p>
                </div>
            </div>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 检查链接可用性
        document.addEventListener('DOMContentLoaded', function() {
            const links = document.querySelectorAll('a[href^="http://localhost:8787"]');
            
            links.forEach(link => {
                link.addEventListener('click', function(e) {
                    // 可以在这里添加链接检查逻辑
                    console.log('访问:', this.href);
                });
            });
        });
    </script>
</body>
</html>
