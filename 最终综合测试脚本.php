<?php
/**
 * EasyAdmin8-webman API文档管理系统 - 最终综合测试脚本
 * 全面测试系统的各项功能和性能
 */

echo "=== EasyAdmin8-webman API文档管理系统 - 最终综合测试 ===\n\n";

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 测试配置
$testConfig = [
    'base_url' => 'http://localhost:8787',
    'timeout' => 10,
    'test_table' => 'users',
    'verbose' => true
];

/**
 * 输出测试结果
 */
function outputResult($test, $result, $details = '') {
    $status = $result ? '✅' : '❌';
    echo "   {$status} {$test}";
    if ($details) {
        echo " - {$details}";
    }
    echo "\n";
    return $result;
}

/**
 * 发送HTTP请求
 */
function sendHttpRequest($url, $method = 'GET', $data = null, $timeout = 10) {
    $ch = curl_init();
    
    curl_setopt_array($ch, [
        CURLOPT_URL => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_TIMEOUT => $timeout,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_USERAGENT => 'API-Doc-Test/1.0',
        CURLOPT_HEADER => true,
    ]);
    
    if ($method === 'POST') {
        curl_setopt($ch, CURLOPT_POST, true);
        if ($data) {
            curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
        }
    }
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    $headerSize = curl_getinfo($ch, CURLINFO_HEADER_SIZE);
    
    curl_close($ch);
    
    if ($error) {
        return ['success' => false, 'error' => $error, 'http_code' => 0];
    }
    
    $headers = substr($response, 0, $headerSize);
    $body = substr($response, $headerSize);
    
    return [
        'success' => true,
        'http_code' => $httpCode,
        'headers' => $headers,
        'body' => $body,
        'size' => strlen($body)
    ];
}

/**
 * 测试1: 文件完整性检查
 */
function testFileIntegrity() {
    echo "1. 文件完整性检查\n";
    
    $requiredFiles = [
        'app/admin/controller/system/ApiDocControllerSimple.php' => '简化版控制器',
        'app/admin/controller/system/ApiDocControllerEnhanced.php' => '增强版控制器',
        'config/api_doc.php' => 'API文档配置',
        'config/route.php' => '路由配置',
        'public/static/admin/css/api-doc.css' => 'CSS样式文件',
        'public/static/admin/js/api-doc-manager.js' => 'JavaScript管理器'
    ];
    
    $passed = 0;
    $total = count($requiredFiles);
    
    foreach ($requiredFiles as $file => $desc) {
        $exists = file_exists($file);
        outputResult($desc, $exists, $exists ? filesize($file) . ' bytes' : '文件不存在');
        if ($exists) $passed++;
    }
    
    echo "   📊 文件完整性: {$passed}/{$total} (" . round(($passed/$total)*100, 1) . "%)\n\n";
    return $passed === $total;
}

/**
 * 测试2: PHP语法检查
 */
function testPhpSyntax() {
    echo "2. PHP语法检查\n";
    
    $phpFiles = [
        'app/admin/controller/system/ApiDocControllerSimple.php',
        'app/admin/controller/system/ApiDocControllerEnhanced.php',
        'config/api_doc.php',
        'config/route.php'
    ];
    
    $passed = 0;
    $total = count($phpFiles);
    
    foreach ($phpFiles as $file) {
        if (file_exists($file)) {
            $output = [];
            $returnCode = 0;
            exec("php -l \"{$file}\" 2>&1", $output, $returnCode);
            
            $success = $returnCode === 0;
            outputResult(basename($file), $success, $success ? '语法正确' : '语法错误');
            if ($success) $passed++;
        } else {
            outputResult(basename($file), false, '文件不存在');
        }
    }
    
    echo "   📊 语法检查: {$passed}/{$total} (" . round(($passed/$total)*100, 1) . "%)\n\n";
    return $passed === $total;
}

/**
 * 测试3: 控制器功能测试
 */
function testControllerFunctions() {
    echo "3. 控制器功能测试\n";
    
    // 模拟必要的类和函数
    if (!function_exists('response')) {
        function response($content, $status = 200, $headers = []) {
            return new MockResponse($content, $status, $headers);
        }
    }
    
    if (!class_exists('MockResponse')) {
        class MockResponse {
            public $content, $status, $headers;
            public function __construct($content, $status = 200, $headers = []) {
                $this->content = $content;
                $this->status = $status;
                $this->headers = $headers;
            }
        }
    }
    
    if (!class_exists('MockRequest')) {
        class MockRequest {
            private $data = [];
            public function get($key, $default = null) { return $this->data[$key] ?? $default; }
            public function post($key, $default = null) { return $this->data[$key] ?? $default; }
            public function setData($data) { $this->data = $data; }
        }
    }
    
    if (!class_exists('support\Request')) {
        class_alias('MockRequest', 'support\Request');
    }
    if (!class_exists('support\Response')) {
        class_alias('MockResponse', 'support\Response');
    }
    
    $passed = 0;
    $total = 0;
    
    // 测试简化版控制器
    try {
        require_once 'app/admin/controller/system/ApiDocControllerSimple.php';
        $controller = new \app\admin\controller\system\ApiDocControllerSimple();
        $request = new MockRequest();
        
        $methods = [
            'getApiDocList' => '获取API文档列表',
            'getTableList' => '获取表列表'
        ];
        
        foreach ($methods as $method => $desc) {
            $total++;
            try {
                $response = $controller->$method($request);
                $success = $response instanceof MockResponse;
                outputResult($desc, $success);
                if ($success) $passed++;
            } catch (Exception $e) {
                outputResult($desc, false, $e->getMessage());
            }
        }
        
    } catch (Exception $e) {
        outputResult('简化版控制器加载', false, $e->getMessage());
        $total++;
    }
    
    // 测试增强版控制器
    try {
        require_once 'app/admin/controller/system/ApiDocControllerEnhanced.php';
        $controller = new \app\admin\controller\system\ApiDocControllerEnhanced();
        $request = new MockRequest();
        
        $methods = [
            'dashboard' => '仪表板功能',
            'analytics' => '分析功能'
        ];
        
        foreach ($methods as $method => $desc) {
            $total++;
            try {
                $response = $controller->$method($request);
                $success = $response instanceof MockResponse;
                outputResult($desc, $success);
                if ($success) $passed++;
            } catch (Exception $e) {
                outputResult($desc, false, $e->getMessage());
            }
        }
        
    } catch (Exception $e) {
        outputResult('增强版控制器加载', false, $e->getMessage());
        $total++;
    }
    
    echo "   📊 控制器测试: {$passed}/{$total} (" . round(($passed/$total)*100, 1) . "%)\n\n";
    return $passed === $total;
}

/**
 * 测试4: 服务器连接测试
 */
function testServerConnection($config) {
    echo "4. 服务器连接测试\n";
    
    $passed = 0;
    $total = 3;
    
    // 测试基础连接
    $response = sendHttpRequest($config['base_url'], 'GET', null, $config['timeout']);
    $success = $response['success'] && $response['http_code'] < 500;
    outputResult('基础服务器连接', $success, 
        $response['success'] ? "HTTP {$response['http_code']}" : $response['error']);
    if ($success) $passed++;
    
    // 测试API文档路由
    $response = sendHttpRequest($config['base_url'] . '/admin/system/apidoc', 'GET', null, $config['timeout']);
    $success = $response['success'] && $response['http_code'] < 500;
    outputResult('API文档路由', $success, 
        $response['success'] ? "HTTP {$response['http_code']}" : $response['error']);
    if ($success) $passed++;
    
    // 测试JSON API
    $response = sendHttpRequest($config['base_url'] . '/admin/system/apidoc/list', 'GET', null, $config['timeout']);
    $success = $response['success'] && $response['http_code'] < 500;
    outputResult('JSON API接口', $success, 
        $response['success'] ? "HTTP {$response['http_code']}" : $response['error']);
    if ($success) $passed++;
    
    echo "   📊 服务器测试: {$passed}/{$total} (" . round(($passed/$total)*100, 1) . "%)\n\n";
    return $passed >= 1; // 至少有一个连接成功
}

/**
 * 测试5: 配置文件验证
 */
function testConfigFiles() {
    echo "5. 配置文件验证\n";
    
    $passed = 0;
    $total = 2;
    
    // 测试API文档配置
    try {
        $config = include 'config/api_doc.php';
        $success = is_array($config) && isset($config['basic']);
        outputResult('API文档配置', $success, $success ? '配置格式正确' : '配置格式错误');
        if ($success) $passed++;
    } catch (Exception $e) {
        outputResult('API文档配置', false, $e->getMessage());
    }
    $total++;
    
    // 测试路由配置
    try {
        $routeContent = file_get_contents('config/route.php');
        $success = strpos($routeContent, 'ApiDocControllerSimple') !== false;
        outputResult('路由配置', $success, $success ? '路由配置正确' : '路由配置错误');
        if ($success) $passed++;
    } catch (Exception $e) {
        outputResult('路由配置', false, $e->getMessage());
    }
    
    echo "   📊 配置验证: {$passed}/{$total} (" . round(($passed/$total)*100, 1) . "%)\n\n";
    return $passed === $total;
}

/**
 * 测试6: 性能基准测试
 */
function testPerformance($config) {
    echo "6. 性能基准测试\n";
    
    $testUrl = $config['base_url'] . '/admin/system/apidoc/list';
    $testCount = 3;
    $times = [];
    
    for ($i = 1; $i <= $testCount; $i++) {
        $startTime = microtime(true);
        $response = sendHttpRequest($testUrl, 'GET', null, $config['timeout']);
        $endTime = microtime(true);
        
        $responseTime = ($endTime - $startTime) * 1000;
        $times[] = $responseTime;
        
        $success = $response['success'] && $response['http_code'] === 200;
        outputResult("性能测试 {$i}", $success, 
            $success ? number_format($responseTime, 2) . 'ms' : '请求失败');
    }
    
    if (!empty($times)) {
        $avgTime = array_sum($times) / count($times);
        $minTime = min($times);
        $maxTime = max($times);
        
        echo "   📊 性能统计:\n";
        echo "      - 平均响应时间: " . number_format($avgTime, 2) . "ms\n";
        echo "      - 最快响应时间: " . number_format($minTime, 2) . "ms\n";
        echo "      - 最慢响应时间: " . number_format($maxTime, 2) . "ms\n";
        
        $performance = $avgTime < 1000 ? 'excellent' : ($avgTime < 3000 ? 'good' : 'poor');
        echo "      - 性能评级: " . ($performance === 'excellent' ? '优秀' : 
            ($performance === 'good' ? '良好' : '需要优化')) . "\n";
    }
    
    echo "\n";
    return !empty($times);
}

/**
 * 生成测试报告
 */
function generateTestReport($results) {
    echo "=== 最终测试报告 ===\n";
    
    $testNames = [
        'file_integrity' => '文件完整性',
        'php_syntax' => 'PHP语法检查',
        'controller_functions' => '控制器功能',
        'server_connection' => '服务器连接',
        'config_files' => '配置文件',
        'performance' => '性能测试'
    ];
    
    $passed = 0;
    $total = count($results);
    
    echo "📊 测试结果详情:\n";
    foreach ($results as $test => $result) {
        $status = $result ? '✅ 通过' : '❌ 失败';
        $name = $testNames[$test] ?? $test;
        echo "   - {$name}: {$status}\n";
        if ($result) $passed++;
    }
    
    $successRate = ($passed / $total) * 100;
    
    echo "\n📈 总体测试结果:\n";
    echo "   - 通过率: " . number_format($successRate, 1) . "%\n";
    echo "   - 通过项: {$passed}/{$total}\n";
    
    if ($successRate >= 100) {
        echo "   🎉 所有测试通过！系统运行完全正常！\n";
        $grade = "A+";
    } elseif ($successRate >= 80) {
        echo "   ✅ 大部分测试通过，系统基本正常\n";
        $grade = "A";
    } elseif ($successRate >= 60) {
        echo "   ⚠️  部分测试通过，系统存在一些问题\n";
        $grade = "B";
    } else {
        echo "   ❌ 多数测试失败，系统存在严重问题\n";
        $grade = "C";
    }
    
    echo "   - 系统评级: {$grade}\n";
    
    echo "\n🔗 可用访问地址:\n";
    echo "   - 简化版首页: http://localhost:8787/admin/system/apidoc\n";
    echo "   - 增强版首页: http://localhost:8787/admin/system/apidoc-enhanced\n";
    echo "   - 备用简化版: http://localhost:8787/admin/system/apidoc-simple\n";
    
    echo "\n💡 使用建议:\n";
    if ($results['file_integrity']) {
        echo "   ✅ 文件完整，可以正常使用\n";
    }
    if ($results['controller_functions']) {
        echo "   ✅ 控制器功能正常，核心功能可用\n";
    }
    if ($results['server_connection']) {
        echo "   ✅ 服务器连接正常，可以通过Web访问\n";
    } else {
        echo "   ⚠️  服务器连接异常，请检查Webman服务器状态\n";
        echo "   🔧 启动命令: php windows.php (Windows) 或 php start.php start (Linux/Mac)\n";
    }
    
    return $successRate;
}

// 执行测试
try {
    echo "开始执行EasyAdmin8-webman API文档管理系统综合测试...\n\n";
    
    $results = [];
    $results['file_integrity'] = testFileIntegrity();
    $results['php_syntax'] = testPhpSyntax();
    $results['controller_functions'] = testControllerFunctions();
    $results['server_connection'] = testServerConnection($testConfig);
    $results['config_files'] = testConfigFiles();
    $results['performance'] = testPerformance($testConfig);
    
    $successRate = generateTestReport($results);
    
    echo "\n=== 测试完成 ===\n";
    
    if ($successRate >= 80) {
        echo "🎊 恭喜！EasyAdmin8-webman API文档管理系统测试通过！\n";
        echo "🚀 系统已准备就绪，可以正常使用！\n";
    } else {
        echo "⚠️  系统存在一些问题，建议检查失败的测试项\n";
        echo "🔧 请参考测试报告进行相应的修复\n";
    }
    
} catch (Exception $e) {
    echo "❌ 测试过程中发生错误: " . $e->getMessage() . "\n";
    echo "📍 错误位置: " . $e->getFile() . ":" . $e->getLine() . "\n";
} catch (Error $e) {
    echo "❌ 测试过程中发生致命错误: " . $e->getMessage() . "\n";
    echo "📍 错误位置: " . $e->getFile() . ":" . $e->getLine() . "\n";
}

echo "\n🎯 EasyAdmin8-webman API文档管理系统综合测试完成！\n";
