/**
 * CURD 代码优化器
 * 用于优化生成的代码质量和性能
 */

layui.use(['layer'], function() {
    var layer = layui.layer;

    // 代码优化器类
    var CodeOptimizer = {
        // 优化规则配置
        rules: {
            // PHP代码优化规则
            php: {
                removeUnusedImports: true,
                addTypeHints: true,
                optimizeQueries: true,
                addDocBlocks: true,
                formatCode: true,
                addValidation: true
            },
            // JavaScript代码优化规则
            javascript: {
                removeConsoleLog: true,
                addErrorHandling: true,
                optimizeSelectors: true,
                addComments: true,
                formatCode: true
            },
            // HTML代码优化规则
            html: {
                removeEmptyLines: true,
                optimizeAttributes: true,
                addSemantic: true,
                formatCode: true
            },
            // CSS代码优化规则
            css: {
                removeUnusedRules: true,
                optimizeSelectors: true,
                addPrefixes: true,
                formatCode: true
            }
        },

        // 初始化
        init: function() {
            this.bindEvents();
        },

        // 绑定事件
        bindEvents: function() {
            var self = this;

            // 代码优化按钮
            $(document).on('click', '#optimize-code-btn', function() {
                self.openOptimizer();
            });

            // 优化单个文件
            $(document).on('click', '.optimize-file-btn', function() {
                var fileType = $(this).data('file-type');
                self.optimizeFile(fileType);
            });

            // 批量优化
            $(document).on('click', '#batch-optimize-btn', function() {
                self.batchOptimize();
            });

            // 优化规则设置
            $(document).on('click', '#optimizer-settings-btn', function() {
                self.openSettings();
            });

            // 应用优化
            $(document).on('click', '#apply-optimization-btn', function() {
                self.applyOptimization();
            });
        },

        // 打开优化器
        openOptimizer: function() {
            var self = this;
            var content = this.buildOptimizerHTML();

            layer.open({
                type: 1,
                title: '代码优化器',
                content: content,
                area: ['900px', '600px'],
                btn: ['关闭'],
                success: function() {
                    self.loadOptimizationReport();
                }
            });
        },

        // 构建优化器HTML
        buildOptimizerHTML: function() {
            return `
                <div class="code-optimizer">
                    <div class="optimizer-toolbar">
                        <button class="layui-btn layui-btn-sm" id="batch-optimize-btn">
                            <i class="layui-icon layui-icon-ok"></i> 批量优化
                        </button>
                        <button class="layui-btn layui-btn-sm layui-btn-primary" id="optimizer-settings-btn">
                            <i class="layui-icon layui-icon-set"></i> 优化设置
                        </button>
                        <button class="layui-btn layui-btn-sm layui-btn-normal" id="apply-optimization-btn">
                            <i class="layui-icon layui-icon-ok-circle"></i> 应用优化
                        </button>
                    </div>
                    
                    <div class="optimizer-content">
                        <div class="optimization-report">
                            <h4>优化报告</h4>
                            <div id="optimization-report-content">
                                <div class="report-loading">正在分析代码...</div>
                            </div>
                        </div>
                        
                        <div class="file-optimization-list">
                            <h4>文件优化列表</h4>
                            <div id="file-optimization-list-content"></div>
                        </div>
                    </div>
                </div>
            `;
        },

        // 加载优化报告
        loadOptimizationReport: function() {
            var self = this;
            
            setTimeout(function() {
                var report = self.analyzeCode();
                self.displayOptimizationReport(report);
                self.displayFileOptimizationList();
            }, 1000);
        },

        // 分析代码
        analyzeCode: function() {
            var report = {
                totalFiles: 0,
                totalIssues: 0,
                categories: {
                    performance: 0,
                    maintainability: 0,
                    security: 0,
                    style: 0
                },
                files: {}
            };

            // 分析每个文件
            var fileTypes = ['controller', 'model', 'view', 'js', 'css'];
            
            fileTypes.forEach(function(fileType) {
                var analysis = this.analyzeFileType(fileType);
                if (analysis) {
                    report.totalFiles++;
                    report.totalIssues += analysis.issues.length;
                    report.files[fileType] = analysis;
                    
                    // 统计问题类别
                    analysis.issues.forEach(function(issue) {
                        if (report.categories[issue.category] !== undefined) {
                            report.categories[issue.category]++;
                        }
                    });
                }
            }.bind(this));

            return report;
        },

        // 分析文件类型
        analyzeFileType: function(fileType) {
            // 这里应该获取实际的代码内容进行分析
            // 为了演示，我们返回模拟的分析结果
            
            var issues = [];
            
            switch (fileType) {
                case 'controller':
                    issues = [
                        {
                            type: 'missing_type_hint',
                            category: 'maintainability',
                            severity: 'medium',
                            message: '缺少参数类型提示',
                            line: 15,
                            suggestion: '添加 Request $request 类型提示'
                        },
                        {
                            type: 'missing_validation',
                            category: 'security',
                            severity: 'high',
                            message: '缺少输入验证',
                            line: 25,
                            suggestion: '添加数据验证规则'
                        }
                    ];
                    break;
                    
                case 'model':
                    issues = [
                        {
                            type: 'missing_fillable',
                            category: 'security',
                            severity: 'high',
                            message: '未定义 fillable 属性',
                            line: 8,
                            suggestion: '定义可批量赋值的字段'
                        }
                    ];
                    break;
                    
                case 'js':
                    issues = [
                        {
                            type: 'missing_error_handling',
                            category: 'maintainability',
                            severity: 'medium',
                            message: '缺少错误处理',
                            line: 20,
                            suggestion: '添加 .fail() 错误处理'
                        }
                    ];
                    break;
            }

            return {
                fileType: fileType,
                issues: issues,
                score: Math.max(0, 100 - issues.length * 10)
            };
        },

        // 显示优化报告
        displayOptimizationReport: function(report) {
            var html = `
                <div class="report-summary">
                    <div class="summary-item">
                        <h5>总文件数</h5>
                        <span class="summary-value">${report.totalFiles}</span>
                    </div>
                    <div class="summary-item">
                        <h5>发现问题</h5>
                        <span class="summary-value ${report.totalIssues > 0 ? 'warning' : 'success'}">${report.totalIssues}</span>
                    </div>
                    <div class="summary-item">
                        <h5>代码质量</h5>
                        <span class="summary-value ${this.getQualityClass(report)}">${this.calculateQuality(report)}%</span>
                    </div>
                </div>
                
                <div class="issue-categories">
                    <h5>问题分类</h5>
                    <div class="category-list">
                        <div class="category-item">
                            <span class="category-name">性能问题</span>
                            <span class="category-count">${report.categories.performance}</span>
                        </div>
                        <div class="category-item">
                            <span class="category-name">可维护性</span>
                            <span class="category-count">${report.categories.maintainability}</span>
                        </div>
                        <div class="category-item">
                            <span class="category-name">安全问题</span>
                            <span class="category-count">${report.categories.security}</span>
                        </div>
                        <div class="category-item">
                            <span class="category-name">代码风格</span>
                            <span class="category-count">${report.categories.style}</span>
                        </div>
                    </div>
                </div>
            `;

            $('#optimization-report-content').html(html);
        },

        // 显示文件优化列表
        displayFileOptimizationList: function() {
            var self = this;
            var html = '';

            var fileTypes = ['controller', 'model', 'view', 'js', 'css'];
            
            fileTypes.forEach(function(fileType) {
                var analysis = self.analyzeFileType(fileType);
                if (analysis) {
                    html += `
                        <div class="file-optimization-item">
                            <div class="file-header">
                                <h5>${self.getFileTypeName(fileType)}</h5>
                                <div class="file-score score-${self.getScoreClass(analysis.score)}">${analysis.score}分</div>
                                <button class="layui-btn layui-btn-xs optimize-file-btn" data-file-type="${fileType}">优化</button>
                            </div>
                            <div class="file-issues">
                                ${self.renderIssues(analysis.issues)}
                            </div>
                        </div>
                    `;
                }
            });

            $('#file-optimization-list-content').html(html);
        },

        // 渲染问题列表
        renderIssues: function(issues) {
            if (issues.length === 0) {
                return '<div class="no-issues">✅ 未发现问题</div>';
            }

            var html = '<div class="issues-list">';
            issues.forEach(function(issue) {
                html += `
                    <div class="issue-item severity-${issue.severity}">
                        <div class="issue-info">
                            <span class="issue-message">${issue.message}</span>
                            <span class="issue-line">第${issue.line}行</span>
                        </div>
                        <div class="issue-suggestion">${issue.suggestion}</div>
                    </div>
                `;
            });
            html += '</div>';

            return html;
        },

        // 优化单个文件
        optimizeFile: function(fileType) {
            var self = this;
            
            layer.msg('正在优化 ' + this.getFileTypeName(fileType) + '...', {icon: 16, time: 0});
            
            setTimeout(function() {
                // 执行优化逻辑
                var optimizedCode = self.performOptimization(fileType);
                
                // 更新代码预览
                if (window.CurdCodePreview && optimizedCode) {
                    window.CurdCodePreview.updateCode(fileType, optimizedCode);
                }
                
                layer.closeAll('loading');
                layer.msg('优化完成', {icon: 1});
                
                // 重新加载优化报告
                self.loadOptimizationReport();
            }, 1500);
        },

        // 执行优化
        performOptimization: function(fileType) {
            // 这里应该实现实际的代码优化逻辑
            // 为了演示，我们返回一个优化后的示例
            
            switch (fileType) {
                case 'controller':
                    return this.optimizeControllerCode();
                case 'model':
                    return this.optimizeModelCode();
                case 'js':
                    return this.optimizeJavaScriptCode();
                default:
                    return null;
            }
        },

        // 优化控制器代码
        optimizeControllerCode: function() {
            return `<?php

namespace app\\admin\\controller;

use support\\Request;
use support\\Response;
use app\\common\\model\\Example;

/**
 * 示例控制器
 * 已优化：添加了类型提示、验证和错误处理
 */
class ExampleController
{
    /**
     * 列表页面
     * @param Request $request
     * @return Response
     */
    public function index(Request $request): Response
    {
        try {
            if ($request->method() === 'POST') {
                return $this->getList($request);
            }
            
            return view('admin/example/index');
        } catch (\\Exception $e) {
            return json(['code' => 0, 'msg' => '操作失败: ' . $e->getMessage()]);
        }
    }

    /**
     * 获取列表数据
     * @param Request $request
     * @return Response
     */
    private function getList(Request $request): Response
    {
        $page = (int) $request->input('page', 1);
        $limit = (int) $request->input('limit', 15);
        
        // 数据验证
        if ($page < 1 || $limit < 1 || $limit > 100) {
            return json(['code' => 0, 'msg' => '参数错误']);
        }
        
        $query = Example::query();
        
        // 搜索条件
        if ($request->input('name')) {
            $query->where('name', 'like', '%' . $request->input('name') . '%');
        }
        
        $total = $query->count();
        $list = $query->offset(($page - 1) * $limit)
                     ->limit($limit)
                     ->orderBy('id', 'desc')
                     ->get();
        
        return json([
            'code' => 1,
            'msg' => '获取成功',
            'count' => $total,
            'data' => $list
        ]);
    }
}`;
        },

        // 优化模型代码
        optimizeModelCode: function() {
            return `<?php

namespace app\\common\\model;

use support\\Model;

/**
 * 示例模型
 * 已优化：添加了 fillable、casts 和访问器
 */
class Example extends Model
{
    protected $table = 'example';
    
    /**
     * 可批量赋值的属性
     */
    protected $fillable = [
        'name',
        'email',
        'status',
        'description'
    ];

    /**
     * 属性类型转换
     */
    protected $casts = [
        'status' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    /**
     * 隐藏的属性
     */
    protected $hidden = [
        'password',
        'deleted_at'
    ];

    /**
     * 获取格式化的状态
     */
    public function getStatusTextAttribute(): string
    {
        return $this->status === 1 ? '启用' : '禁用';
    }

    /**
     * 获取格式化的创建时间
     */
    public function getCreatedAtFormattedAttribute(): string
    {
        return $this->created_at ? $this->created_at->format('Y-m-d H:i:s') : '';
    }
}`;
        },

        // 优化JavaScript代码
        optimizeJavaScriptCode: function() {
            return `layui.use(['table', 'form', 'layer'], function() {
    var table = layui.table;
    var form = layui.form;
    var layer = layui.layer;

    // 配置
    var config = {
        tableId: 'data-table',
        apiUrl: '/admin/example'
    };

    /**
     * 初始化
     */
    function init() {
        renderTable();
        bindEvents();
    }

    /**
     * 渲染表格
     */
    function renderTable() {
        table.render({
            elem: '#' + config.tableId,
            url: config.apiUrl,
            method: 'POST',
            page: true,
            limit: 15,
            limits: [15, 30, 50, 100],
            cols: [[
                {type: 'checkbox', fixed: 'left'},
                {field: 'id', title: 'ID', width: 80, sort: true},
                {field: 'name', title: '名称', width: 150},
                {field: 'email', title: '邮箱', width: 200},
                {field: 'status_text', title: '状态', width: 100},
                {field: 'created_at_formatted', title: '创建时间', width: 160},
                {title: '操作', width: 150, align: 'center', toolbar: '#toolbar'}
            ]],
            done: function(res, curr, count) {
                // 表格渲染完成回调
                console.log('表格渲染完成，共' + count + '条数据');
            }
        });
    }

    /**
     * 绑定事件
     */
    function bindEvents() {
        // 搜索表单提交
        form.on('submit(searchForm)', function(data) {
            table.reload(config.tableId, {
                where: data.field,
                page: {curr: 1}
            });
            return false;
        });

        // 添加按钮
        $('#add-btn').on('click', function() {
            openDialog('添加', config.apiUrl + '/add');
        });

        // 删除按钮
        $('#delete-btn').on('click', function() {
            var checkStatus = table.checkStatus(config.tableId);
            var data = checkStatus.data;
            
            if (data.length === 0) {
                layer.msg('请选择要删除的数据', {icon: 2});
                return;
            }
            
            confirmDelete(data);
        });
    }

    /**
     * 打开对话框
     */
    function openDialog(title, url, area) {
        area = area || ['800px', '600px'];
        
        layer.open({
            type: 2,
            title: title,
            area: area,
            content: url,
            success: function(layero, index) {
                // 对话框打开成功回调
            },
            end: function() {
                // 刷新表格
                table.reload(config.tableId);
            }
        });
    }

    /**
     * 确认删除
     */
    function confirmDelete(data) {
        layer.confirm('确定删除选中的 ' + data.length + ' 条数据吗？', {
            icon: 3,
            title: '确认删除'
        }, function(index) {
            var ids = data.map(function(item) {
                return item.id;
            });
            
            deleteData(ids, index);
        });
    }

    /**
     * 删除数据
     */
    function deleteData(ids, layerIndex) {
        $.post(config.apiUrl + '/delete', {ids: ids})
            .done(function(res) {
                if (res.code === 1) {
                    layer.msg('删除成功', {icon: 1});
                    table.reload(config.tableId);
                } else {
                    layer.msg(res.msg || '删除失败', {icon: 2});
                }
            })
            .fail(function(xhr, status, error) {
                layer.msg('网络错误: ' + error, {icon: 2});
                console.error('删除请求失败:', xhr, status, error);
            })
            .always(function() {
                if (layerIndex) {
                    layer.close(layerIndex);
                }
            });
    }

    // 初始化
    init();
});`;
        },

        // 批量优化
        batchOptimize: function() {
            var self = this;
            var fileTypes = ['controller', 'model', 'view', 'js'];
            var completed = 0;
            
            layer.msg('正在批量优化...', {icon: 16, time: 0});
            
            fileTypes.forEach(function(fileType, index) {
                setTimeout(function() {
                    self.optimizeFile(fileType);
                    completed++;
                    
                    if (completed === fileTypes.length) {
                        layer.closeAll('loading');
                        layer.msg('批量优化完成', {icon: 1});
                    }
                }, index * 500);
            });
        },

        // 辅助方法
        getFileTypeName: function(fileType) {
            var names = {
                'controller': '控制器',
                'model': '模型',
                'view': '视图',
                'js': 'JavaScript',
                'css': '样式文件'
            };
            return names[fileType] || fileType;
        },

        getQualityClass: function(report) {
            var quality = this.calculateQuality(report);
            if (quality >= 90) return 'excellent';
            if (quality >= 70) return 'good';
            if (quality >= 50) return 'warning';
            return 'danger';
        },

        calculateQuality: function(report) {
            if (report.totalFiles === 0) return 100;
            return Math.max(0, 100 - (report.totalIssues / report.totalFiles) * 20);
        },

        getScoreClass: function(score) {
            if (score >= 90) return 'excellent';
            if (score >= 70) return 'good';
            if (score >= 50) return 'warning';
            return 'danger';
        },

        // 打开设置
        openSettings: function() {
            layer.open({
                type: 1,
                title: '优化设置',
                content: this.buildSettingsHTML(),
                area: ['600px', '500px'],
                btn: ['保存', '取消'],
                yes: function(index) {
                    // 保存设置逻辑
                    layer.close(index);
                    layer.msg('设置已保存', {icon: 1});
                }
            });
        },

        // 构建设置HTML
        buildSettingsHTML: function() {
            return `
                <div class="optimizer-settings">
                    <div class="layui-form">
                        <div class="layui-form-item">
                            <label class="layui-form-label">PHP优化</label>
                            <div class="layui-input-block">
                                <input type="checkbox" name="php_type_hints" title="添加类型提示" checked>
                                <input type="checkbox" name="php_validation" title="添加数据验证" checked>
                                <input type="checkbox" name="php_docblocks" title="添加文档注释" checked>
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">JavaScript优化</label>
                            <div class="layui-input-block">
                                <input type="checkbox" name="js_error_handling" title="添加错误处理" checked>
                                <input type="checkbox" name="js_comments" title="添加注释" checked>
                                <input type="checkbox" name="js_console_remove" title="移除console.log">
                            </div>
                        </div>
                    </div>
                </div>
            `;
        },

        // 应用优化
        applyOptimization: function() {
            layer.confirm('确定应用所有优化建议吗？', function(index) {
                layer.msg('正在应用优化...', {icon: 16, time: 2000});
                
                setTimeout(function() {
                    layer.msg('优化应用完成', {icon: 1});
                }, 2000);
                
                layer.close(index);
            });
        }
    };

    // 暴露到全局
    window.CurdCodeOptimizer = CodeOptimizer;

    // 自动初始化
    $(document).ready(function() {
        CodeOptimizer.init();
    });
});
