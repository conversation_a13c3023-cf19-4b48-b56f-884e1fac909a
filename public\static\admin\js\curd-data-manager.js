/**
 * CURD 数据导入导出管理器
 */

layui.use(['layer', 'upload'], function() {
    var layer = layui.layer;
    var upload = layui.upload;

    // 全局变量
    var importData = null;
    var exportSettings = {};

    // 初始化数据管理器
    function initDataManager() {
        bindDataEvents();
        initUploadComponent();
    }

    // 绑定数据管理相关事件
    function bindDataEvents() {
        // 导入配置
        $(document).on('click', '#import-config-btn', function() {
            openImportModal();
        });

        // 导出配置
        $(document).on('click', '#export-config-btn', function() {
            openExportModal();
        });

        // 导入表结构
        $(document).on('click', '#import-table-btn', function() {
            importTableStructure();
        });

        // 导出表结构
        $(document).on('click', '#export-table-btn', function() {
            exportTableStructure();
        });

        // 批量导入字段配置
        $(document).on('click', '#batch-import-fields', function() {
            batchImportFields();
        });

        // 生成示例数据
        $(document).on('click', '#generate-sample-data', function() {
            generateSampleData();
        });
    }

    // 初始化上传组件
    function initUploadComponent() {
        // 配置文件上传
        upload.render({
            elem: '#config-upload-btn',
            accept: 'file',
            exts: 'json|yaml|yml',
            before: function(obj) {
                layer.msg('正在上传配置文件...', {icon: 16, time: 0});
            },
            done: function(res, index, upload) {
                layer.closeAll('loading');
                if (res.code === 1) {
                    importData = res.data;
                    layer.msg('配置文件上传成功', {icon: 1});
                    displayImportPreview(importData);
                } else {
                    layer.msg('上传失败: ' + res.msg, {icon: 2});
                }
            },
            error: function() {
                layer.closeAll('loading');
                layer.msg('上传失败', {icon: 2});
            }
        });

        // SQL文件上传
        upload.render({
            elem: '#sql-upload-btn',
            accept: 'file',
            exts: 'sql',
            before: function(obj) {
                layer.msg('正在解析SQL文件...', {icon: 16, time: 0});
            },
            done: function(res, index, upload) {
                layer.closeAll('loading');
                if (res.code === 1) {
                    parseSqlStructure(res.data);
                } else {
                    layer.msg('解析失败: ' + res.msg, {icon: 2});
                }
            }
        });
    }

    // 打开导入弹窗
    function openImportModal() {
        var content = `
            <div class="import-modal">
                <div class="layui-tab" lay-filter="import-tab">
                    <ul class="layui-tab-title">
                        <li class="layui-this">配置文件</li>
                        <li>SQL结构</li>
                        <li>Excel数据</li>
                        <li>JSON数据</li>
                    </ul>
                    <div class="layui-tab-content">
                        <div class="layui-tab-item layui-show">
                            <div class="import-section">
                                <h4>导入配置文件</h4>
                                <p>支持JSON、YAML格式的配置文件</p>
                                <div class="upload-area">
                                    <button type="button" class="layui-btn" id="config-upload-btn">
                                        <i class="layui-icon layui-icon-upload"></i> 选择文件
                                    </button>
                                    <div class="upload-tips">
                                        支持 .json, .yaml, .yml 格式
                                    </div>
                                </div>
                                <div id="import-preview" style="display: none;">
                                    <h5>预览内容</h5>
                                    <div class="preview-content"></div>
                                </div>
                            </div>
                        </div>
                        <div class="layui-tab-item">
                            <div class="import-section">
                                <h4>导入SQL结构</h4>
                                <p>从CREATE TABLE语句中解析表结构</p>
                                <div class="upload-area">
                                    <button type="button" class="layui-btn" id="sql-upload-btn">
                                        <i class="layui-icon layui-icon-upload"></i> 选择SQL文件
                                    </button>
                                    <div class="upload-tips">
                                        支持标准的CREATE TABLE语句
                                    </div>
                                </div>
                                <div class="sql-input-area">
                                    <h5>或直接粘贴SQL语句</h5>
                                    <textarea id="sql-input" class="layui-textarea" placeholder="CREATE TABLE example_table (...)"></textarea>
                                    <button type="button" class="layui-btn layui-btn-sm" id="parse-sql-btn">解析SQL</button>
                                </div>
                            </div>
                        </div>
                        <div class="layui-tab-item">
                            <div class="import-section">
                                <h4>导入Excel数据</h4>
                                <p>从Excel文件中导入字段配置</p>
                                <div class="upload-area">
                                    <button type="button" class="layui-btn" id="excel-upload-btn">
                                        <i class="layui-icon layui-icon-upload"></i> 选择Excel文件
                                    </button>
                                    <div class="upload-tips">
                                        支持 .xlsx, .xls 格式，第一行为标题
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="layui-tab-item">
                            <div class="import-section">
                                <h4>导入JSON数据</h4>
                                <p>从JSON格式的数据中导入配置</p>
                                <textarea id="json-input" class="layui-textarea" placeholder='{"fields": [...], "options": {...}}'></textarea>
                                <button type="button" class="layui-btn layui-btn-sm" id="parse-json-btn">解析JSON</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        layer.open({
            type: 1,
            title: '导入数据',
            content: content,
            area: ['600px', '500px'],
            btn: ['确定导入', '取消'],
            yes: function(index) {
                if (importData) {
                    applyImportData(importData);
                    layer.close(index);
                } else {
                    layer.msg('请先选择要导入的数据', {icon: 2});
                }
            },
            success: function() {
                // 初始化标签页
                layui.element.render('tab');
                
                // 绑定解析按钮事件
                $('#parse-sql-btn').on('click', function() {
                    var sql = $('#sql-input').val();
                    if (sql.trim()) {
                        parseSqlText(sql);
                    } else {
                        layer.msg('请输入SQL语句', {icon: 2});
                    }
                });

                $('#parse-json-btn').on('click', function() {
                    var json = $('#json-input').val();
                    if (json.trim()) {
                        parseJsonText(json);
                    } else {
                        layer.msg('请输入JSON数据', {icon: 2});
                    }
                });
            }
        });
    }

    // 打开导出弹窗
    function openExportModal() {
        var content = `
            <div class="export-modal">
                <div class="layui-form" lay-filter="export-form">
                    <div class="layui-form-item">
                        <label class="layui-form-label">导出格式</label>
                        <div class="layui-input-block">
                            <input type="radio" name="export_format" value="json" title="JSON" checked>
                            <input type="radio" name="export_format" value="yaml" title="YAML">
                            <input type="radio" name="export_format" value="excel" title="Excel">
                            <input type="radio" name="export_format" value="sql" title="SQL">
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">导出内容</label>
                        <div class="layui-input-block">
                            <input type="checkbox" name="export_table_info" checked title="表信息" lay-skin="primary">
                            <input type="checkbox" name="export_field_configs" checked title="字段配置" lay-skin="primary">
                            <input type="checkbox" name="export_generate_options" checked title="生成选项" lay-skin="primary">
                            <input type="checkbox" name="export_quality_report" title="质量报告" lay-skin="primary">
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">文件名</label>
                        <div class="layui-input-block">
                            <input type="text" name="export_filename" class="layui-input" placeholder="自动生成">
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">压缩选项</label>
                        <div class="layui-input-block">
                            <input type="checkbox" name="compress_output" title="压缩输出" lay-skin="primary">
                            <input type="checkbox" name="include_comments" checked title="包含注释" lay-skin="primary">
                        </div>
                    </div>
                </div>
            </div>
        `;

        layer.open({
            type: 1,
            title: '导出配置',
            content: content,
            area: ['500px', '400px'],
            btn: ['开始导出', '取消'],
            yes: function(index) {
                var formData = layui.form.val('export-form');
                executeExport(formData);
                layer.close(index);
            },
            success: function() {
                layui.form.render();
            }
        });
    }

    // 显示导入预览
    function displayImportPreview(data) {
        var preview = '<div class="import-preview-data">';
        
        if (data.table) {
            preview += '<h6>表信息</h6>';
            preview += '<p>表名: ' + data.table.name + '</p>';
            preview += '<p>注释: ' + (data.table.comment || '无') + '</p>';
        }
        
        if (data.fields && data.fields.length > 0) {
            preview += '<h6>字段信息 (' + data.fields.length + '个)</h6>';
            preview += '<ul>';
            data.fields.slice(0, 5).forEach(function(field) {
                preview += '<li>' + field.name + ' (' + field.type + ')';
                if (field.comment) preview += ' - ' + field.comment;
                preview += '</li>';
            });
            if (data.fields.length > 5) {
                preview += '<li>... 还有 ' + (data.fields.length - 5) + ' 个字段</li>';
            }
            preview += '</ul>';
        }
        
        preview += '</div>';
        
        $('#import-preview .preview-content').html(preview);
        $('#import-preview').show();
    }

    // 解析SQL结构
    function parseSqlStructure(sqlData) {
        // 这里应该调用后端API解析SQL
        layer.msg('SQL结构解析功能开发中', {icon: 0});
    }

    // 解析SQL文本
    function parseSqlText(sql) {
        // 简单的SQL解析示例
        var tableMatch = sql.match(/CREATE\s+TABLE\s+`?(\w+)`?\s*\(/i);
        if (!tableMatch) {
            layer.msg('无法识别的SQL格式', {icon: 2});
            return;
        }
        
        var tableName = tableMatch[1];
        var fields = [];
        
        // 简单的字段解析
        var fieldMatches = sql.match(/`?(\w+)`?\s+(\w+)(\([^)]+\))?\s*([^,\n]*)/g);
        if (fieldMatches) {
            fieldMatches.forEach(function(match) {
                var parts = match.trim().split(/\s+/);
                if (parts.length >= 2) {
                    fields.push({
                        name: parts[0].replace(/`/g, ''),
                        type: parts[1],
                        comment: ''
                    });
                }
            });
        }
        
        importData = {
            table: { name: tableName, comment: '' },
            fields: fields
        };
        
        displayImportPreview(importData);
        layer.msg('SQL解析完成，找到 ' + fields.length + ' 个字段', {icon: 1});
    }

    // 解析JSON文本
    function parseJsonText(json) {
        try {
            importData = JSON.parse(json);
            displayImportPreview(importData);
            layer.msg('JSON解析成功', {icon: 1});
        } catch (e) {
            layer.msg('JSON格式错误: ' + e.message, {icon: 2});
        }
    }

    // 应用导入数据
    function applyImportData(data) {
        if (data.table) {
            $('input[name="table_name"]').val(data.table.name);
            window.tableInfo = data.table;
        }
        
        if (data.fields) {
            window.fieldConfigs = data.fields;
            if (window.CurdGeneratorV2 && window.CurdGeneratorV2.loadFieldConfigs) {
                window.CurdGeneratorV2.loadFieldConfigs();
            }
        }
        
        if (data.options) {
            // 应用生成选项
            Object.keys(data.options).forEach(function(key) {
                var $input = $('input[name="' + key + '"]');
                if ($input.length > 0) {
                    if ($input.attr('type') === 'checkbox') {
                        $input.prop('checked', data.options[key]);
                    } else {
                        $input.val(data.options[key]);
                    }
                }
            });
            layui.form.render();
        }
        
        layer.msg('数据导入成功', {icon: 1});
    }

    // 执行导出
    function executeExport(options) {
        var exportData = {};
        
        // 收集导出数据
        if (options.export_table_info && window.tableInfo) {
            exportData.table = window.tableInfo;
        }
        
        if (options.export_field_configs && window.fieldConfigs) {
            exportData.fields = window.fieldConfigs;
        }
        
        if (options.export_generate_options) {
            exportData.options = collectGenerateOptions();
        }
        
        // 生成文件名
        var filename = options.export_filename || generateExportFilename(options.export_format);
        
        // 根据格式导出
        switch (options.export_format) {
            case 'json':
                exportAsJson(exportData, filename, options);
                break;
            case 'yaml':
                exportAsYaml(exportData, filename, options);
                break;
            case 'excel':
                exportAsExcel(exportData, filename, options);
                break;
            case 'sql':
                exportAsSql(exportData, filename, options);
                break;
        }
    }

    // 导出为JSON
    function exportAsJson(data, filename, options) {
        var content = JSON.stringify(data, null, options.compress_output ? 0 : 2);
        downloadFile(content, filename + '.json', 'application/json');
        layer.msg('JSON文件导出成功', {icon: 1});
    }

    // 导出为YAML
    function exportAsYaml(data, filename, options) {
        // 简单的YAML转换
        var yaml = convertToYaml(data);
        downloadFile(yaml, filename + '.yaml', 'text/yaml');
        layer.msg('YAML文件导出成功', {icon: 1});
    }

    // 导出为Excel
    function exportAsExcel(data, filename, options) {
        layer.msg('Excel导出功能开发中', {icon: 0});
    }

    // 导出为SQL
    function exportAsSql(data, filename, options) {
        var sql = generateCreateTableSql(data);
        downloadFile(sql, filename + '.sql', 'text/sql');
        layer.msg('SQL文件导出成功', {icon: 1});
    }

    // 生成CREATE TABLE SQL
    function generateCreateTableSql(data) {
        if (!data.table || !data.fields) {
            return '-- 无效的表数据';
        }
        
        var sql = 'CREATE TABLE `' + data.table.name + '` (\n';
        var fieldSqls = [];
        
        data.fields.forEach(function(field) {
            var fieldSql = '  `' + field.name + '` ' + field.type.toUpperCase();
            if (field.length) fieldSql += '(' + field.length + ')';
            if (!field.nullable) fieldSql += ' NOT NULL';
            if (field.default) fieldSql += ' DEFAULT \'' + field.default + '\'';
            if (field.auto_increment) fieldSql += ' AUTO_INCREMENT';
            if (field.comment) fieldSql += ' COMMENT \'' + field.comment + '\'';
            fieldSqls.push(fieldSql);
        });
        
        sql += fieldSqls.join(',\n');
        
        // 添加主键
        var primaryKeys = data.fields.filter(f => f.primary).map(f => '`' + f.name + '`');
        if (primaryKeys.length > 0) {
            sql += ',\n  PRIMARY KEY (' + primaryKeys.join(', ') + ')';
        }
        
        sql += '\n)';
        if (data.table.comment) {
            sql += ' COMMENT=\'' + data.table.comment + '\'';
        }
        sql += ';';
        
        return sql;
    }

    // 简单的YAML转换
    function convertToYaml(obj, indent) {
        indent = indent || 0;
        var yaml = '';
        var spaces = '  '.repeat(indent);
        
        for (var key in obj) {
            if (obj.hasOwnProperty(key)) {
                var value = obj[key];
                if (typeof value === 'object' && value !== null) {
                    if (Array.isArray(value)) {
                        yaml += spaces + key + ':\n';
                        value.forEach(function(item) {
                            if (typeof item === 'object') {
                                yaml += spaces + '  -\n';
                                yaml += convertToYaml(item, indent + 2);
                            } else {
                                yaml += spaces + '  - ' + item + '\n';
                            }
                        });
                    } else {
                        yaml += spaces + key + ':\n';
                        yaml += convertToYaml(value, indent + 1);
                    }
                } else {
                    yaml += spaces + key + ': ' + value + '\n';
                }
            }
        }
        
        return yaml;
    }

    // 收集生成选项
    function collectGenerateOptions() {
        var options = {};
        $('input[type="checkbox"]').each(function() {
            var name = $(this).attr('name');
            if (name && name.startsWith('generate_') || name.startsWith('enable_')) {
                options[name] = $(this).is(':checked');
            }
        });
        return options;
    }

    // 生成导出文件名
    function generateExportFilename(format) {
        var tableName = $('select[name="table_name"]').val() || 'curd_config';
        var timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
        return tableName + '_' + timestamp;
    }

    // 下载文件
    function downloadFile(content, filename, mimeType) {
        var blob = new Blob([content], { type: mimeType + ';charset=utf-8' });
        var url = window.URL.createObjectURL(blob);
        var a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        window.URL.revokeObjectURL(url);
    }

    // 暴露全局函数
    window.CurdDataManager = {
        init: initDataManager,
        import: openImportModal,
        export: openExportModal
    };

    // 自动初始化
    $(document).ready(function() {
        initDataManager();
    });
});
