<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简化连接测试</title>
    <link rel="stylesheet" href="https://unpkg.com/layui@2.8.18/dist/css/layui.css">
    <style>
        .container { max-width: 600px; margin: 50px auto; padding: 20px; }
        .test-result { margin-top: 20px; padding: 15px; border-radius: 5px; }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
    </style>
</head>
<body>
    <div class="container">
        <div class="layui-card">
            <div class="layui-card-header">
                <h2>🔧 简化数据库连接测试</h2>
            </div>
            <div class="layui-card-body">
                <div class="layui-form">
                    <div class="layui-form-item">
                        <label class="layui-form-label">数据库连接</label>
                        <div class="layui-input-block">
                            <select name="connection" lay-filter="connectionSelect" id="connection-select">
                                <option value="">请选择数据库连接</option>
                                <option value="mysql">默认连接 (mysql)</option>
                                <option value="mysql_read">读库连接 (mysql_read)</option>
                                <option value="mysql_second">第二数据库 (mysql_second)</option>
                                <option value="mysql_log">日志数据库 (mysql_log)</option>
                                <option value="mysql_cache">缓存数据库 (mysql_cache)</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">数据库表</label>
                        <div class="layui-input-block">
                            <select name="table_name" lay-filter="tableSelect" id="table-select">
                                <option value="">请先选择数据库连接</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <div class="layui-input-block">
                            <button type="button" class="layui-btn" id="manual-test-btn">手动测试连接</button>
                            <button type="button" class="layui-btn layui-btn-primary" id="clear-btn">清空结果</button>
                        </div>
                    </div>
                </div>
                
                <div id="test-results"></div>
            </div>
        </div>
    </div>

    <script src="https://unpkg.com/layui@2.8.18/dist/layui.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    <script>
        layui.use(['form', 'layer'], function() {
            var form = layui.form;
            var layer = layui.layer;
            
            console.log('Layui form initialized');
            
            // 监听数据库连接选择
            form.on('select(connectionSelect)', function(data) {
                console.log('连接选择事件触发:', data.value);
                addResult(`🔄 连接选择事件触发: ${data.value}`, 'info');
                
                if (data.value) {
                    loadTables(data.value);
                } else {
                    $('#table-select').html('<option value="">请先选择数据库连接</option>');
                    form.render('select');
                }
            });
            
            // 监听表选择
            form.on('select(tableSelect)', function(data) {
                console.log('表选择事件触发:', data.value);
                addResult(`📋 表选择事件触发: ${data.value}`, 'info');
            });
            
            // 手动测试按钮
            $('#manual-test-btn').on('click', function() {
                var connection = $('#connection-select').val();
                if (connection) {
                    addResult(`🚀 手动测试连接: ${connection}`, 'info');
                    loadTables(connection);
                } else {
                    addResult('⚠️ 请先选择数据库连接', 'error');
                }
            });
            
            // 清空结果按钮
            $('#clear-btn').on('click', function() {
                $('#test-results').empty();
            });
            
            // 加载表列表函数
            function loadTables(connection) {
                addResult(`📡 开始请求表列表，连接: ${connection}`, 'info');
                
                var startTime = Date.now();
                
                $.ajax({
                    url: '/curdtest/tables',
                    method: 'POST',
                    data: { connection: connection },
                    timeout: 10000,
                    beforeSend: function() {
                        addResult('📤 发送AJAX请求...', 'info');
                    }
                })
                .done(function(response) {
                    var duration = Date.now() - startTime;
                    console.log('请求成功:', response);
                    
                    addResult(`✅ 请求成功 (${duration}ms)`, 'success');
                    
                    if (response && response.code === 1) {
                        addResult(`🎯 获取到 ${response.data.length} 个表`, 'success');
                        
                        // 更新表选择框
                        var html = '<option value="">请选择数据表</option>';
                        response.data.forEach(function(table) {
                            html += `<option value="${table.name}">${table.name} (${table.comment || '无注释'})</option>`;
                        });
                        $('#table-select').html(html);
                        form.render('select');
                        
                        // 显示表列表
                        var tableList = response.data.map(t => `${t.name} (${t.comment || '无注释'})`).join(', ');
                        addResult(`📋 表列表: ${tableList}`, 'info');
                        
                        if (response.msg && response.msg.includes('演示数据')) {
                            addResult('⚠️ 注意: 使用演示数据模式', 'info');
                        }
                        
                    } else {
                        addResult(`❌ API返回错误: ${response ? response.msg : '无响应'}`, 'error');
                    }
                })
                .fail(function(xhr, textStatus, errorThrown) {
                    var duration = Date.now() - startTime;
                    console.error('请求失败:', textStatus, errorThrown);
                    
                    addResult(`❌ 请求失败 (${duration}ms)`, 'error');
                    addResult(`💥 错误: ${textStatus} - ${errorThrown}`, 'error');
                    addResult(`📊 HTTP状态: ${xhr.status}`, 'error');
                    
                    if (xhr.responseText) {
                        addResult(`📄 服务器响应: ${xhr.responseText}`, 'error');
                    }
                    
                    // 错误分析
                    if (xhr.status === 0) {
                        addResult('🔍 可能原因: 网络连接问题或服务器未启动', 'error');
                    } else if (xhr.status === 404) {
                        addResult('🔍 可能原因: 路由不存在，检查路由配置', 'error');
                    } else if (xhr.status === 500) {
                        addResult('🔍 可能原因: 服务器内部错误，检查PHP错误日志', 'error');
                    }
                });
            }
            
            // 添加测试结果
            function addResult(message, type) {
                var timestamp = new Date().toLocaleTimeString();
                var className = type === 'success' ? 'success' : (type === 'error' ? 'error' : 'info');
                var html = `<div class="test-result ${className}">[${timestamp}] ${message}</div>`;
                $('#test-results').append(html);
                
                // 自动滚动到底部
                var container = $('#test-results')[0];
                container.scrollTop = container.scrollHeight;
            }
            
            // 页面加载完成
            addResult('🚀 简化测试页面已加载', 'success');
            addResult('💡 请选择数据库连接进行测试', 'info');
        });
    </script>
</body>
</html>
