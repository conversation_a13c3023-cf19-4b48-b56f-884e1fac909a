<?php
/**
 * 缓存修复验证脚本
 * 用于测试缓存助手类是否正常工作
 */

echo "=== 缓存修复验证脚本 ===\n\n";

// 检查必要文件
$requiredFiles = [
    'vendor/autoload.php' => 'Composer 自动加载器',
    'app/common/services/CacheHelper.php' => '缓存助手类',
    'app/common/services/AuthServiceOptimized.php' => '优化后的权限服务',
    'app/common/controller/AdminController.php' => '优化后的基础控制器',
    'config/auth.php' => '权限配置文件',
];

echo "1. 检查必要文件\n";
foreach ($requiredFiles as $file => $desc) {
    if (file_exists($file)) {
        echo "   ✅ {$desc} ({$file})\n";
    } else {
        echo "   ❌ {$desc} ({$file}) - 文件不存在\n";
    }
}

echo "\n2. 检查语法错误\n";
$phpFiles = [
    'app/common/services/CacheHelper.php',
    'app/common/services/AuthServiceOptimized.php',
    'app/common/controller/AdminController.php',
    'config/auth.php',
];

foreach ($phpFiles as $file) {
    if (file_exists($file)) {
        $output = [];
        $returnCode = 0;
        exec("php -l \"$file\" 2>&1", $output, $returnCode);
        
        if ($returnCode === 0) {
            echo "   ✅ {$file} - 语法正确\n";
        } else {
            echo "   ❌ {$file} - 语法错误:\n";
            foreach ($output as $line) {
                echo "      {$line}\n";
            }
        }
    }
}

echo "\n3. 检查类和方法\n";

// 检查 CacheHelper 类
if (file_exists('app/common/services/CacheHelper.php')) {
    $content = file_get_contents('app/common/services/CacheHelper.php');
    
    $methods = [
        'remember' => '记忆缓存方法',
        'get' => '获取缓存方法',
        'set' => '设置缓存方法',
        'forget' => '删除缓存方法',
        'forgetByPrefix' => '按前缀删除缓存方法',
    ];
    
    foreach ($methods as $method => $desc) {
        if (strpos($content, "function $method(") !== false) {
            echo "   ✅ CacheHelper::{$method}() - {$desc}\n";
        } else {
            echo "   ❌ CacheHelper::{$method}() - {$desc} 缺失\n";
        }
    }
}

echo "\n4. 检查配置文件结构\n";
if (file_exists('config/auth.php')) {
    try {
        $authConfig = include 'config/auth.php';
        
        $requiredKeys = [
            'auth_on' => '权限开关',
            'tables' => '数据表配置',
            'cache' => '缓存配置',
            'super_admin' => '超级管理员配置',
        ];
        
        foreach ($requiredKeys as $key => $desc) {
            if (isset($authConfig[$key])) {
                echo "   ✅ {$desc} ({$key})\n";
            } else {
                echo "   ❌ {$desc} ({$key}) - 配置缺失\n";
            }
        }
        
        // 检查缓存配置的详细结构
        if (isset($authConfig['cache'])) {
            $cacheKeys = ['prefix', 'ttl', 'keys'];
            foreach ($cacheKeys as $key) {
                if (isset($authConfig['cache'][$key])) {
                    echo "   ✅ 缓存配置 - {$key}\n";
                } else {
                    echo "   ❌ 缓存配置 - {$key} 缺失\n";
                }
            }
        }
        
    } catch (Exception $e) {
        echo "   ❌ 配置文件解析错误: " . $e->getMessage() . "\n";
    }
}

echo "\n5. 检查优化点\n";

// 检查 AdminController 是否使用了 CacheHelper
if (file_exists('app/common/controller/AdminController.php')) {
    $content = file_get_contents('app/common/controller/AdminController.php');
    
    if (strpos($content, 'use app\common\services\CacheHelper;') !== false) {
        echo "   ✅ AdminController 已导入 CacheHelper\n";
    } else {
        echo "   ❌ AdminController 未导入 CacheHelper\n";
    }
    
    if (strpos($content, 'CacheHelper::remember(') !== false) {
        echo "   ✅ AdminController 使用了 CacheHelper::remember()\n";
    } else {
        echo "   ❌ AdminController 未使用 CacheHelper::remember()\n";
    }
}

// 检查 AuthServiceOptimized 是否使用了 CacheHelper
if (file_exists('app/common/services/AuthServiceOptimized.php')) {
    $content = file_get_contents('app/common/services/AuthServiceOptimized.php');
    
    if (strpos($content, 'use app\common\services\CacheHelper;') !== false) {
        echo "   ✅ AuthServiceOptimized 已导入 CacheHelper\n";
    } else {
        echo "   ❌ AuthServiceOptimized 未导入 CacheHelper\n";
    }
    
    if (strpos($content, 'CacheHelper::remember(') !== false) {
        echo "   ✅ AuthServiceOptimized 使用了 CacheHelper::remember()\n";
    } else {
        echo "   ❌ AuthServiceOptimized 未使用 CacheHelper::remember()\n";
    }
}

echo "\n6. 修复建议\n";
echo "   如果发现任何 ❌ 项目，请检查对应文件并修复问题。\n";
echo "   主要修复内容:\n";
echo "   - 将 Cache::remember() 替换为 CacheHelper::remember()\n";
echo "   - 确保所有文件语法正确\n";
echo "   - 确保配置文件结构完整\n";

echo "\n7. 测试缓存功能\n";
if (file_exists('vendor/autoload.php')) {
    try {
        require_once 'vendor/autoload.php';
        
        // 简单测试缓存助手
        if (class_exists('app\common\services\CacheHelper')) {
            echo "   ✅ CacheHelper 类可以正常加载\n";
            
            // 这里可以添加更多的功能测试
            // 但需要确保不影响现有系统
            
        } else {
            echo "   ❌ CacheHelper 类无法加载\n";
        }
        
    } catch (Exception $e) {
        echo "   ❌ 测试过程中出现错误: " . $e->getMessage() . "\n";
    }
} else {
    echo "   ⚠️  无法进行功能测试，缺少 vendor/autoload.php\n";
}

echo "\n=== 验证完成 ===\n";
echo "如果所有项目都显示 ✅，说明缓存修复成功！\n";
echo "现在可以尝试启动项目: php windows.php\n";
