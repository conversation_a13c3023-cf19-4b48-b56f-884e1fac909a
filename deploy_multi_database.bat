@echo off
chcp 65001 >nul
echo.
echo ========================================
echo   EasyAdmin8-webman 多数据库配置部署
echo ========================================
echo.

:menu
echo 请选择操作:
echo 1. 部署多数据库配置
echo 2. 验证配置
echo 3. 测试数据库连接
echo 4. 恢复原始配置
echo 5. 查看使用说明
echo 6. 退出
echo.
set /p choice=请输入选择 (1-6): 

if "%choice%"=="1" goto deploy
if "%choice%"=="2" goto verify
if "%choice%"=="3" goto test
if "%choice%"=="4" goto restore
if "%choice%"=="5" goto help
if "%choice%"=="6" goto exit
echo 无效选择，请重新输入
goto menu

:deploy
echo.
echo 🚀 部署多数据库配置...
echo ========================================

echo 第一步：备份原始配置
if exist config\database.php (
    copy config\database.php config\database.backup.php >nul
    echo ✅ 原始配置已备份为 config\database.backup.php
) else (
    echo ⚠️  原始配置文件不存在
)

echo.
echo 第二步：部署优化配置
if exist config\database_optimized.php (
    copy config\database_optimized.php config\database.php >nul
    echo ✅ 优化配置已部署为 config\database.php
) else (
    echo ❌ 优化配置文件不存在，请先运行验证
    goto menu
)

echo.
echo 第三步：创建环境配置示例
if exist .env.multi-database.example (
    echo ✅ 环境配置示例已存在: .env.multi-database.example
    echo 💡 请根据此文件更新你的 .env 配置
) else (
    echo ❌ 环境配置示例不存在
)

echo.
echo 🎉 多数据库配置部署完成！
echo.
echo 接下来请：
echo 1. 根据 .env.multi-database.example 更新你的 .env 文件
echo 2. 重启 webman 服务器
echo 3. 访问 /admin/system/databasetest 进行测试
echo.
echo 按任意键返回菜单...
pause >nul
goto menu

:verify
echo.
echo 🔍 验证配置...
echo ========================================
php test_database_config.php
echo.
echo 按任意键返回菜单...
pause >nul
goto menu

:test
echo.
echo 🧪 测试数据库连接...
echo ========================================
echo 请确保：
echo 1. 已部署多数据库配置
echo 2. 已更新 .env 文件
echo 3. webman 服务器正在运行
echo.
echo 测试方法：
echo 1. 在浏览器中访问: http://localhost:8787/admin/system/databasetest
echo 2. 或运行以下 PHP 代码测试：
echo.
echo ^<?php
echo require_once 'vendor/autoload.php';
echo use app\common\service\DatabaseManager;
echo $result = DatabaseManager::testConnection();
echo var_dump($result);
echo ?^>
echo.
echo 按任意键返回菜单...
pause >nul
goto menu

:restore
echo.
echo 🔄 恢复原始配置...
echo ========================================
if exist config\database.backup.php (
    copy config\database.backup.php config\database.php >nul
    echo ✅ 原始配置已恢复
    echo 💡 请重启 webman 服务器使配置生效
) else (
    echo ❌ 备份文件不存在，无法恢复
)
echo.
echo 按任意键返回菜单...
pause >nul
goto menu

:help
echo.
echo 📖 使用说明
echo ========================================
echo.
echo 🔧 多数据库配置功能：
echo   • 支持 9 种数据库连接类型
echo   • 读写分离自动路由
echo   • 连接池管理
echo   • 事务支持（单库/跨库）
echo   • 连接监控和管理
echo.
echo 📋 支持的数据库：
echo   • MySQL（主库、读库、第二库、日志库、缓存库）
echo   • PostgreSQL
echo   • SQLite
echo   • SQL Server
echo.
echo 🚀 使用步骤：
echo   1. 运行"部署多数据库配置"
echo   2. 根据 .env.multi-database.example 更新 .env
echo   3. 重启 webman 服务器
echo   4. 访问测试页面验证连接
echo.
echo 💻 代码使用示例：
echo   use app\common\service\DatabaseManager;
echo   
echo   // 基本连接
echo   $conn = DatabaseManager::connection('mysql_second');
echo   
echo   // 读写分离
echo   $readConn = DatabaseManager::readConnection();
echo   $writeConn = DatabaseManager::writeConnection();
echo   
echo   // 专用连接
echo   $logConn = DatabaseManager::logConnection();
echo   $cacheConn = DatabaseManager::cacheConnection();
echo.
echo 🔧 配置文件：
echo   • config/database.php - 主配置文件
echo   • .env - 环境变量配置
echo   • .env.multi-database.example - 配置示例
echo.
echo 🌐 测试页面：
echo   访问: http://localhost:8787/admin/system/databasetest
echo.
echo 按任意键返回菜单...
pause >nul
goto menu

:exit
echo.
echo 👋 感谢使用多数据库配置工具！
echo.
echo 📋 部署总结：
echo   - 配置文件: config/database.php
echo   - 管理器类: app/common/service/DatabaseManager.php
echo   - 测试控制器: app/admin/controller/system/DatabaseTestController.php
echo   - 测试页面: /admin/system/databasetest
echo   - 环境配置: .env.multi-database.example
echo.
echo 💡 重要提醒：
echo   1. 记得根据示例文件更新 .env 配置
echo   2. 部署后需要重启 webman 服务器
echo   3. 建议先在测试环境验证配置
echo   4. 生产环境请做好数据备份
echo.
exit /b 0