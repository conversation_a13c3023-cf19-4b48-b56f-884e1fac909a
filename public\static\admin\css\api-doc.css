/**
 * API文档管理专用样式
 * 用于美化API文档展示界面
 */

/* ==================== 基础样式 ==================== */
.api-doc-container {
    background-color: #f8f9fa;
    min-height: 100vh;
}

.api-doc-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2rem 0;
    margin-bottom: 2rem;
}

.api-doc-title {
    font-size: 2.5rem;
    font-weight: 300;
    margin-bottom: 0.5rem;
}

.api-doc-subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
}

/* ==================== 统计卡片样式 ==================== */
.stats-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    border: none;
    overflow: hidden;
}

.stats-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.stats-card .card-body {
    padding: 1.5rem;
}

.stats-number {
    font-size: 2.5rem;
    font-weight: 700;
    line-height: 1;
    margin-bottom: 0.5rem;
}

.stats-label {
    font-size: 0.875rem;
    color: #6c757d;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-weight: 600;
}

.stats-icon {
    font-size: 3rem;
    opacity: 0.1;
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
}

/* 统计卡片颜色主题 */
.stats-card.primary { border-left: 4px solid #007bff; }
.stats-card.primary .stats-number { color: #007bff; }

.stats-card.success { border-left: 4px solid #28a745; }
.stats-card.success .stats-number { color: #28a745; }

.stats-card.info { border-left: 4px solid #17a2b8; }
.stats-card.info .stats-number { color: #17a2b8; }

.stats-card.warning { border-left: 4px solid #ffc107; }
.stats-card.warning .stats-number { color: #ffc107; }

/* ==================== API文档卡片样式 ==================== */
.api-doc-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 15px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    border: none;
    overflow: hidden;
    position: relative;
}

.api-doc-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.api-doc-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea, #764ba2);
}

.api-doc-card .card-body {
    padding: 1.5rem;
}

.api-doc-card .card-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.api-doc-card .table-name {
    font-family: 'Courier New', monospace;
    background-color: #f8f9fa;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.875rem;
    color: #495057;
}

/* ==================== HTTP方法标签样式 ==================== */
.badge-method-get {
    background-color: #28a745;
    color: white;
    font-weight: 600;
    padding: 0.5rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.badge-method-post {
    background-color: #007bff;
    color: white;
    font-weight: 600;
    padding: 0.5rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.badge-method-put {
    background-color: #ffc107;
    color: #212529;
    font-weight: 600;
    padding: 0.5rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.badge-method-delete {
    background-color: #dc3545;
    color: white;
    font-weight: 600;
    padding: 0.5rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.badge-method-patch {
    background-color: #6f42c1;
    color: white;
    font-weight: 600;
    padding: 0.5rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* ==================== 响应状态码样式 ==================== */
.badge-response-2 {
    background-color: #28a745;
    color: white;
    font-weight: 600;
    padding: 0.375rem 0.75rem;
    border-radius: 15px;
}

.badge-response-3 {
    background-color: #17a2b8;
    color: white;
    font-weight: 600;
    padding: 0.375rem 0.75rem;
    border-radius: 15px;
}

.badge-response-4 {
    background-color: #ffc107;
    color: #212529;
    font-weight: 600;
    padding: 0.375rem 0.75rem;
    border-radius: 15px;
}

.badge-response-5 {
    background-color: #dc3545;
    color: white;
    font-weight: 600;
    padding: 0.375rem 0.75rem;
    border-radius: 15px;
}

/* ==================== 手风琴样式优化 ==================== */
.api-accordion .accordion-item {
    border: none;
    margin-bottom: 1rem;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
}

.api-accordion .accordion-header {
    border-radius: 12px 12px 0 0;
}

.api-accordion .accordion-button {
    background-color: #f8f9fa;
    border: none;
    padding: 1.25rem 1.5rem;
    font-weight: 600;
    color: #2c3e50;
    border-radius: 12px 12px 0 0;
}

.api-accordion .accordion-button:not(.collapsed) {
    background-color: #e9ecef;
    color: #495057;
    box-shadow: none;
}

.api-accordion .accordion-button:focus {
    box-shadow: none;
    border-color: transparent;
}

.api-accordion .accordion-body {
    padding: 1.5rem;
    background-color: white;
}

/* ==================== 代码块样式 ==================== */
.code-block {
    background-color: #2d3748;
    color: #e2e8f0;
    border-radius: 8px;
    padding: 1rem;
    font-family: 'Courier New', monospace;
    font-size: 0.875rem;
    line-height: 1.5;
    overflow-x: auto;
    margin: 1rem 0;
}

.code-block.json {
    background-color: #1a202c;
}

.code-block.xml {
    background-color: #2d3748;
}

.code-block.html {
    background-color: #2c5282;
}

/* JSON语法高亮 */
.json-key { color: #63b3ed; }
.json-string { color: #68d391; }
.json-number { color: #f6ad55; }
.json-boolean { color: #fc8181; }
.json-null { color: #a0aec0; }

/* ==================== 测试界面样式 ==================== */
.api-test-panel {
    background-color: #f8f9fa;
    border-radius: 12px;
    padding: 1.5rem;
    margin-top: 1rem;
    border: 2px dashed #dee2e6;
}

.api-test-form .form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.5rem;
}

.api-test-form .form-control {
    border-radius: 8px;
    border: 1px solid #ced4da;
    padding: 0.75rem;
    transition: all 0.3s ease;
}

.api-test-form .form-control:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* 测试结果样式 */
.test-result {
    margin-top: 1rem;
    border-radius: 8px;
    overflow: hidden;
}

.test-result.success {
    border-left: 4px solid #28a745;
    background-color: #d4edda;
}

.test-result.error {
    border-left: 4px solid #dc3545;
    background-color: #f8d7da;
}

.test-result .result-header {
    padding: 1rem;
    font-weight: 600;
}

.test-result.success .result-header {
    color: #155724;
}

.test-result.error .result-header {
    color: #721c24;
}

.test-result .result-content {
    padding: 1rem;
    background-color: white;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
}

/* ==================== 按钮样式优化 ==================== */
.btn-api {
    border-radius: 8px;
    padding: 0.5rem 1rem;
    font-weight: 600;
    transition: all 0.3s ease;
    border: none;
}

.btn-api:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.btn-api.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.btn-api.btn-success {
    background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
}

.btn-api.btn-info {
    background: linear-gradient(135deg, #3ca55c 0%, #b2fefa 100%);
}

.btn-api.btn-warning {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

/* ==================== 表格样式优化 ==================== */
.api-table {
    background-color: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 15px rgba(0, 0, 0, 0.08);
}

.api-table th {
    background-color: #f8f9fa;
    border: none;
    padding: 1rem;
    font-weight: 600;
    color: #495057;
    text-transform: uppercase;
    font-size: 0.875rem;
    letter-spacing: 0.5px;
}

.api-table td {
    border: none;
    padding: 1rem;
    vertical-align: middle;
}

.api-table tbody tr {
    border-bottom: 1px solid #f1f3f4;
    transition: background-color 0.3s ease;
}

.api-table tbody tr:hover {
    background-color: #f8f9fa;
}

/* ==================== 模态框样式优化 ==================== */
.modal-api .modal-content {
    border-radius: 12px;
    border: none;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
}

.modal-api .modal-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 12px 12px 0 0;
    border: none;
    padding: 1.5rem;
}

.modal-api .modal-title {
    font-weight: 600;
}

.modal-api .btn-close {
    filter: invert(1);
}

.modal-api .modal-body {
    padding: 2rem;
}

.modal-api .modal-footer {
    border: none;
    padding: 1.5rem 2rem;
    background-color: #f8f9fa;
}

/* ==================== 加载动画 ==================== */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* ==================== 响应式设计 ==================== */
@media (max-width: 768px) {
    .api-doc-title {
        font-size: 2rem;
    }
    
    .stats-card .card-body {
        padding: 1rem;
    }
    
    .stats-number {
        font-size: 2rem;
    }
    
    .api-doc-card .card-body {
        padding: 1rem;
    }
    
    .api-accordion .accordion-button {
        padding: 1rem;
        font-size: 0.875rem;
    }
    
    .api-accordion .accordion-body {
        padding: 1rem;
    }
    
    .modal-api .modal-body {
        padding: 1rem;
    }
}

@media (max-width: 576px) {
    .api-doc-header {
        padding: 1rem 0;
    }
    
    .api-doc-title {
        font-size: 1.75rem;
    }
    
    .api-doc-subtitle {
        font-size: 1rem;
    }
    
    .btn-group .btn {
        padding: 0.375rem 0.5rem;
        font-size: 0.875rem;
    }
}

/* ==================== 打印样式 ==================== */
@media print {
    .api-doc-header,
    .btn,
    .modal,
    .api-test-panel {
        display: none !important;
    }
    
    .api-doc-card,
    .stats-card {
        box-shadow: none !important;
        border: 1px solid #dee2e6 !important;
    }
    
    .code-block {
        background-color: #f8f9fa !important;
        color: #212529 !important;
        border: 1px solid #dee2e6 !important;
    }
}

/* ==================== 深色主题支持 ==================== */
@media (prefers-color-scheme: dark) {
    .api-doc-container {
        background-color: #1a1a1a;
        color: #e9ecef;
    }
    
    .stats-card,
    .api-doc-card {
        background-color: #2d3748;
        color: #e9ecef;
    }
    
    .api-table {
        background-color: #2d3748;
        color: #e9ecef;
    }
    
    .api-table th {
        background-color: #4a5568;
    }
    
    .modal-api .modal-content {
        background-color: #2d3748;
        color: #e9ecef;
    }
    
    .form-control {
        background-color: #4a5568;
        border-color: #718096;
        color: #e9ecef;
    }
    
    .form-control:focus {
        background-color: #4a5568;
        border-color: #80bdff;
        color: #e9ecef;
    }
}
