<?php

namespace app\admin\controller\system;

/**
 * API版本管理控制器
 * 提供API版本控制、版本比较、版本发布等功能
 */
class ApiVersionController
{
    /**
     * 版本管理首页
     */
    public function index($request = null)
    {
        if (!$request) {
            $request = new \RequestMock();
        }

        $versions = $this->getVersionList();
        $statistics = $this->getVersionStatistics();

        $html = $this->renderVersionManagePage($versions, $statistics);
        return response($html);
    }

    /**
     * 获取版本列表
     */
    public function getVersionList($request = null)
    {
        $versions = [
            [
                'id' => 1,
                'version' => 'v1.0.0',
                'name' => '初始版本',
                'description' => 'API文档系统的第一个正式版本',
                'status' => 'stable',
                'release_date' => '2025-01-15',
                'api_count' => 25,
                'changes' => [
                    'added' => ['用户管理API', '文章管理API', '分类管理API'],
                    'modified' => [],
                    'deprecated' => [],
                    'removed' => []
                ]
            ],
            [
                'id' => 2,
                'version' => 'v1.1.0',
                'name' => '功能增强版',
                'description' => '增加了高级搜索和批量操作功能',
                'status' => 'stable',
                'release_date' => '2025-01-18',
                'api_count' => 32,
                'changes' => [
                    'added' => ['高级搜索API', '批量操作API', '统计分析API'],
                    'modified' => ['用户管理API优化', '文章API性能提升'],
                    'deprecated' => ['旧版搜索API'],
                    'removed' => []
                ]
            ],
            [
                'id' => 3,
                'version' => 'v2.0.0',
                'name' => '重大更新版',
                'description' => '架构重构，性能大幅提升',
                'status' => 'beta',
                'release_date' => '2025-01-20',
                'api_count' => 45,
                'changes' => [
                    'added' => ['GraphQL支持', '实时通知API', '文件管理API'],
                    'modified' => ['所有API响应格式统一', 'RESTful规范优化'],
                    'deprecated' => ['v1.0版本所有API'],
                    'removed' => ['旧版认证API']
                ]
            ]
        ];

        return response(json_encode([
            'code' => 200,
            'msg' => 'success',
            'data' => $versions
        ]));
    }

    /**
     * 获取版本统计信息
     */
    public function getVersionStatistics()
    {
        return [
            'total_versions' => 3,
            'stable_versions' => 2,
            'beta_versions' => 1,
            'deprecated_versions' => 0,
            'total_apis' => 45,
            'active_apis' => 42,
            'deprecated_apis' => 3,
            'version_adoption' => [
                'v1.0.0' => 15,
                'v1.1.0' => 65,
                'v2.0.0' => 20
            ]
        ];
    }

    /**
     * 版本比较
     */
    public function compareVersions($request = null)
    {
        if (!$request) {
            $request = new \RequestMock();
        }

        $version1 = $request->get('version1', 'v1.1.0');
        $version2 = $request->get('version2', 'v2.0.0');

        $comparison = $this->performVersionComparison($version1, $version2);

        return response(json_encode([
            'code' => 200,
            'msg' => 'success',
            'data' => $comparison
        ]));
    }

    /**
     * 执行版本比较
     */
    private function performVersionComparison($version1, $version2)
    {
        return [
            'version1' => $version1,
            'version2' => $version2,
            'differences' => [
                'added_apis' => [
                    ['name' => 'GraphQL查询接口', 'endpoint' => '/api/graphql', 'method' => 'POST'],
                    ['name' => '实时通知接口', 'endpoint' => '/api/notifications/realtime', 'method' => 'GET'],
                    ['name' => '文件上传接口', 'endpoint' => '/api/files/upload', 'method' => 'POST']
                ],
                'modified_apis' => [
                    ['name' => '用户列表接口', 'endpoint' => '/api/users', 'changes' => '响应格式优化，增加分页信息'],
                    ['name' => '文章详情接口', 'endpoint' => '/api/articles/{id}', 'changes' => '增加作者详细信息']
                ],
                'deprecated_apis' => [
                    ['name' => '旧版搜索接口', 'endpoint' => '/api/search/old', 'reason' => '性能问题，建议使用新版搜索']
                ],
                'removed_apis' => [
                    ['name' => '旧版认证接口', 'endpoint' => '/api/auth/old', 'reason' => '安全漏洞，已完全移除']
                ]
            ],
            'compatibility_score' => 85,
            'migration_guide' => [
                'breaking_changes' => 2,
                'recommended_actions' => [
                    '更新认证方式到新版API',
                    '迁移搜索功能到新接口',
                    '测试响应格式变更'
                ]
            ]
        ];
    }

    /**
     * 创建新版本
     */
    public function createVersion($request = null)
    {
        if (!$request) {
            $request = new \RequestMock();
        }

        $versionData = [
            'version' => $request->post('version', 'v2.1.0'),
            'name' => $request->post('name', '新功能版本'),
            'description' => $request->post('description', ''),
            'base_version' => $request->post('base_version', 'v2.0.0'),
            'status' => 'draft'
        ];

        // 模拟创建版本
        $newVersion = $this->simulateVersionCreation($versionData);

        return response(json_encode([
            'code' => 200,
            'msg' => '版本创建成功',
            'data' => $newVersion
        ]));
    }

    /**
     * 模拟版本创建
     */
    private function simulateVersionCreation($versionData)
    {
        return array_merge($versionData, [
            'id' => 4,
            'created_at' => date('Y-m-d H:i:s'),
            'created_by' => 'admin',
            'api_count' => 0,
            'changes' => [
                'added' => [],
                'modified' => [],
                'deprecated' => [],
                'removed' => []
            ]
        ]);
    }

    /**
     * 版本发布
     */
    public function releaseVersion($request = null)
    {
        if (!$request) {
            $request = new \RequestMock();
        }

        $versionId = $request->post('version_id', 4);
        $releaseNotes = $request->post('release_notes', '');

        // 模拟版本发布
        $result = $this->simulateVersionRelease($versionId, $releaseNotes);

        return response(json_encode([
            'code' => 200,
            'msg' => '版本发布成功',
            'data' => $result
        ]));
    }

    /**
     * 模拟版本发布
     */
    private function simulateVersionRelease($versionId, $releaseNotes)
    {
        return [
            'version_id' => $versionId,
            'status' => 'released',
            'release_date' => date('Y-m-d H:i:s'),
            'release_notes' => $releaseNotes,
            'deployment_status' => 'success',
            'rollback_available' => true
        ];
    }

    /**
     * 渲染版本管理页面
     */
    private function renderVersionManagePage($versions, $statistics)
    {
        return '
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API版本管理 - EasyAdmin8</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .version-card {
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            transition: transform 0.3s;
            border: none;
            margin-bottom: 1rem;
        }
        .version-card:hover {
            transform: translateY(-5px);
        }
        .status-badge {
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
        }
        .status-stable { background: #28a745; }
        .status-beta { background: #ffc107; color: #000; }
        .status-deprecated { background: #6c757d; }
        .change-item {
            padding: 0.5rem;
            margin: 0.25rem 0;
            border-radius: 5px;
        }
        .change-added { background: #d4edda; border-left: 4px solid #28a745; }
        .change-modified { background: #d1ecf1; border-left: 4px solid #17a2b8; }
        .change-deprecated { background: #fff3cd; border-left: 4px solid #ffc107; }
        .change-removed { background: #f8d7da; border-left: 4px solid #dc3545; }
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 1.5rem;
        }
    </style>
</head>
<body class="bg-light">
    <div class="container-fluid py-4">
        <!-- 页面标题 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h1 class="h3 mb-0">
                            <i class="bi bi-git me-2"></i>
                            API版本管理
                        </h1>
                        <p class="text-muted mb-0">管理API版本、比较差异、发布更新</p>
                    </div>
                    <div>
                        <button class="btn btn-primary me-2" onclick="createNewVersion()">
                            <i class="bi bi-plus-circle me-1"></i>创建版本
                        </button>
                        <button class="btn btn-outline-primary" onclick="compareVersions()">
                            <i class="bi bi-arrow-left-right me-1"></i>版本比较
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 统计信息 -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="stats-card text-center">
                    <i class="bi bi-collection display-4 mb-2"></i>
                    <h3>' . $statistics['total_versions'] . '</h3>
                    <p class="mb-0">总版本数</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card text-center">
                    <i class="bi bi-check-circle display-4 mb-2"></i>
                    <h3>' . $statistics['stable_versions'] . '</h3>
                    <p class="mb-0">稳定版本</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card text-center">
                    <i class="bi bi-api display-4 mb-2"></i>
                    <h3>' . $statistics['total_apis'] . '</h3>
                    <p class="mb-0">API总数</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card text-center">
                    <i class="bi bi-activity display-4 mb-2"></i>
                    <h3>' . $statistics['active_apis'] . '</h3>
                    <p class="mb-0">活跃API</p>
                </div>
            </div>
        </div>

        <!-- 版本列表 -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-list-ul me-2"></i>
                            版本列表
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            ' . $this->renderVersionCards($versions) . '
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function createNewVersion() {
            alert("创建新版本功能");
        }

        function compareVersions() {
            alert("版本比较功能");
        }

        function releaseVersion(versionId) {
            if (confirm("确定要发布此版本吗？")) {
                alert("版本发布功能 - 版本ID: " + versionId);
            }
        }
    </script>
</body>
</html>';
    }

    /**
     * 渲染版本卡片
     */
    private function renderVersionCards($versions)
    {
        $html = '';
        foreach ($versions as $version) {
            $statusClass = 'status-' . $version['status'];
            $statusText = [
                'stable' => '稳定版',
                'beta' => '测试版',
                'deprecated' => '已弃用'
            ][$version['status']] ?? $version['status'];

            $html .= '
            <div class="col-lg-4 col-md-6 mb-3">
                <div class="version-card card h-100">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h6 class="mb-0">' . $version['version'] . '</h6>
                        <span class="badge status-badge ' . $statusClass . '">' . $statusText . '</span>
                    </div>
                    <div class="card-body">
                        <h6 class="card-title">' . $version['name'] . '</h6>
                        <p class="card-text text-muted small">' . $version['description'] . '</p>

                        <div class="mb-3">
                            <small class="text-muted">
                                <i class="bi bi-calendar me-1"></i>' . $version['release_date'] . '
                                <span class="ms-3">
                                    <i class="bi bi-api me-1"></i>' . $version['api_count'] . ' APIs
                                </span>
                            </small>
                        </div>

                        <div class="changes-summary">
                            <h6 class="small mb-2">变更摘要:</h6>
                            ' . $this->renderChangeSummary($version['changes']) . '
                        </div>
                    </div>
                    <div class="card-footer">
                        <div class="btn-group w-100" role="group">
                            <button class="btn btn-sm btn-outline-primary" onclick="viewVersion(' . $version['id'] . ')">
                                <i class="bi bi-eye me-1"></i>查看
                            </button>
                            <button class="btn btn-sm btn-outline-success" onclick="releaseVersion(' . $version['id'] . ')">
                                <i class="bi bi-rocket me-1"></i>发布
                            </button>
                        </div>
                    </div>
                </div>
            </div>';
        }
        return $html;
    }

    /**
     * 渲染变更摘要
     */
    private function renderChangeSummary($changes)
    {
        $html = '';
        if (!empty($changes['added'])) {
            $html .= '<div class="change-item change-added">
                <small><i class="bi bi-plus-circle me-1"></i>新增: ' . count($changes['added']) . ' 项</small>
            </div>';
        }
        if (!empty($changes['modified'])) {
            $html .= '<div class="change-item change-modified">
                <small><i class="bi bi-pencil me-1"></i>修改: ' . count($changes['modified']) . ' 项</small>
            </div>';
        }
        if (!empty($changes['deprecated'])) {
            $html .= '<div class="change-item change-deprecated">
                <small><i class="bi bi-exclamation-triangle me-1"></i>弃用: ' . count($changes['deprecated']) . ' 项</small>
            </div>';
        }
        if (!empty($changes['removed'])) {
            $html .= '<div class="change-item change-removed">
                <small><i class="bi bi-x-circle me-1"></i>移除: ' . count($changes['removed']) . ' 项</small>
            </div>';
        }
        return $html ?: '<small class="text-muted">无变更</small>';
    }

    /**
     * 版本回滚
     */
    public function rollbackVersion($request = null)
    {
        if (!$request) {
            $request = new \RequestMock();
        }

        $versionId = $request->post('version_id');
        $targetVersion = $request->post('target_version');

        $result = [
            'code' => 200,
            'msg' => '版本回滚成功',
            'data' => [
                'rollback_id' => uniqid(),
                'from_version' => $versionId,
                'to_version' => $targetVersion,
                'rollback_time' => date('Y-m-d H:i:s'),
                'affected_apis' => 15,
                'status' => 'completed'
            ]
        ];

        return response(json_encode($result));
    }

    /**
     * 获取版本变更日志
     */
    public function getChangeLog($request = null)
    {
        if (!$request) {
            $request = new \RequestMock();
        }

        $versionId = $request->get('version_id', 2);

        $changeLog = [
            'version' => 'v1.1.0',
            'release_date' => '2025-01-18',
            'changes' => [
                [
                    'type' => 'added',
                    'category' => 'API',
                    'title' => '新增高级搜索API',
                    'description' => '支持多条件组合搜索，提供更精确的查询结果',
                    'endpoint' => '/api/search/advanced',
                    'impact' => 'low'
                ],
                [
                    'type' => 'modified',
                    'category' => 'Performance',
                    'title' => '用户API性能优化',
                    'description' => '优化数据库查询，响应时间减少50%',
                    'endpoint' => '/api/users',
                    'impact' => 'medium'
                ],
                [
                    'type' => 'deprecated',
                    'category' => 'API',
                    'title' => '旧版搜索API标记为弃用',
                    'description' => '建议迁移到新的高级搜索API',
                    'endpoint' => '/api/search/old',
                    'impact' => 'high'
                ]
            ]
        ];

        return response(json_encode([
            'code' => 200,
            'msg' => 'success',
            'data' => $changeLog
        ]));
    }
}
