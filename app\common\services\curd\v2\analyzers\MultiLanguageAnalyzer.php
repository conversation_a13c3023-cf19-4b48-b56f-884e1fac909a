<?php

namespace app\common\services\curd\v2\analyzers;

/**
 * 多语言分析器
 * 分析多语言代码生成需求并提供跨语言支持
 */
class MultiLanguageAnalyzer
{
    protected array $supportedLanguages = [
        'php' => 'PHP',
        'java' => 'Java',
        'python' => 'Python',
        'javascript' => 'JavaScript/Node.js',
        'typescript' => 'TypeScript',
        'csharp' => 'C#',
        'go' => 'Go',
        'rust' => 'Rust',
    ];

    protected array $frameworks = [
        'php' => ['laravel', 'symfony', 'webman', 'thinkphp'],
        'java' => ['spring_boot', 'spring_mvc', 'mybatis'],
        'python' => ['django', 'flask', 'fastapi', 'sqlalchemy'],
        'javascript' => ['express', 'koa', 'nestjs', 'fastify'],
        'typescript' => ['nestjs', 'express', 'koa'],
        'csharp' => ['asp_net_core', 'entity_framework'],
        'go' => ['gin', 'echo', 'fiber', 'gorm'],
        'rust' => ['actix_web', 'warp', 'rocket', 'diesel'],
    ];

    /**
     * 分析多语言需求
     */
    public function analyzeMultiLanguageRequirements(array $tableInfo, array $options = []): array
    {
        $requirements = [];

        // 分析目标语言
        $targetLanguages = $options['target_languages'] ?? ['java', 'python', 'javascript'];
        
        foreach ($targetLanguages as $language) {
            $requirements[$language] = $this->analyzeLanguageRequirements($language, $tableInfo, $options);
        }

        return [
            'requirements' => $requirements,
            'language_mapping' => $this->generateLanguageMapping($tableInfo),
            'framework_recommendations' => $this->recommendFrameworks($targetLanguages),
            'migration_plan' => $this->generateMigrationPlan($targetLanguages, $tableInfo),
        ];
    }

    /**
     * 分析单个语言需求
     */
    protected function analyzeLanguageRequirements(string $language, array $tableInfo, array $options): array
    {
        $requirements = [
            'language' => $language,
            'framework' => $this->getRecommendedFramework($language),
            'data_types' => $this->mapDataTypes($tableInfo['fields'], $language),
            'naming_conventions' => $this->getNamingConventions($language),
            'project_structure' => $this->getProjectStructure($language),
            'dependencies' => $this->getRequiredDependencies($language),
            'features' => $this->getLanguageFeatures($language, $tableInfo),
        ];

        return $requirements;
    }

    /**
     * 映射数据类型
     */
    protected function mapDataTypes(array $fields, string $language): array
    {
        $typeMapping = $this->getTypeMapping($language);
        $mappedTypes = [];

        foreach ($fields as $field) {
            $phpType = $field['type'];
            $mappedType = $typeMapping[$phpType] ?? 'string';
            
            $mappedTypes[$field['name']] = [
                'original_type' => $phpType,
                'mapped_type' => $mappedType,
                'nullable' => !($field['required'] ?? false),
                'default_value' => $this->getDefaultValue($mappedType, $language),
            ];
        }

        return $mappedTypes;
    }

    /**
     * 获取类型映射
     */
    protected function getTypeMapping(string $language): array
    {
        $mappings = [
            'java' => [
                'int' => 'Integer',
                'bigint' => 'Long',
                'varchar' => 'String',
                'text' => 'String',
                'date' => 'LocalDate',
                'datetime' => 'LocalDateTime',
                'timestamp' => 'LocalDateTime',
                'decimal' => 'BigDecimal',
                'float' => 'Double',
                'boolean' => 'Boolean',
                'json' => 'String',
            ],
            'python' => [
                'int' => 'int',
                'bigint' => 'int',
                'varchar' => 'str',
                'text' => 'str',
                'date' => 'date',
                'datetime' => 'datetime',
                'timestamp' => 'datetime',
                'decimal' => 'Decimal',
                'float' => 'float',
                'boolean' => 'bool',
                'json' => 'dict',
            ],
            'javascript' => [
                'int' => 'number',
                'bigint' => 'number',
                'varchar' => 'string',
                'text' => 'string',
                'date' => 'Date',
                'datetime' => 'Date',
                'timestamp' => 'Date',
                'decimal' => 'number',
                'float' => 'number',
                'boolean' => 'boolean',
                'json' => 'object',
            ],
            'typescript' => [
                'int' => 'number',
                'bigint' => 'number',
                'varchar' => 'string',
                'text' => 'string',
                'date' => 'Date',
                'datetime' => 'Date',
                'timestamp' => 'Date',
                'decimal' => 'number',
                'float' => 'number',
                'boolean' => 'boolean',
                'json' => 'object',
            ],
            'csharp' => [
                'int' => 'int',
                'bigint' => 'long',
                'varchar' => 'string',
                'text' => 'string',
                'date' => 'DateTime',
                'datetime' => 'DateTime',
                'timestamp' => 'DateTime',
                'decimal' => 'decimal',
                'float' => 'double',
                'boolean' => 'bool',
                'json' => 'string',
            ],
            'go' => [
                'int' => 'int',
                'bigint' => 'int64',
                'varchar' => 'string',
                'text' => 'string',
                'date' => 'time.Time',
                'datetime' => 'time.Time',
                'timestamp' => 'time.Time',
                'decimal' => 'float64',
                'float' => 'float64',
                'boolean' => 'bool',
                'json' => 'interface{}',
            ],
        ];

        return $mappings[$language] ?? [];
    }

    /**
     * 获取命名约定
     */
    protected function getNamingConventions(string $language): array
    {
        $conventions = [
            'java' => [
                'class' => 'PascalCase',
                'method' => 'camelCase',
                'variable' => 'camelCase',
                'constant' => 'UPPER_SNAKE_CASE',
                'package' => 'lowercase.dot.separated',
            ],
            'python' => [
                'class' => 'PascalCase',
                'method' => 'snake_case',
                'variable' => 'snake_case',
                'constant' => 'UPPER_SNAKE_CASE',
                'module' => 'snake_case',
            ],
            'javascript' => [
                'class' => 'PascalCase',
                'method' => 'camelCase',
                'variable' => 'camelCase',
                'constant' => 'UPPER_SNAKE_CASE',
                'file' => 'kebab-case',
            ],
            'typescript' => [
                'class' => 'PascalCase',
                'method' => 'camelCase',
                'variable' => 'camelCase',
                'constant' => 'UPPER_SNAKE_CASE',
                'interface' => 'PascalCase',
                'type' => 'PascalCase',
            ],
            'csharp' => [
                'class' => 'PascalCase',
                'method' => 'PascalCase',
                'variable' => 'camelCase',
                'property' => 'PascalCase',
                'constant' => 'PascalCase',
                'namespace' => 'PascalCase',
            ],
            'go' => [
                'type' => 'PascalCase',
                'function' => 'PascalCase',
                'variable' => 'camelCase',
                'constant' => 'PascalCase',
                'package' => 'lowercase',
            ],
        ];

        return $conventions[$language] ?? [];
    }

    /**
     * 获取项目结构
     */
    protected function getProjectStructure(string $language): array
    {
        $structures = [
            'java' => [
                'src/main/java/com/example/model/',
                'src/main/java/com/example/controller/',
                'src/main/java/com/example/service/',
                'src/main/java/com/example/repository/',
                'src/main/resources/',
                'src/test/java/',
                'pom.xml',
            ],
            'python' => [
                'app/',
                'app/models/',
                'app/views/',
                'app/controllers/',
                'app/services/',
                'tests/',
                'requirements.txt',
                'setup.py',
            ],
            'javascript' => [
                'src/',
                'src/models/',
                'src/controllers/',
                'src/routes/',
                'src/services/',
                'tests/',
                'package.json',
            ],
            'typescript' => [
                'src/',
                'src/models/',
                'src/controllers/',
                'src/routes/',
                'src/services/',
                'src/types/',
                'tests/',
                'package.json',
                'tsconfig.json',
            ],
            'csharp' => [
                'Controllers/',
                'Models/',
                'Services/',
                'Data/',
                'Tests/',
                'Program.cs',
                'appsettings.json',
                '*.csproj',
            ],
            'go' => [
                'cmd/',
                'internal/',
                'pkg/',
                'api/',
                'models/',
                'handlers/',
                'services/',
                'tests/',
                'go.mod',
                'go.sum',
            ],
        ];

        return $structures[$language] ?? [];
    }

    /**
     * 获取推荐框架
     */
    protected function getRecommendedFramework(string $language): string
    {
        $recommendations = [
            'java' => 'spring_boot',
            'python' => 'fastapi',
            'javascript' => 'express',
            'typescript' => 'nestjs',
            'csharp' => 'asp_net_core',
            'go' => 'gin',
            'rust' => 'actix_web',
        ];

        return $recommendations[$language] ?? '';
    }

    /**
     * 获取所需依赖
     */
    protected function getRequiredDependencies(string $language): array
    {
        $dependencies = [
            'java' => [
                'spring-boot-starter-web',
                'spring-boot-starter-data-jpa',
                'mysql-connector-java',
                'spring-boot-starter-validation',
                'spring-boot-starter-test',
            ],
            'python' => [
                'fastapi',
                'sqlalchemy',
                'pydantic',
                'uvicorn',
                'pymysql',
                'pytest',
            ],
            'javascript' => [
                'express',
                'sequelize',
                'mysql2',
                'joi',
                'jest',
                'supertest',
            ],
            'typescript' => [
                '@nestjs/core',
                '@nestjs/common',
                '@nestjs/typeorm',
                'typeorm',
                'mysql2',
                'class-validator',
                '@nestjs/testing',
            ],
            'csharp' => [
                'Microsoft.AspNetCore.App',
                'Microsoft.EntityFrameworkCore',
                'Pomelo.EntityFrameworkCore.MySql',
                'Microsoft.AspNetCore.Mvc.Testing',
                'xunit',
            ],
            'go' => [
                'github.com/gin-gonic/gin',
                'gorm.io/gorm',
                'gorm.io/driver/mysql',
                'github.com/go-playground/validator/v10',
                'github.com/stretchr/testify',
            ],
        ];

        return $dependencies[$language] ?? [];
    }

    /**
     * 获取语言特性
     */
    protected function getLanguageFeatures(string $language, array $tableInfo): array
    {
        $features = [
            'java' => [
                'annotations' => true,
                'generics' => true,
                'interfaces' => true,
                'inheritance' => true,
                'dependency_injection' => true,
                'orm_support' => 'JPA/Hibernate',
                'validation' => 'Bean Validation',
                'testing' => 'JUnit',
            ],
            'python' => [
                'type_hints' => true,
                'decorators' => true,
                'context_managers' => true,
                'async_await' => true,
                'orm_support' => 'SQLAlchemy',
                'validation' => 'Pydantic',
                'testing' => 'pytest',
            ],
            'javascript' => [
                'async_await' => true,
                'promises' => true,
                'destructuring' => true,
                'arrow_functions' => true,
                'orm_support' => 'Sequelize',
                'validation' => 'Joi',
                'testing' => 'Jest',
            ],
            'typescript' => [
                'static_typing' => true,
                'interfaces' => true,
                'generics' => true,
                'decorators' => true,
                'async_await' => true,
                'orm_support' => 'TypeORM',
                'validation' => 'class-validator',
                'testing' => 'Jest',
            ],
            'csharp' => [
                'static_typing' => true,
                'generics' => true,
                'linq' => true,
                'async_await' => true,
                'attributes' => true,
                'orm_support' => 'Entity Framework',
                'validation' => 'Data Annotations',
                'testing' => 'xUnit',
            ],
            'go' => [
                'static_typing' => true,
                'interfaces' => true,
                'goroutines' => true,
                'channels' => true,
                'struct_tags' => true,
                'orm_support' => 'GORM',
                'validation' => 'validator',
                'testing' => 'testing package',
            ],
        ];

        return $features[$language] ?? [];
    }

    /**
     * 生成语言映射
     */
    protected function generateLanguageMapping(array $tableInfo): array
    {
        $modelName = $this->getModelName($tableInfo['name']);
        
        return [
            'table_name' => $tableInfo['name'],
            'model_names' => [
                'php' => $modelName,
                'java' => $modelName,
                'python' => $this->toSnakeCase($modelName),
                'javascript' => $modelName,
                'typescript' => $modelName,
                'csharp' => $modelName,
                'go' => $modelName,
            ],
            'file_names' => [
                'php' => $modelName . '.php',
                'java' => $modelName . '.java',
                'python' => $this->toSnakeCase($modelName) . '.py',
                'javascript' => $this->toKebabCase($modelName) . '.js',
                'typescript' => $this->toKebabCase($modelName) . '.ts',
                'csharp' => $modelName . '.cs',
                'go' => $this->toSnakeCase($modelName) . '.go',
            ],
        ];
    }

    /**
     * 推荐框架
     */
    protected function recommendFrameworks(array $targetLanguages): array
    {
        $recommendations = [];
        
        foreach ($targetLanguages as $language) {
            $frameworks = $this->frameworks[$language] ?? [];
            $recommended = $this->getRecommendedFramework($language);
            
            $recommendations[$language] = [
                'recommended' => $recommended,
                'alternatives' => array_filter($frameworks, fn($f) => $f !== $recommended),
                'reasons' => $this->getFrameworkReasons($language, $recommended),
            ];
        }
        
        return $recommendations;
    }

    /**
     * 生成迁移计划
     */
    protected function generateMigrationPlan(array $targetLanguages, array $tableInfo): array
    {
        $plan = [
            'phases' => [],
            'timeline' => [],
            'dependencies' => [],
            'risks' => [],
        ];

        // 迁移阶段
        foreach ($targetLanguages as $index => $language) {
            $phase = "phase" . ($index + 1);
            $plan['phases'][$phase] = [
                'language' => $language,
                'framework' => $this->getRecommendedFramework($language),
                'tasks' => [
                    '设置项目结构',
                    '生成模型代码',
                    '生成控制器代码',
                    '配置数据库连接',
                    '编写测试用例',
                    '部署和验证',
                ],
                'estimated_duration' => $this->getEstimatedDuration($language),
            ];
        }

        return $plan;
    }

    /**
     * 获取默认值
     */
    protected function getDefaultValue(string $type, string $language): string
    {
        $defaults = [
            'java' => [
                'Integer' => 'null',
                'Long' => 'null',
                'String' => 'null',
                'Boolean' => 'null',
                'Double' => 'null',
                'BigDecimal' => 'null',
                'LocalDate' => 'null',
                'LocalDateTime' => 'null',
            ],
            'python' => [
                'int' => 'None',
                'str' => 'None',
                'bool' => 'None',
                'float' => 'None',
                'date' => 'None',
                'datetime' => 'None',
                'dict' => 'None',
            ],
            'javascript' => [
                'number' => 'null',
                'string' => 'null',
                'boolean' => 'null',
                'Date' => 'null',
                'object' => 'null',
            ],
        ];

        return $defaults[$language][$type] ?? 'null';
    }

    /**
     * 获取框架推荐理由
     */
    protected function getFrameworkReasons(string $language, string $framework): array
    {
        $reasons = [
            'java' => [
                'spring_boot' => ['生态成熟', '企业级支持', '自动配置', '微服务友好'],
            ],
            'python' => [
                'fastapi' => ['高性能', '自动文档', '类型提示', '现代化设计'],
            ],
            'javascript' => [
                'express' => ['简单易用', '生态丰富', '社区活跃', '灵活性高'],
            ],
            'typescript' => [
                'nestjs' => ['企业级架构', '装饰器支持', '依赖注入', 'TypeScript原生'],
            ],
        ];

        return $reasons[$language][$framework] ?? [];
    }

    /**
     * 获取预估时间
     */
    protected function getEstimatedDuration(string $language): string
    {
        $durations = [
            'java' => '3-5天',
            'python' => '2-3天',
            'javascript' => '2-3天',
            'typescript' => '3-4天',
            'csharp' => '3-5天',
            'go' => '2-4天',
        ];

        return $durations[$language] ?? '3-4天';
    }

    /**
     * 辅助方法
     */
    protected function getModelName(string $tableName): string
    {
        $name = preg_replace('/^[a-z]+_/', '', $tableName);
        return str_replace(' ', '', ucwords(str_replace('_', ' ', $name)));
    }

    protected function toSnakeCase(string $string): string
    {
        return strtolower(preg_replace('/(?<!^)[A-Z]/', '_$0', $string));
    }

    protected function toKebabCase(string $string): string
    {
        return strtolower(preg_replace('/(?<!^)[A-Z]/', '-$0', $string));
    }
}
