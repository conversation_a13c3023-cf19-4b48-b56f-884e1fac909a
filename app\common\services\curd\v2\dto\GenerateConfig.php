<?php

namespace app\common\services\curd\v2\dto;

/**
 * 生成配置数据传输对象
 */
class GenerateConfig
{
    protected TableInfo $tableInfo;
    protected array $options = [];
    
    // 生成文件配置
    protected bool $generateController = true;
    protected bool $generateModel = true;
    protected bool $generateView = true;
    protected bool $generateJs = true;
    
    // 功能配置
    protected bool $enableSoftDelete = false;
    protected bool $enableTimestamps = false;
    protected bool $enableExport = true;
    protected bool $enableBatch = true;
    protected bool $enableAuth = true;
    
    // 路径配置
    protected string $controllerNamespace = 'app\\admin\\controller';
    protected string $modelNamespace = 'app\\admin\\model';
    protected string $viewPath = 'app/admin/view/admin';
    protected string $jsPath = 'public/static/admin/js';

    public function getTableInfo(): TableInfo
    {
        return $this->tableInfo;
    }

    public function setTableInfo(TableInfo $tableInfo): self
    {
        $this->tableInfo = $tableInfo;
        return $this;
    }

    public function getOptions(): array
    {
        return $this->options;
    }

    public function setOptions(array $options): self
    {
        $this->options = $options;
        return $this;
    }

    public function getOption(string $key, $default = null)
    {
        return $this->options[$key] ?? $default;
    }

    public function setOption(string $key, $value): self
    {
        $this->options[$key] = $value;
        return $this;
    }

    // 生成文件配置
    public function isGenerateController(): bool
    {
        return $this->generateController;
    }

    public function setGenerateController(bool $generateController): self
    {
        $this->generateController = $generateController;
        return $this;
    }

    public function isGenerateModel(): bool
    {
        return $this->generateModel;
    }

    public function setGenerateModel(bool $generateModel): self
    {
        $this->generateModel = $generateModel;
        return $this;
    }

    public function isGenerateView(): bool
    {
        return $this->generateView;
    }

    public function setGenerateView(bool $generateView): self
    {
        $this->generateView = $generateView;
        return $this;
    }

    public function isGenerateJs(): bool
    {
        return $this->generateJs;
    }

    public function setGenerateJs(bool $generateJs): self
    {
        $this->generateJs = $generateJs;
        return $this;
    }

    // 功能配置
    public function isEnableSoftDelete(): bool
    {
        return $this->enableSoftDelete;
    }

    public function setEnableSoftDelete(bool $enableSoftDelete): self
    {
        $this->enableSoftDelete = $enableSoftDelete;
        return $this;
    }

    public function isEnableTimestamps(): bool
    {
        return $this->enableTimestamps;
    }

    public function setEnableTimestamps(bool $enableTimestamps): self
    {
        $this->enableTimestamps = $enableTimestamps;
        return $this;
    }

    public function isEnableExport(): bool
    {
        return $this->enableExport;
    }

    public function setEnableExport(bool $enableExport): self
    {
        $this->enableExport = $enableExport;
        return $this;
    }

    public function isEnableBatch(): bool
    {
        return $this->enableBatch;
    }

    public function setEnableBatch(bool $enableBatch): self
    {
        $this->enableBatch = $enableBatch;
        return $this;
    }

    public function isEnableAuth(): bool
    {
        return $this->enableAuth;
    }

    public function setEnableAuth(bool $enableAuth): self
    {
        $this->enableAuth = $enableAuth;
        return $this;
    }

    // 路径配置
    public function getControllerNamespace(): string
    {
        return $this->controllerNamespace;
    }

    public function setControllerNamespace(string $controllerNamespace): self
    {
        $this->controllerNamespace = $controllerNamespace;
        return $this;
    }

    public function getModelNamespace(): string
    {
        return $this->modelNamespace;
    }

    public function setModelNamespace(string $modelNamespace): self
    {
        $this->modelNamespace = $modelNamespace;
        return $this;
    }

    public function getViewPath(): string
    {
        return $this->viewPath;
    }

    public function setViewPath(string $viewPath): self
    {
        $this->viewPath = $viewPath;
        return $this;
    }

    public function getJsPath(): string
    {
        return $this->jsPath;
    }

    public function setJsPath(string $jsPath): self
    {
        $this->jsPath = $jsPath;
        return $this;
    }

    /**
     * 获取控制器类名
     */
    public function getControllerClassName(): string
    {
        return ucfirst(camel_case($this->tableInfo->getName())) . 'Controller';
    }

    /**
     * 获取模型类名
     */
    public function getModelClassName(): string
    {
        return ucfirst(camel_case($this->tableInfo->getName()));
    }

    /**
     * 获取控制器文件路径
     */
    public function getControllerFilePath(): string
    {
        $path = str_replace('\\', '/', $this->controllerNamespace);
        $path = str_replace('app/', '', $path);
        return $path . '/' . $this->getControllerClassName() . '.php';
    }

    /**
     * 获取模型文件路径
     */
    public function getModelFilePath(): string
    {
        $path = str_replace('\\', '/', $this->modelNamespace);
        $path = str_replace('app/', '', $path);
        return $path . '/' . $this->getModelClassName() . '.php';
    }

    /**
     * 转换为数组
     */
    public function toArray(): array
    {
        return [
            'table_info' => $this->tableInfo->toArray(),
            'options' => $this->options,
            'generate_controller' => $this->generateController,
            'generate_model' => $this->generateModel,
            'generate_view' => $this->generateView,
            'generate_js' => $this->generateJs,
            'enable_soft_delete' => $this->enableSoftDelete,
            'enable_timestamps' => $this->enableTimestamps,
            'enable_export' => $this->enableExport,
            'enable_batch' => $this->enableBatch,
            'enable_auth' => $this->enableAuth,
            'controller_namespace' => $this->controllerNamespace,
            'model_namespace' => $this->modelNamespace,
            'view_path' => $this->viewPath,
            'js_path' => $this->jsPath,
        ];
    }
}
