<?php

namespace app\common\services\curd\v2\engines;

use app\common\services\curd\v2\dto\GenerateConfig;
use app\common\services\curd\v2\dto\FieldInfo;

/**
 * 模板引擎
 * 负责渲染代码模板，生成最终的代码文件
 */
class TemplateEngine
{
    protected string $templatePath;
    protected array $templateCache = [];

    public function __construct(string $templatePath = null)
    {
        $this->templatePath = $templatePath ?: app_path() . '/common/services/curd/v2/templates';
    }

    /**
     * 渲染所有模板
     */
    public function render(GenerateConfig $config): array
    {
        $files = [];
        
        if ($config->isGenerateController()) {
            $files['controller'] = $this->renderController($config);
        }
        
        if ($config->isGenerateModel()) {
            $files['model'] = $this->renderModel($config);
        }
        
        if ($config->isGenerateView()) {
            $files['view_index'] = $this->renderViewIndex($config);
            $files['view_form'] = $this->renderViewForm($config);
        }
        
        if ($config->isGenerateJs()) {
            $files['js'] = $this->renderJs($config);
        }
        
        return $files;
    }

    /**
     * 渲染控制器
     */
    protected function renderController(GenerateConfig $config): string
    {
        $tableInfo = $config->getTableInfo();
        $className = $config->getControllerClassName();
        $modelClass = $config->getModelClassName();
        
        $variables = [
            'namespace' => $config->getControllerNamespace(),
            'className' => $className,
            'modelClass' => $modelClass,
            'modelNamespace' => $config->getModelNamespace(),
            'tableName' => $tableInfo->getName(),
            'tableComment' => $tableInfo->getComment(),
            'primaryKey' => $tableInfo->getPrimaryKey()?->getName() ?? 'id',
            'searchableFields' => $tableInfo->getSearchableFields(),
            'enableSoftDelete' => $config->isEnableSoftDelete(),
            'enableExport' => $config->isEnableExport(),
            'enableBatch' => $config->isEnableBatch(),
            'enableAuth' => $config->isEnableAuth(),
        ];
        
        return $this->renderTemplate('controller/controller.tpl', $variables);
    }

    /**
     * 渲染模型
     */
    protected function renderModel(GenerateConfig $config): string
    {
        $tableInfo = $config->getTableInfo();
        $className = $config->getModelClassName();
        
        $fillableFields = [];
        $casts = [];
        $dates = [];
        
        foreach ($tableInfo->getFields() as $field) {
            if (!$field->isPrimary() && $field->isShowInForm()) {
                $fillableFields[] = $field->getName();
            }
            
            // 类型转换
            switch ($field->getPhpType()) {
                case 'int':
                    $casts[$field->getName()] = 'integer';
                    break;
                case 'float':
                    $casts[$field->getName()] = 'float';
                    break;
                case 'bool':
                    $casts[$field->getName()] = 'boolean';
                    break;
            }
            
            // 日期字段
            if (in_array($field->getComponent(), ['date', 'datetime'])) {
                $dates[] = $field->getName();
            }
        }
        
        $variables = [
            'namespace' => $config->getModelNamespace(),
            'className' => $className,
            'tableName' => $tableInfo->getFullName(),
            'primaryKey' => $tableInfo->getPrimaryKey()?->getName() ?? 'id',
            'fillable' => $fillableFields,
            'casts' => $casts,
            'dates' => $dates,
            'enableTimestamps' => $config->isEnableTimestamps(),
            'enableSoftDelete' => $config->isEnableSoftDelete(),
        ];
        
        return $this->renderTemplate('model/model.tpl', $variables);
    }

    /**
     * 渲染列表视图
     */
    protected function renderViewIndex(GenerateConfig $config): string
    {
        $tableInfo = $config->getTableInfo();
        $listFields = $tableInfo->getListFields();
        $searchableFields = $tableInfo->getSearchableFields();
        
        $variables = [
            'tableName' => $tableInfo->getName(),
            'tableComment' => $tableInfo->getComment(),
            'listFields' => $listFields,
            'searchableFields' => $searchableFields,
            'primaryKey' => $tableInfo->getPrimaryKey()?->getName() ?? 'id',
            'enableExport' => $config->isEnableExport(),
            'enableBatch' => $config->isEnableBatch(),
        ];
        
        return $this->renderTemplate('view/index.tpl', $variables);
    }

    /**
     * 渲染表单视图
     */
    protected function renderViewForm(GenerateConfig $config): string
    {
        $tableInfo = $config->getTableInfo();
        $formFields = $tableInfo->getFormFields();
        
        $formHtml = '';
        foreach ($formFields as $field) {
            $formHtml .= $this->renderFormField($field);
        }
        
        $variables = [
            'tableName' => $tableInfo->getName(),
            'tableComment' => $tableInfo->getComment(),
            'formFields' => $formFields,
            'formHtml' => $formHtml,
        ];
        
        return $this->renderTemplate('view/form.tpl', $variables);
    }

    /**
     * 渲染表单字段
     */
    protected function renderFormField(FieldInfo $field): string
    {
        $component = $field->getComponent();
        $templateFile = "components/{$component}.tpl";
        
        $variables = [
            'field' => $field,
            'name' => $field->getName(),
            'label' => $field->getComment(),
            'required' => $field->isRequired(),
            'options' => $field->getOptions(),
        ];
        
        return $this->renderTemplate($templateFile, $variables);
    }

    /**
     * 渲染 JavaScript
     */
    protected function renderJs(GenerateConfig $config): string
    {
        $tableInfo = $config->getTableInfo();
        
        $variables = [
            'tableName' => $tableInfo->getName(),
            'tableComment' => $tableInfo->getComment(),
            'listFields' => $tableInfo->getListFields(),
            'searchableFields' => $tableInfo->getSearchableFields(),
            'enableExport' => $config->isEnableExport(),
            'enableBatch' => $config->isEnableBatch(),
        ];
        
        return $this->renderTemplate('js/index.tpl', $variables);
    }

    /**
     * 渲染模板
     */
    protected function renderTemplate(string $templateFile, array $variables = []): string
    {
        $templatePath = $this->templatePath . '/' . $templateFile;
        
        if (!file_exists($templatePath)) {
            throw new \Exception("模板文件不存在: {$templatePath}");
        }
        
        // 缓存模板内容
        if (!isset($this->templateCache[$templateFile])) {
            $this->templateCache[$templateFile] = file_get_contents($templatePath);
        }
        
        $template = $this->templateCache[$templateFile];
        
        // 替换变量
        foreach ($variables as $key => $value) {
            $placeholder = '{{' . $key . '}}';
            
            if (is_array($value)) {
                $value = $this->renderArray($value);
            } elseif (is_bool($value)) {
                $value = $value ? 'true' : 'false';
            } elseif (is_null($value)) {
                $value = 'null';
            }
            
            $template = str_replace($placeholder, $value, $template);
        }
        
        // 处理条件语句
        $template = $this->processConditions($template, $variables);
        
        // 处理循环语句
        $template = $this->processLoops($template, $variables);
        
        return $template;
    }

    /**
     * 渲染数组
     */
    protected function renderArray(array $array): string
    {
        if (empty($array)) {
            return '[]';
        }
        
        $items = [];
        foreach ($array as $key => $value) {
            if (is_string($key)) {
                $items[] = "'{$key}' => " . $this->formatValue($value);
            } else {
                $items[] = $this->formatValue($value);
            }
        }
        
        return '[' . implode(', ', $items) . ']';
    }

    /**
     * 格式化值
     */
    protected function formatValue($value): string
    {
        if (is_string($value)) {
            return "'{$value}'";
        } elseif (is_bool($value)) {
            return $value ? 'true' : 'false';
        } elseif (is_null($value)) {
            return 'null';
        } else {
            return (string)$value;
        }
    }

    /**
     * 处理条件语句
     */
    protected function processConditions(string $template, array $variables): string
    {
        // 处理 @if 条件
        $pattern = '/@if\s*\(\s*([^)]+)\s*\)(.*?)@endif/s';
        return preg_replace_callback($pattern, function($matches) use ($variables) {
            $condition = trim($matches[1]);
            $content = $matches[2];
            
            // 简单的条件判断
            if (isset($variables[$condition]) && $variables[$condition]) {
                return $content;
            }
            
            return '';
        }, $template);
    }

    /**
     * 处理循环语句
     */
    protected function processLoops(string $template, array $variables): string
    {
        // 处理 @foreach 循环
        $pattern = '/@foreach\s*\(\s*([^)]+)\s*\)(.*?)@endforeach/s';
        return preg_replace_callback($pattern, function($matches) use ($variables) {
            $loopDef = trim($matches[1]);
            $content = $matches[2];
            
            // 解析循环定义，如: $fields as $field
            if (preg_match('/\$(\w+)\s+as\s+\$(\w+)/', $loopDef, $loopMatches)) {
                $arrayVar = $loopMatches[1];
                $itemVar = $loopMatches[2];
                
                if (isset($variables[$arrayVar]) && is_array($variables[$arrayVar])) {
                    $result = '';
                    foreach ($variables[$arrayVar] as $item) {
                        $itemContent = $content;
                        // 替换循环变量
                        $itemContent = str_replace('{{' . $itemVar . '}}', $item, $itemContent);
                        $result .= $itemContent;
                    }
                    return $result;
                }
            }
            
            return '';
        }, $template);
    }

    /**
     * 获取可用模板
     */
    public function getAvailableTemplates(): array
    {
        $templates = [];
        $iterator = new \RecursiveIteratorIterator(
            new \RecursiveDirectoryIterator($this->templatePath)
        );
        
        foreach ($iterator as $file) {
            if ($file->isFile() && $file->getExtension() === 'tpl') {
                $relativePath = str_replace($this->templatePath . '/', '', $file->getPathname());
                $templates[] = $relativePath;
            }
        }
        
        return $templates;
    }
}
