<?php

namespace app\common\services;

use support\Cache;

/**
 * 缓存助手类
 * 提供类似 Laravel 的 remember 方法
 */
class CacheHelper
{
    /**
     * 获取缓存，如果不存在则执行回调并缓存结果
     *
     * @param string $key 缓存键
     * @param int $ttl 过期时间（秒）
     * @param callable $callback 回调函数
     * @return mixed
     */
    public static function remember(string $key, int $ttl, callable $callback)
    {
        $key = self::sanitizeKey($key);
        $value = Cache::get($key);

        if ($value === null) {
            $value = $callback();
            Cache::set($key, $value, $ttl);
        }

        return $value;
    }

    /**
     * 永久缓存（直到手动删除）
     *
     * @param string $key 缓存键
     * @param callable $callback 回调函数
     * @return mixed
     */
    public static function rememberForever(string $key, callable $callback)
    {
        $value = Cache::get($key);

        if ($value === null) {
            $value = $callback();
            Cache::set($key, $value, 0); // 0 表示永不过期
        }

        return $value;
    }

    /**
     * 获取或设置缓存
     *
     * @param string $key 缓存键
     * @param mixed $value 缓存值
     * @param int $ttl 过期时间
     * @return mixed
     */
    public static function get(string $key, $default = null)
    {
        $key = self::sanitizeKey($key);
        $value = Cache::get($key);
        return $value !== null ? $value : $default;
    }

    /**
     * 设置缓存
     *
     * @param string $key 缓存键
     * @param mixed $value 缓存值
     * @param int $ttl 过期时间
     * @return bool
     */
    public static function set(string $key, $value, int $ttl = 3600): bool
    {
        $key = self::sanitizeKey($key);
        return Cache::set($key, $value, $ttl);
    }

    /**
     * 删除缓存
     *
     * @param string $key 缓存键
     * @return bool
     */
    public static function forget(string $key): bool
    {
        $key = self::sanitizeKey($key);
        return Cache::delete($key);
    }

    /**
     * 批量删除缓存（通过前缀）
     * 注意：这个方法在不同的缓存驱动中可能表现不同
     *
     * @param string $prefix 缓存键前缀
     * @return bool
     */
    public static function forgetByPrefix(string $prefix): bool
    {
        // 这里需要根据具体的缓存驱动实现
        // 对于 Redis，可以使用 SCAN 命令
        // 对于文件缓存，需要遍历文件

        // 简单实现：假设使用 Redis
        try {
            $redis = Cache::store();
            if (method_exists($redis, 'keys')) {
                $keys = $redis->keys($prefix . '*');
                if (!empty($keys)) {
                    return $redis->del($keys) > 0;
                }
            }
        } catch (\Exception $e) {
            // 如果不支持，则静默失败
        }

        return true;
    }

    /**
     * 检查缓存是否存在
     *
     * @param string $key 缓存键
     * @return bool
     */
    public static function has(string $key): bool
    {
        $key = self::sanitizeKey($key);
        return Cache::get($key) !== null;
    }

    /**
     * 增加缓存值（仅适用于数值）
     *
     * @param string $key 缓存键
     * @param int $value 增加的值
     * @return int|false
     */
    public static function increment(string $key, int $value = 1)
    {
        $key = self::sanitizeKey($key);
        $current = (int)Cache::get($key, 0);
        $new = $current + $value;

        if (Cache::set($key, $new)) {
            return $new;
        }

        return false;
    }

    /**
     * 减少缓存值（仅适用于数值）
     *
     * @param string $key 缓存键
     * @param int $value 减少的值
     * @return int|false
     */
    public static function decrement(string $key, int $value = 1)
    {
        return self::increment($key, -$value);
    }

    /**
     * 获取多个缓存值
     *
     * @param array $keys 缓存键数组
     * @return array
     */
    public static function many(array $keys): array
    {
        $result = [];
        foreach ($keys as $key) {
            $sanitizedKey = self::sanitizeKey($key);
            $result[$key] = Cache::get($sanitizedKey);
        }
        return $result;
    }

    /**
     * 设置多个缓存值
     *
     * @param array $values 键值对数组
     * @param int $ttl 过期时间
     * @return bool
     */
    public static function putMany(array $values, int $ttl = 3600): bool
    {
        $success = true;
        foreach ($values as $key => $value) {
            $sanitizedKey = self::sanitizeKey($key);
            if (!Cache::set($sanitizedKey, $value, $ttl)) {
                $success = false;
            }
        }
        return $success;
    }

    /**
     * 清空所有缓存
     * 注意：这个操作很危险，请谨慎使用
     *
     * @return bool
     */
    public static function flush(): bool
    {
        try {
            return Cache::clear();
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * 获取缓存标签（如果支持）
     *
     * @param array $tags 标签数组
     * @return self
     */
    public static function tags(array $tags): self
    {
        // 这里可以实现标签缓存的逻辑
        // 目前返回自身以支持链式调用
        return new self();
    }

    /**
     * 清理缓存键，移除不允许的字符
     * Symfony Cache 不允许这些字符: {}()/\@:
     *
     * @param string $key 原始缓存键
     * @return string 清理后的缓存键
     */
    protected static function sanitizeKey(string $key): string
    {
        // 替换不允许的字符为下划线
        $sanitized = preg_replace('/[{}()\\/\\\\@:]/', '_', $key);

        // 确保键不为空且不超过长度限制
        if (empty($sanitized)) {
            $sanitized = 'cache_key';
        }

        // 限制长度（PSR-16 建议最大64字符）
        if (strlen($sanitized) > 64) {
            $sanitized = substr($sanitized, 0, 60) . '_' . crc32($key);
        }

        return $sanitized;
    }
}
