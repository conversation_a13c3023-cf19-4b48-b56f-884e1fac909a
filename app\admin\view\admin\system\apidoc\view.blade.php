@extends('admin.layouts.app')

@section('content')
<div class="container-fluid">
    <!-- 页面标题 -->
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <h4 class="page-title">{{ $page_title }}</h4>
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        @foreach($breadcrumb as $item)
                            <li class="breadcrumb-item">
                                @if($item['url'])
                                    <a href="{{ $item['url'] }}">{{ $item['title'] }}</a>
                                @else
                                    {{ $item['title'] }}
                                @endif
                            </li>
                        @endforeach
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- API文档头部信息 -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h3 class="mb-1">
                                <i class="mdi mdi-api text-primary"></i>
                                {{ $api_doc['title'] }}
                            </h3>
                            <p class="text-muted mb-2">{{ $api_doc['description'] }}</p>
                            <div class="d-flex align-items-center">
                                <span class="badge badge-soft-primary me-2">版本 {{ $api_doc['version'] }}</span>
                                <span class="badge badge-soft-info me-2">基础URL: {{ $api_doc['base_url'] }}</span>
                                <span class="badge badge-soft-success">{{ count($api_doc['endpoints']) }}个接口</span>
                            </div>
                        </div>
                        <div class="col-md-4 text-end">
                            <div class="btn-group" role="group">
                                <button type="button" class="btn btn-primary" onclick="testAllApis()">
                                    <i class="mdi mdi-play"></i> 测试所有接口
                                </button>
                                <div class="btn-group" role="group">
                                    <button type="button" class="btn btn-success dropdown-toggle" data-bs-toggle="dropdown">
                                        <i class="mdi mdi-download"></i> 导出文档
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item" href="javascript:void(0)" onclick="exportDoc('html')">HTML格式</a></li>
                                        <li><a class="dropdown-item" href="javascript:void(0)" onclick="exportDoc('markdown')">Markdown格式</a></li>
                                        <li><a class="dropdown-item" href="javascript:void(0)" onclick="exportDoc('json')">JSON格式</a></li>
                                        <li><a class="dropdown-item" href="javascript:void(0)" onclick="exportDoc('pdf')">PDF格式</a></li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- API接口列表 -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <h4 class="header-title mb-3">
                        <i class="mdi mdi-format-list-bulleted"></i> 接口列表
                    </h4>

                    <div class="accordion" id="apiAccordion">
                        @foreach($api_doc['endpoints'] as $index => $endpoint)
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="heading{{ $index }}">
                                <button class="accordion-button @if($index !== 0) collapsed @endif" type="button" 
                                        data-bs-toggle="collapse" data-bs-target="#collapse{{ $index }}" 
                                        aria-expanded="@if($index === 0) true @else false @endif" 
                                        aria-controls="collapse{{ $index }}">
                                    <div class="d-flex align-items-center w-100">
                                        <span class="badge badge-method-{{ strtolower($endpoint['method']) }} me-3">
                                            {{ $endpoint['method'] }}
                                        </span>
                                        <span class="me-3"><code>{{ $endpoint['path'] }}</code></span>
                                        <span class="text-muted">{{ $endpoint['summary'] }}</span>
                                        <div class="ms-auto me-3">
                                            <button type="button" class="btn btn-sm btn-soft-primary" 
                                                    onclick="testSingleApi('{{ $endpoint['method'] }}', '{{ $endpoint['path'] }}', event)">
                                                <i class="mdi mdi-play"></i> 测试
                                            </button>
                                        </div>
                                    </div>
                                </button>
                            </h2>
                            <div id="collapse{{ $index }}" class="accordion-collapse collapse @if($index === 0) show @endif" 
                                 aria-labelledby="heading{{ $index }}" data-bs-parent="#apiAccordion">
                                <div class="accordion-body">
                                    <div class="row">
                                        <!-- 接口信息 -->
                                        <div class="col-md-6">
                                            <h6 class="mb-3">接口信息</h6>
                                            <table class="table table-sm">
                                                <tr>
                                                    <td width="80"><strong>方法:</strong></td>
                                                    <td><span class="badge badge-method-{{ strtolower($endpoint['method']) }}">{{ $endpoint['method'] }}</span></td>
                                                </tr>
                                                <tr>
                                                    <td><strong>路径:</strong></td>
                                                    <td><code>{{ $endpoint['path'] }}</code></td>
                                                </tr>
                                                <tr>
                                                    <td><strong>描述:</strong></td>
                                                    <td>{{ $endpoint['description'] }}</td>
                                                </tr>
                                            </table>

                                            @if(!empty($endpoint['parameters']))
                                            <h6 class="mb-3 mt-4">请求参数</h6>
                                            <div class="table-responsive">
                                                <table class="table table-sm table-bordered">
                                                    <thead class="table-light">
                                                        <tr>
                                                            <th>参数名</th>
                                                            <th>类型</th>
                                                            <th>必填</th>
                                                            <th>描述</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        @foreach($endpoint['parameters'] as $param)
                                                        <tr>
                                                            <td><code>{{ $param['name'] }}</code></td>
                                                            <td><span class="badge badge-soft-info">{{ $param['type'] }}</span></td>
                                                            <td>
                                                                @if(isset($param['required']) && $param['required'])
                                                                    <span class="badge badge-soft-danger">是</span>
                                                                @else
                                                                    <span class="badge badge-soft-secondary">否</span>
                                                                @endif
                                                            </td>
                                                            <td>{{ $param['description'] }}</td>
                                                        </tr>
                                                        @endforeach
                                                    </tbody>
                                                </table>
                                            </div>
                                            @endif
                                        </div>

                                        <!-- 响应示例 -->
                                        <div class="col-md-6">
                                            <h6 class="mb-3">响应示例</h6>
                                            @foreach($endpoint['responses'] as $code => $response)
                                            <div class="mb-3">
                                                <h6 class="mb-2">
                                                    <span class="badge badge-response-{{ substr($code, 0, 1) }}">{{ $code }}</span>
                                                    {{ $response['description'] }}
                                                </h6>
                                                <pre class="bg-light p-3 rounded"><code class="language-json">{{ json_encode($response['example'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) }}</code></pre>
                                            </div>
                                            @endforeach
                                        </div>
                                    </div>

                                    <!-- API测试区域 -->
                                    <div class="row mt-4">
                                        <div class="col-12">
                                            <div class="card bg-light">
                                                <div class="card-header">
                                                    <h6 class="mb-0">
                                                        <i class="mdi mdi-play"></i> 在线测试
                                                    </h6>
                                                </div>
                                                <div class="card-body">
                                                    <form class="api-test-form" data-method="{{ $endpoint['method'] }}" data-path="{{ $endpoint['path'] }}">
                                                        @if(!empty($endpoint['parameters']))
                                                        <div class="row">
                                                            @foreach($endpoint['parameters'] as $param)
                                                            <div class="col-md-6 mb-3">
                                                                <label class="form-label">
                                                                    {{ $param['name'] }}
                                                                    @if(isset($param['required']) && $param['required'])
                                                                        <span class="text-danger">*</span>
                                                                    @endif
                                                                </label>
                                                                <input type="text" class="form-control" 
                                                                       name="{{ $param['name'] }}" 
                                                                       placeholder="{{ $param['description'] }}"
                                                                       @if(isset($param['required']) && $param['required']) required @endif>
                                                                <small class="text-muted">类型: {{ $param['type'] }}</small>
                                                            </div>
                                                            @endforeach
                                                        </div>
                                                        @endif
                                                        
                                                        <div class="d-flex justify-content-between align-items-center">
                                                            <button type="button" class="btn btn-primary" onclick="executeApiTest(this)">
                                                                <i class="mdi mdi-send"></i> 发送请求
                                                            </button>
                                                            <button type="button" class="btn btn-secondary" onclick="clearTestForm(this)">
                                                                <i class="mdi mdi-refresh"></i> 清空
                                                            </button>
                                                        </div>
                                                    </form>
                                                    
                                                    <!-- 测试结果 -->
                                                    <div class="test-result mt-3" style="display: none;">
                                                        <h6>测试结果:</h6>
                                                        <div class="result-content"></div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('style')
<style>
/* HTTP方法标签样式 */
.badge-method-get { background-color: #28a745; }
.badge-method-post { background-color: #007bff; }
.badge-method-put { background-color: #ffc107; color: #000; }
.badge-method-delete { background-color: #dc3545; }
.badge-method-patch { background-color: #6f42c1; }

/* 响应状态码标签样式 */
.badge-response-2 { background-color: #28a745; }
.badge-response-3 { background-color: #17a2b8; }
.badge-response-4 { background-color: #ffc107; color: #000; }
.badge-response-5 { background-color: #dc3545; }

/* 代码块样式 */
pre {
    max-height: 300px;
    overflow-y: auto;
}

/* 手风琴样式优化 */
.accordion-button:not(.collapsed) {
    background-color: #f8f9fa;
    border-color: #dee2e6;
}

.accordion-button:focus {
    box-shadow: none;
    border-color: #dee2e6;
}

/* 测试结果样式 */
.test-result .result-content {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    padding: 1rem;
    font-family: 'Courier New', monospace;
    font-size: 0.875rem;
}

.test-result .success {
    border-left: 4px solid #28a745;
}

.test-result .error {
    border-left: 4px solid #dc3545;
}
</style>
@endsection

@section('script')
<script>
// 测试单个API
function testSingleApi(method, path, event) {
    event.stopPropagation();
    // 这里可以实现单个API测试逻辑
    toastr.info(`测试 ${method} ${path}`);
}

// 测试所有API
function testAllApis() {
    if (confirm('确定要测试所有API接口吗？这可能需要一些时间。')) {
        // 这里可以实现批量API测试逻辑
        toastr.info('批量测试功能开发中...');
    }
}

// 导出文档
function exportDoc(format) {
    const tableName = '{{ $table_name }}';
    const url = `/admin/system/apidoc/export?table=${tableName}&format=${format}`;
    window.open(url, '_blank');
}

// 执行API测试
function executeApiTest(button) {
    const form = button.closest('.api-test-form');
    const method = form.dataset.method;
    const path = form.dataset.path;
    const resultDiv = form.parentElement.querySelector('.test-result');
    const resultContent = resultDiv.querySelector('.result-content');
    
    // 收集表单数据
    const formData = new FormData(form);
    const params = {};
    for (let [key, value] of formData.entries()) {
        if (value.trim() !== '') {
            params[key] = value;
        }
    }
    
    // 显示加载状态
    const originalText = button.innerHTML;
    button.innerHTML = '<i class="mdi mdi-loading mdi-spin"></i> 测试中...';
    button.disabled = true;
    
    // 发送测试请求
    fetch('/admin/system/apidoc/test', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: JSON.stringify({
            table_name: '{{ $table_name }}',
            endpoint: path,
            method: method,
            params: params
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.code === 200) {
            resultContent.innerHTML = `
                <div class="success">
                    <h6 class="text-success mb-2">✓ 测试成功</h6>
                    <p><strong>响应时间:</strong> ${data.data.response_time}</p>
                    <p><strong>状态码:</strong> ${data.data.status_code}</p>
                    <p><strong>响应内容:</strong></p>
                    <pre>${JSON.stringify(data.data.response_body, null, 2)}</pre>
                </div>
            `;
            resultContent.className = 'result-content success';
        } else {
            resultContent.innerHTML = `
                <div class="error">
                    <h6 class="text-danger mb-2">✗ 测试失败</h6>
                    <p>${data.msg}</p>
                </div>
            `;
            resultContent.className = 'result-content error';
        }
        resultDiv.style.display = 'block';
    })
    .catch(error => {
        console.error('Error:', error);
        resultContent.innerHTML = `
            <div class="error">
                <h6 class="text-danger mb-2">✗ 请求失败</h6>
                <p>网络错误或服务器异常</p>
            </div>
        `;
        resultContent.className = 'result-content error';
        resultDiv.style.display = 'block';
    })
    .finally(() => {
        button.innerHTML = originalText;
        button.disabled = false;
    });
}

// 清空测试表单
function clearTestForm(button) {
    const form = button.closest('.api-test-form');
    form.reset();
    
    const resultDiv = form.parentElement.querySelector('.test-result');
    resultDiv.style.display = 'none';
}

// 页面加载完成后的初始化
document.addEventListener('DOMContentLoaded', function() {
    // 初始化代码高亮
    if (typeof Prism !== 'undefined') {
        Prism.highlightAll();
    }
    
    // 阻止手风琴内部按钮触发折叠
    document.querySelectorAll('.accordion-body button').forEach(button => {
        button.addEventListener('click', function(e) {
            e.stopPropagation();
        });
    });
});
</script>
@endsection
