<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

/**
 * API文档表迁移
 */
class CreateApiDocsTable extends Migration
{
    /**
     * 运行迁移
     */
    public function up()
    {
        Schema::create('api_docs', function (Blueprint $table) {
            $table->id();
            
            // 基础信息
            $table->string('table_name', 100)->comment('数据表名');
            $table->string('display_name', 200)->comment('显示名称');
            $table->text('description')->nullable()->comment('描述');
            $table->string('version', 20)->default('1.0.0')->comment('版本号');
            $table->string('base_url', 200)->default('/api')->comment('基础URL');
            $table->string('auth_type', 20)->default('jwt')->comment('认证类型');
            
            // JSON数据
            $table->json('endpoints')->comment('API接口列表');
            $table->json('metadata')->nullable()->comment('元数据');
            $table->json('options')->nullable()->comment('生成选项');
            
            // 状态和分享
            $table->tinyInteger('status')->default(1)->comment('状态：0停用 1正常 2草稿');
            $table->string('share_token', 64)->nullable()->comment('分享令牌');
            $table->timestamp('share_expires_at')->nullable()->comment('分享过期时间');
            
            // 统计信息
            $table->unsignedInteger('view_count')->default(0)->comment('查看次数');
            $table->unsignedInteger('test_count')->default(0)->comment('测试次数');
            $table->unsignedInteger('export_count')->default(0)->comment('导出次数');
            
            // 创建和更新信息
            $table->unsignedBigInteger('created_by')->default(0)->comment('创建者ID');
            $table->unsignedBigInteger('updated_by')->default(0)->comment('更新者ID');
            $table->timestamps();
            
            // 索引
            $table->unique('table_name', 'uk_table_name');
            $table->index('status', 'idx_status');
            $table->index('created_by', 'idx_created_by');
            $table->index('share_token', 'idx_share_token');
            $table->index('created_at', 'idx_created_at');
            $table->index(['status', 'created_at'], 'idx_status_created');
        });
        
        // 添加表注释
        DB::statement("ALTER TABLE `api_docs` COMMENT = 'API文档表'");
    }

    /**
     * 回滚迁移
     */
    public function down()
    {
        Schema::dropIfExists('api_docs');
    }
}

/**
 * API测试历史表迁移
 */
class CreateApiTestHistoryTable extends Migration
{
    /**
     * 运行迁移
     */
    public function up()
    {
        Schema::create('api_test_history', function (Blueprint $table) {
            $table->id();
            
            // 关联信息
            $table->unsignedBigInteger('api_doc_id')->comment('API文档ID');
            $table->string('endpoint', 200)->comment('接口路径');
            $table->string('method', 10)->comment('HTTP方法');
            
            // 请求信息
            $table->json('request_params')->nullable()->comment('请求参数');
            $table->json('request_headers')->nullable()->comment('请求头');
            $table->text('request_body')->nullable()->comment('请求体');
            
            // 响应信息
            $table->integer('response_status')->comment('响应状态码');
            $table->json('response_headers')->nullable()->comment('响应头');
            $table->text('response_body')->nullable()->comment('响应体');
            $table->integer('response_time')->comment('响应时间(毫秒)');
            
            // 测试结果
            $table->tinyInteger('test_result')->comment('测试结果：0失败 1成功');
            $table->text('error_message')->nullable()->comment('错误信息');
            
            // 环境信息
            $table->string('environment', 50)->default('development')->comment('测试环境');
            $table->string('user_agent', 500)->nullable()->comment('用户代理');
            $table->string('ip_address', 45)->nullable()->comment('IP地址');
            
            // 创建信息
            $table->unsignedBigInteger('created_by')->default(0)->comment('测试者ID');
            $table->timestamp('created_at')->useCurrent()->comment('创建时间');
            
            // 索引
            $table->index('api_doc_id', 'idx_api_doc_id');
            $table->index(['endpoint', 'method'], 'idx_endpoint_method');
            $table->index('test_result', 'idx_test_result');
            $table->index('created_by', 'idx_created_by');
            $table->index('created_at', 'idx_created_at');
            
            // 外键约束
            $table->foreign('api_doc_id')->references('id')->on('api_docs')->onDelete('cascade');
        });
        
        // 添加表注释
        DB::statement("ALTER TABLE `api_test_history` COMMENT = 'API测试历史表'");
    }

    /**
     * 回滚迁移
     */
    public function down()
    {
        Schema::dropIfExists('api_test_history');
    }
}

/**
 * API文档分享记录表迁移
 */
class CreateApiDocShareTable extends Migration
{
    /**
     * 运行迁移
     */
    public function up()
    {
        Schema::create('api_doc_shares', function (Blueprint $table) {
            $table->id();
            
            // 关联信息
            $table->unsignedBigInteger('api_doc_id')->comment('API文档ID');
            $table->string('share_token', 64)->comment('分享令牌');
            $table->string('share_name', 200)->nullable()->comment('分享名称');
            $table->text('share_description')->nullable()->comment('分享描述');
            
            // 分享配置
            $table->tinyInteger('is_public')->default(1)->comment('是否公开：0否 1是');
            $table->string('password', 100)->nullable()->comment('访问密码');
            $table->timestamp('expires_at')->nullable()->comment('过期时间');
            $table->json('permissions')->nullable()->comment('权限配置');
            
            // 访问统计
            $table->unsignedInteger('view_count')->default(0)->comment('访问次数');
            $table->timestamp('last_accessed_at')->nullable()->comment('最后访问时间');
            $table->string('last_accessed_ip', 45)->nullable()->comment('最后访问IP');
            
            // 状态
            $table->tinyInteger('status')->default(1)->comment('状态：0停用 1正常');
            
            // 创建信息
            $table->unsignedBigInteger('created_by')->default(0)->comment('创建者ID');
            $table->timestamp('created_at')->useCurrent()->comment('创建时间');
            $table->timestamp('updated_at')->useCurrent()->useCurrentOnUpdate()->comment('更新时间');
            
            // 索引
            $table->index('api_doc_id', 'idx_api_doc_id');
            $table->unique('share_token', 'uk_share_token');
            $table->index('status', 'idx_status');
            $table->index('created_by', 'idx_created_by');
            $table->index('expires_at', 'idx_expires_at');
            
            // 外键约束
            $table->foreign('api_doc_id')->references('id')->on('api_docs')->onDelete('cascade');
        });
        
        // 添加表注释
        DB::statement("ALTER TABLE `api_doc_shares` COMMENT = 'API文档分享记录表'");
    }

    /**
     * 回滚迁移
     */
    public function down()
    {
        Schema::dropIfExists('api_doc_shares');
    }
}

/**
 * API文档模板表迁移
 */
class CreateApiDocTemplatesTable extends Migration
{
    /**
     * 运行迁移
     */
    public function up()
    {
        Schema::create('api_doc_templates', function (Blueprint $table) {
            $table->id();
            
            // 基础信息
            $table->string('name', 100)->comment('模板名称');
            $table->string('display_name', 200)->comment('显示名称');
            $table->text('description')->nullable()->comment('模板描述');
            $table->string('type', 50)->default('custom')->comment('模板类型');
            $table->string('format', 20)->default('html')->comment('输出格式');
            
            // 模板内容
            $table->longText('template_content')->comment('模板内容');
            $table->json('template_config')->nullable()->comment('模板配置');
            $table->json('sample_data')->nullable()->comment('示例数据');
            
            // 状态和权限
            $table->tinyInteger('status')->default(1)->comment('状态：0停用 1正常');
            $table->tinyInteger('is_system')->default(0)->comment('是否系统模板：0否 1是');
            $table->tinyInteger('is_public')->default(0)->comment('是否公开：0否 1是');
            
            // 使用统计
            $table->unsignedInteger('use_count')->default(0)->comment('使用次数');
            $table->timestamp('last_used_at')->nullable()->comment('最后使用时间');
            
            // 版本信息
            $table->string('version', 20)->default('1.0.0')->comment('版本号');
            $table->unsignedBigInteger('parent_id')->default(0)->comment('父模板ID');
            
            // 创建和更新信息
            $table->unsignedBigInteger('created_by')->default(0)->comment('创建者ID');
            $table->unsignedBigInteger('updated_by')->default(0)->comment('更新者ID');
            $table->timestamps();
            
            // 索引
            $table->unique('name', 'uk_name');
            $table->index('type', 'idx_type');
            $table->index('format', 'idx_format');
            $table->index('status', 'idx_status');
            $table->index('is_system', 'idx_is_system');
            $table->index('created_by', 'idx_created_by');
        });
        
        // 添加表注释
        DB::statement("ALTER TABLE `api_doc_templates` COMMENT = 'API文档模板表'");
    }

    /**
     * 回滚迁移
     */
    public function down()
    {
        Schema::dropIfExists('api_doc_templates');
    }
}

/**
 * 执行所有迁移
 */
function runApiDocMigrations()
{
    try {
        // 创建API文档表
        $apiDocsTable = new CreateApiDocsTable();
        $apiDocsTable->up();
        echo "✅ API文档表创建成功\n";
        
        // 创建API测试历史表
        $testHistoryTable = new CreateApiTestHistoryTable();
        $testHistoryTable->up();
        echo "✅ API测试历史表创建成功\n";
        
        // 创建API文档分享记录表
        $shareTable = new CreateApiDocShareTable();
        $shareTable->up();
        echo "✅ API文档分享记录表创建成功\n";
        
        // 创建API文档模板表
        $templatesTable = new CreateApiDocTemplatesTable();
        $templatesTable->up();
        echo "✅ API文档模板表创建成功\n";
        
        echo "\n🎉 所有API文档相关表创建完成！\n";
        
    } catch (Exception $e) {
        echo "❌ 迁移失败：" . $e->getMessage() . "\n";
        return false;
    }
    
    return true;
}

// 如果直接运行此文件，则执行迁移
if (basename(__FILE__) === basename($_SERVER['SCRIPT_NAME'])) {
    runApiDocMigrations();
}
