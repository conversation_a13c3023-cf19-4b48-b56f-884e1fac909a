define(["jquery", "easy-admin"], function ($, ea) {

    var init = {
        table_elem: '#currentTable',
        table_render_id: 'currentTableRenderId',
        index_url: '{{tableName}}/index',
        add_url: '{{tableName}}/add',
        edit_url: '{{tableName}}/edit',
        delete_url: '{{tableName}}/delete',
@if(enableExport)
        export_url: '{{tableName}}/export',
@endif
@if(enableBatch)
        batch_url: '{{tableName}}/batch',
@endif
        modify_url: '{{tableName}}/modify',
    };

    var Controller = {

        index: function () {
            ea.table.render({
                init: init,
                toolbar: ['refresh', 'add'@if(enableBatch), 'delete'@endif@if(enableExport), 'export'@endif],
                cols: [[
                    {type: "checkbox"},
@foreach($listFields as $field)
@if($field->getName() == 'id')
                    {field: '{{$field->getName()}}', width: 80, title: '{{$field->getComment()}}'},
@elseif($field->getComponent() == 'switch')
                    {field: '{{$field->getName()}}', width: 100, title: '{{$field->getComment()}}', templet: '#statusTpl', unresize: true},
@elseif($field->getComponent() == 'image')
                    {field: '{{$field->getName()}}', width: 120, title: '{{$field->getComment()}}', templet: ea.table.image},
@elseif($field->getComponent() == 'datetime')
                    {field: '{{$field->getName()}}', width: 180, title: '{{$field->getComment()}}'},
@elseif($field->getComponent() == 'date')
                    {field: '{{$field->getName()}}', width: 120, title: '{{$field->getComment()}}'},
@else
                    {field: '{{$field->getName()}}', minWidth: 80, title: '{{$field->getComment()}}'},
@endif
@endforeach
                    {
                        width: 250,
                        title: '操作',
                        templet: '#currentTableBar',
                        fixed: "right",
                        align: "center",
                        unresize: true
                    }
                ]],
            });

            ea.listen();
        },

        add: function () {
            ea.listen();
        },

        edit: function () {
            ea.listen();
        },

    };

    return Controller;
});
