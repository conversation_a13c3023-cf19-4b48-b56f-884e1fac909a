<?php

namespace app\common\services\curd\v2\dto;

/**
 * 生成结果数据传输对象
 */
class GenerateResult
{
    protected bool $success;
    protected string $message;
    protected array $data;
    protected array $errors;

    public function __construct(
        bool $success,
        string $message = '',
        array $data = [],
        array $errors = []
    ) {
        $this->success = $success;
        $this->message = $message;
        $this->data = $data;
        $this->errors = $errors;
    }

    public function isSuccess(): bool
    {
        return $this->success;
    }

    public function getMessage(): string
    {
        return $this->message;
    }

    public function getData(): array
    {
        return $this->data;
    }

    public function getErrors(): array
    {
        return $this->errors;
    }

    public function hasErrors(): bool
    {
        return !empty($this->errors);
    }

    public function addError(string $error): self
    {
        $this->errors[] = $error;
        return $this;
    }

    public function setData(array $data): self
    {
        $this->data = $data;
        return $this;
    }

    /**
     * 转换为数组格式
     */
    public function toArray(): array
    {
        return [
            'success' => $this->success,
            'message' => $this->message,
            'data' => $this->data,
            'errors' => $this->errors,
        ];
    }

    /**
     * 转换为 JSON 响应格式
     */
    public function toResponse(): array
    {
        return [
            'code' => $this->success ? 0 : 1,
            'msg' => $this->message,
            'data' => $this->data,
            'errors' => $this->errors,
        ];
    }

    /**
     * 创建成功结果
     */
    public static function success(string $message = '操作成功', array $data = []): self
    {
        return new self(true, $message, $data);
    }

    /**
     * 创建失败结果
     */
    public static function error(string $message = '操作失败', array $errors = []): self
    {
        return new self(false, $message, [], $errors);
    }
}
