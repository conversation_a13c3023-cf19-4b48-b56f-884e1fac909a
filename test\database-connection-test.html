<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据库连接测试 - CURD生成器V2</title>
    <link rel="stylesheet" href="https://unpkg.com/layui@2.8.18/dist/css/layui.css">
    <style>
        .test-container {
            padding: 20px;
            max-width: 1000px;
            margin: 0 auto;
        }
        .test-section {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #e8e8e8;
            border-radius: 6px;
            background: #fff;
        }
        .test-result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
        }
        .result-success {
            background: #f6ffed;
            border: 1px solid #b7eb8f;
            color: #389e0d;
        }
        .result-error {
            background: #fff2f0;
            border: 1px solid #ffccc7;
            color: #cf1322;
        }
        .result-info {
            background: #f0f9ff;
            border: 1px solid #91d5ff;
            color: #0958d9;
        }
        .connection-item {
            display: flex;
            align-items: center;
            padding: 10px;
            margin: 5px 0;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s;
        }
        .connection-item:hover {
            border-color: #40a9ff;
            background: #f0f9ff;
        }
        .connection-item.active {
            border-color: #1890ff;
            background: #e6f7ff;
        }
        .connection-status {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 10px;
        }
        .status-unknown { background: #d9d9d9; }
        .status-testing { background: #faad14; }
        .status-success { background: #52c41a; }
        .status-error { background: #ff4d4f; }
        .table-list {
            max-height: 200px;
            overflow-y: auto;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            padding: 10px;
            background: #fafafa;
        }
        .table-item {
            padding: 5px 10px;
            margin: 2px 0;
            background: #fff;
            border-radius: 3px;
            border-left: 3px solid #1890ff;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="layui-card">
            <div class="layui-card-header">
                <h2>🔧 数据库连接测试工具</h2>
                <p>测试CURD生成器V2的数据库连接功能是否正常工作</p>
            </div>
            <div class="layui-card-body">
                <!-- 连接测试区域 -->
                <div class="test-section">
                    <h3>📡 数据库连接测试</h3>
                    <p>点击下面的连接项测试各个数据库连接是否正常</p>
                    
                    <div id="connection-list">
                        <div class="connection-item" data-connection="mysql">
                            <div class="connection-status status-unknown" id="status-mysql"></div>
                            <div class="connection-info">
                                <strong>默认连接 (mysql)</strong>
                                <div style="font-size: 12px; color: #666;">主数据库连接，前缀: ea8_</div>
                            </div>
                        </div>
                        
                        <div class="connection-item" data-connection="mysql_read">
                            <div class="connection-status status-unknown" id="status-mysql_read"></div>
                            <div class="connection-info">
                                <strong>读库连接 (mysql_read)</strong>
                                <div style="font-size: 12px; color: #666;">读库连接，前缀: ea8_</div>
                            </div>
                        </div>
                        
                        <div class="connection-item" data-connection="mysql_second">
                            <div class="connection-status status-unknown" id="status-mysql_second"></div>
                            <div class="connection-info">
                                <strong>第二数据库 (mysql_second)</strong>
                                <div style="font-size: 12px; color: #666;">第二数据库连接，前缀: ddwx_</div>
                            </div>
                        </div>
                        
                        <div class="connection-item" data-connection="mysql_log">
                            <div class="connection-status status-unknown" id="status-mysql_log"></div>
                            <div class="connection-info">
                                <strong>日志数据库 (mysql_log)</strong>
                                <div style="font-size: 12px; color: #666;">日志数据库连接，前缀: ea8_</div>
                            </div>
                        </div>
                        
                        <div class="connection-item" data-connection="mysql_cache">
                            <div class="connection-status status-unknown" id="status-mysql_cache"></div>
                            <div class="connection-info">
                                <strong>缓存数据库 (mysql_cache)</strong>
                                <div style="font-size: 12px; color: #666;">缓存数据库连接，前缀: ea8_</div>
                            </div>
                        </div>
                    </div>
                    
                    <div style="margin-top: 15px;">
                        <button class="layui-btn layui-btn-primary" id="test-all-connections">测试所有连接</button>
                        <button class="layui-btn layui-btn-normal" id="test-mysql-second">重点测试第二数据库</button>
                        <button class="layui-btn layui-btn-warm" id="clear-results">清空结果</button>
                    </div>
                </div>

                <!-- 表列表测试区域 -->
                <div class="test-section">
                    <h3>📋 表列表获取测试</h3>
                    <p>选择数据库连接后，查看能否正确获取表列表</p>
                    
                    <div class="layui-form">
                        <div class="layui-form-item">
                            <label class="layui-form-label">选择连接</label>
                            <div class="layui-input-block">
                                <select name="test_connection" lay-filter="testConnectionSelect">
                                    <option value="">请选择数据库连接</option>
                                    <option value="mysql">默认连接 (mysql)</option>
                                    <option value="mysql_read">读库连接 (mysql_read)</option>
                                    <option value="mysql_second">第二数据库 (mysql_second)</option>
                                    <option value="mysql_log">日志数据库 (mysql_log)</option>
                                    <option value="mysql_cache">缓存数据库 (mysql_cache)</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div id="table-list-result" style="display: none;">
                        <h4>表列表结果：</h4>
                        <div id="table-list-content" class="table-list"></div>
                        <div id="table-list-stats" style="margin-top: 10px; font-size: 13px; color: #666;"></div>
                    </div>
                </div>

                <!-- 事件监听器测试区域 -->
                <div class="test-section">
                    <h3>⚡ 事件监听器测试</h3>
                    <p>测试JavaScript事件监听器是否正确绑定和触发</p>
                    
                    <div class="layui-form">
                        <div class="layui-form-item">
                            <label class="layui-form-label">连接选择</label>
                            <div class="layui-input-block">
                                <select name="event_connection" lay-filter="eventConnectionSelect">
                                    <option value="">请选择连接</option>
                                    <option value="mysql">默认连接</option>
                                    <option value="mysql_second">第二数据库</option>
                                </select>
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">表选择</label>
                            <div class="layui-input-block">
                                <select name="event_table" lay-filter="eventTableSelect">
                                    <option value="">请先选择连接</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div id="event-log" class="test-result result-info" style="display: none;">
                        <h5>事件日志：</h5>
                        <div id="event-log-content"></div>
                    </div>
                </div>

                <!-- 测试结果汇总 -->
                <div class="test-section">
                    <h3>📊 测试结果汇总</h3>
                    <div class="layui-row layui-col-space15">
                        <div class="layui-col-md3">
                            <div class="layui-card">
                                <div class="layui-card-body" style="text-align: center;">
                                    <h2 id="total-tests" style="color: #1890ff;">0</h2>
                                    <p>总测试数</p>
                                </div>
                            </div>
                        </div>
                        <div class="layui-col-md3">
                            <div class="layui-card">
                                <div class="layui-card-body" style="text-align: center;">
                                    <h2 id="passed-tests" style="color: #52c41a;">0</h2>
                                    <p>通过测试</p>
                                </div>
                            </div>
                        </div>
                        <div class="layui-col-md3">
                            <div class="layui-card">
                                <div class="layui-card-body" style="text-align: center;">
                                    <h2 id="failed-tests" style="color: #ff4d4f;">0</h2>
                                    <p>失败测试</p>
                                </div>
                            </div>
                        </div>
                        <div class="layui-col-md3">
                            <div class="layui-card">
                                <div class="layui-card-body" style="text-align: center;">
                                    <h2 id="success-rate" style="color: #faad14;">0%</h2>
                                    <p>成功率</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 引入JavaScript库 -->
    <script src="https://unpkg.com/layui@2.8.18/dist/layui.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <script>
        layui.use(['form', 'layer'], function() {
            var form = layui.form;
            var layer = layui.layer;
            
            // 测试统计
            var testStats = {
                total: 0,
                passed: 0,
                failed: 0
            };
            
            // 初始化
            init();
            
            function init() {
                bindEvents();
                logEvent('测试工具初始化完成');
            }
            
            // 绑定事件
            function bindEvents() {
                // 连接项点击测试
                $('.connection-item').on('click', function() {
                    var connection = $(this).data('connection');
                    testSingleConnection(connection);
                });
                
                // 测试所有连接
                $('#test-all-connections').on('click', function() {
                    testAllConnections();
                });
                
                // 重点测试第二数据库
                $('#test-mysql-second').on('click', function() {
                    testMysqlSecondConnection();
                });
                
                // 清空结果
                $('#clear-results').on('click', function() {
                    clearAllResults();
                });
                
                // 表列表测试连接选择
                form.on('select(testConnectionSelect)', function(data) {
                    if (data.value) {
                        loadTableList(data.value);
                    }
                });
                
                // 事件监听器测试
                form.on('select(eventConnectionSelect)', function(data) {
                    logEvent('连接选择事件触发: ' + data.value);
                    if (data.value) {
                        loadEventTables(data.value);
                    }
                });
                
                form.on('select(eventTableSelect)', function(data) {
                    logEvent('表选择事件触发: ' + data.value);
                });
            }
            
            // 测试单个连接
            function testSingleConnection(connection) {
                updateConnectionStatus(connection, 'testing');
                testStats.total++;
                
                $.post('/curdtest/tables', {
                    connection: connection
                }, function(res) {
                    if (res.code === 1) {
                        updateConnectionStatus(connection, 'success');
                        testStats.passed++;
                        logEvent('✅ ' + connection + ' 连接测试成功，找到 ' + res.data.length + ' 个表');
                    } else {
                        updateConnectionStatus(connection, 'error');
                        testStats.failed++;
                        logEvent('❌ ' + connection + ' 连接测试失败: ' + res.msg);
                    }
                    updateTestStats();
                }).fail(function() {
                    updateConnectionStatus(connection, 'error');
                    testStats.failed++;
                    logEvent('💥 ' + connection + ' 网络请求失败');
                    updateTestStats();
                });
            }
            
            // 测试所有连接
            function testAllConnections() {
                var connections = ['mysql', 'mysql_read', 'mysql_second', 'mysql_log', 'mysql_cache'];
                connections.forEach(function(connection, index) {
                    setTimeout(function() {
                        testSingleConnection(connection);
                    }, index * 500);
                });
            }
            
            // 重点测试第二数据库
            function testMysqlSecondConnection() {
                logEvent('🎯 开始重点测试第二数据库连接...');
                
                // 测试连接
                testSingleConnection('mysql_second');
                
                // 测试表列表加载
                setTimeout(function() {
                    loadTableList('mysql_second');
                }, 1000);
            }
            
            // 加载表列表
            function loadTableList(connection) {
                $('#table-list-result').show();
                $('#table-list-content').html('<div class="layui-icon layui-icon-loading layui-anim layui-anim-rotate"></div> 正在加载...');
                
                $.post('/curdtest/tables', {
                    connection: connection
                }, function(res) {
                    if (res.code === 1) {
                        var html = '';
                        res.data.forEach(function(table) {
                            html += '<div class="table-item">';
                            html += '<strong>' + table.name + '</strong>';
                            if (table.comment) {
                                html += ' <span style="color: #666;">(' + table.comment + ')</span>';
                            }
                            html += '</div>';
                        });
                        $('#table-list-content').html(html);
                        $('#table-list-stats').text('共找到 ' + res.data.length + ' 个表');
                        
                        if (res.msg.indexOf('演示数据') !== -1) {
                            $('#table-list-stats').append(' <span style="color: #fa8c16;">[演示模式]</span>');
                        }
                        
                        logEvent('📋 ' + connection + ' 表列表加载成功: ' + res.data.length + ' 个表');
                    } else {
                        $('#table-list-content').html('<div style="color: #ff4d4f;">加载失败: ' + res.msg + '</div>');
                        logEvent('❌ ' + connection + ' 表列表加载失败: ' + res.msg);
                    }
                }).fail(function() {
                    $('#table-list-content').html('<div style="color: #ff4d4f;">网络请求失败</div>');
                    logEvent('💥 ' + connection + ' 表列表网络请求失败');
                });
            }
            
            // 加载事件测试表列表
            function loadEventTables(connection) {
                var mockTables = {
                    'mysql': [
                        { name: 'users', comment: '用户表' },
                        { name: 'articles', comment: '文章表' }
                    ],
                    'mysql_second': [
                        { name: 'admin', comment: '管理员表' },
                        { name: 'member', comment: '会员表' },
                        { name: 'shop_product', comment: '商品表' }
                    ]
                };
                
                var tables = mockTables[connection] || [];
                var html = '<option value="">请选择表</option>';
                tables.forEach(function(table) {
                    html += '<option value="' + table.name + '">' + table.name + ' (' + table.comment + ')</option>';
                });
                
                $('select[name="event_table"]').html(html);
                form.render('select');
            }
            
            // 更新连接状态
            function updateConnectionStatus(connection, status) {
                var $status = $('#status-' + connection);
                $status.removeClass('status-unknown status-testing status-success status-error');
                $status.addClass('status-' + status);
                
                var $item = $('.connection-item[data-connection="' + connection + '"]');
                $item.removeClass('active');
                if (status === 'success') {
                    $item.addClass('active');
                }
            }
            
            // 记录事件日志
            function logEvent(message) {
                var timestamp = new Date().toLocaleTimeString();
                var logHtml = '<div>[' + timestamp + '] ' + message + '</div>';
                
                $('#event-log').show();
                $('#event-log-content').append(logHtml);
                
                // 自动滚动到底部
                var logContent = $('#event-log-content')[0];
                logContent.scrollTop = logContent.scrollHeight;
                
                console.log('[DB Test] ' + message);
            }
            
            // 更新测试统计
            function updateTestStats() {
                $('#total-tests').text(testStats.total);
                $('#passed-tests').text(testStats.passed);
                $('#failed-tests').text(testStats.failed);
                
                var rate = testStats.total > 0 ? ((testStats.passed / testStats.total) * 100).toFixed(1) : 0;
                $('#success-rate').text(rate + '%');
            }
            
            // 清空所有结果
            function clearAllResults() {
                // 重置连接状态
                $('.connection-status').removeClass('status-testing status-success status-error').addClass('status-unknown');
                $('.connection-item').removeClass('active');
                
                // 清空表列表
                $('#table-list-result').hide();
                $('#table-list-content').empty();
                $('#table-list-stats').empty();
                
                // 清空事件日志
                $('#event-log').hide();
                $('#event-log-content').empty();
                
                // 重置统计
                testStats = { total: 0, passed: 0, failed: 0 };
                updateTestStats();
                
                logEvent('🧹 所有测试结果已清空');
            }
        });
    </script>
</body>
</html>
