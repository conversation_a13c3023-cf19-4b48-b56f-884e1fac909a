<?php
echo "=== 测试控制器加载 ===\n";

// 引入辅助函数
require_once 'bootstrap/helpers.php';

echo "1. 测试基础控制器加载...\n";
try {
    $controller = new \app\admin\controller\system\ApiDocControllerSimple();
    echo "   ✅ ApiDocControllerSimple 加载成功\n";
} catch (Exception $e) {
    echo "   ❌ ApiDocControllerSimple 加载失败: " . $e->getMessage() . "\n";
}

echo "\n2. 测试新增控制器加载...\n";

$controllers = [
    'ApiVersionController' => '\\app\\admin\\controller\\system\\ApiVersionController',
    'ApiTestController' => '\\app\\admin\\controller\\system\\ApiTestController',
    'CacheController' => '\\app\\admin\\controller\\system\\CacheController',
    'SearchController' => '\\app\\admin\\controller\\system\\SearchController',
    'MonitorController' => '\\app\\admin\\controller\\system\\MonitorController'
];

foreach ($controllers as $name => $class) {
    try {
        $controller = new $class();
        echo "   ✅ {$name} 加载成功\n";
        
        // 测试index方法
        if (method_exists($controller, 'index')) {
            echo "      - index方法存在\n";
        } else {
            echo "      - ❌ index方法不存在\n";
        }
    } catch (Exception $e) {
        echo "   ❌ {$name} 加载失败: " . $e->getMessage() . "\n";
    }
}

echo "\n3. 测试路由访问...\n";

// 模拟请求测试
$testUrls = [
    '/admin/system/api-version',
    '/admin/system/api-test', 
    '/admin/system/cache',
    '/admin/system/search',
    '/admin/system/monitor'
];

foreach ($testUrls as $url) {
    echo "   测试URL: {$url}\n";
    
    // 模拟请求
    $_SERVER['REQUEST_URI'] = $url;
    $_SERVER['REQUEST_METHOD'] = 'GET';
    
    try {
        // 简单的路由测试
        $path = parse_url($url, PHP_URL_PATH);
        
        if (strpos($path, '/admin/system/api-version') === 0) {
            $controller = new \app\admin\controller\system\ApiVersionController();
            echo "      ✅ 路由匹配成功\n";
        } elseif (strpos($path, '/admin/system/api-test') === 0) {
            $controller = new \app\admin\controller\system\ApiTestController();
            echo "      ✅ 路由匹配成功\n";
        } elseif (strpos($path, '/admin/system/cache') === 0) {
            $controller = new \app\admin\controller\system\CacheController();
            echo "      ✅ 路由匹配成功\n";
        } elseif (strpos($path, '/admin/system/search') === 0) {
            $controller = new \app\admin\controller\system\SearchController();
            echo "      ✅ 路由匹配成功\n";
        } elseif (strpos($path, '/admin/system/monitor') === 0) {
            $controller = new \app\admin\controller\system\MonitorController();
            echo "      ✅ 路由匹配成功\n";
        } else {
            echo "      ❌ 路由匹配失败\n";
        }
    } catch (Exception $e) {
        echo "      ❌ 路由测试失败: " . $e->getMessage() . "\n";
    }
}

echo "\n=== 测试完成 ===\n";
