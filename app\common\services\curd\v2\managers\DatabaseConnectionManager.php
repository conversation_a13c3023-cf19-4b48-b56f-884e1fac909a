<?php

namespace app\common\services\curd\v2\managers;

use support\Db;
use support\Log;

/**
 * 数据库连接管理器
 * 智能管理和优化数据库连接选择逻辑
 */
class DatabaseConnectionManager
{
    /**
     * 连接配置缓存
     */
    private static array $connectionCache = [];

    /**
     * 连接状态缓存
     */
    private static array $statusCache = [];

    /**
     * 缓存过期时间（秒）
     */
    private const CACHE_TTL = 300; // 5分钟

    /**
     * 获取所有可用的数据库连接
     */
    public function getAvailableConnections(): array
    {
        $cacheKey = 'available_connections';
        
        if ($this->isCacheValid($cacheKey)) {
            return self::$connectionCache[$cacheKey]['data'];
        }

        $connections = [];
        $config = config('database.connections', []);

        foreach ($config as $name => $connectionConfig) {
            $connectionInfo = $this->analyzeConnection($name, $connectionConfig);
            if ($connectionInfo) {
                $connections[$name] = $connectionInfo;
            }
        }

        // 缓存结果
        $this->cacheData($cacheKey, $connections);

        return $connections;
    }

    /**
     * 分析单个连接配置
     */
    private function analyzeConnection(string $name, array $config): ?array
    {
        try {
            // 基本信息
            $info = [
                'name' => $name,
                'display_name' => $this->getDisplayName($name),
                'driver' => $config['driver'] ?? 'unknown',
                'host' => $config['host'] ?? 'localhost',
                'port' => $config['port'] ?? 3306,
                'database' => $config['database'] ?? '',
                'charset' => $config['charset'] ?? 'utf8mb4',
                'status' => 'unknown',
                'table_count' => 0,
                'size' => 0,
                'last_check' => null,
                'features' => [],
                'performance' => [],
                'recommended_for' => []
            ];

            // 检查连接状态
            $status = $this->checkConnectionStatus($name);
            $info['status'] = $status['status'];
            $info['last_check'] = $status['timestamp'];

            if ($status['status'] === 'active') {
                // 获取数据库统计信息
                $stats = $this->getDatabaseStats($name);
                $info['table_count'] = $stats['table_count'];
                $info['size'] = $stats['size'];
                
                // 分析连接特性
                $info['features'] = $this->analyzeConnectionFeatures($name, $config);
                
                // 性能分析
                $info['performance'] = $this->analyzePerformance($name);
                
                // 推荐用途
                $info['recommended_for'] = $this->getRecommendedUsage($name, $config, $stats);
            }

            return $info;

        } catch (\Exception $e) {
            Log::warning("分析数据库连接失败: {$name}", ['error' => $e->getMessage()]);
            
            return [
                'name' => $name,
                'display_name' => $this->getDisplayName($name),
                'driver' => $config['driver'] ?? 'unknown',
                'status' => 'error',
                'error' => $e->getMessage(),
                'last_check' => time()
            ];
        }
    }

    /**
     * 获取连接显示名称
     */
    private function getDisplayName(string $name): string
    {
        $displayNames = [
            'mysql' => '主数据库 (MySQL)',
            'mysql_read' => '读库 (MySQL Read)',
            'mysql_second' => '第二数据库 (MySQL Second)',
            'mysql_log' => '日志数据库 (MySQL Log)',
            'mysql_cache' => '缓存数据库 (MySQL Cache)',
            'pgsql' => '主数据库 (PostgreSQL)',
            'sqlite' => '本地数据库 (SQLite)',
            'sqlsrv' => '主数据库 (SQL Server)'
        ];

        return $displayNames[$name] ?? ucfirst(str_replace('_', ' ', $name));
    }

    /**
     * 检查连接状态
     */
    private function checkConnectionStatus(string $name): array
    {
        $cacheKey = "status_{$name}";
        
        if ($this->isCacheValid($cacheKey)) {
            return self::$statusCache[$cacheKey]['data'];
        }

        try {
            $startTime = microtime(true);
            
            // 尝试连接并执行简单查询
            $result = Db::connection($name)->select('SELECT 1 as test');
            
            $responseTime = round((microtime(true) - $startTime) * 1000, 2);
            
            $status = [
                'status' => 'active',
                'response_time' => $responseTime,
                'timestamp' => time(),
                'message' => '连接正常'
            ];

        } catch (\Exception $e) {
            $status = [
                'status' => 'error',
                'response_time' => null,
                'timestamp' => time(),
                'message' => $e->getMessage()
            ];
        }

        // 缓存状态（较短的缓存时间）
        $this->cacheData($cacheKey, $status, 60);

        return $status;
    }

    /**
     * 获取数据库统计信息
     */
    private function getDatabaseStats(string $name): array
    {
        try {
            $connection = Db::connection($name);
            
            // 获取表数量
            $tables = $connection->select("SHOW TABLES");
            $tableCount = count($tables);
            
            // 获取数据库大小（MySQL）
            $sizeQuery = "
                SELECT 
                    ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS size_mb
                FROM information_schema.tables 
                WHERE table_schema = DATABASE()
            ";
            
            $sizeResult = $connection->select($sizeQuery);
            $size = $sizeResult[0]->size_mb ?? 0;

            return [
                'table_count' => $tableCount,
                'size' => $size,
                'tables' => array_column($tables, array_keys((array)$tables[0])[0])
            ];

        } catch (\Exception $e) {
            return [
                'table_count' => 0,
                'size' => 0,
                'tables' => []
            ];
        }
    }

    /**
     * 分析连接特性
     */
    private function analyzeConnectionFeatures(string $name, array $config): array
    {
        $features = [];

        // 根据连接名称推断特性
        if (strpos($name, 'read') !== false) {
            $features[] = '只读优化';
            $features[] = '查询性能优化';
        }

        if (strpos($name, 'log') !== false) {
            $features[] = '日志存储';
            $features[] = '大数据量支持';
        }

        if (strpos($name, 'cache') !== false) {
            $features[] = '缓存存储';
            $features[] = '高速访问';
        }

        // 根据驱动类型添加特性
        switch ($config['driver'] ?? '') {
            case 'mysql':
                $features[] = 'MySQL兼容';
                $features[] = 'ACID事务';
                break;
            case 'pgsql':
                $features[] = 'PostgreSQL兼容';
                $features[] = '高级数据类型';
                break;
            case 'sqlite':
                $features[] = '轻量级';
                $features[] = '无服务器';
                break;
        }

        return array_unique($features);
    }

    /**
     * 分析性能指标
     */
    private function analyzePerformance(string $name): array
    {
        try {
            $connection = Db::connection($name);
            
            // 执行性能测试查询
            $startTime = microtime(true);
            $connection->select('SELECT 1');
            $queryTime = round((microtime(true) - $startTime) * 1000, 2);

            // 获取连接池信息（如果支持）
            $performance = [
                'avg_query_time' => $queryTime,
                'connection_status' => 'good'
            ];

            // 性能评级
            if ($queryTime < 10) {
                $performance['rating'] = 'excellent';
                $performance['rating_text'] = '优秀';
            } elseif ($queryTime < 50) {
                $performance['rating'] = 'good';
                $performance['rating_text'] = '良好';
            } elseif ($queryTime < 100) {
                $performance['rating'] = 'fair';
                $performance['rating_text'] = '一般';
            } else {
                $performance['rating'] = 'poor';
                $performance['rating_text'] = '较差';
            }

            return $performance;

        } catch (\Exception $e) {
            return [
                'avg_query_time' => null,
                'connection_status' => 'error',
                'rating' => 'unknown',
                'rating_text' => '未知'
            ];
        }
    }

    /**
     * 获取推荐用途
     */
    private function getRecommendedUsage(string $name, array $config, array $stats): array
    {
        $recommendations = [];

        // 根据连接名称推荐
        if (strpos($name, 'read') !== false) {
            $recommendations[] = '数据查询和报表';
            $recommendations[] = '只读操作';
        } elseif (strpos($name, 'log') !== false) {
            $recommendations[] = '日志记录';
            $recommendations[] = '审计数据';
        } elseif (strpos($name, 'cache') !== false) {
            $recommendations[] = '缓存数据';
            $recommendations[] = '临时存储';
        } else {
            $recommendations[] = 'CRUD操作';
            $recommendations[] = '业务数据';
        }

        // 根据表数量推荐
        if ($stats['table_count'] > 50) {
            $recommendations[] = '大型应用';
        } elseif ($stats['table_count'] > 10) {
            $recommendations[] = '中型应用';
        } else {
            $recommendations[] = '小型应用';
        }

        return $recommendations;
    }

    /**
     * 获取智能推荐的连接
     */
    public function getRecommendedConnection(array $criteria = []): ?string
    {
        $connections = $this->getAvailableConnections();
        $scores = [];

        foreach ($connections as $name => $info) {
            if ($info['status'] !== 'active') {
                continue;
            }

            $score = 0;

            // 性能评分
            switch ($info['performance']['rating'] ?? 'unknown') {
                case 'excellent': $score += 100; break;
                case 'good': $score += 80; break;
                case 'fair': $score += 60; break;
                case 'poor': $score += 40; break;
            }

            // 表数量评分（有表的优先）
            $score += min($info['table_count'] * 2, 50);

            // 根据用途评分
            if (isset($criteria['purpose'])) {
                $purpose = $criteria['purpose'];
                if (in_array($purpose, $info['recommended_for'])) {
                    $score += 30;
                }
            }

            // 主库优先
            if ($name === 'mysql' || strpos($name, 'main') !== false) {
                $score += 20;
            }

            $scores[$name] = $score;
        }

        if (empty($scores)) {
            return null;
        }

        // 返回得分最高的连接
        arsort($scores);
        return array_key_first($scores);
    }

    /**
     * 获取连接的表列表（带缓存）
     */
    public function getConnectionTables(string $connection): array
    {
        $cacheKey = "tables_{$connection}";
        
        if ($this->isCacheValid($cacheKey)) {
            return self::$connectionCache[$cacheKey]['data'];
        }

        try {
            $db = Db::connection($connection);
            $tables = $db->select("SHOW TABLE STATUS");
            
            $result = [];
            foreach ($tables as $table) {
                $result[] = [
                    'name' => $table->Name,
                    'comment' => $table->Comment ?: '',
                    'engine' => $table->Engine,
                    'rows' => $table->Rows,
                    'size' => round(($table->Data_length + $table->Index_length) / 1024 / 1024, 2),
                    'created' => $table->Create_time,
                    'updated' => $table->Update_time
                ];
            }

            $this->cacheData($cacheKey, $result);
            return $result;

        } catch (\Exception $e) {
            // 返回模拟数据
            return $this->getMockTables($connection);
        }
    }

    /**
     * 获取模拟表数据
     */
    private function getMockTables(string $connection): array
    {
        $mockTables = [
            'mysql' => [
                ['name' => 'admin', 'comment' => '管理员表', 'engine' => 'InnoDB', 'rows' => 10, 'size' => 0.1],
                ['name' => 'member', 'comment' => '会员表', 'engine' => 'InnoDB', 'rows' => 1000, 'size' => 2.5],
                ['name' => 'article', 'comment' => '文章表', 'engine' => 'InnoDB', 'rows' => 500, 'size' => 1.8],
                ['name' => 'category', 'comment' => '分类表', 'engine' => 'InnoDB', 'rows' => 50, 'size' => 0.3],
            ],
            'mysql_second' => [
                ['name' => 'admin', 'comment' => '管理员表', 'engine' => 'InnoDB', 'rows' => 5, 'size' => 0.05],
                ['name' => 'member', 'comment' => '会员表', 'engine' => 'InnoDB', 'rows' => 2000, 'size' => 5.2],
                ['name' => 'shop_product', 'comment' => '商品表', 'engine' => 'InnoDB', 'rows' => 800, 'size' => 3.1],
                ['name' => 'shop_order', 'comment' => '订单表', 'engine' => 'InnoDB', 'rows' => 1500, 'size' => 4.8],
                ['name' => 'article', 'comment' => '文章表', 'engine' => 'InnoDB', 'rows' => 300, 'size' => 1.2],
                ['name' => 'business', 'comment' => '商家表', 'engine' => 'InnoDB', 'rows' => 100, 'size' => 0.8],
            ],
            'mysql_read' => [
                ['name' => 'statistics', 'comment' => '统计表', 'engine' => 'InnoDB', 'rows' => 10000, 'size' => 15.6],
                ['name' => 'reports', 'comment' => '报表表', 'engine' => 'InnoDB', 'rows' => 5000, 'size' => 8.2],
            ],
            'mysql_log' => [
                ['name' => 'access_log', 'comment' => '访问日志', 'engine' => 'InnoDB', 'rows' => 50000, 'size' => 25.8],
                ['name' => 'error_log', 'comment' => '错误日志', 'engine' => 'InnoDB', 'rows' => 1000, 'size' => 2.1],
                ['name' => 'operation_log', 'comment' => '操作日志', 'engine' => 'InnoDB', 'rows' => 20000, 'size' => 12.5],
            ],
            'mysql_cache' => [
                ['name' => 'cache_data', 'comment' => '缓存数据', 'engine' => 'MEMORY', 'rows' => 5000, 'size' => 3.2],
                ['name' => 'sessions', 'comment' => '会话数据', 'engine' => 'InnoDB', 'rows' => 2000, 'size' => 1.5],
            ]
        ];

        return $mockTables[$connection] ?? [
            ['name' => 'demo_table', 'comment' => '演示表', 'engine' => 'InnoDB', 'rows' => 100, 'size' => 0.5]
        ];
    }

    /**
     * 检查缓存是否有效
     */
    private function isCacheValid(string $key): bool
    {
        if (!isset(self::$connectionCache[$key])) {
            return false;
        }

        $cache = self::$connectionCache[$key];
        return (time() - $cache['timestamp']) < $cache['ttl'];
    }

    /**
     * 缓存数据
     */
    private function cacheData(string $key, $data, int $ttl = null): void
    {
        self::$connectionCache[$key] = [
            'data' => $data,
            'timestamp' => time(),
            'ttl' => $ttl ?? self::CACHE_TTL
        ];
    }

    /**
     * 清除缓存
     */
    public function clearCache(string $connection = null): void
    {
        if ($connection) {
            $keys = array_filter(array_keys(self::$connectionCache), function($key) use ($connection) {
                return strpos($key, $connection) !== false;
            });
            
            foreach ($keys as $key) {
                unset(self::$connectionCache[$key]);
            }
        } else {
            self::$connectionCache = [];
        }
    }

    /**
     * 测试连接
     */
    public function testConnection(string $connection): array
    {
        try {
            $startTime = microtime(true);
            
            $db = Db::connection($connection);
            $result = $db->select('SELECT VERSION() as version, DATABASE() as database, USER() as user');
            
            $responseTime = round((microtime(true) - $startTime) * 1000, 2);
            
            return [
                'success' => true,
                'response_time' => $responseTime,
                'info' => $result[0] ?? null,
                'message' => '连接测试成功'
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'response_time' => null,
                'info' => null,
                'message' => $e->getMessage()
            ];
        }
    }
}
