<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API调试工具</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .result { margin-top: 10px; padding: 10px; background: #f5f5f5; border-radius: 3px; }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        button { padding: 8px 16px; margin: 5px; cursor: pointer; }
        pre { white-space: pre-wrap; word-wrap: break-word; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 CURD生成器V2 - API调试工具</h1>
        
        <div class="test-section">
            <h3>📡 直接测试API接口</h3>
            <p>直接调用后端API接口，查看响应结果</p>
            
            <button onclick="testConnection('mysql')">测试 mysql</button>
            <button onclick="testConnection('mysql_second')">测试 mysql_second</button>
            <button onclick="testConnection('mysql_read')">测试 mysql_read</button>
            <button onclick="testConnection('mysql_log')">测试 mysql_log</button>
            <button onclick="testConnection('mysql_cache')">测试 mysql_cache</button>
            
            <div id="api-results"></div>
        </div>
        
        <div class="test-section">
            <h3>🌐 网络请求测试</h3>
            <p>测试网络请求是否能正常到达服务器</p>
            
            <button onclick="testNetworkConnectivity()">测试网络连通性</button>
            <button onclick="testRouteExists()">测试路由是否存在</button>
            
            <div id="network-results"></div>
        </div>
        
        <div class="test-section">
            <h3>🔍 详细调试信息</h3>
            <p>显示详细的请求和响应信息</p>
            
            <button onclick="debugMysqlSecond()">调试 mysql_second 连接</button>
            <button onclick="clearResults()">清空结果</button>
            
            <div id="debug-results"></div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        // 测试特定数据库连接
        function testConnection(connection) {
            const startTime = Date.now();
            
            addResult('api-results', `🚀 开始测试连接: ${connection}`, 'info');
            
            $.ajax({
                url: '/curdtest/tables',
                method: 'POST',
                data: { connection: connection },
                timeout: 10000,
                beforeSend: function(xhr) {
                    addResult('api-results', `📤 发送请求到: /curdtest/tables`, 'info');
                    addResult('api-results', `📋 请求数据: ${JSON.stringify({connection: connection})}`, 'info');
                }
            })
            .done(function(response, textStatus, xhr) {
                const duration = Date.now() - startTime;
                addResult('api-results', `✅ ${connection} 请求成功 (${duration}ms)`, 'success');
                addResult('api-results', `📊 响应状态: ${xhr.status} ${textStatus}`, 'info');
                addResult('api-results', `📄 响应头: ${JSON.stringify(xhr.getAllResponseHeaders())}`, 'info');
                
                if (response && response.code === 1) {
                    addResult('api-results', `🎯 成功获取 ${response.data.length} 个表`, 'success');
                    addResult('api-results', `📋 表列表: ${response.data.map(t => t.name).join(', ')}`, 'info');
                    
                    if (response.msg.includes('演示数据')) {
                        addResult('api-results', `⚠️ 使用演示数据模式`, 'warning');
                    }
                } else {
                    addResult('api-results', `❌ API返回错误: ${response ? response.msg : '无响应数据'}`, 'error');
                }
                
                addResult('api-results', `📝 完整响应: <pre>${JSON.stringify(response, null, 2)}</pre>`, 'info');
            })
            .fail(function(xhr, textStatus, errorThrown) {
                const duration = Date.now() - startTime;
                addResult('api-results', `❌ ${connection} 请求失败 (${duration}ms)`, 'error');
                addResult('api-results', `💥 错误类型: ${textStatus}`, 'error');
                addResult('api-results', `🔍 错误详情: ${errorThrown}`, 'error');
                addResult('api-results', `📊 HTTP状态: ${xhr.status}`, 'error');
                addResult('api-results', `📄 响应内容: <pre>${xhr.responseText}</pre>`, 'error');
            });
        }
        
        // 测试网络连通性
        function testNetworkConnectivity() {
            addResult('network-results', '🌐 测试网络连通性...', 'info');
            
            // 测试基本的网络连接
            $.ajax({
                url: '/',
                method: 'GET',
                timeout: 5000
            })
            .done(function() {
                addResult('network-results', '✅ 基本网络连接正常', 'success');
                
                // 测试admin路径
                $.ajax({
                    url: '/admin',
                    method: 'GET',
                    timeout: 5000
                })
                .done(function() {
                    addResult('network-results', '✅ Admin路径可访问', 'success');
                })
                .fail(function(xhr) {
                    if (xhr.status === 302 || xhr.status === 401) {
                        addResult('network-results', '✅ Admin路径存在 (重定向到登录)', 'success');
                    } else {
                        addResult('network-results', `⚠️ Admin路径状态: ${xhr.status}`, 'warning');
                    }
                });
            })
            .fail(function(xhr) {
                addResult('network-results', `❌ 基本网络连接失败: ${xhr.status}`, 'error');
            });
        }
        
        // 测试路由是否存在
        function testRouteExists() {
            addResult('network-results', '🛣️ 测试CURD路由...', 'info');
            
            // 测试GET请求
            $.ajax({
                url: '/curdtest/tables',
                method: 'GET',
                timeout: 5000
            })
            .done(function(response) {
                addResult('network-results', '✅ GET /curdtest/tables 路由存在', 'success');
                addResult('network-results', `📝 GET响应: <pre>${JSON.stringify(response, null, 2)}</pre>`, 'info');
            })
            .fail(function(xhr) {
                if (xhr.status === 405) {
                    addResult('network-results', '✅ 路由存在但不支持GET (正常)', 'success');
                } else if (xhr.status === 404) {
                    addResult('network-results', '❌ 路由不存在 (404)', 'error');
                } else {
                    addResult('network-results', `⚠️ 路由状态: ${xhr.status}`, 'warning');
                }
            });
        }
        
        // 调试mysql_second连接
        function debugMysqlSecond() {
            addResult('debug-results', '🔍 开始调试 mysql_second 连接...', 'info');
            
            // 详细的调试信息
            const debugInfo = {
                timestamp: new Date().toISOString(),
                userAgent: navigator.userAgent,
                url: window.location.href,
                referrer: document.referrer
            };
            
            addResult('debug-results', `🕐 调试时间: ${debugInfo.timestamp}`, 'info');
            addResult('debug-results', `🌐 用户代理: ${debugInfo.userAgent}`, 'info');
            addResult('debug-results', `📍 当前URL: ${debugInfo.url}`, 'info');
            
            // 发送带有详细调试信息的请求
            $.ajax({
                url: '/curdtest/tables',
                method: 'POST',
                data: { 
                    connection: 'mysql_second',
                    debug: true,
                    timestamp: debugInfo.timestamp
                },
                timeout: 15000,
                beforeSend: function(xhr) {
                    xhr.setRequestHeader('X-Debug-Mode', 'true');
                    xhr.setRequestHeader('X-Requested-With', 'XMLHttpRequest');
                    addResult('debug-results', '📤 发送调试请求...', 'info');
                }
            })
            .done(function(response, textStatus, xhr) {
                addResult('debug-results', '✅ 调试请求成功', 'success');
                
                // 分析响应
                if (response && typeof response === 'object') {
                    if (response.code === 1) {
                        addResult('debug-results', `🎯 API调用成功，返回 ${response.data.length} 个表`, 'success');
                        
                        // 显示每个表的详细信息
                        response.data.forEach((table, index) => {
                            addResult('debug-results', `📋 表${index + 1}: ${table.name} (${table.comment || '无注释'})`, 'info');
                        });
                        
                        if (response.msg && response.msg.includes('演示数据')) {
                            addResult('debug-results', '⚠️ 注意: 当前使用的是演示数据，可能是因为数据库连接失败', 'warning');
                        }
                    } else {
                        addResult('debug-results', `❌ API返回错误码: ${response.code}`, 'error');
                        addResult('debug-results', `💬 错误消息: ${response.msg}`, 'error');
                    }
                } else {
                    addResult('debug-results', '❌ 响应格式异常', 'error');
                    addResult('debug-results', `📄 原始响应: ${JSON.stringify(response)}`, 'error');
                }
                
                // 显示完整的调试信息
                addResult('debug-results', `📊 完整调试信息: <pre>${JSON.stringify({
                    request: debugInfo,
                    response: response,
                    headers: xhr.getAllResponseHeaders()
                }, null, 2)}</pre>`, 'info');
            })
            .fail(function(xhr, textStatus, errorThrown) {
                addResult('debug-results', '❌ 调试请求失败', 'error');
                addResult('debug-results', `💥 失败原因: ${textStatus} - ${errorThrown}`, 'error');
                addResult('debug-results', `📊 HTTP状态: ${xhr.status}`, 'error');
                
                if (xhr.responseText) {
                    addResult('debug-results', `📄 服务器响应: <pre>${xhr.responseText}</pre>`, 'error');
                }
                
                // 可能的原因分析
                if (xhr.status === 0) {
                    addResult('debug-results', '🔍 可能原因: 网络连接问题或CORS限制', 'warning');
                } else if (xhr.status === 404) {
                    addResult('debug-results', '🔍 可能原因: 路由配置错误或控制器不存在', 'warning');
                } else if (xhr.status === 500) {
                    addResult('debug-results', '🔍 可能原因: 服务器内部错误，检查PHP错误日志', 'warning');
                }
            });
        }
        
        // 添加结果到指定容器
        function addResult(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            const div = document.createElement('div');
            div.className = `result ${type}`;
            div.innerHTML = `[${new Date().toLocaleTimeString()}] ${message}`;
            container.appendChild(div);
            
            // 自动滚动到底部
            container.scrollTop = container.scrollHeight;
        }
        
        // 清空结果
        function clearResults() {
            document.getElementById('api-results').innerHTML = '';
            document.getElementById('network-results').innerHTML = '';
            document.getElementById('debug-results').innerHTML = '';
        }
        
        // 页面加载完成后自动运行基本测试
        $(document).ready(function() {
            addResult('api-results', '🚀 API调试工具已加载', 'success');
            addResult('network-results', '🌐 网络测试工具已准备', 'success');
            addResult('debug-results', '🔍 调试工具已就绪', 'success');
        });
    </script>
</body>
</html>
