<?php

namespace {{namespace}};

use app\common\controller\AdminController;
use app\common\services\annotation\ControllerAnnotation;
use app\common\services\annotation\NodeAnnotation;
use {{modelNamespace}}\{{modelClass}};
use support\Request;
use support\Response;

#[ControllerAnnotation(title: '{{tableComment}}管理')]
class {{className}} extends AdminController
{
    /**
     * 模型
     */
    public function initialize()
    {
        parent::initialize();
        $this->model = new {{modelClass}}();
    }

    #[NodeAnnotation(title: '{{tableComment}}列表', auth: true)]
    public function index(Request $request): Response
    {
        if ($request->isAjax()) {
            [$page, $limit, $where] = $this->buildTableParams();
            
            $list = $this->model
                ->where($where)
                ->order($this->order, $this->sort)
                ->paginate($limit);

            $data = [
                'code' => 0,
                'msg' => '',
                'count' => $list->total(),
                'data' => $list->items(),
            ];
            return json($data);
        }
        
        return $this->fetch();
    }

    #[NodeAnnotation(title: '{{tableComment}}添加', auth: true)]
    public function add(Request $request): Response
    {
        if ($request->isAjax()) {
            $post = $request->post();
            
            try {
                $save = $this->model->save($post);
                if ($save) {
                    return $this->success('保存成功');
                } else {
                    return $this->error('保存失败');
                }
            } catch (\Exception $e) {
                return $this->error('保存失败: ' . $e->getMessage());
            }
        }
        
        return $this->fetch();
    }

    #[NodeAnnotation(title: '{{tableComment}}编辑', auth: true)]
    public function edit(Request $request): Response
    {
        $id = $request->input('id');
        $row = $this->model->find($id);
        
        if (empty($row)) {
            return $this->error('数据不存在');
        }
        
        if ($request->isAjax()) {
            $post = $request->post();
            
            try {
                $save = $row->save($post);
                if ($save) {
                    return $this->success('保存成功');
                } else {
                    return $this->error('保存失败');
                }
            } catch (\Exception $e) {
                return $this->error('保存失败: ' . $e->getMessage());
            }
        }
        
        $this->assign('row', $row);
        return $this->fetch();
    }

    #[NodeAnnotation(title: '{{tableComment}}删除', auth: true)]
    public function delete(Request $request): Response
    {
        $ids = $request->input('id');
        if (empty($ids)) {
            return $this->error('参数错误');
        }
        
        try {
            $delete = $this->model->whereIn('{{primaryKey}}', $ids)->delete();
            if ($delete) {
                return $this->success('删除成功');
            } else {
                return $this->error('删除失败');
            }
        } catch (\Exception $e) {
            return $this->error('删除失败: ' . $e->getMessage());
        }
    }

@if(enableExport)
    #[NodeAnnotation(title: '{{tableComment}}导出', auth: true)]
    public function export(Request $request): Response
    {
        [$page, $limit, $where] = $this->buildTableParams();
        
        $list = $this->model
            ->where($where)
            ->limit(100000)
            ->orderByDesc('{{primaryKey}}')
            ->get();

        return $this->exportExcel($list->toArray(), '{{tableComment}}数据');
    }
@endif

@if(enableBatch)
    #[NodeAnnotation(title: '{{tableComment}}批量操作', auth: true)]
    public function batch(Request $request): Response
    {
        $action = $request->input('action');
        $ids = $request->input('ids');
        
        if (empty($action) || empty($ids)) {
            return $this->error('参数错误');
        }
        
        try {
            switch ($action) {
                case 'delete':
                    $result = $this->model->whereIn('{{primaryKey}}', $ids)->delete();
                    break;
                case 'enable':
                    $result = $this->model->whereIn('{{primaryKey}}', $ids)->update(['status' => 1]);
                    break;
                case 'disable':
                    $result = $this->model->whereIn('{{primaryKey}}', $ids)->update(['status' => 0]);
                    break;
                default:
                    return $this->error('不支持的操作');
            }
            
            if ($result) {
                return $this->success('操作成功');
            } else {
                return $this->error('操作失败');
            }
        } catch (\Exception $e) {
            return $this->error('操作失败: ' . $e->getMessage());
        }
    }
@endif

    #[NodeAnnotation(title: '{{tableComment}}修改', auth: true)]
    public function modify(Request $request): Response
    {
        $post = $request->post();
        $id = $post['id'] ?? 0;
        $field = $post['field'] ?? '';
        $value = $post['value'] ?? '';
        
        if (empty($id) || empty($field)) {
            return $this->error('参数错误');
        }
        
        try {
            $row = $this->model->find($id);
            if (empty($row)) {
                return $this->error('数据不存在');
            }
            
            $row->$field = $value;
            $save = $row->save();
            
            if ($save) {
                return $this->success('修改成功');
            } else {
                return $this->error('修改失败');
            }
        } catch (\Exception $e) {
            return $this->error('修改失败: ' . $e->getMessage());
        }
    }
}
