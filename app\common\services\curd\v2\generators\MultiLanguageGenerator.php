<?php

namespace app\common\services\curd\v2\generators;

/**
 * 多语言生成器
 * 根据多语言需求分析结果生成不同语言的代码
 */
class MultiLanguageGenerator
{
    /**
     * 生成多语言代码
     */
    public function generateMultiLanguageCode(array $multiLanguageAnalysis, array $tableInfo, array $options = []): array
    {
        $generatedCode = [];

        foreach ($multiLanguageAnalysis['requirements'] as $language => $requirements) {
            $generatedCode[$language] = $this->generateLanguageCode($language, $requirements, $tableInfo, $options);
        }

        return [
            'generated_code' => $generatedCode,
            'project_configs' => $this->generateProjectConfigs($multiLanguageAnalysis, $tableInfo),
            'deployment_configs' => $this->generateDeploymentConfigs($multiLanguageAnalysis, $tableInfo),
            'documentation' => $this->generateMultiLanguageDocumentation($multiLanguageAnalysis, $tableInfo),
        ];
    }

    /**
     * 生成单个语言的代码
     */
    protected function generateLanguageCode(string $language, array $requirements, array $tableInfo, array $options): array
    {
        $code = [];

        // 生成模型代码
        $code['model'] = $this->generateModel($language, $requirements, $tableInfo);

        // 生成控制器代码
        $code['controller'] = $this->generateController($language, $requirements, $tableInfo);

        // 生成服务代码
        $code['service'] = $this->generateService($language, $requirements, $tableInfo);

        // 生成数据访问层代码
        $code['repository'] = $this->generateRepository($language, $requirements, $tableInfo);

        // 生成验证代码
        $code['validation'] = $this->generateValidation($language, $requirements, $tableInfo);

        // 生成测试代码
        $code['tests'] = $this->generateTests($language, $requirements, $tableInfo);

        return $code;
    }

    /**
     * 生成模型代码
     */
    protected function generateModel(string $language, array $requirements, array $tableInfo): string
    {
        $modelName = $requirements['language_mapping']['model_names'][$language] ?? 'Model';
        
        switch ($language) {
            case 'java':
                return $this->generateJavaModel($modelName, $requirements, $tableInfo);
            case 'python':
                return $this->generatePythonModel($modelName, $requirements, $tableInfo);
            case 'javascript':
                return $this->generateJavaScriptModel($modelName, $requirements, $tableInfo);
            case 'typescript':
                return $this->generateTypeScriptModel($modelName, $requirements, $tableInfo);
            case 'csharp':
                return $this->generateCSharpModel($modelName, $requirements, $tableInfo);
            case 'go':
                return $this->generateGoModel($modelName, $requirements, $tableInfo);
            default:
                return '';
        }
    }

    /**
     * 生成 Java 模型
     */
    protected function generateJavaModel(string $modelName, array $requirements, array $tableInfo): string
    {
        $fields = $this->generateJavaFields($requirements['data_types']);
        $gettersSetters = $this->generateJavaGettersSetters($requirements['data_types']);
        
        return <<<EOT
package com.example.model;

import javax.persistence.*;
import javax.validation.constraints.*;
import java.time.LocalDateTime;
import java.time.LocalDate;
import java.math.BigDecimal;

@Entity
@Table(name = "{$tableInfo['name']}")
public class {$modelName} {
{$fields}

    // Constructors
    public {$modelName}() {}

{$gettersSetters}

    @Override
    public String toString() {
        return "{$modelName}{" +
                "id=" + id +
                // Add other fields
                '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof {$modelName})) return false;
        {$modelName} that = ({$modelName}) o;
        return Objects.equals(id, that.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }
}
EOT;
    }

    /**
     * 生成 Python 模型
     */
    protected function generatePythonModel(string $modelName, array $requirements, array $tableInfo): string
    {
        $fields = $this->generatePythonFields($requirements['data_types']);
        $className = $this->toPascalCase($modelName);
        
        return <<<EOT
from sqlalchemy import Column, Integer, String, DateTime, Date, Boolean, Text, Decimal
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.sql import func
from pydantic import BaseModel
from typing import Optional
from datetime import datetime, date

Base = declarative_base()

class {$className}(Base):
    __tablename__ = '{$tableInfo['name']}'
    
{$fields}

    def __repr__(self):
        return f"<{$className}(id={self.id})>"

    def to_dict(self):
        return {c.name: getattr(self, c.name) for c in self.__table__.columns}

class {$className}Schema(BaseModel):
{$this->generatePydanticFields($requirements['data_types'])}

    class Config:
        orm_mode = True

class {$className}Create(BaseModel):
{$this->generatePydanticCreateFields($requirements['data_types'])}

class {$className}Update(BaseModel):
{$this->generatePydanticUpdateFields($requirements['data_types'])}
EOT;
    }

    /**
     * 生成 TypeScript 模型
     */
    protected function generateTypeScriptModel(string $modelName, array $requirements, array $tableInfo): string
    {
        $fields = $this->generateTypeScriptFields($requirements['data_types']);
        $interface = $this->generateTypeScriptInterface($requirements['data_types']);
        
        return <<<EOT
import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn } from 'typeorm';
import { IsNotEmpty, IsOptional, IsString, IsNumber, IsBoolean, IsDate } from 'class-validator';

@Entity('{$tableInfo['name']}')
export class {$modelName} {
{$fields}
}

export interface I{$modelName} {
{$interface}
}

export class Create{$modelName}Dto {
{$this->generateTypeScriptCreateDto($requirements['data_types'])}
}

export class Update{$modelName}Dto {
{$this->generateTypeScriptUpdateDto($requirements['data_types'])}
}

export class {$modelName}Response {
{$this->generateTypeScriptResponseDto($requirements['data_types'])}
}
EOT;
    }

    /**
     * 生成控制器代码
     */
    protected function generateController(string $language, array $requirements, array $tableInfo): string
    {
        $modelName = $requirements['language_mapping']['model_names'][$language] ?? 'Model';
        
        switch ($language) {
            case 'java':
                return $this->generateJavaController($modelName, $requirements, $tableInfo);
            case 'python':
                return $this->generatePythonController($modelName, $requirements, $tableInfo);
            case 'typescript':
                return $this->generateTypeScriptController($modelName, $requirements, $tableInfo);
            default:
                return '';
        }
    }

    /**
     * 生成 Java 控制器
     */
    protected function generateJavaController(string $modelName, array $requirements, array $tableInfo): string
    {
        $resourceName = strtolower($modelName);
        
        return <<<EOT
package com.example.controller;

import com.example.model.{$modelName};
import com.example.service.{$modelName}Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

@RestController
@RequestMapping("/api/{$resourceName}")
@CrossOrigin(origins = "*")
public class {$modelName}Controller {

    @Autowired
    private {$modelName}Service {$resourceName}Service;

    @GetMapping
    public ResponseEntity<Page<{$modelName}>> getAll(Pageable pageable) {
        Page<{$modelName}> {$resourceName}s = {$resourceName}Service.findAll(pageable);
        return ResponseEntity.ok({$resourceName}s);
    }

    @GetMapping("/{id}")
    public ResponseEntity<{$modelName}> getById(@PathVariable Long id) {
        {$modelName} {$resourceName} = {$resourceName}Service.findById(id);
        if ({$resourceName} != null) {
            return ResponseEntity.ok({$resourceName});
        }
        return ResponseEntity.notFound().build();
    }

    @PostMapping
    public ResponseEntity<{$modelName}> create(@Valid @RequestBody {$modelName} {$resourceName}) {
        {$modelName} created{$modelName} = {$resourceName}Service.save({$resourceName});
        return ResponseEntity.status(HttpStatus.CREATED).body(created{$modelName});
    }

    @PutMapping("/{id}")
    public ResponseEntity<{$modelName}> update(@PathVariable Long id, @Valid @RequestBody {$modelName} {$resourceName}) {
        {$modelName} updated{$modelName} = {$resourceName}Service.update(id, {$resourceName});
        if (updated{$modelName} != null) {
            return ResponseEntity.ok(updated{$modelName});
        }
        return ResponseEntity.notFound().build();
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Void> delete(@PathVariable Long id) {
        boolean deleted = {$resourceName}Service.deleteById(id);
        if (deleted) {
            return ResponseEntity.noContent().build();
        }
        return ResponseEntity.notFound().build();
    }
}
EOT;
    }

    /**
     * 生成 Python 控制器
     */
    protected function generatePythonController(string $modelName, array $requirements, array $tableInfo): string
    {
        $className = $this->toPascalCase($modelName);
        $resourceName = $this->toSnakeCase($modelName);
        
        return <<<EOT
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import List, Optional
from ..database import get_db
from ..models.{$resourceName} import {$className}
from ..schemas.{$resourceName} import {$className}Schema, {$className}Create, {$className}Update
from ..services.{$resourceName}_service import {$className}Service

router = APIRouter(prefix="/{$resourceName}", tags=["{$resourceName}"])

@router.get("/", response_model=List[{$className}Schema])
async def get_{$resourceName}s(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    db: Session = Depends(get_db)
):
    """获取{$className}列表"""
    service = {$className}Service(db)
    return service.get_all(skip=skip, limit=limit)

@router.get("/{{{$resourceName}_id}}", response_model={$className}Schema)
async def get_{$resourceName}(
    {$resourceName}_id: int,
    db: Session = Depends(get_db)
):
    """根据ID获取{$className}"""
    service = {$className}Service(db)
    {$resourceName} = service.get_by_id({$resourceName}_id)
    if not {$resourceName}:
        raise HTTPException(status_code=404, detail="{$className} not found")
    return {$resourceName}

@router.post("/", response_model={$className}Schema, status_code=201)
async def create_{$resourceName}(
    {$resourceName}_data: {$className}Create,
    db: Session = Depends(get_db)
):
    """创建{$className}"""
    service = {$className}Service(db)
    return service.create({$resourceName}_data)

@router.put("/{{{$resourceName}_id}}", response_model={$className}Schema)
async def update_{$resourceName}(
    {$resourceName}_id: int,
    {$resourceName}_data: {$className}Update,
    db: Session = Depends(get_db)
):
    """更新{$className}"""
    service = {$className}Service(db)
    {$resourceName} = service.update({$resourceName}_id, {$resourceName}_data)
    if not {$resourceName}:
        raise HTTPException(status_code=404, detail="{$className} not found")
    return {$resourceName}

@router.delete("/{{{$resourceName}_id}}", status_code=204)
async def delete_{$resourceName}(
    {$resourceName}_id: int,
    db: Session = Depends(get_db)
):
    """删除{$className}"""
    service = {$className}Service(db)
    success = service.delete({$resourceName}_id)
    if not success:
        raise HTTPException(status_code=404, detail="{$className} not found")
EOT;
    }

    /**
     * 生成项目配置
     */
    protected function generateProjectConfigs(array $multiLanguageAnalysis, array $tableInfo): array
    {
        $configs = [];

        foreach ($multiLanguageAnalysis['requirements'] as $language => $requirements) {
            $configs[$language] = $this->generateProjectConfig($language, $requirements);
        }

        return $configs;
    }

    /**
     * 生成单个语言的项目配置
     */
    protected function generateProjectConfig(string $language, array $requirements): array
    {
        switch ($language) {
            case 'java':
                return [
                    'pom.xml' => $this->generateMavenPom($requirements),
                    'application.yml' => $this->generateSpringBootConfig($requirements),
                ];
            case 'python':
                return [
                    'requirements.txt' => $this->generatePythonRequirements($requirements),
                    'main.py' => $this->generateFastAPIMain($requirements),
                ];
            case 'typescript':
                return [
                    'package.json' => $this->generateNodePackageJson($requirements),
                    'tsconfig.json' => $this->generateTypeScriptConfig($requirements),
                ];
            default:
                return [];
        }
    }

    /**
     * 生成部署配置
     */
    protected function generateDeploymentConfigs(array $multiLanguageAnalysis, array $tableInfo): array
    {
        $configs = [];

        foreach ($multiLanguageAnalysis['requirements'] as $language => $requirements) {
            $configs[$language] = [
                'Dockerfile' => $this->generateDockerfile($language, $requirements),
                'docker-compose.yml' => $this->generateDockerCompose($language, $requirements),
                '.dockerignore' => $this->generateDockerIgnore($language),
            ];
        }

        return $configs;
    }

    /**
     * 生成多语言文档
     */
    protected function generateMultiLanguageDocumentation(array $multiLanguageAnalysis, array $tableInfo): array
    {
        $docs = [];

        foreach ($multiLanguageAnalysis['requirements'] as $language => $requirements) {
            $docs[$language] = [
                'README.md' => $this->generateLanguageReadme($language, $requirements, $tableInfo),
                'API.md' => $this->generateLanguageApiDoc($language, $requirements, $tableInfo),
                'SETUP.md' => $this->generateLanguageSetupDoc($language, $requirements),
            ];
        }

        return $docs;
    }

    // 辅助方法
    protected function generateJavaFields(array $dataTypes): string
    {
        $fields = [];
        foreach ($dataTypes as $fieldName => $typeInfo) {
            $javaType = $typeInfo['mapped_type'];
            $nullable = $typeInfo['nullable'] ? '' : '@NotNull';
            
            if ($fieldName === 'id') {
                $fields[] = "    @Id\n    @GeneratedValue(strategy = GenerationType.IDENTITY)\n    private Long id;";
            } else {
                $fields[] = "    {$nullable}\n    @Column(name = \"{$fieldName}\")\n    private {$javaType} {$fieldName};";
            }
        }
        return implode("\n\n", $fields);
    }

    protected function generateJavaGettersSetters(array $dataTypes): string
    {
        $methods = [];
        foreach ($dataTypes as $fieldName => $typeInfo) {
            $javaType = $typeInfo['mapped_type'];
            $capitalizedName = ucfirst($fieldName);
            
            $methods[] = "    public {$javaType} get{$capitalizedName}() {\n        return {$fieldName};\n    }";
            $methods[] = "    public void set{$capitalizedName}({$javaType} {$fieldName}) {\n        this.{$fieldName} = {$fieldName};\n    }";
        }
        return implode("\n\n", $methods);
    }

    protected function generatePythonFields(array $dataTypes): string
    {
        $fields = [];
        foreach ($dataTypes as $fieldName => $typeInfo) {
            $sqlType = $this->mapToSQLAlchemyType($typeInfo['mapped_type']);
            
            if ($fieldName === 'id') {
                $fields[] = "    id = Column(Integer, primary_key=True, index=True)";
            } else {
                $nullable = $typeInfo['nullable'] ? 'True' : 'False';
                $fields[] = "    {$fieldName} = Column({$sqlType}, nullable={$nullable})";
            }
        }
        return implode("\n", $fields);
    }

    protected function mapToSQLAlchemyType(string $pythonType): string
    {
        $mapping = [
            'int' => 'Integer',
            'str' => 'String',
            'bool' => 'Boolean',
            'float' => 'Float',
            'date' => 'Date',
            'datetime' => 'DateTime',
            'dict' => 'Text',
        ];
        
        return $mapping[$pythonType] ?? 'String';
    }

    protected function toPascalCase(string $string): string
    {
        return str_replace(' ', '', ucwords(str_replace('_', ' ', $string)));
    }

    protected function toSnakeCase(string $string): string
    {
        return strtolower(preg_replace('/(?<!^)[A-Z]/', '_$0', $string));
    }

    // 其他生成方法的简化实现
    protected function generateService(string $language, array $requirements, array $tableInfo): string { return "// Service code for {$language}"; }
    protected function generateRepository(string $language, array $requirements, array $tableInfo): string { return "// Repository code for {$language}"; }
    protected function generateValidation(string $language, array $requirements, array $tableInfo): string { return "// Validation code for {$language}"; }
    protected function generateTests(string $language, array $requirements, array $tableInfo): string { return "// Test code for {$language}"; }
    protected function generateMavenPom(array $requirements): string { return "<!-- Maven POM -->"; }
    protected function generateSpringBootConfig(array $requirements): string { return "# Spring Boot Config"; }
    protected function generatePythonRequirements(array $requirements): string { return "# Python Requirements"; }
    protected function generateFastAPIMain(array $requirements): string { return "# FastAPI Main"; }
    protected function generateNodePackageJson(array $requirements): string { return "// Node Package JSON"; }
    protected function generateTypeScriptConfig(array $requirements): string { return "// TypeScript Config"; }
    protected function generateDockerfile(string $language, array $requirements): string { return "# Dockerfile for {$language}"; }
    protected function generateDockerCompose(string $language, array $requirements): string { return "# Docker Compose for {$language}"; }
    protected function generateDockerIgnore(string $language): string { return "# Docker Ignore for {$language}"; }
    protected function generateLanguageReadme(string $language, array $requirements, array $tableInfo): string { return "# README for {$language}"; }
    protected function generateLanguageApiDoc(string $language, array $requirements, array $tableInfo): string { return "# API Doc for {$language}"; }
    protected function generateLanguageSetupDoc(string $language, array $requirements): string { return "# Setup Doc for {$language}"; }
}
