<?php

namespace app\common\services\curd\v2\helpers;

/**
 * API文档辅助类
 * 提供API文档生成和管理的辅助功能
 */
class ApiDocHelper
{
    /**
     * 获取API文档配置
     */
    public static function getConfig(string $key = null, $default = null)
    {
        $config = config('api_doc', []);
        
        if ($key === null) {
            return $config;
        }
        
        return data_get($config, $key, $default);
    }

    /**
     * 格式化表名
     */
    public static function formatTableName(string $tableName): string
    {
        // 移除表前缀
        $prefix = self::getConfig('database.table_prefix', '');
        if ($prefix && strpos($tableName, $prefix) === 0) {
            $tableName = substr($tableName, strlen($prefix));
        }
        
        return $tableName;
    }

    /**
     * 获取模型名称
     */
    public static function getModelName(string $tableName): string
    {
        $tableName = self::formatTableName($tableName);
        
        // 转换为驼峰命名
        $name = str_replace('_', ' ', $tableName);
        $name = ucwords($name);
        $name = str_replace(' ', '', $name);
        
        return $name;
    }

    /**
     * 获取控制器名称
     */
    public static function getControllerName(string $tableName): string
    {
        return self::getModelName($tableName) . 'Controller';
    }

    /**
     * 映射数据库字段类型到API类型
     */
    public static function mapFieldType(string $dbType): string
    {
        $mapping = self::getConfig('database.field_type_mapping', []);
        
        // 提取基础类型（去除长度等信息）
        $baseType = preg_replace('/\([^)]*\)/', '', strtolower($dbType));
        
        return $mapping[$baseType] ?? 'string';
    }

    /**
     * 生成API路径
     */
    public static function generateApiPath(string $tableName, string $action = null): string
    {
        $baseUrl = self::getConfig('basic.base_url', '/api');
        $tableName = self::formatTableName($tableName);
        
        $path = rtrim($baseUrl, '/') . '/' . $tableName;
        
        if ($action) {
            $path .= '/' . $action;
        }
        
        return $path;
    }

    /**
     * 生成HTTP状态码说明
     */
    public static function getHttpStatusDescription(int $code): string
    {
        $descriptions = [
            200 => '请求成功',
            201 => '创建成功',
            204 => '删除成功',
            400 => '请求参数错误',
            401 => '未授权访问',
            403 => '禁止访问',
            404 => '资源不存在',
            422 => '数据验证失败',
            429 => '请求过于频繁',
            500 => '服务器内部错误'
        ];
        
        return $descriptions[$code] ?? '未知状态';
    }

    /**
     * 生成示例数据
     */
    public static function generateExampleData(array $fields): array
    {
        $example = [];
        
        foreach ($fields as $field) {
            $fieldName = $field['name'];
            $fieldType = self::mapFieldType($field['type']);
            
            switch ($fieldType) {
                case 'integer':
                    $example[$fieldName] = $fieldName === 'id' ? 1 : rand(1, 100);
                    break;
                case 'number':
                    $example[$fieldName] = round(rand(1, 1000) / 10, 2);
                    break;
                case 'boolean':
                    $example[$fieldName] = (bool)rand(0, 1);
                    break;
                case 'array':
                    $example[$fieldName] = [];
                    break;
                case 'object':
                    $example[$fieldName] = new \stdClass();
                    break;
                default:
                    $example[$fieldName] = self::generateExampleString($fieldName);
            }
        }
        
        return $example;
    }

    /**
     * 生成示例字符串
     */
    protected static function generateExampleString(string $fieldName): string
    {
        $examples = [
            'name' => '示例名称',
            'title' => '示例标题',
            'content' => '这是示例内容',
            'description' => '这是示例描述',
            'email' => '<EMAIL>',
            'phone' => '13800138000',
            'address' => '示例地址',
            'url' => 'https://example.com',
            'status' => '1',
            'type' => '1',
            'category' => '示例分类',
            'tag' => '示例标签',
            'remark' => '示例备注',
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];
        
        return $examples[$fieldName] ?? '示例值';
    }

    /**
     * 生成验证规则
     */
    public static function generateValidationRules(array $fields): array
    {
        $rules = [];
        
        foreach ($fields as $field) {
            $fieldName = $field['name'];
            $fieldType = $field['type'];
            $isNullable = $field['nullable'] ?? false;
            $maxLength = $field['length'] ?? null;
            
            // 跳过自动字段
            if (in_array($fieldName, ['id', 'created_at', 'updated_at'])) {
                continue;
            }
            
            $rule = [];
            
            // 必填验证
            if (!$isNullable) {
                $rule[] = 'required';
            }
            
            // 类型验证
            switch (self::mapFieldType($fieldType)) {
                case 'integer':
                    $rule[] = 'integer';
                    break;
                case 'number':
                    $rule[] = 'numeric';
                    break;
                case 'string':
                    $rule[] = 'string';
                    if ($maxLength) {
                        $rule[] = "max:{$maxLength}";
                    }
                    break;
            }
            
            // 特殊字段验证
            if (strpos($fieldName, 'email') !== false) {
                $rule[] = 'email';
            }
            
            if (strpos($fieldName, 'url') !== false) {
                $rule[] = 'url';
            }
            
            if (!empty($rule)) {
                $rules[$fieldName] = implode('|', $rule);
            }
        }
        
        return $rules;
    }

    /**
     * 生成API文档缓存键
     */
    public static function getCacheKey(string $type, string $identifier = ''): string
    {
        $prefix = self::getConfig('cache.prefix', 'api_doc:');
        return $prefix . $type . ($identifier ? ':' . $identifier : '');
    }

    /**
     * 检查是否启用缓存
     */
    public static function isCacheEnabled(): bool
    {
        return self::getConfig('cache.enabled', true);
    }

    /**
     * 获取缓存TTL
     */
    public static function getCacheTTL(): int
    {
        return self::getConfig('cache.ttl', 3600);
    }

    /**
     * 格式化文件大小
     */
    public static function formatFileSize(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        $factor = floor((strlen($bytes) - 1) / 3);
        
        return sprintf("%.1f%s", $bytes / pow(1024, $factor), $units[$factor]);
    }

    /**
     * 生成唯一标识符
     */
    public static function generateUniqueId(string $prefix = ''): string
    {
        return $prefix . uniqid() . mt_rand(1000, 9999);
    }

    /**
     * 验证表名是否有效
     */
    public static function isValidTableName(string $tableName): bool
    {
        // 检查是否在排除列表中
        $excludedTables = self::getConfig('database.excluded_tables', []);
        if (in_array($tableName, $excludedTables)) {
            return false;
        }
        
        // 检查表名格式
        if (!preg_match('/^[a-zA-Z_][a-zA-Z0-9_]*$/', $tableName)) {
            return false;
        }
        
        return true;
    }

    /**
     * 验证字段名是否有效
     */
    public static function isValidFieldName(string $fieldName): bool
    {
        // 检查是否在排除列表中
        $excludedFields = self::getConfig('database.excluded_fields', []);
        if (in_array($fieldName, $excludedFields)) {
            return false;
        }
        
        return true;
    }

    /**
     * 生成API文档元数据
     */
    public static function generateMetadata(string $tableName): array
    {
        return [
            'table_name' => $tableName,
            'model_name' => self::getModelName($tableName),
            'controller_name' => self::getControllerName($tableName),
            'api_path' => self::generateApiPath($tableName),
            'generated_at' => date('Y-m-d H:i:s'),
            'version' => self::getConfig('basic.version', '1.0.0'),
            'generator' => 'EasyAdmin8-webman CURD Generator V2'
        ];
    }

    /**
     * 获取支持的HTTP方法
     */
    public static function getSupportedMethods(): array
    {
        return self::getConfig('generation.http_methods', ['GET', 'POST', 'PUT', 'DELETE']);
    }

    /**
     * 获取支持的导出格式
     */
    public static function getSupportedExportFormats(): array
    {
        $formats = self::getConfig('export.formats', []);
        
        // 过滤掉未启用的格式
        return array_filter($formats, function($format) {
            return !isset($format['enabled']) || $format['enabled'] === true;
        });
    }

    /**
     * 生成分享令牌
     */
    public static function generateShareToken(): string
    {
        $length = self::getConfig('security.public_share.token_length', 32);
        return bin2hex(random_bytes($length / 2));
    }

    /**
     * 检查是否允许公开分享
     */
    public static function isPublicShareEnabled(): bool
    {
        return self::getConfig('security.public_share.enabled', false);
    }

    /**
     * 获取测试环境列表
     */
    public static function getTestEnvironments(): array
    {
        return self::getConfig('testing.environments', []);
    }

    /**
     * 获取默认测试环境
     */
    public static function getDefaultTestEnvironment(): ?array
    {
        $environments = self::getTestEnvironments();
        
        foreach ($environments as $env) {
            if (isset($env['default']) && $env['default']) {
                return $env;
            }
        }
        
        return reset($environments) ?: null;
    }

    /**
     * 记录操作日志
     */
    public static function logOperation(string $operation, array $data = []): void
    {
        if (!self::getConfig('logging.enabled', true)) {
            return;
        }
        
        $logOperations = self::getConfig('logging.log_operations', []);
        if (!isset($logOperations[$operation]) || !$logOperations[$operation]) {
            return;
        }
        
        $logData = [
            'operation' => $operation,
            'timestamp' => date('Y-m-d H:i:s'),
            'user_id' => session('admin.id', 0),
            'ip' => request()->getRealIp(),
            'user_agent' => request()->header('User-Agent'),
            'data' => $data
        ];
        
        // 这里可以实现具体的日志记录逻辑
        error_log('[API_DOC] ' . json_encode($logData, JSON_UNESCAPED_UNICODE));
    }

    /**
     * 清理过期文件
     */
    public static function cleanupExpiredFiles(): int
    {
        $storagePath = self::getConfig('export.storage_path', 'storage/api_docs');
        $retentionDays = self::getConfig('export.retention_days', 7);
        
        if (!is_dir($storagePath)) {
            return 0;
        }
        
        $expireTime = time() - ($retentionDays * 24 * 3600);
        $deletedCount = 0;
        
        $files = glob($storagePath . '/*');
        foreach ($files as $file) {
            if (is_file($file) && filemtime($file) < $expireTime) {
                if (unlink($file)) {
                    $deletedCount++;
                }
            }
        }
        
        return $deletedCount;
    }

    /**
     * 获取性能统计信息
     */
    public static function getPerformanceStats(): array
    {
        return [
            'memory_usage' => memory_get_usage(true),
            'memory_peak' => memory_get_peak_usage(true),
            'execution_time' => microtime(true) - $_SERVER['REQUEST_TIME_FLOAT'],
            'included_files' => count(get_included_files())
        ];
    }
}
