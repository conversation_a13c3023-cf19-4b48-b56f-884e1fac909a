<?php
/**
 * 测试模拟表功能
 */

echo "=== 测试模拟表功能 ===\n\n";

// 模拟 webman 环境
require_once __DIR__ . '/vendor/autoload.php';

// 模拟配置函数
if (!function_exists('config')) {
    function config($key, $default = null) {
        $configs = [
            'database.connections.mysql.database' => 'easyadmin8',
            'database.connections.mysql.prefix' => 'ea8_',
            'database.connections.mysql_read.database' => 'easyadmin8_read',
            'database.connections.mysql_read.prefix' => 'ea8_',
            'database.connections.mysql_second.database' => 'easyadmin8_second',
            'database.connections.mysql_second.prefix' => 'ea8_',
        ];
        
        return $configs[$key] ?? $default;
    }
}

try {
    echo "🧪 测试 TableAnalyzer 模拟功能\n";
    
    $analyzer = new \app\common\services\curd\v2\analyzers\TableAnalyzer();
    
    $connections = ['mysql', 'mysql_read', 'mysql_second', 'mysql_log', 'mysql_cache'];
    
    foreach ($connections as $connection) {
        echo "\n📋 测试连接: {$connection}\n";
        
        try {
            // 测试获取表列表
            $tables = $analyzer->getAllTables($connection);
            echo "   ✅ 获取表列表成功: " . count($tables) . " 个表\n";
            
            foreach ($tables as $table) {
                echo "      - {$table['name']} ({$table['comment']})\n";
            }
            
            // 测试第一个表的分析
            if (!empty($tables)) {
                $firstTable = $tables[0];
                echo "\n   🔍 分析表: {$firstTable['name']}\n";
                
                // 测试表是否存在
                $exists = $analyzer->tableExists($firstTable['name'], 'ea8_', $connection);
                echo "      表存在检查: " . ($exists ? '✅ 存在' : '❌ 不存在') . "\n";
                
                if ($exists) {
                    // 测试表分析
                    try {
                        $tableInfo = $analyzer->analyze($firstTable['name'], 'ea8_', $connection);
                        echo "      ✅ 表分析成功\n";
                        echo "         表名: " . $tableInfo->getName() . "\n";
                        echo "         前缀: " . $tableInfo->getPrefix() . "\n";
                        echo "         注释: " . $tableInfo->getComment() . "\n";
                        echo "         字段数: " . count($tableInfo->getFields()) . "\n";
                        
                        // 显示前3个字段
                        $fields = $tableInfo->getFields();
                        echo "         前3个字段:\n";
                        for ($i = 0; $i < min(3, count($fields)); $i++) {
                            $field = $fields[$i];
                            echo "           - {$field->getName()} ({$field->getType()}) - {$field->getComment()}\n";
                        }
                        
                    } catch (\Exception $e) {
                        echo "      ❌ 表分析失败: " . $e->getMessage() . "\n";
                    }
                }
            }
            
        } catch (\Exception $e) {
            echo "   ❌ 测试失败: " . $e->getMessage() . "\n";
        }
        
        echo "\n" . str_repeat("-", 50) . "\n";
    }
    
    echo "\n🧪 测试 CurdGenerator 模拟功能\n";
    
    $generator = new \app\common\services\curd\v2\CurdGenerator();
    
    foreach (['mysql', 'mysql_read', 'mysql_second'] as $connection) {
        echo "\n📋 测试连接: {$connection}\n";
        
        try {
            $tables = $generator->getAllTables($connection);
            echo "   ✅ 获取表列表成功: " . count($tables) . " 个表\n";
            
            foreach ($tables as $table) {
                echo "      - {$table['name']} ({$table['comment']})\n";
            }
            
            // 测试第一个表
            if (!empty($tables)) {
                $firstTable = $tables[0];
                echo "\n   🔍 测试表: {$firstTable['name']}\n";
                
                // 测试表验证
                $isValid = $generator->validateTable($firstTable['name'], 'ea8_', $connection);
                echo "      表验证: " . ($isValid ? '✅ 通过' : '❌ 失败') . "\n";
                
                if ($isValid) {
                    // 测试获取表信息
                    try {
                        $tableInfo = $generator->getTableInfo($firstTable['name'], 'ea8_', $connection);
                        echo "      ✅ 获取表信息成功\n";
                        echo "         表名: " . $tableInfo['name'] . "\n";
                        echo "         注释: " . $tableInfo['comment'] . "\n";
                        echo "         字段数: " . count($tableInfo['fields']) . "\n";
                        
                    } catch (\Exception $e) {
                        echo "      ❌ 获取表信息失败: " . $e->getMessage() . "\n";
                    }
                }
            }
            
        } catch (\Exception $e) {
            echo "   ❌ 测试失败: " . $e->getMessage() . "\n";
        }
    }
    
} catch (\Exception $e) {
    echo "❌ 初始化失败: " . $e->getMessage() . "\n";
}

echo "\n=== 模拟功能测试完成 ===\n";
echo "\n💡 说明:\n";
echo "由于数据库连接问题，系统自动启用了模拟数据模式\n";
echo "这样可以在没有真实数据库的情况下演示多数据库 CURD 生成器功能\n";
echo "\n🎯 现在可以测试前端功能:\n";
echo "1. 访问: http://localhost:8787/admin/system/curdgeneratev2\n";
echo "2. 登录后台\n";
echo "3. 选择不同的数据库连接\n";
echo "4. 点击刷新表列表 - 应该能看到模拟的表数据\n";
echo "5. 选择表进行分析 - 应该能看到模拟的字段信息\n";

echo "\n🔧 要使用真实数据库:\n";
echo "1. 确保 MySQL 服务正在运行\n";
echo "2. 创建数据库和用户\n";
echo "3. 更新 config/database.php 中的连接信息\n";
echo "4. 重启 webman 服务\n";
?>
