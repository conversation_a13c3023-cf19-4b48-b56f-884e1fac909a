<?php
/**
 * 视图布局文件修复验证脚本
 */

echo "=== EasyAdmin8-webman 视图布局修复验证 ===\n\n";

// 检查布局文件是否存在
$layoutFiles = [
    'app/admin/view/admin/layout/base.blade.php' => 'CURD生成器V2布局文件',
    'app/admin/view/admin/layouts/app.blade.php' => 'API文档布局文件',
];

echo "1. 检查布局文件是否存在\n";
foreach ($layoutFiles as $file => $description) {
    if (file_exists($file)) {
        echo "   ✅ {$description}: {$file}\n";
    } else {
        echo "   ❌ {$description}: {$file} - 文件不存在\n";
    }
}

// 检查视图文件的引用
echo "\n2. 检查视图文件引用\n";
$viewFiles = [
    'app/admin/view/admin/system/curdgeneratev2/index.blade.php' => 'admin.layout.base',
    'app/admin/view/admin/system/apidoc/index.blade.php' => 'admin.layouts.app',
    'app/admin/view/admin/system/apidoc/view.blade.php' => 'admin.layouts.app',
];

foreach ($viewFiles as $file => $layout) {
    if (file_exists($file)) {
        $content = file_get_contents($file);
        if (strpos($content, "@extends('{$layout}')") !== false) {
            echo "   ✅ {$file} -> {$layout}\n";
        } else {
            echo "   ⚠️  {$file} -> 布局引用可能不匹配\n";
        }
    } else {
        echo "   ❌ {$file} - 视图文件不存在\n";
    }
}

// 检查布局文件内容
echo "\n3. 检查布局文件内容完整性\n";
foreach ($layoutFiles as $file => $description) {
    if (file_exists($file)) {
        $content = file_get_contents($file);
        $checks = [
            '@yield(\'content\')' => '内容区域',
            '@stack(\'styles\')' => '样式扩展',
            '@stack(\'scripts\')' => '脚本扩展',
            'layui.js' => 'Layui框架',
            'window.CONFIG' => '配置变量'
        ];
        
        echo "   检查 {$description}:\n";
        foreach ($checks as $pattern => $name) {
            if (strpos($content, $pattern) !== false) {
                echo "     ✅ {$name}\n";
            } else {
                echo "     ❌ {$name} - 缺失\n";
            }
        }
    }
}

// 检查目录结构
echo "\n4. 检查目录结构\n";
$directories = [
    'app/admin/view/admin/layout' => '布局目录',
    'app/admin/view/admin/layouts' => '布局目录(复数)',
    'app/admin/view/admin/system/curdgeneratev2' => 'CURD生成器V2视图目录',
    'app/admin/view/admin/system/apidoc' => 'API文档视图目录',
];

foreach ($directories as $dir => $description) {
    if (is_dir($dir)) {
        echo "   ✅ {$description}: {$dir}\n";
    } else {
        echo "   ❌ {$description}: {$dir} - 目录不存在\n";
    }
}

// 检查权限
echo "\n5. 检查文件权限\n";
foreach ($layoutFiles as $file => $description) {
    if (file_exists($file)) {
        if (is_readable($file)) {
            echo "   ✅ {$description}: 可读\n";
        } else {
            echo "   ❌ {$description}: 不可读\n";
        }
    }
}

echo "\n=== 验证完成 ===\n";
echo "如果所有检查都显示 ✅，说明视图布局问题已修复\n";
echo "现在可以尝试访问 CURD 生成器 V2 页面\n";
echo "访问地址: http://localhost:8787/admin/system/curdgeneratev2\n";
?>