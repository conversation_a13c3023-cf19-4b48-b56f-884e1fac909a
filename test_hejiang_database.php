<?php
/**
 * 测试 hejiang 数据库连接
 */

echo "=== 测试 hejiang 数据库连接 ===\n\n";

// 直接测试数据库连接
$config = [
    'host' => '127.0.0.1',
    'port' => 3306,
    'database' => 'hejiang',
    'username' => 'root',
    'password' => '5GeNi1v7P7Xcur5W',
    'prefix' => 'zjhj_bd_'
];

echo "📋 连接信息:\n";
echo "   主机: {$config['host']}:{$config['port']}\n";
echo "   数据库: {$config['database']}\n";
echo "   用户名: {$config['username']}\n";
echo "   密码: " . str_repeat('*', strlen($config['password'])) . "\n";
echo "   前缀: {$config['prefix']}\n\n";

try {
    echo "🔍 测试数据库连接...\n";
    
    $dsn = "mysql:host={$config['host']};port={$config['port']};dbname={$config['database']};charset=utf8mb4";
    $pdo = new PDO($dsn, $config['username'], $config['password'], [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_TIMEOUT => 5
    ]);
    
    echo "   ✅ 连接成功!\n\n";
    
    // 获取 MySQL 版本
    $stmt = $pdo->query("SELECT VERSION() as version");
    $version = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "📊 MySQL 版本: {$version['version']}\n\n";
    
    // 获取所有表
    echo "📋 获取数据库表列表...\n";
    $stmt = $pdo->query("SHOW TABLES");
    $allTables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    echo "   总表数: " . count($allTables) . "\n";
    
    if (count($allTables) > 0) {
        echo "   所有表:\n";
        foreach ($allTables as $table) {
            echo "      - {$table}\n";
        }
        echo "\n";
        
        // 筛选带前缀的表
        $prefixTables = array_filter($allTables, function($table) use ($config) {
            return strpos($table, $config['prefix']) === 0;
        });
        
        echo "🎯 带前缀 '{$config['prefix']}' 的表:\n";
        echo "   匹配表数: " . count($prefixTables) . "\n";
        
        if (count($prefixTables) > 0) {
            foreach ($prefixTables as $table) {
                $displayName = substr($table, strlen($config['prefix']));
                echo "      - {$table} (显示为: {$displayName})\n";
            }
            echo "\n";
            
            // 分析第一个表的结构
            $firstTable = reset($prefixTables);
            echo "🔍 分析表结构: {$firstTable}\n";
            
            try {
                // 获取表注释
                $stmt = $pdo->prepare("
                    SELECT table_comment 
                    FROM information_schema.tables 
                    WHERE table_schema = ? AND table_name = ?
                ");
                $stmt->execute([$config['database'], $firstTable]);
                $tableInfo = $stmt->fetch(PDO::FETCH_ASSOC);
                $tableComment = $tableInfo['table_comment'] ?? '';
                
                echo "   表注释: " . ($tableComment ?: '无注释') . "\n";
                
                // 获取字段信息
                $stmt = $pdo->query("SHOW FULL COLUMNS FROM {$firstTable}");
                $fields = $stmt->fetchAll(PDO::FETCH_ASSOC);
                
                echo "   字段数: " . count($fields) . "\n";
                echo "   字段详情:\n";
                
                foreach ($fields as $field) {
                    $nullable = $field['Null'] === 'YES' ? '可空' : '非空';
                    $key = $field['Key'] ? " [{$field['Key']}]" : '';
                    $comment = $field['Comment'] ? " - {$field['Comment']}" : '';
                    
                    echo "      - {$field['Field']} ({$field['Type']}) {$nullable}{$key}{$comment}\n";
                }
                
                // 获取索引信息
                echo "\n   索引信息:\n";
                $stmt = $pdo->query("SHOW INDEX FROM {$firstTable}");
                $indexes = $stmt->fetchAll(PDO::FETCH_ASSOC);
                
                $indexGroups = [];
                foreach ($indexes as $index) {
                    $indexGroups[$index['Key_name']][] = $index['Column_name'];
                }
                
                foreach ($indexGroups as $indexName => $columns) {
                    $type = $indexName === 'PRIMARY' ? '主键' : '索引';
                    echo "      - {$type}: {$indexName} (" . implode(', ', $columns) . ")\n";
                }
                
            } catch (PDOException $e) {
                echo "   ❌ 分析表结构失败: " . $e->getMessage() . "\n";
            }
            
        } else {
            echo "   ⚠️  没有找到带前缀 '{$config['prefix']}' 的表\n";
            echo "   💡 建议:\n";
            echo "      1. 检查前缀是否正确\n";
            echo "      2. 或者修改前缀配置\n";
            echo "      3. 或者使用无前缀连接\n";
        }
        
    } else {
        echo "   ⚠️  数据库中没有表\n";
    }
    
} catch (PDOException $e) {
    echo "   ❌ 连接失败: " . $e->getMessage() . "\n";
    
    if (strpos($e->getMessage(), 'Access denied') !== false) {
        echo "\n💡 认证失败解决方案:\n";
        echo "   1. 检查用户名和密码是否正确\n";
        echo "   2. 确认用户是否有访问该数据库的权限\n";
        echo "   3. 检查 MySQL 用户权限设置\n";
    } elseif (strpos($e->getMessage(), 'Unknown database') !== false) {
        echo "\n💡 数据库不存在解决方案:\n";
        echo "   1. 创建数据库: CREATE DATABASE hejiang;\n";
        echo "   2. 或者修改配置中的数据库名\n";
    } elseif (strpos($e->getMessage(), 'Connection refused') !== false) {
        echo "\n💡 连接被拒绝解决方案:\n";
        echo "   1. 检查 MySQL 服务是否启动\n";
        echo "   2. 检查主机和端口是否正确\n";
        echo "   3. 检查防火墙设置\n";
    }
    
    exit(1);
}

echo "\n=== 测试 webman 框架集成 ===\n";

// 测试是否可以通过 webman 的 Db 类连接
try {
    // 模拟 webman 环境
    if (file_exists('vendor/autoload.php')) {
        require_once 'vendor/autoload.php';
        
        // 测试通过 webman API 获取表列表
        echo "🧪 测试 API 接口...\n";
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, 'http://localhost:8787/admin/system/curdgeneratev2');
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query([
            'action' => 'get_tables',
            'connection' => 'mysql_second'
        ]));
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/x-www-form-urlencoded',
        ]);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($httpCode === 200) {
            $json = json_decode($response, true);
            if ($json && isset($json['code'])) {
                echo "   ✅ API 响应成功\n";
                echo "   📊 响应码: {$json['code']}\n";
                echo "   📝 消息: {$json['msg']}\n";
                
                if (isset($json['data']) && is_array($json['data'])) {
                    echo "   📈 返回表数: " . count($json['data']) . "\n";
                    
                    if (count($json['data']) > 0) {
                        echo "   📋 表列表:\n";
                        foreach ($json['data'] as $table) {
                            if (is_array($table) && isset($table['name'])) {
                                echo "      - {$table['name']} ({$table['comment']})\n";
                            }
                        }
                    }
                }
            } else {
                echo "   ❌ API 响应格式错误\n";
            }
        } else {
            echo "   ❌ API 请求失败 (HTTP {$httpCode})\n";
        }
    }
    
} catch (Exception $e) {
    echo "   ❌ webman 集成测试失败: " . $e->getMessage() . "\n";
}

echo "\n=== 测试完成 ===\n";

if (isset($pdo)) {
    echo "🎉 hejiang 数据库连接配置成功!\n\n";
    
    echo "📋 配置总结:\n";
    echo "   连接名: mysql_second\n";
    echo "   数据库: hejiang\n";
    echo "   前缀: zjhj_bd_\n";
    echo "   状态: ✅ 可用\n\n";
    
    echo "🧪 现在可以测试:\n";
    echo "   1. 访问: http://localhost:8787/admin/system/curdgeneratev2\n";
    echo "   2. 登录后台\n";
    echo "   3. 选择数据库连接: mysql_second (第二数据库)\n";
    echo "   4. 点击刷新表列表\n";
    echo "   5. 应该能看到 hejiang 数据库中的表\n\n";
    
    echo "🔧 如需调整:\n";
    echo "   • 修改前缀: 编辑 config/database.php 中的 prefix 值\n";
    echo "   • 添加更多连接: 复制 mysql_second 配置并修改\n";
} else {
    echo "❌ 数据库连接配置失败\n";
    echo "请检查连接信息并重新配置\n";
}
?>
