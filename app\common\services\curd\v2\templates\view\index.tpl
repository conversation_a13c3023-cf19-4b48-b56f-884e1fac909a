@include('admin.layout.head')
<div class="layuimini-container">
    <div class="layuimini-main">
        
        <!-- 搜索表单 -->
@if(searchableFields)
        <fieldset class="table-search-fieldset">
            <legend>搜索信息</legend>
            <div style="margin: 10px 10px 10px 10px">
                <form class="layui-form layui-form-pane" action="">
                    <div class="layui-form-item">
@foreach($searchableFields as $field)
                        <div class="layui-inline">
                            <label class="layui-form-label">{{$field->getComment()}}</label>
                            <div class="layui-input-inline">
@if($field->getComponent() == 'select')
                                <select name="{{$field->getName()}}" lay-search>
                                    <option value="">请选择{{$field->getComment()}}</option>
                                    <!-- 这里可以添加选项 -->
                                </select>
@elseif($field->getComponent() == 'date')
                                <input type="text" name="{{$field->getName()}}" autocomplete="off" class="layui-input" placeholder="请选择{{$field->getComment()}}">
@elseif($field->getComponent() == 'datetime')
                                <input type="text" name="{{$field->getName()}}" autocomplete="off" class="layui-input" placeholder="请选择{{$field->getComment()}}">
@else
                                <input type="text" name="{{$field->getName()}}" autocomplete="off" class="layui-input" placeholder="请输入{{$field->getComment()}}">
@endif
                            </div>
                        </div>
@endforeach
                        <div class="layui-inline">
                            <button type="submit" class="layui-btn layui-btn-primary" lay-submit lay-filter="data-search-btn">
                                <i class="layui-icon"></i> 搜索
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </fieldset>
@endif

        <!-- 数据表格 -->
        <table class="layui-hide" id="currentTable" lay-filter="currentTable"></table>

        <!-- 工具栏 -->
        <script type="text/html" id="toolbarDemo">
            <div class="layui-btn-container">
                <button class="layui-btn layui-btn-normal layui-btn-sm data-add-btn" lay-event="add">
                    <i class="layui-icon layui-icon-add-1"></i> 添加
                </button>
@if(enableBatch)
                <button class="layui-btn layui-btn-sm layui-btn-warm data-batch-btn" lay-event="batchDelete">
                    <i class="layui-icon layui-icon-delete"></i> 批量删除
                </button>
@endif
@if(enableExport)
                <button class="layui-btn layui-btn-sm layui-btn-normal data-export-btn" lay-event="export">
                    <i class="layui-icon layui-icon-export"></i> 导出
                </button>
@endif
            </div>
        </script>

        <!-- 行工具栏 -->
        <script type="text/html" id="currentTableBar">
            <a class="layui-btn layui-btn-xs data-count-edit" lay-event="edit">
                <i class="layui-icon layui-icon-edit"></i> 编辑
            </a>
            <a class="layui-btn layui-btn-xs layui-btn-danger data-count-delete" lay-event="delete">
                <i class="layui-icon layui-icon-close"></i> 删除
            </a>
        </script>

        <!-- 状态切换 -->
        <script type="text/html" id="statusTpl">
            <input type="checkbox" name="status" value="@{{d.{{primaryKey}}}}" lay-skin="switch" lay-text="启用|禁用" lay-filter="statusDemo" @{{ d.status == 1 ? 'checked' : '' }}>
        </script>

    </div>
</div>
@include('admin.layout.foot')
