<?php
/**
 * MySQL 连接凭据测试脚本
 */

echo "=== MySQL 连接凭据测试 ===\n\n";

// 常见的 MySQL 凭据组合
$credentials = [
    ['username' => 'root', 'password' => ''],
    ['username' => 'root', 'password' => 'root'],
    ['username' => 'root', 'password' => '123456'],
    ['username' => 'root', 'password' => 'password'],
    ['username' => 'root', 'password' => 'admin'],
    ['username' => 'mysql', 'password' => ''],
    ['username' => 'mysql', 'password' => 'mysql'],
    ['username' => 'admin', 'password' => 'admin'],
];

$hosts = ['127.0.0.1', 'localhost'];
$ports = [3306, 3307];

$successfulConnection = null;

foreach ($hosts as $host) {
    foreach ($ports as $port) {
        echo "🔍 测试主机: {$host}:{$port}\n";
        
        foreach ($credentials as $cred) {
            $username = $cred['username'];
            $password = $cred['password'];
            $passwordDisplay = $password === '' ? '(空密码)' : $password;
            
            echo "   尝试: {$username} / {$passwordDisplay}\n";
            
            try {
                $dsn = "mysql:host={$host};port={$port};charset=utf8mb4";
                $pdo = new PDO($dsn, $username, $password, [
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::ATTR_TIMEOUT => 3
                ]);
                
                echo "   ✅ 连接成功！\n";
                
                // 获取 MySQL 版本
                $stmt = $pdo->query("SELECT VERSION() as version");
                $version = $stmt->fetch(PDO::FETCH_ASSOC);
                echo "   📊 MySQL 版本: {$version['version']}\n";
                
                // 获取可用数据库
                $stmt = $pdo->query("SHOW DATABASES");
                $databases = $stmt->fetchAll(PDO::FETCH_COLUMN);
                echo "   📋 可用数据库: " . implode(', ', $databases) . "\n";
                
                $successfulConnection = [
                    'host' => $host,
                    'port' => $port,
                    'username' => $username,
                    'password' => $password,
                    'databases' => $databases
                ];
                
                echo "\n   🎯 找到可用连接！\n\n";
                break 3; // 跳出所有循环
                
            } catch (PDOException $e) {
                if (strpos($e->getMessage(), 'Access denied') !== false) {
                    echo "   ❌ 认证失败\n";
                } elseif (strpos($e->getMessage(), 'Connection refused') !== false) {
                    echo "   ❌ 连接被拒绝 (服务可能未启动)\n";
                } else {
                    echo "   ❌ 错误: " . $e->getMessage() . "\n";
                }
            }
        }
        echo "\n";
    }
}

if ($successfulConnection) {
    echo "🎉 找到可用的 MySQL 连接！\n\n";
    
    $conn = $successfulConnection;
    echo "📋 连接信息:\n";
    echo "   主机: {$conn['host']}\n";
    echo "   端口: {$conn['port']}\n";
    echo "   用户名: {$conn['username']}\n";
    echo "   密码: " . ($conn['password'] === '' ? '(空密码)' : $conn['password']) . "\n";
    echo "   可用数据库: " . implode(', ', $conn['databases']) . "\n\n";
    
    // 检查是否有 easyadmin8 数据库
    if (in_array('easyadmin8', $conn['databases'])) {
        echo "✅ easyadmin8 数据库存在\n";
        
        // 检查表
        try {
            $dsn = "mysql:host={$conn['host']};port={$conn['port']};dbname=easyadmin8;charset=utf8mb4";
            $pdo = new PDO($dsn, $conn['username'], $conn['password']);
            
            $stmt = $pdo->query("SHOW TABLES");
            $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
            echo "📊 easyadmin8 中的表数量: " . count($tables) . "\n";
            
            if (count($tables) > 0) {
                echo "📋 表列表:\n";
                foreach ($tables as $table) {
                    echo "   - {$table}\n";
                }
            }
            
        } catch (PDOException $e) {
            echo "❌ 查询 easyadmin8 失败: " . $e->getMessage() . "\n";
        }
    } else {
        echo "⚠️  easyadmin8 数据库不存在\n";
        
        // 建议使用现有数据库
        $suitableDbs = array_filter($conn['databases'], function($db) {
            return !in_array($db, ['information_schema', 'performance_schema', 'mysql', 'sys']);
        });
        
        if (!empty($suitableDbs)) {
            echo "💡 可以使用的现有数据库:\n";
            foreach ($suitableDbs as $db) {
                echo "   - {$db}\n";
                
                // 检查这个数据库的表
                try {
                    $dsn = "mysql:host={$conn['host']};port={$conn['port']};dbname={$db};charset=utf8mb4";
                    $pdo = new PDO($dsn, $conn['username'], $conn['password']);
                    
                    $stmt = $pdo->query("SHOW TABLES");
                    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
                    echo "     表数量: " . count($tables) . "\n";
                    
                } catch (PDOException $e) {
                    echo "     查询失败: " . $e->getMessage() . "\n";
                }
            }
        }
    }
    
    echo "\n🔧 更新配置建议:\n";
    echo "请更新 config/database.php 或 .env 文件:\n";
    echo "DB_HOST={$conn['host']}\n";
    echo "DB_PORT={$conn['port']}\n";
    echo "DB_USERNAME={$conn['username']}\n";
    echo "DB_PASSWORD={$conn['password']}\n";
    
    if (in_array('easyadmin8', $conn['databases'])) {
        echo "DB_DATABASE=easyadmin8\n";
    } elseif (!empty($suitableDbs)) {
        $firstDb = reset($suitableDbs);
        echo "DB_DATABASE={$firstDb}\n";
    } else {
        echo "DB_DATABASE=test  # 或创建新数据库\n";
    }
    
} else {
    echo "❌ 未找到可用的 MySQL 连接\n\n";
    echo "🔧 解决方案:\n";
    echo "1. 确保 MySQL 服务正在运行\n";
    echo "2. 检查 MySQL 配置和用户权限\n";
    echo "3. 尝试重置 root 密码\n";
    echo "4. 检查防火墙设置\n";
    echo "5. 考虑使用其他数据库用户\n";
}

echo "\n=== 测试完成 ===\n";
?>
