<?php
/**
 * 开发服务器路由器
 */

// 静态文件处理
$request_uri = $_SERVER["REQUEST_URI"];
$path = parse_url($request_uri, PHP_URL_PATH);

// 处理静态文件
if ($path !== "/" && file_exists(__DIR__ . "/public" . $path)) {
    return false; // 让内置服务器处理静态文件
}

// 设置工作目录
chdir(__DIR__);

// 加载应用
require_once "vendor/autoload.php";

// 加载环境变量
if (class_exists("Dotenv\Dotenv") && file_exists(".env")) {
    if (method_exists("Dotenv\Dotenv", "createUnsafeImmutable")) {
        Dotenv\Dotenv::createUnsafeImmutable(__DIR__)->load();
    } else {
        Dotenv\Dotenv::createMutable(__DIR__)->load();
    }
}

// 加载配置
support\App::loadAllConfig(["route"]);

try {
    // 创建请求和响应对象
    $request = new support\Request();
    $response = new support\Response();
    
    // 处理请求
    $app = new Webman\App($request, $response);
    $result = $app->process($request);
    
    // 输出响应
    if ($result instanceof support\Response) {
        http_response_code($result->getStatusCode());
        
        foreach ($result->getHeaders() as $name => $values) {
            foreach ($values as $value) {
                header("$name: $value", false);
            }
        }
        
        echo $result->getBody();
    } else {
        echo $result;
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo "错误: " . $e->getMessage();
    if ($_ENV["APP_DEBUG"] ?? false) {
        echo "\n\n" . $e->getTraceAsString();
    }
}
