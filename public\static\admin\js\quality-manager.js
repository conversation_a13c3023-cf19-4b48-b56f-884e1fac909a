/**
 * CURD 代码质量管理组件
 * 支持代码质量分析、优化和报告生成
 */
define(['jquery', 'layui'], function($, layui) {
    'use strict';
    
    var QualityManager = function(options) {
        this.options = $.extend({
            apiUrl: '/admin/system/curd_generate_v2',
            container: '#quality-manager-container',
            enableAutoAnalyze: true,
            enableAutoOptimize: false
        }, options);
        
        this.container = $(this.options.container);
        this.currentCode = null;
        this.currentTableInfo = null;
        this.qualityAnalysis = null;
        this.optimizationResult = null;
        
        this.severityColors = {
            'error': '#ff4d4f',
            'warning': '#fa8c16',
            'info': '#1890ff'
        };
        
        this.gradeColors = {
            'A': '#52c41a',
            'B': '#73d13d',
            'C': '#fadb14',
            'D': '#fa8c16',
            'F': '#ff4d4f'
        };
        
        this.init();
    };
    
    QualityManager.prototype = {
        
        /**
         * 初始化质量管理器
         */
        init: function() {
            this.bindEvents();
            this.initUI();
        },
        
        /**
         * 绑定事件
         */
        bindEvents: function() {
            var self = this;
            
            // 分析代码质量
            $(document).on('click', '.analyze-quality-btn', function() {
                self.analyzeCodeQuality();
            });
            
            // 优化代码质量
            $(document).on('click', '.optimize-quality-btn', function() {
                self.optimizeCodeQuality();
            });
            
            // 查看质量报告
            $(document).on('click', '.view-quality-report-btn', function() {
                self.viewQualityReport();
            });
            
            // 查看问题详情
            $(document).on('click', '.view-issue-detail', function() {
                var issueIndex = $(this).data('index');
                self.showIssueDetail(issueIndex);
            });
            
            // 应用优化建议
            $(document).on('click', '.apply-optimization', function() {
                self.applyOptimization();
            });
            
            // 切换自动优化
            $(document).on('change', '.auto-optimize-switch', function() {
                var enabled = $(this).prop('checked');
                self.toggleAutoOptimize(enabled);
            });
        },
        
        /**
         * 初始化UI
         */
        initUI: function() {
            if (this.container.length === 0) {
                return;
            }
            
            var html = `
                <div class="quality-manager">
                    <div class="quality-header">
                        <h3>代码质量检查</h3>
                        <div class="quality-actions">
                            <button type="button" class="layui-btn layui-btn-sm analyze-quality-btn">
                                <i class="layui-icon layui-icon-search"></i> 分析质量
                            </button>
                            <button type="button" class="layui-btn layui-btn-sm layui-btn-normal optimize-quality-btn">
                                <i class="layui-icon layui-icon-engine"></i> 自动优化
                            </button>
                            <button type="button" class="layui-btn layui-btn-sm layui-btn-warm view-quality-report-btn">
                                <i class="layui-icon layui-icon-chart"></i> 质量报告
                            </button>
                        </div>
                    </div>
                    <div class="quality-content">
                        <div class="quality-options" id="quality-options"></div>
                        <div class="quality-overview" id="quality-overview"></div>
                        <div class="quality-issues" id="quality-issues"></div>
                        <div class="quality-suggestions" id="quality-suggestions"></div>
                    </div>
                </div>
            `;
            
            this.container.html(html);
            this.renderQualityOptions();
        },
        
        /**
         * 渲染质量选项
         */
        renderQualityOptions: function() {
            var container = $('#quality-options');
            
            var html = `
                <div class="options-panel">
                    <h4>质量检查选项</h4>
                    <div class="options-grid">
                        <label class="layui-form-switch">
                            <input type="checkbox" class="auto-optimize-switch" ${this.options.enableAutoOptimize ? 'checked' : ''}>
                            <div class="layui-form-switch-text">自动优化|手动优化</div>
                        </label>
                    </div>
                </div>
            `;
            
            container.html(html);
            layui.form.render();
        },
        
        /**
         * 分析代码质量
         */
        analyzeCodeQuality: function() {
            var self = this;
            
            if (!this.currentCode) {
                layer.msg('请先生成代码');
                return;
            }
            
            var loading = layer.load(2, { content: '正在分析代码质量...' });
            
            $.ajax({
                url: this.options.apiUrl,
                type: 'POST',
                data: {
                    action: 'analyze_code_quality',
                    generated_code: this.currentCode,
                    table_info: this.currentTableInfo || {},
                    config: {}
                },
                success: function(response) {
                    layer.close(loading);
                    
                    if (response.code === 0) {
                        self.qualityAnalysis = response.data;
                        self.renderQualityOverview();
                        self.renderQualityIssues();
                        self.renderQualitySuggestions();
                        layer.msg('代码质量分析完成', { icon: 1 });
                    } else {
                        layer.msg(response.msg || '分析失败');
                    }
                },
                error: function() {
                    layer.close(loading);
                    layer.msg('网络错误');
                }
            });
        },
        
        /**
         * 渲染质量概览
         */
        renderQualityOverview: function() {
            var container = $('#quality-overview');
            var metrics = this.qualityAnalysis.metrics;
            var summary = this.qualityAnalysis.summary;
            
            var gradeColor = this.gradeColors[summary.grade];
            
            var html = `
                <div class="quality-overview-panel">
                    <div class="quality-score">
                        <div class="score-circle" style="border-color: ${gradeColor}">
                            <div class="score-value" style="color: ${gradeColor}">${metrics.quality_score}</div>
                            <div class="score-grade" style="color: ${gradeColor}">${summary.grade}</div>
                        </div>
                        <div class="score-status">${summary.overall_assessment}</div>
                    </div>
                    <div class="quality-metrics">
                        <div class="metric-item">
                            <div class="metric-value">${metrics.total_issues}</div>
                            <div class="metric-label">总问题数</div>
                        </div>
                        <div class="metric-item">
                            <div class="metric-value">${metrics.error_count}</div>
                            <div class="metric-label">错误</div>
                        </div>
                        <div class="metric-item">
                            <div class="metric-value">${metrics.warning_count}</div>
                            <div class="metric-label">警告</div>
                        </div>
                        <div class="metric-item">
                            <div class="metric-value">${metrics.info_count}</div>
                            <div class="metric-label">建议</div>
                        </div>
                        <div class="metric-item">
                            <div class="metric-value">${metrics.maintainability_index}</div>
                            <div class="metric-label">可维护性</div>
                        </div>
                        <div class="metric-item">
                            <div class="metric-value">${metrics.technical_debt_hours}h</div>
                            <div class="metric-label">技术债务</div>
                        </div>
                    </div>
                </div>
            `;
            
            container.html(html);
        },
        
        /**
         * 渲染质量问题
         */
        renderQualityIssues: function() {
            var container = $('#quality-issues');
            var issues = this.qualityAnalysis.issues;
            
            if (issues.length === 0) {
                container.html('<div class="no-issues">🎉 没有发现代码质量问题</div>');
                return;
            }
            
            var html = '<div class="issues-list">';
            
            issues.forEach(function(issue, index) {
                var severityColor = this.severityColors[issue.severity];
                var severityName = this.getSeverityName(issue.severity);
                
                html += `
                    <div class="issue-card" data-index="${index}">
                        <div class="issue-header">
                            <div class="issue-severity">
                                <span class="severity-badge" style="background-color: ${severityColor}">${severityName}</span>
                                <span class="issue-type">${this.getTypeName(issue.type)}</span>
                            </div>
                            <div class="issue-line">第 ${issue.line} 行</div>
                        </div>
                        <div class="issue-body">
                            <div class="issue-message">${issue.message}</div>
                            <div class="issue-suggestion">${issue.suggestion}</div>
                        </div>
                        <div class="issue-footer">
                            <button type="button" class="layui-btn layui-btn-xs view-issue-detail" data-index="${index}">
                                查看详情
                            </button>
                        </div>
                    </div>
                `;
            }.bind(this));
            
            html += '</div>';
            container.html(html);
        },
        
        /**
         * 渲染质量建议
         */
        renderQualitySuggestions: function() {
            var container = $('#quality-suggestions');
            var suggestions = this.qualityAnalysis.suggestions;
            
            if (suggestions.length === 0) {
                container.html('<div class="no-suggestions">暂无优化建议</div>');
                return;
            }
            
            var html = '<div class="suggestions-list">';
            
            suggestions.forEach(function(suggestion, index) {
                var priorityColor = this.getPriorityColor(suggestion.priority);
                
                html += `
                    <div class="suggestion-card">
                        <div class="suggestion-header">
                            <div class="suggestion-title">${suggestion.title}</div>
                            <span class="priority-badge" style="background-color: ${priorityColor}">${suggestion.priority}</span>
                        </div>
                        <div class="suggestion-body">
                            <div class="suggestion-description">${suggestion.description}</div>
                            <div class="suggestion-actions">
                                <h5>建议操作:</h5>
                                <ul>
                                    ${suggestion.action_items.map(item => `<li>${item}</li>`).join('')}
                                </ul>
                            </div>
                        </div>
                    </div>
                `;
            }.bind(this));
            
            html += '</div>';
            container.html(html);
        },
        
        /**
         * 优化代码质量
         */
        optimizeCodeQuality: function() {
            var self = this;
            
            if (!this.qualityAnalysis) {
                layer.msg('请先分析代码质量');
                return;
            }
            
            var loading = layer.load(2, { content: '正在优化代码质量...' });
            
            $.ajax({
                url: this.options.apiUrl,
                type: 'POST',
                data: {
                    action: 'optimize_code_quality',
                    generated_code: this.currentCode,
                    quality_analysis: this.qualityAnalysis
                },
                success: function(response) {
                    layer.close(loading);
                    
                    if (response.code === 0) {
                        self.optimizationResult = response.data;
                        self.showOptimizationResult();
                        layer.msg('代码优化完成', { icon: 1 });
                    } else {
                        layer.msg(response.msg || '优化失败');
                    }
                },
                error: function() {
                    layer.close(loading);
                    layer.msg('网络错误');
                }
            });
        },
        
        /**
         * 显示优化结果
         */
        showOptimizationResult: function() {
            var result = this.optimizationResult;
            var summary = result.improvement_summary;
            
            var html = `
                <div class="optimization-result">
                    <div class="result-header">
                        <h3>代码优化结果</h3>
                        <div class="optimization-stats">
                            <div class="stat-item">
                                <div class="stat-value">${summary.total_optimizations}</div>
                                <div class="stat-label">总优化数</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value">${summary.critical_improvements}</div>
                                <div class="stat-label">关键改进</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value">${summary.high_improvements}</div>
                                <div class="stat-label">重要改进</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value">+${summary.quality_boost_estimate}</div>
                                <div class="stat-label">质量提升</div>
                            </div>
                        </div>
                    </div>
                    <div class="result-content">
                        <div class="optimization-log">
                            <h4>优化日志</h4>
                            ${this.renderOptimizationLog(result.optimization_log)}
                        </div>
                        <div class="result-actions">
                            <button type="button" class="layui-btn apply-optimization">应用优化</button>
                            <button type="button" class="layui-btn layui-btn-primary" onclick="layer.closeAll()">取消</button>
                        </div>
                    </div>
                </div>
            `;
            
            layer.open({
                type: 1,
                title: '代码优化结果',
                content: html,
                area: ['900px', '700px'],
                maxmin: true
            });
        },
        
        /**
         * 渲染优化日志
         */
        renderOptimizationLog: function(log) {
            var html = '<div class="log-list">';
            
            log.forEach(function(entry) {
                var impactColor = this.getImpactColor(entry.impact);
                
                html += `
                    <div class="log-entry">
                        <div class="log-header">
                            <span class="impact-badge" style="background-color: ${impactColor}">${entry.impact}</span>
                            <span class="log-type">${this.getTypeName(entry.issue.type)}</span>
                        </div>
                        <div class="log-content">
                            <div class="log-issue">${entry.issue.message}</div>
                            <div class="log-optimization">${entry.optimization}</div>
                        </div>
                    </div>
                `;
            }.bind(this));
            
            html += '</div>';
            return html;
        },
        
        /**
         * 查看质量报告
         */
        viewQualityReport: function() {
            var self = this;
            
            if (!this.currentCode) {
                layer.msg('请先生成代码');
                return;
            }
            
            var loading = layer.load(2, { content: '正在生成质量报告...' });
            
            $.ajax({
                url: this.options.apiUrl,
                type: 'POST',
                data: {
                    action: 'get_quality_report',
                    generated_code: this.currentCode,
                    table_info: this.currentTableInfo || {}
                },
                success: function(response) {
                    layer.close(loading);
                    
                    if (response.code === 0) {
                        self.showQualityReport(response.data);
                    } else {
                        layer.msg(response.msg || '生成失败');
                    }
                },
                error: function() {
                    layer.close(loading);
                    layer.msg('网络错误');
                }
            });
        },
        
        /**
         * 显示质量报告
         */
        showQualityReport: function(report) {
            var html = `
                <div class="quality-report">
                    <div class="report-header">
                        <h3>代码质量报告</h3>
                        <div class="report-summary">
                            <div class="summary-item">
                                <div class="summary-label">质量等级</div>
                                <div class="summary-value grade-${report.analysis.summary.grade}">${report.analysis.summary.grade}</div>
                            </div>
                            <div class="summary-item">
                                <div class="summary-label">质量分数</div>
                                <div class="summary-value">${report.analysis.metrics.quality_score}</div>
                            </div>
                        </div>
                    </div>
                    <div class="report-content">
                        ${this.renderReportSections(report)}
                    </div>
                </div>
            `;
            
            layer.open({
                type: 1,
                title: '代码质量报告',
                content: html,
                area: ['1000px', '800px'],
                maxmin: true
            });
        },
        
        /**
         * 渲染报告章节
         */
        renderReportSections: function(report) {
            var html = `
                <div class="layui-tab layui-tab-brief">
                    <ul class="layui-tab-title">
                        <li class="layui-this">质量分析</li>
                        <li>基准对比</li>
                        <li>改进建议</li>
                    </ul>
                    <div class="layui-tab-content">
                        <div class="layui-tab-item layui-show">
                            ${this.renderAnalysisSection(report.analysis)}
                        </div>
                        <div class="layui-tab-item">
                            ${this.renderBenchmarkSection(report.metrics_comparison)}
                        </div>
                        <div class="layui-tab-item">
                            ${this.renderRecommendationSection(report.recommendations)}
                        </div>
                    </div>
                </div>
            `;
            
            return html;
        },
        
        /**
         * 显示问题详情
         */
        showIssueDetail: function(issueIndex) {
            var issue = this.qualityAnalysis.issues[issueIndex];
            if (!issue) return;
            
            var severityColor = this.severityColors[issue.severity];
            
            var html = `
                <div class="issue-detail">
                    <div class="detail-header">
                        <h4>
                            <span class="severity-badge" style="background-color: ${severityColor}">${this.getSeverityName(issue.severity)}</span>
                            ${issue.message}
                        </h4>
                    </div>
                    <div class="detail-content">
                        <div class="detail-section">
                            <h5>问题描述</h5>
                            <p>${issue.message}</p>
                        </div>
                        <div class="detail-section">
                            <h5>位置信息</h5>
                            <p>第 ${issue.line} 行</p>
                        </div>
                        <div class="detail-section">
                            <h5>优化建议</h5>
                            <p>${issue.suggestion}</p>
                        </div>
                        <div class="detail-section">
                            <h5>问题类型</h5>
                            <p>${this.getTypeName(issue.type)}</p>
                        </div>
                    </div>
                </div>
            `;
            
            layer.open({
                type: 1,
                title: '问题详情',
                content: html,
                area: ['600px', '500px']
            });
        },
        
        /**
         * 应用优化
         */
        applyOptimization: function() {
            if (this.optimizationResult) {
                this.currentCode = this.optimizationResult.optimized_code;
                
                // 触发代码更新事件
                if (window.onCodeOptimized) {
                    window.onCodeOptimized(this.currentCode);
                }
                
                layer.closeAll();
                layer.msg('优化已应用', { icon: 1 });
                
                // 重新分析质量
                this.analyzeCodeQuality();
            }
        },
        
        /**
         * 切换自动优化
         */
        toggleAutoOptimize: function(enabled) {
            this.options.enableAutoOptimize = enabled;
        },
        
        /**
         * 设置当前代码
         */
        setCurrentCode: function(code, tableInfo) {
            this.currentCode = code;
            this.currentTableInfo = tableInfo;
            
            if (this.options.enableAutoAnalyze && code) {
                this.analyzeCodeQuality();
            }
        },
        
        /**
         * 获取严重程度名称
         */
        getSeverityName: function(severity) {
            var names = {
                'error': '错误',
                'warning': '警告',
                'info': '建议'
            };
            return names[severity] || severity;
        },
        
        /**
         * 获取类型名称
         */
        getTypeName: function(type) {
            var names = {
                'naming_convention': '命名规范',
                'code_complexity': '代码复杂度',
                'security_issues': '安全问题',
                'performance_issues': '性能问题',
                'best_practices': '最佳实践',
                'documentation': '文档完整性'
            };
            return names[type] || type;
        },
        
        /**
         * 获取优先级颜色
         */
        getPriorityColor: function(priority) {
            var colors = {
                'critical': '#ff4d4f',
                'high': '#fa8c16',
                'medium': '#fadb14',
                'low': '#52c41a'
            };
            return colors[priority] || '#1890ff';
        },
        
        /**
         * 获取影响颜色
         */
        getImpactColor: function(impact) {
            var colors = {
                'critical': '#ff4d4f',
                'high': '#fa8c16',
                'medium': '#fadb14',
                'low': '#52c41a'
            };
            return colors[impact] || '#1890ff';
        },
        
        /**
         * 渲染分析章节
         */
        renderAnalysisSection: function(analysis) {
            // 简化实现
            return '<div>质量分析详情...</div>';
        },
        
        /**
         * 渲染基准对比章节
         */
        renderBenchmarkSection: function(comparison) {
            // 简化实现
            return '<div>基准对比详情...</div>';
        },
        
        /**
         * 渲染建议章节
         */
        renderRecommendationSection: function(recommendations) {
            // 简化实现
            return '<div>改进建议详情...</div>';
        },
        
        /**
         * 销毁组件
         */
        destroy: function() {
            this.container.empty();
            $(document).off('.quality-manager');
        }
    };
    
    return QualityManager;
});
