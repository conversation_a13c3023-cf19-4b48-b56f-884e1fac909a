# 视图布局文件修复

## 问题描述
项目中的某些视图文件引用了不存在的布局文件，导致 `View [admin.layout.base] not found` 错误。

## 缺失的布局文件
1. `app/admin/view/admin/layout/base.blade.php` - 被 `curdgeneratev2/index.blade.php` 引用
2. `app/admin/view/admin/layouts/app.blade.php` - 被 API 文档相关视图引用

## 解决方案
创建了缺失的布局文件，包含以下结构：

### base.blade.php 和 app.blade.php 布局结构
```html
<!DOCTYPE html>
<html>
<head>
    <!-- 基础 meta 标签 -->
    <!-- CSS 样式引用 -->
    <!-- JavaScript 配置 -->
    <!-- 核心 JS 库引用 -->
    @stack('styles')
</head>
<body>
    <div class="layuimini-container">
        <div class="layuimini-main">
            @yield('content')
        </div>
    </div>
    @stack('scripts')
</body>
</html>
```

## 布局文件功能
- 包含系统配置变量
- 引用必要的 CSS 和 JavaScript 文件
- 提供 `@yield('content')` 内容区域
- 支持 `@stack('styles')` 和 `@stack('scripts')` 扩展

## 相关视图文件
- `app/admin/view/admin/system/curdgeneratev2/index.blade.php` - 使用 `admin.layout.base`
- `app/admin/view/admin/system/apidoc/index.blade.php` - 使用 `admin.layouts.app`
- `app/admin/view/admin/system/apidoc/view.blade.php` - 使用 `admin.layouts.app`

## 注意事项
- 布局文件基于现有的 `head.blade.php` 和 `foot.blade.php` 创建
- 保持了与项目现有样式和脚本的兼容性
- 支持 Blade 模板的扩展功能