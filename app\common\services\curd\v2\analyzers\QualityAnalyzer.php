<?php

namespace app\common\services\curd\v2\analyzers;

/**
 * 代码质量分析器
 * 分析生成代码的质量并提供优化建议
 */
class QualityAnalyzer
{
    protected array $qualityRules = [
        'naming_convention' => '命名规范检查',
        'code_complexity' => '代码复杂度检查',
        'security_issues' => '安全问题检查',
        'performance_issues' => '性能问题检查',
        'best_practices' => '最佳实践检查',
        'documentation' => '文档完整性检查',
    ];

    protected array $severityLevels = [
        'error' => ['level' => 3, 'name' => '错误', 'color' => '#ff4d4f'],
        'warning' => ['level' => 2, 'name' => '警告', 'color' => '#fa8c16'],
        'info' => ['level' => 1, 'name' => '建议', 'color' => '#1890ff'],
    ];

    /**
     * 分析代码质量
     */
    public function analyzeCodeQuality(array $generatedCode, array $tableInfo, array $config = []): array
    {
        $issues = [];
        $metrics = [];

        // 分析控制器代码
        if (!empty($generatedCode['controller'])) {
            $controllerIssues = $this->analyzeControllerCode($generatedCode['controller'], $tableInfo);
            $issues = array_merge($issues, $controllerIssues);
        }

        // 分析模型代码
        if (!empty($generatedCode['model'])) {
            $modelIssues = $this->analyzeModelCode($generatedCode['model'], $tableInfo);
            $issues = array_merge($issues, $modelIssues);
        }

        // 分析视图代码
        if (!empty($generatedCode['view'])) {
            $viewIssues = $this->analyzeViewCode($generatedCode['view'], $tableInfo);
            $issues = array_merge($issues, $viewIssues);
        }

        // 分析 API 代码
        if (!empty($generatedCode['api_controller'])) {
            $apiIssues = $this->analyzeApiCode($generatedCode['api_controller'], $tableInfo);
            $issues = array_merge($issues, $apiIssues);
        }

        // 分析关联关系代码
        if (!empty($generatedCode['relationships'])) {
            $relationshipIssues = $this->analyzeRelationshipCode($generatedCode['relationships'], $tableInfo);
            $issues = array_merge($issues, $relationshipIssues);
        }

        // 计算质量指标
        $metrics = $this->calculateQualityMetrics($issues, $generatedCode);

        return [
            'issues' => $issues,
            'metrics' => $metrics,
            'summary' => $this->generateQualitySummary($issues, $metrics),
            'suggestions' => $this->generateOptimizationSuggestions($issues, $metrics),
        ];
    }

    /**
     * 分析控制器代码
     */
    protected function analyzeControllerCode(string $code, array $tableInfo): array
    {
        $issues = [];

        // 检查命名规范
        $issues = array_merge($issues, $this->checkNamingConvention($code, 'controller'));

        // 检查方法复杂度
        $issues = array_merge($issues, $this->checkMethodComplexity($code));

        // 检查安全问题
        $issues = array_merge($issues, $this->checkSecurityIssues($code));

        // 检查错误处理
        $issues = array_merge($issues, $this->checkErrorHandling($code));

        // 检查验证规则
        $issues = array_merge($issues, $this->checkValidationRules($code, $tableInfo));

        return $issues;
    }

    /**
     * 分析模型代码
     */
    protected function analyzeModelCode(string $code, array $tableInfo): array
    {
        $issues = [];

        // 检查模型属性
        $issues = array_merge($issues, $this->checkModelAttributes($code, $tableInfo));

        // 检查关联关系
        $issues = array_merge($issues, $this->checkModelRelationships($code));

        // 检查访问器和修改器
        $issues = array_merge($issues, $this->checkAccessorsAndMutators($code));

        // 检查模型事件
        $issues = array_merge($issues, $this->checkModelEvents($code));

        return $issues;
    }

    /**
     * 分析视图代码
     */
    protected function analyzeViewCode(string $code, array $tableInfo): array
    {
        $issues = [];

        // 检查 XSS 防护
        $issues = array_merge($issues, $this->checkXssProtection($code));

        // 检查表单验证
        $issues = array_merge($issues, $this->checkFormValidation($code));

        // 检查用户体验
        $issues = array_merge($issues, $this->checkUserExperience($code));

        // 检查响应式设计
        $issues = array_merge($issues, $this->checkResponsiveDesign($code));

        return $issues;
    }

    /**
     * 分析 API 代码
     */
    protected function analyzeApiCode(string $code, array $tableInfo): array
    {
        $issues = [];

        // 检查 RESTful 规范
        $issues = array_merge($issues, $this->checkRestfulConventions($code));

        // 检查 API 安全
        $issues = array_merge($issues, $this->checkApiSecurity($code));

        // 检查响应格式
        $issues = array_merge($issues, $this->checkResponseFormat($code));

        // 检查错误处理
        $issues = array_merge($issues, $this->checkApiErrorHandling($code));

        return $issues;
    }

    /**
     * 分析关联关系代码
     */
    protected function analyzeRelationshipCode(string $code, array $tableInfo): array
    {
        $issues = [];

        // 检查关联关系定义
        $issues = array_merge($issues, $this->checkRelationshipDefinitions($code));

        // 检查 N+1 查询问题
        $issues = array_merge($issues, $this->checkNPlusOneQueries($code));

        // 检查关联关系性能
        $issues = array_merge($issues, $this->checkRelationshipPerformance($code));

        return $issues;
    }

    /**
     * 检查命名规范
     */
    protected function checkNamingConvention(string $code, string $type): array
    {
        $issues = [];

        // 检查类名
        if (!preg_match('/class\s+[A-Z][a-zA-Z0-9]*/', $code)) {
            $issues[] = [
                'type' => 'naming_convention',
                'severity' => 'warning',
                'message' => '类名应使用 PascalCase 命名规范',
                'line' => $this->findLineNumber($code, 'class'),
                'suggestion' => '使用大写字母开头的驼峰命名法，如 UserController',
            ];
        }

        // 检查方法名
        if (preg_match_all('/public\s+function\s+([A-Z][a-zA-Z0-9]*)/', $code, $matches)) {
            foreach ($matches[1] as $methodName) {
                $issues[] = [
                    'type' => 'naming_convention',
                    'severity' => 'warning',
                    'message' => "方法名 '{$methodName}' 应使用 camelCase 命名规范",
                    'line' => $this->findLineNumber($code, "function {$methodName}"),
                    'suggestion' => '使用小写字母开头的驼峰命名法，如 getUserList',
                ];
            }
        }

        return $issues;
    }

    /**
     * 检查方法复杂度
     */
    protected function checkMethodComplexity(string $code): array
    {
        $issues = [];

        // 检查方法长度
        preg_match_all('/public\s+function\s+(\w+).*?\{(.*?)\n\s*\}/s', $code, $matches, PREG_SET_ORDER);

        foreach ($matches as $match) {
            $methodName = $match[1];
            $methodBody = $match[2];
            $lineCount = substr_count($methodBody, "\n");

            if ($lineCount > 50) {
                $issues[] = [
                    'type' => 'code_complexity',
                    'severity' => 'warning',
                    'message' => "方法 '{$methodName}' 过长 ({$lineCount} 行)",
                    'line' => $this->findLineNumber($code, "function {$methodName}"),
                    'suggestion' => '考虑将长方法拆分为多个小方法，提高代码可读性',
                ];
            }

            // 检查嵌套层级
            $nestingLevel = $this->calculateNestingLevel($methodBody);
            if ($nestingLevel > 4) {
                $issues[] = [
                    'type' => 'code_complexity',
                    'severity' => 'warning',
                    'message' => "方法 '{$methodName}' 嵌套层级过深 ({$nestingLevel} 层)",
                    'line' => $this->findLineNumber($code, "function {$methodName}"),
                    'suggestion' => '减少嵌套层级，使用早期返回或提取方法',
                ];
            }
        }

        return $issues;
    }

    /**
     * 检查安全问题 (优化版)
     */
    protected function checkSecurityIssues(string $code): array
    {
        $issues = [];

        // SQL注入检查 - 更精确的模式匹配
        $sqlInjectionPatterns = [
            '/\$[a-zA-Z_]+\s*\.\s*["\'][^"\']*["\']/' => 'SQL字符串拼接可能导致注入',
            '/query\s*\(\s*["\'][^"\']*\$[a-zA-Z_]+/' => '动态SQL查询可能存在注入风险',
            '/WHERE\s+[^=]*=\s*["\']?\$[a-zA-Z_]+["\']?(?!\s*\))/' => 'WHERE条件直接使用变量可能存在注入',
            '/INSERT\s+INTO\s+[^(]*\([^)]*\$[a-zA-Z_]+/' => 'INSERT语句直接使用变量可能存在注入',
        ];

        foreach ($sqlInjectionPatterns as $pattern => $message) {
            if (preg_match($pattern, $code)) {
                $issues[] = [
                    'type' => 'security_issues',
                    'severity' => 'error',
                    'message' => $message,
                    'suggestion' => '使用参数化查询、ORM方法或预处理语句',
                    'line' => $this->findLineNumber($code, $pattern),
                    'security_level' => 'critical'
                ];
            }
        }

        // XSS检查 - 更全面的检测
        $xssPatterns = [
            '/echo\s+\$[a-zA-Z_]+(?!.*(?:htmlspecialchars|e\(|strip_tags))/' => '直接输出变量可能存在XSS风险',
            '/print\s+\$[a-zA-Z_]+(?!.*(?:htmlspecialchars|e\(|strip_tags))/' => '直接打印变量可能存在XSS风险',
            '/\{\{\s*\$[a-zA-Z_]+\s*\}\}(?!.*\|e)/' => '模板输出未转义可能存在XSS风险',
            '/<\?=\s*\$[a-zA-Z_]+(?!.*(?:htmlspecialchars|e\())/' => 'PHP短标签输出未转义可能存在XSS风险',
        ];

        foreach ($xssPatterns as $pattern => $message) {
            if (preg_match($pattern, $code)) {
                $issues[] = [
                    'type' => 'security_issues',
                    'severity' => 'warning',
                    'message' => $message,
                    'suggestion' => '使用htmlspecialchars()、e()函数或模板转义',
                    'line' => $this->findLineNumber($code, $pattern),
                    'security_level' => 'high'
                ];
            }
        }

        // CSRF检查
        if (preg_match('/function\s+(store|update|destroy)/', $code)) {
            if (!preg_match('/csrf|token|middleware.*auth/', $code)) {
                $issues[] = [
                    'type' => 'security_issues',
                    'severity' => 'warning',
                    'message' => '敏感操作缺少CSRF保护',
                    'suggestion' => '添加CSRF中间件或token验证',
                    'line' => $this->findLineNumber($code, 'function\s+(store|update|destroy)'),
                    'security_level' => 'medium'
                ];
            }
        }

        // 权限检查 - 更详细的检测
        $sensitiveActions = ['store', 'update', 'destroy', 'delete'];
        foreach ($sensitiveActions as $action) {
            if (preg_match("/function\s+{$action}/", $code)) {
                if (!preg_match('/auth|permission|can|authorize|middleware/', $code)) {
                    $issues[] = [
                        'type' => 'security_issues',
                        'severity' => 'warning',
                        'message' => "敏感操作 {$action} 缺少权限检查",
                        'suggestion' => '添加权限验证中间件或手动权限检查',
                        'line' => $this->findLineNumber($code, "function\s+{$action}"),
                        'security_level' => 'medium'
                    ];
                }
            }
        }

        // 文件上传安全检查
        if (preg_match('/move_uploaded_file|file_put_contents|fwrite/', $code)) {
            if (!preg_match('/mime_content_type|pathinfo|getimagesize|in_array.*mime/', $code)) {
                $issues[] = [
                    'type' => 'security_issues',
                    'severity' => 'error',
                    'message' => '文件上传缺少类型验证',
                    'suggestion' => '添加文件类型、大小和内容验证',
                    'line' => $this->findLineNumber($code, 'move_uploaded_file|file_put_contents|fwrite'),
                    'security_level' => 'critical'
                ];
            }
        }

        // 敏感信息泄露检查
        $sensitivePatterns = [
            '/password\s*=\s*["\'][^"\']+["\']/' => '硬编码密码可能泄露敏感信息',
            '/api_key\s*=\s*["\'][^"\']+["\']/' => '硬编码API密钥可能泄露',
            '/secret\s*=\s*["\'][^"\']+["\']/' => '硬编码密钥可能泄露',
            '/token\s*=\s*["\'][^"\']+["\']/' => '硬编码令牌可能泄露',
        ];

        foreach ($sensitivePatterns as $pattern => $message) {
            if (preg_match($pattern, $code)) {
                $issues[] = [
                    'type' => 'security_issues',
                    'severity' => 'error',
                    'message' => $message,
                    'suggestion' => '使用环境变量或配置文件存储敏感信息',
                    'line' => $this->findLineNumber($code, $pattern),
                    'security_level' => 'critical'
                ];
            }
        }

        // 路径遍历检查
        if (preg_match('/\$[a-zA-Z_]+.*\.\.\//', $code)) {
            $issues[] = [
                'type' => 'security_issues',
                'severity' => 'error',
                'message' => '可能存在路径遍历漏洞',
                'suggestion' => '验证和过滤用户输入的路径参数',
                'line' => $this->findLineNumber($code, '\$[a-zA-Z_]+.*\.\.\/'),
                'security_level' => 'high'
            ];
        }

        // 命令注入检查
        if (preg_match('/exec|system|shell_exec|passthru/', $code)) {
            if (!preg_match('/escapeshellarg|escapeshellcmd/', $code)) {
                $issues[] = [
                    'type' => 'security_issues',
                    'severity' => 'error',
                    'message' => '系统命令执行可能存在注入风险',
                    'suggestion' => '使用escapeshellarg()或escapeshellcmd()转义参数',
                    'line' => $this->findLineNumber($code, 'exec|system|shell_exec|passthru'),
                    'security_level' => 'critical'
                ];
            }
        }

        return $issues;
    }

    /**
     * 检查错误处理
     */
    protected function checkErrorHandling(string $code): array
    {
        $issues = [];

        // 检查 try-catch 使用
        $tryCount = substr_count($code, 'try {');
        $methodCount = substr_count($code, 'public function');

        if ($tryCount < $methodCount * 0.8) {
            $issues[] = [
                'type' => 'best_practices',
                'severity' => 'info',
                'message' => '建议增加异常处理',
                'line' => 1,
                'suggestion' => '在可能出错的操作周围添加 try-catch 块',
            ];
        }

        // 检查错误日志
        if (!preg_match('/Log::|logger|error_log/', $code)) {
            $issues[] = [
                'type' => 'best_practices',
                'severity' => 'info',
                'message' => '建议添加错误日志记录',
                'line' => 1,
                'suggestion' => '在 catch 块中记录错误日志，便于调试',
            ];
        }

        return $issues;
    }

    /**
     * 检查验证规则
     */
    protected function checkValidationRules(string $code, array $tableInfo): array
    {
        $issues = [];

        // 检查必填字段验证
        $requiredFields = array_filter($tableInfo['fields'], function($field) {
            return !empty($field['required']);
        });

        foreach ($requiredFields as $field) {
            if (!preg_match("/'{$field['name']}'\s*=>\s*['\"].*required/", $code)) {
                $issues[] = [
                    'type' => 'best_practices',
                    'severity' => 'warning',
                    'message' => "字段 '{$field['name']}' 缺少必填验证",
                    'line' => $this->findLineNumber($code, $field['name']),
                    'suggestion' => "为必填字段添加 'required' 验证规则",
                ];
            }
        }

        return $issues;
    }

    /**
     * 计算质量指标
     */
    protected function calculateQualityMetrics(array $issues, array $generatedCode): array
    {
        $totalIssues = count($issues);
        $errorCount = count(array_filter($issues, fn($issue) => $issue['severity'] === 'error'));
        $warningCount = count(array_filter($issues, fn($issue) => $issue['severity'] === 'warning'));
        $infoCount = count(array_filter($issues, fn($issue) => $issue['severity'] === 'info'));

        // 计算代码行数
        $totalLines = 0;
        foreach ($generatedCode as $code) {
            if (is_string($code)) {
                $totalLines += substr_count($code, "\n");
            }
        }

        // 计算质量分数 (0-100)
        $qualityScore = 100;
        $qualityScore -= $errorCount * 10;    // 错误扣10分
        $qualityScore -= $warningCount * 5;   // 警告扣5分
        $qualityScore -= $infoCount * 2;      // 建议扣2分
        $qualityScore = max(0, $qualityScore);

        // 计算技术债务
        $technicalDebt = $errorCount * 4 + $warningCount * 2 + $infoCount * 1; // 小时

        return [
            'quality_score' => $qualityScore,
            'total_issues' => $totalIssues,
            'error_count' => $errorCount,
            'warning_count' => $warningCount,
            'info_count' => $infoCount,
            'total_lines' => $totalLines,
            'issues_per_100_lines' => $totalLines > 0 ? round(($totalIssues / $totalLines) * 100, 2) : 0,
            'technical_debt_hours' => $technicalDebt,
            'maintainability_index' => $this->calculateMaintainabilityIndex($qualityScore, $totalLines, $totalIssues),
        ];
    }

    /**
     * 生成质量总结
     */
    protected function generateQualitySummary(array $issues, array $metrics): array
    {
        $grade = $this->getQualityGrade($metrics['quality_score']);

        return [
            'grade' => $grade,
            'score' => $metrics['quality_score'],
            'status' => $this->getQualityStatus($metrics['quality_score']),
            'primary_concerns' => $this->getPrimaryConcerns($issues),
            'strengths' => $this->getCodeStrengths($metrics),
            'overall_assessment' => $this->getOverallAssessment($metrics),
        ];
    }

    /**
     * 生成优化建议
     */
    protected function generateOptimizationSuggestions(array $issues, array $metrics): array
    {
        $suggestions = [];

        // 基于问题类型生成建议
        $issueTypes = array_count_values(array_column($issues, 'type'));

        foreach ($issueTypes as $type => $count) {
            if ($count >= 3) {
                $suggestions[] = [
                    'category' => $type,
                    'priority' => $this->getSuggestionPriority($type, $count),
                    'title' => $this->getSuggestionTitle($type),
                    'description' => $this->getSuggestionDescription($type, $count),
                    'action_items' => $this->getSuggestionActionItems($type),
                ];
            }
        }

        // 基于质量分数生成建议
        if ($metrics['quality_score'] < 70) {
            $suggestions[] = [
                'category' => 'overall_quality',
                'priority' => 'high',
                'title' => '整体代码质量需要改进',
                'description' => '当前代码质量分数较低，建议优先解决错误和警告问题',
                'action_items' => [
                    '优先修复所有错误级别的问题',
                    '逐步解决警告级别的问题',
                    '考虑重构复杂的方法',
                    '增加代码注释和文档',
                ],
            ];
        }

        return $suggestions;
    }

    /**
     * 计算嵌套层级
     */
    protected function calculateNestingLevel(string $code): int
    {
        $maxLevel = 0;
        $currentLevel = 0;

        for ($i = 0; $i < strlen($code); $i++) {
            if ($code[$i] === '{') {
                $currentLevel++;
                $maxLevel = max($maxLevel, $currentLevel);
            } elseif ($code[$i] === '}') {
                $currentLevel--;
            }
        }

        return $maxLevel;
    }

    /**
     * 查找行号
     */
    protected function findLineNumber(string $code, string $search): int
    {
        $lines = explode("\n", $code);
        foreach ($lines as $lineNum => $line) {
            if (strpos($line, $search) !== false) {
                return $lineNum + 1;
            }
        }
        return 1;
    }

    /**
     * 计算可维护性指数
     */
    protected function calculateMaintainabilityIndex(int $qualityScore, int $totalLines, int $totalIssues): int
    {
        // 简化的可维护性指数计算
        $complexity = $totalIssues / max($totalLines, 1) * 100;
        $maintainability = $qualityScore - $complexity;
        return max(0, min(100, round($maintainability)));
    }

    /**
     * 获取质量等级
     */
    protected function getQualityGrade(int $score): string
    {
        if ($score >= 90) return 'A';
        if ($score >= 80) return 'B';
        if ($score >= 70) return 'C';
        if ($score >= 60) return 'D';
        return 'F';
    }

    /**
     * 获取质量状态
     */
    protected function getQualityStatus(int $score): string
    {
        if ($score >= 90) return 'excellent';
        if ($score >= 80) return 'good';
        if ($score >= 70) return 'fair';
        if ($score >= 60) return 'poor';
        return 'critical';
    }

    /**
     * 获取主要关注点
     */
    protected function getPrimaryConcerns(array $issues): array
    {
        $concerns = [];
        $errorIssues = array_filter($issues, fn($issue) => $issue['severity'] === 'error');

        if (!empty($errorIssues)) {
            $concerns[] = '存在' . count($errorIssues) . '个错误级别的问题需要立即修复';
        }

        $securityIssues = array_filter($issues, fn($issue) => $issue['type'] === 'security_issues');
        if (!empty($securityIssues)) {
            $concerns[] = '发现' . count($securityIssues) . '个安全相关问题';
        }

        return $concerns;
    }

    /**
     * 获取代码优势
     */
    protected function getCodeStrengths(array $metrics): array
    {
        $strengths = [];

        if ($metrics['quality_score'] >= 80) {
            $strengths[] = '整体代码质量良好';
        }

        if ($metrics['error_count'] === 0) {
            $strengths[] = '没有发现错误级别的问题';
        }

        if ($metrics['issues_per_100_lines'] < 5) {
            $strengths[] = '代码问题密度较低';
        }

        return $strengths;
    }

    /**
     * 获取整体评估
     */
    protected function getOverallAssessment(array $metrics): string
    {
        $score = $metrics['quality_score'];

        if ($score >= 90) {
            return '代码质量优秀，符合最佳实践标准';
        } elseif ($score >= 80) {
            return '代码质量良好，有少量改进空间';
        } elseif ($score >= 70) {
            return '代码质量一般，建议进行适当优化';
        } elseif ($score >= 60) {
            return '代码质量较差，需要重点改进';
        } else {
            return '代码质量严重不足，建议重构';
        }
    }

    /**
     * 获取建议优先级
     */
    protected function getSuggestionPriority(string $type, int $count): string
    {
        if ($type === 'security_issues') return 'critical';
        if ($type === 'code_complexity' && $count >= 5) return 'high';
        if ($count >= 10) return 'high';
        if ($count >= 5) return 'medium';
        return 'low';
    }

    /**
     * 获取建议标题
     */
    protected function getSuggestionTitle(string $type): string
    {
        $titles = [
            'naming_convention' => '改进命名规范',
            'code_complexity' => '降低代码复杂度',
            'security_issues' => '修复安全问题',
            'performance_issues' => '优化性能问题',
            'best_practices' => '遵循最佳实践',
            'documentation' => '完善代码文档',
        ];

        return $titles[$type] ?? '代码改进';
    }

    /**
     * 获取建议描述
     */
    protected function getSuggestionDescription(string $type, int $count): string
    {
        return "发现 {$count} 个 {$this->qualityRules[$type]} 相关问题，建议优先处理";
    }

    /**
     * 获取建议行动项
     */
    protected function getSuggestionActionItems(string $type): array
    {
        $actionItems = [
            'naming_convention' => [
                '统一使用 PascalCase 命名类名',
                '统一使用 camelCase 命名方法和变量',
                '使用有意义的名称描述功能',
            ],
            'code_complexity' => [
                '将长方法拆分为多个小方法',
                '减少嵌套层级，使用早期返回',
                '提取重复代码为公共方法',
            ],
            'security_issues' => [
                '使用参数化查询防止 SQL 注入',
                '对用户输入进行验证和过滤',
                '添加适当的权限检查',
            ],
        ];

        return $actionItems[$type] ?? ['根据具体问题进行相应改进'];
    }

    // 其他检查方法的简化实现
    protected function checkModelAttributes(string $code, array $tableInfo): array { return []; }
    protected function checkModelRelationships(string $code): array { return []; }
    protected function checkAccessorsAndMutators(string $code): array { return []; }
    protected function checkModelEvents(string $code): array { return []; }
    protected function checkXssProtection(string $code): array { return []; }
    protected function checkFormValidation(string $code): array { return []; }
    protected function checkUserExperience(string $code): array { return []; }
    protected function checkResponsiveDesign(string $code): array { return []; }
    protected function checkRestfulConventions(string $code): array { return []; }
    protected function checkApiSecurity(string $code): array { return []; }
    protected function checkResponseFormat(string $code): array { return []; }
    protected function checkApiErrorHandling(string $code): array { return []; }
    protected function checkRelationshipDefinitions(string $code): array { return []; }
    protected function checkNPlusOneQueries(string $code): array { return []; }
    protected function checkRelationshipPerformance(string $code): array { return []; }
}
