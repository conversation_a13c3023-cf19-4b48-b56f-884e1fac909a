/* CURD 生成器 V2 样式 */

/* 全局样式 */
.curd-generator-v2 {
    background: #f8f9fa;
    min-height: 100vh;
    padding: 20px 0;
}

/* 顶部导航栏 */
.curd-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 30px 0;
    margin-bottom: 30px;
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
}

.curd-title {
    font-size: 32px;
    font-weight: 600;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 15px;
}

.curd-title i {
    font-size: 36px;
    color: #ffd700;
}

.curd-subtitle {
    font-size: 16px;
    margin: 10px 0 0 0;
    opacity: 0.9;
    font-weight: 300;
}

.curd-badges {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    justify-content: flex-end;
    align-items: center;
}

.curd-badges .layui-badge {
    padding: 8px 16px;
    font-size: 13px;
    border-radius: 20px;
    display: flex;
    align-items: center;
    gap: 6px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    transition: transform 0.2s ease;
}

.curd-badges .layui-badge:hover {
    transform: translateY(-2px);
}

/* 步骤导航卡片 */
.curd-steps-card {
    position: sticky;
    top: 20px;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    border: none;
}

.curd-steps-card .layui-card-header {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
    border-radius: 12px 12px 0 0;
    padding: 20px;
    font-size: 16px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
}

/* 步骤导航 */
.curd-steps-nav {
    padding: 0;
}

.step-item {
    display: flex;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid #f0f0f0;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.step-item:last-child {
    border-bottom: none;
}

.step-item:hover {
    background: #f8f9ff;
}

.step-item.active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.step-item.completed {
    background: #f0f9ff;
    color: #1890ff;
}

.step-number {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    position: relative;
    background: #e8e8e8;
    color: #666;
    font-weight: 600;
    transition: all 0.3s ease;
}

.step-item.active .step-number {
    background: rgba(255,255,255,0.2);
    color: white;
}

.step-item.completed .step-number {
    background: #52c41a;
    color: white;
}

.step-number i {
    font-size: 20px;
}

.step-num {
    position: absolute;
    bottom: -2px;
    right: -2px;
    background: #ff4d4f;
    color: white;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    font-size: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.step-content h4 {
    margin: 0 0 5px 0;
    font-size: 16px;
    font-weight: 600;
}

.step-content p {
    margin: 0;
    font-size: 13px;
    opacity: 0.8;
}

.step-item.active .step-content p {
    opacity: 0.9;
}

/* 进度条 */
.curd-progress {
    padding: 20px;
    border-top: 1px solid #f0f0f0;
}

.progress-bar {
    height: 6px;
    background: #f0f0f0;
    border-radius: 3px;
    overflow: hidden;
    margin-bottom: 10px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%);
    border-radius: 3px;
    transition: width 0.5s ease;
}

.progress-text {
    text-align: center;
    font-size: 13px;
    color: #666;
    font-weight: 500;
}

/* 内容卡片 */
.curd-content-card {
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    border: none;
    min-height: 600px;
}

.content-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
}

.content-header h3 {
    margin: 0;
    font-size: 20px;
    font-weight: 600;
    color: #333;
}

.step-actions {
    display: flex;
    gap: 10px;
}

.step-actions .layui-btn {
    border-radius: 6px;
    padding: 8px 20px;
    font-weight: 500;
}

/* 步骤内容 */
.step-intro {
    display: flex;
    align-items: center;
    padding: 30px;
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    color: white;
    border-radius: 12px;
    margin-bottom: 30px;
}

.intro-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: rgba(255,255,255,0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 25px;
}

.intro-icon i {
    font-size: 36px;
}

.intro-text h3 {
    margin: 0 0 10px 0;
    font-size: 24px;
    font-weight: 600;
}

.intro-text p {
    margin: 0;
    font-size: 16px;
    opacity: 0.9;
    line-height: 1.5;
}

/* 配置面板 */
.config-panel, .info-panel {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.05);
    border: 1px solid #f0f0f0;
    overflow: hidden;
}

.panel-header {
    background: #fafafa;
    padding: 15px 20px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.panel-header h4 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #333;
    display: flex;
    align-items: center;
    gap: 8px;
}

.panel-body {
    padding: 25px;
}

/* 表单样式优化 */
.layui-form-label {
    font-weight: 500;
    color: #333;
    display: flex;
    align-items: center;
    gap: 8px;
}

.layui-form-label i {
    color: #1890ff;
}

.layui-input, .layui-select {
    border-radius: 6px;
    border: 1px solid #d9d9d9;
    transition: all 0.3s ease;
}

.layui-input:focus, .layui-select:focus {
    border-color: #40a9ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.layui-btn {
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.layui-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

/* 信息网格 */
.info-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
}

.info-item {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    border-left: 4px solid #1890ff;
}

.info-label {
    font-size: 12px;
    color: #666;
    margin-bottom: 5px;
    font-weight: 500;
}

.info-value {
    font-size: 14px;
    color: #333;
    font-weight: 600;
}

/* 状态徽章 */
.panel-status {
    display: flex;
    gap: 8px;
}

.status-badge {
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.status-badge.success {
    background: #f6ffed;
    color: #52c41a;
    border: 1px solid #b7eb8f;
}

.status-badge.warning {
    background: #fffbe6;
    color: #faad14;
    border: 1px solid #ffe58f;
}

.status-badge.error {
    background: #fff2f0;
    color: #ff4d4f;
    border: 1px solid #ffccc7;
}

/* 连接状态面板 */
.connection-status-panel {
    margin-top: 20px;
}

.status-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
}

.status-icon {
    font-size: 16px;
    color: #ccc;
}

.status-item.connected .status-icon {
    color: #52c41a;
}

.status-item.error .status-icon {
    color: #ff4d4f;
}

.status-text {
    font-size: 14px;
    font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .curd-header {
        padding: 20px 0;
    }

    .curd-title {
        font-size: 24px;
    }

    .curd-badges {
        justify-content: flex-start;
        margin-top: 15px;
    }

    .step-intro {
        flex-direction: column;
        text-align: center;
    }

    .intro-icon {
        margin-right: 0;
        margin-bottom: 20px;
    }

    .info-grid {
        grid-template-columns: 1fr;
    }

    .content-header {
        flex-direction: column;
        gap: 15px;
        align-items: flex-start;
    }
}

/* 工具组样式 */
.tool-group {
    margin-bottom: 25px;
    padding-bottom: 20px;
    border-bottom: 1px solid #f0f0f0;
}

.tool-group:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.tool-group h5 {
    margin: 0 0 15px 0;
    font-size: 14px;
    font-weight: 600;
    color: #333;
    display: flex;
    align-items: center;
    gap: 8px;
}

.tool-group h5:before {
    content: '';
    width: 3px;
    height: 14px;
    background: #1890ff;
    border-radius: 2px;
}

.tool-group .layui-btn {
    margin-bottom: 8px;
}

/* 统计项样式 */
.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #f0f0f0;
}

.stat-item:last-child {
    border-bottom: none;
}

.stat-label {
    font-size: 13px;
    color: #666;
    font-weight: 500;
}

.stat-value {
    font-size: 16px;
    font-weight: 600;
    color: #1890ff;
}

/* 字段配置表格样式 */
.field-config-container {
    max-height: 600px;
    overflow-y: auto;
    border: 1px solid #f0f0f0;
    border-radius: 6px;
}

.field-config-table {
    margin: 0;
}

.field-config-table th {
    background: #fafafa;
    font-weight: 600;
    color: #333;
    border-bottom: 2px solid #e8e8e8;
    position: sticky;
    top: 0;
    z-index: 10;
}

.field-config-table td {
    vertical-align: middle;
    padding: 12px 8px;
}

.field-config-table .drag-handle {
    cursor: move;
    color: #ccc;
    font-size: 16px;
    transition: color 0.3s ease;
}

.field-config-table .drag-handle:hover {
    color: #1890ff;
}

.field-config-table .field-name {
    font-weight: 600;
    color: #333;
    cursor: pointer;
}

.field-config-table .field-name:hover {
    color: #1890ff;
}

.field-config-table .field-type {
    font-family: 'Courier New', monospace;
    background: #f5f5f5;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 12px;
}

/* 文件树样式 */
.file-tree {
    padding: 0;
}

.file-item {
    display: flex;
    align-items: center;
    padding: 12px 15px;
    cursor: pointer;
    border-radius: 6px;
    margin-bottom: 5px;
    transition: all 0.3s ease;
    position: relative;
}

.file-item:hover {
    background: #f0f9ff;
}

.file-item.active {
    background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
    color: white;
}

.file-item i {
    margin-right: 10px;
    font-size: 16px;
    color: #1890ff;
}

.file-item.active i {
    color: white;
}

.file-item span {
    font-weight: 500;
    flex: 1;
}

.file-info {
    font-size: 11px;
    opacity: 0.7;
    margin-top: 2px;
}

/* 代码预览面板 */
.code-preview-panel {
    height: 600px;
    display: flex;
    flex-direction: column;
}

.code-preview-body {
    flex: 1;
    padding: 0;
    overflow: hidden;
}

.code-container {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.code-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;
    background: #fafafa;
    border-bottom: 1px solid #e8e8e8;
}

.code-info {
    display: flex;
    gap: 15px;
    font-size: 12px;
    color: #666;
}

.file-path {
    font-family: 'Courier New', monospace;
    background: #f0f0f0;
    padding: 2px 6px;
    border-radius: 3px;
}

.file-size, .line-count {
    font-weight: 500;
}

.code-actions {
    display: flex;
    gap: 5px;
}

.code-action-btn {
    width: 28px;
    height: 28px;
    border: none;
    background: #f0f0f0;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.code-action-btn:hover {
    background: #1890ff;
    color: white;
}

.code-content {
    flex: 1;
    overflow: auto;
    background: #f8f9fa;
}

.code-content pre {
    margin: 0;
    padding: 20px;
    background: transparent;
    border: none;
    height: 100%;
    overflow: auto;
}

.code-content code {
    font-family: 'Fira Code', 'Courier New', monospace;
    font-size: 13px;
    line-height: 1.6;
}

/* 代码高亮样式 */
.language-php .token.keyword {
    color: #d73a49;
    font-weight: 600;
}

.language-php .token.string {
    color: #032f62;
}

.language-php .token.comment {
    color: #6a737d;
    font-style: italic;
}

.language-php .token.function {
    color: #6f42c1;
}

.language-php .token.variable {
    color: #e36209;
}

/* 动画效果 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.step-content, .step-panel {
    animation: fadeInUp 0.5s ease;
}

/* 加载状态 */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255,255,255,0.9);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    border-radius: 12px;
    color: #666;
    font-size: 14px;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #1890ff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 15px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 排序提示 */
.sort-hint {
    background: #e6f7ff;
    border: 1px solid #91d5ff;
    border-radius: 6px;
    padding: 12px 15px;
    margin-bottom: 20px;
    color: #0050b3;
    font-size: 13px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.sort-hint i {
    color: #1890ff;
}

/* 面板操作按钮 */
.panel-actions {
    display: flex;
    align-items: center;
    gap: 10px;
}

/* 全屏模式 */
.code-preview-fullscreen {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 9999;
    background: white;
}

.code-preview-fullscreen .code-preview-panel {
    height: 100vh;
    border-radius: 0;
}

/* 生成选项样式 */
.generate-options {
    margin-bottom: 25px;
}

.option-group {
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #f0f0f0;
}

.option-group:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.option-group h5 {
    margin: 0 0 12px 0;
    font-size: 14px;
    font-weight: 600;
    color: #333;
}

.option-group .layui-form {
    margin: 0;
}

.option-group .layui-form-item {
    margin-bottom: 8px;
}

.generate-actions {
    margin-top: 20px;
}

.generate-actions .layui-btn {
    margin-bottom: 10px;
}

/* 生成状态 */
.generate-status {
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    background: #f0f0f0;
    color: #666;
}

.generate-status.waiting {
    background: #e6f7ff;
    color: #1890ff;
}

.generate-status.generating {
    background: #fff7e6;
    color: #fa8c16;
}

.generate-status.success {
    background: #f6ffed;
    color: #52c41a;
}

.generate-status.error {
    background: #fff2f0;
    color: #ff4d4f;
}

/* 空结果状态 */
.empty-result {
    text-align: center;
    padding: 60px 20px;
    color: #999;
}

.empty-icon {
    font-size: 64px;
    margin-bottom: 20px;
    opacity: 0.3;
}

.empty-text h4 {
    margin: 0 0 10px 0;
    font-size: 18px;
    color: #666;
}

.empty-text p {
    margin: 0;
    font-size: 14px;
    color: #999;
}

/* 生成结果列表 */
.result-list {
    padding: 0;
}

.result-item {
    display: flex;
    align-items: center;
    padding: 15px;
    border-bottom: 1px solid #f0f0f0;
    transition: background-color 0.3s ease;
}

.result-item:hover {
    background: #f8f9fa;
}

.result-item:last-child {
    border-bottom: none;
}

.result-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    font-size: 18px;
    color: white;
}

.result-icon.success {
    background: #52c41a;
}

.result-icon.error {
    background: #ff4d4f;
}

.result-icon.warning {
    background: #faad14;
}

.result-content {
    flex: 1;
}

.result-title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin: 0 0 5px 0;
}

.result-desc {
    font-size: 13px;
    color: #666;
    margin: 0;
}

.result-actions {
    display: flex;
    gap: 8px;
}

.result-actions .layui-btn {
    padding: 4px 12px;
    font-size: 12px;
}

/* 进度条 */
.generate-progress {
    margin: 20px 0;
}

.progress-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
    font-size: 13px;
    color: #666;
}

.progress-bar-container {
    height: 8px;
    background: #f0f0f0;
    border-radius: 4px;
    overflow: hidden;
}

.progress-bar-fill {
    height: 100%;
    background: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%);
    border-radius: 4px;
    transition: width 0.5s ease;
    width: 0%;
}

/* 文件预览弹窗 */
.file-preview-modal {
    max-width: 90vw;
    max-height: 90vh;
}

.file-preview-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid #f0f0f0;
}

.file-preview-title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
}

.file-preview-actions {
    display: flex;
    gap: 8px;
}

.file-preview-body {
    padding: 0;
    max-height: 70vh;
    overflow: auto;
}

.file-preview-content {
    padding: 20px;
    font-family: 'Courier New', monospace;
    font-size: 13px;
    line-height: 1.6;
    background: #f8f9fa;
    white-space: pre-wrap;
    word-wrap: break-word;
}

/* 滚动条样式 */
.field-config-container::-webkit-scrollbar,
.code-content::-webkit-scrollbar,
.file-preview-body::-webkit-scrollbar {
    width: 6px;
}

.field-config-container::-webkit-scrollbar-track,
.code-content::-webkit-scrollbar-track,
.file-preview-body::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.field-config-container::-webkit-scrollbar-thumb,
.code-content::-webkit-scrollbar-thumb,
.file-preview-body::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.field-config-container::-webkit-scrollbar-thumb:hover,
.code-content::-webkit-scrollbar-thumb:hover,
.file-preview-body::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 成功动画 */
@keyframes successPulse {
    0% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.1);
        opacity: 0.8;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

.result-icon.success {
    animation: successPulse 2s ease-in-out;
}

/* 质量检查样式 */
.quality-results {
    padding: 20px;
}

.quality-success {
    text-align: center;
    padding: 40px 20px;
    color: #52c41a;
}

.quality-success h3 {
    margin: 15px 0 10px 0;
    font-size: 20px;
    color: #52c41a;
}

.quality-success p {
    margin: 0;
    color: #666;
}

.quality-summary {
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #f0f0f0;
}

.quality-summary h3 {
    margin: 0 0 15px 0;
    font-size: 18px;
    color: #333;
}

.quality-stats {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

.stat-item {
    padding: 6px 12px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.stat-item.error {
    background: #fff2f0;
    color: #ff4d4f;
    border: 1px solid #ffccc7;
}

.stat-item.warning {
    background: #fffbe6;
    color: #faad14;
    border: 1px solid #ffe58f;
}

.stat-item.info {
    background: #e6f7ff;
    color: #1890ff;
    border: 1px solid #91d5ff;
}

.stat-item.fixable {
    background: #f6ffed;
    color: #52c41a;
    border: 1px solid #b7eb8f;
}

.quality-issues {
    margin-bottom: 20px;
}

.quality-issue {
    margin-bottom: 12px;
    padding: 15px;
    border-radius: 6px;
    border-left: 4px solid #ddd;
}

.quality-issue.error {
    background: #fff2f0;
    border-left-color: #ff4d4f;
}

.quality-issue.warning {
    background: #fffbe6;
    border-left-color: #faad14;
}

.quality-issue.info {
    background: #e6f7ff;
    border-left-color: #1890ff;
}

.issue-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 8px;
}

.issue-header i {
    margin-right: 8px;
    font-size: 16px;
}

.issue-name {
    font-weight: 600;
    color: #333;
    flex: 1;
}

.fix-btn {
    margin-left: 10px;
}

.issue-description {
    font-size: 13px;
    color: #666;
    line-height: 1.5;
}

.quality-actions {
    text-align: center;
    padding-top: 15px;
    border-top: 1px solid #f0f0f0;
}

/* 质量报告样式 */
.quality-report {
    padding: 20px;
}

.report-header {
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #f0f0f0;
}

.report-header h3 {
    margin: 0 0 10px 0;
    font-size: 18px;
    color: #333;
}

.report-header p {
    margin: 0;
    font-size: 13px;
    color: #666;
}

.report-summary {
    margin-bottom: 20px;
}

.report-summary h4 {
    margin: 0 0 15px 0;
    font-size: 16px;
    color: #333;
}

.summary-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 15px;
}

.summary-item {
    text-align: center;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 6px;
    border: 1px solid #e8e8e8;
}

.summary-item span {
    display: block;
    font-size: 12px;
    color: #666;
    margin-bottom: 5px;
}

.summary-item strong {
    font-size: 20px;
    color: #1890ff;
    font-weight: 600;
}

.report-recommendations {
    margin-bottom: 20px;
}

.report-recommendations h4 {
    margin: 0 0 15px 0;
    font-size: 16px;
    color: #333;
}

.report-recommendations ul {
    margin: 0;
    padding-left: 20px;
}

.report-recommendations li {
    margin-bottom: 8px;
    line-height: 1.5;
    color: #666;
}

/* 批量配置弹窗样式 */
.batch-config-modal {
    padding: 20px;
}

.template-load-modal {
    padding: 20px;
}

/* 拖拽排序样式 */
.drag-handle {
    cursor: move;
    color: #ccc;
    transition: color 0.3s ease;
}

.drag-handle:hover {
    color: #1890ff;
}

.sortable-ghost {
    opacity: 0.5;
    background: #f0f9ff;
}

.sortable-chosen {
    background: #e6f7ff;
}

/* 代码高亮增强 */
.code-content pre[class*="language-"] {
    margin: 0;
    padding: 20px;
    background: #f8f9fa;
    border: none;
    border-radius: 0;
}

.code-content code[class*="language-"] {
    font-family: 'Fira Code', 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
    font-size: 13px;
    line-height: 1.6;
}

/* Prism主题覆盖 */
.token.comment,
.token.prolog,
.token.doctype,
.token.cdata {
    color: #6a737d;
    font-style: italic;
}

.token.punctuation {
    color: #586e75;
}

.token.property,
.token.tag,
.token.boolean,
.token.number,
.token.constant,
.token.symbol,
.token.deleted {
    color: #d73a49;
}

.token.selector,
.token.attr-name,
.token.string,
.token.char,
.token.builtin,
.token.inserted {
    color: #032f62;
}

.token.operator,
.token.entity,
.token.url,
.language-css .token.string,
.style .token.string {
    color: #a67f59;
}

.token.atrule,
.token.attr-value,
.token.keyword {
    color: #d73a49;
    font-weight: 600;
}

.token.function,
.token.class-name {
    color: #6f42c1;
}

.token.regex,
.token.important,
.token.variable {
    color: #e36209;
}

/* 响应式优化 */
@media (max-width: 768px) {
    .generate-actions .layui-btn {
        font-size: 14px;
        padding: 10px 15px;
    }

    .result-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .result-actions {
        width: 100%;
        justify-content: flex-end;
    }

    .file-preview-modal {
        max-width: 95vw;
        max-height: 95vh;
    }

    .quality-stats {
        flex-direction: column;
        gap: 8px;
    }

    .summary-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 10px;
    }

    .issue-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .fix-btn {
        margin-left: 0;
        align-self: flex-end;
    }
}

/* 文件徽章样式 */
.file-badge {
    position: absolute;
    top: 5px;
    right: 8px;
    background: #f0f0f0;
    color: #666;
    font-size: 10px;
    padding: 2px 6px;
    border-radius: 10px;
    font-weight: bold;
    text-transform: uppercase;
}

.file-item.active .file-badge {
    background: #1890ff;
    color: #fff;
}

/* 不同文件类型的徽章颜色 */
.file-item[data-file="controller"] .file-badge,
.file-item[data-file="model"] .file-badge,
.file-item[data-file="route"] .file-badge,
.file-item[data-file="migration"] .file-badge,
.file-item[data-file="seeder"] .file-badge,
.file-item[data-file="test"] .file-badge,
.file-item[data-file="api"] .file-badge {
    background: #777bb4;
    color: #fff;
}

.file-item[data-file="view"] .file-badge {
    background: #e34c26;
    color: #fff;
}

.file-item[data-file="js"] .file-badge {
    background: #f7df1e;
    color: #333;
}

.file-item[data-file="css"] .file-badge {
    background: #1572b6;
    color: #fff;
}

/* 文件状态指示器 */
.file-item .file-status {
    position: absolute;
    bottom: 5px;
    right: 8px;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #d9d9d9;
}

.file-item .file-status.generated {
    background: #52c41a;
}

.file-item .file-status.modified {
    background: #fa8c16;
}

.file-item .file-status.error {
    background: #ff4d4f;
}

/* 实时生成控制面板样式 */
.realtime-control-panel {
    background: #fff;
    border: 1px solid #e8e8e8;
    border-radius: 6px;
    margin-bottom: 20px;
    overflow: hidden;
}

.realtime-control-panel .panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 15px;
    background: #f8f9fa;
    border-bottom: 1px solid #e8e8e8;
}

.realtime-control-panel .panel-header h5 {
    margin: 0;
    font-size: 14px;
    color: #333;
    display: flex;
    align-items: center;
}

.realtime-control-panel .panel-header h5 i {
    margin-right: 6px;
    color: #1890ff;
}

.realtime-control-panel .panel-body {
    padding: 15px;
}

.file-type-controls {
    margin-bottom: 15px;
}

.file-type-controls label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #333;
}

.checkbox-group {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
}

.checkbox-group input[type="checkbox"] {
    margin-right: 5px;
}

.generation-status {
    margin-bottom: 15px;
    padding: 8px 12px;
    background: #f0f9ff;
    border: 1px solid #d1ecf1;
    border-radius: 4px;
    font-size: 13px;
}

#generation-status-text {
    font-weight: 500;
}

#generation-status-text.status-ready {
    color: #52c41a;
}

#generation-status-text.status-preparing {
    color: #fa8c16;
}

#generation-status-text.status-generating {
    color: #1890ff;
}

#generation-status-text.status-completed {
    color: #52c41a;
}

#generation-status-text.status-error {
    color: #ff4d4f;
}

.generation-progress {
    margin-top: 8px;
    height: 4px;
    background: #f0f0f0;
    border-radius: 2px;
    overflow: hidden;
}

.generation-progress .progress-bar {
    height: 100%;
    background: linear-gradient(90deg, #1890ff, #40a9ff);
    border-radius: 2px;
    transition: width 0.3s ease;
    width: 0%;
}

.manual-controls {
    text-align: center;
}

/* 模板管理器样式 */
.template-manager {
    height: 600px;
    display: flex;
    flex-direction: column;
}

.template-toolbar {
    padding: 15px;
    border-bottom: 1px solid #e8e8e8;
    background: #f8f9fa;
}

.template-content {
    flex: 1;
    display: flex;
    overflow: hidden;
}

.template-list {
    width: 300px;
    border-right: 1px solid #e8e8e8;
    overflow-y: auto;
}

.template-filter {
    padding: 15px;
    border-bottom: 1px solid #e8e8e8;
}

.template-item {
    padding: 15px;
    border-bottom: 1px solid #f0f0f0;
    cursor: pointer;
    transition: background 0.3s;
}

.template-item:hover {
    background: #f8f9fa;
}

.template-item.active {
    background: #e6f7ff;
    border-left: 3px solid #1890ff;
}

.template-info h5 {
    margin: 0 0 5px 0;
    font-size: 14px;
    color: #333;
}

.template-info p {
    margin: 0 0 8px 0;
    font-size: 12px;
    color: #666;
    line-height: 1.4;
}

.template-meta {
    display: flex;
    gap: 8px;
}

.template-type,
.template-custom,
.template-builtin {
    font-size: 11px;
    padding: 2px 6px;
    border-radius: 10px;
    font-weight: bold;
}

.template-type {
    background: #f0f0f0;
    color: #666;
}

.template-custom {
    background: #fff7e6;
    color: #fa8c16;
}

.template-builtin {
    background: #f6ffed;
    color: #52c41a;
}

.template-actions {
    margin-top: 10px;
    display: flex;
    gap: 5px;
}

.template-editor {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.editor-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    border-bottom: 1px solid #e8e8e8;
    background: #f8f9fa;
}

.editor-header h4 {
    margin: 0;
    font-size: 16px;
    color: #333;
}

.editor-form {
    flex: 1;
    padding: 15px;
    overflow-y: auto;
}

/* 批量生成结果样式 */
.batch-results {
    max-height: 500px;
    overflow-y: auto;
}

.result-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;
    border-bottom: 1px solid #f0f0f0;
}

.result-item h4 {
    margin: 0;
    font-size: 14px;
    color: #333;
}

.result-actions {
    display: flex;
    gap: 5px;
}

/* 代码优化器样式 */
.code-optimizer {
    height: 550px;
    display: flex;
    flex-direction: column;
}

.optimizer-toolbar {
    padding: 15px;
    border-bottom: 1px solid #e8e8e8;
    background: #f8f9fa;
}

.optimizer-content {
    flex: 1;
    display: flex;
    overflow: hidden;
}

.optimization-report {
    width: 350px;
    border-right: 1px solid #e8e8e8;
    padding: 15px;
    overflow-y: auto;
}

.file-optimization-list {
    flex: 1;
    padding: 15px;
    overflow-y: auto;
}

.report-loading {
    text-align: center;
    padding: 40px;
    color: #999;
}

.report-summary {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
}

.summary-item {
    flex: 1;
    text-align: center;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 6px;
}

.summary-item h5 {
    margin: 0 0 8px 0;
    font-size: 12px;
    color: #666;
    text-transform: uppercase;
}

.summary-value {
    font-size: 24px;
    font-weight: bold;
    color: #333;
}

.summary-value.success {
    color: #52c41a;
}

.summary-value.warning {
    color: #fa8c16;
}

.summary-value.danger {
    color: #ff4d4f;
}

.issue-categories h5 {
    margin: 0 0 15px 0;
    font-size: 14px;
    color: #333;
}

.category-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.category-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background: #fff;
    border: 1px solid #e8e8e8;
    border-radius: 4px;
}

.category-name {
    font-size: 13px;
    color: #333;
}

.category-count {
    font-size: 12px;
    font-weight: bold;
    color: #666;
    background: #f0f0f0;
    padding: 2px 8px;
    border-radius: 10px;
}

.file-optimization-item {
    margin-bottom: 20px;
    border: 1px solid #e8e8e8;
    border-radius: 6px;
    overflow: hidden;
}

.file-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 15px;
    background: #f8f9fa;
    border-bottom: 1px solid #e8e8e8;
}

.file-header h5 {
    margin: 0;
    font-size: 14px;
    color: #333;
}

.file-score {
    font-size: 12px;
    font-weight: bold;
    padding: 4px 8px;
    border-radius: 12px;
    color: #fff;
}

.file-score.score-excellent {
    background: #52c41a;
}

.file-score.score-good {
    background: #1890ff;
}

.file-score.score-warning {
    background: #fa8c16;
}

.file-score.score-danger {
    background: #ff4d4f;
}

.file-issues {
    padding: 15px;
}

.no-issues {
    text-align: center;
    padding: 20px;
    color: #52c41a;
    font-weight: 500;
}

.issues-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.issue-item {
    padding: 12px;
    border-radius: 6px;
    border-left: 4px solid #e8e8e8;
}

.issue-item.severity-high {
    background: #fff2f0;
    border-left-color: #ff4d4f;
}

.issue-item.severity-medium {
    background: #fff7e6;
    border-left-color: #fa8c16;
}

.issue-item.severity-low {
    background: #f6ffed;
    border-left-color: #52c41a;
}

.issue-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 5px;
}

.issue-message {
    font-weight: 500;
    color: #333;
}

.issue-line {
    font-size: 12px;
    color: #999;
    background: #f0f0f0;
    padding: 2px 6px;
    border-radius: 10px;
}

.issue-suggestion {
    font-size: 12px;
    color: #666;
    line-height: 1.4;
}

.optimizer-settings {
    padding: 20px;
}

.optimizer-settings .layui-form-item {
    margin-bottom: 20px;
}

.optimizer-settings .layui-input-block {
    display: flex;
    flex-direction: column;
    gap: 8px;
}
