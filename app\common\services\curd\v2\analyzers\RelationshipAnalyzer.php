<?php

namespace app\common\services\curd\v2\analyzers;

use support\Db;

/**
 * 关联关系分析器
 * 自动检测和分析表之间的关联关系
 */
class RelationshipAnalyzer
{
    protected array $tableCache = [];
    protected array $relationshipCache = [];

    /**
     * 分析表的所有关联关系
     */
    public function analyzeTableRelationships(string $tableName): array
    {
        $cacheKey = $tableName;
        if (isset($this->relationshipCache[$cacheKey])) {
            return $this->relationshipCache[$cacheKey];
        }

        $relationships = [];

        // 分析外键关系 (belongsTo)
        $belongsToRelations = $this->analyzeBelongsToRelations($tableName);
        $relationships = array_merge($relationships, $belongsToRelations);

        // 分析反向关系 (hasMany/hasOne)
        $hasManyRelations = $this->analyzeHasManyRelations($tableName);
        $relationships = array_merge($relationships, $hasManyRelations);

        // 分析多对多关系 (belongsToMany)
        $belongsToManyRelations = $this->analyzeBelongsToManyRelations($tableName);
        $relationships = array_merge($relationships, $belongsToManyRelations);

        // 缓存结果
        $this->relationshipCache[$cacheKey] = $relationships;

        return $relationships;
    }

    /**
     * 分析 belongsTo 关系 (外键关系)
     */
    protected function analyzeBelongsToRelations(string $tableName): array
    {
        $relationships = [];
        $foreignKeys = $this->getForeignKeys($tableName);

        foreach ($foreignKeys as $fk) {
            $relationship = [
                'type' => 'belongsTo',
                'related_table' => $fk['referenced_table'],
                'related_model' => $this->tableToModelName($fk['referenced_table']),
                'foreign_key' => $fk['column_name'],
                'owner_key' => $fk['referenced_column_name'],
                'method_name' => $this->generateMethodName($fk['referenced_table'], false),
                'description' => "属于 {$fk['referenced_table']}",
                'confidence' => 100, // 外键关系置信度最高
            ];

            $relationships[] = $relationship;
        }

        // 分析命名约定的外键 (没有正式外键约束的)
        $conventionForeignKeys = $this->analyzeConventionForeignKeys($tableName);
        foreach ($conventionForeignKeys as $cfk) {
            $relationship = [
                'type' => 'belongsTo',
                'related_table' => $cfk['referenced_table'],
                'related_model' => $this->tableToModelName($cfk['referenced_table']),
                'foreign_key' => $cfk['column_name'],
                'owner_key' => $cfk['referenced_column_name'],
                'method_name' => $this->generateMethodName($cfk['referenced_table'], false),
                'description' => "属于 {$cfk['referenced_table']} (约定)",
                'confidence' => $cfk['confidence'],
            ];

            $relationships[] = $relationship;
        }

        return $relationships;
    }

    /**
     * 分析 hasMany/hasOne 关系 (反向关系)
     */
    protected function analyzeHasManyRelations(string $tableName): array
    {
        $relationships = [];
        $reverseRelations = $this->getReverseRelationships($tableName);

        foreach ($reverseRelations as $relation) {
            // 判断是 hasOne 还是 hasMany
            $isUnique = $this->isUniqueRelation($relation['table'], $relation['foreign_key']);
            $type = $isUnique ? 'hasOne' : 'hasMany';

            $relationship = [
                'type' => $type,
                'related_table' => $relation['table'],
                'related_model' => $this->tableToModelName($relation['table']),
                'foreign_key' => $relation['foreign_key'],
                'local_key' => $relation['local_key'],
                'method_name' => $this->generateMethodName($relation['table'], !$isUnique),
                'description' => $isUnique ? "拥有一个 {$relation['table']}" : "拥有多个 {$relation['table']}",
                'confidence' => $relation['confidence'],
            ];

            $relationships[] = $relationship;
        }

        return $relationships;
    }

    /**
     * 分析 belongsToMany 关系 (多对多关系)
     */
    protected function analyzeBelongsToManyRelations(string $tableName): array
    {
        $relationships = [];
        $pivotTables = $this->findPivotTables($tableName);

        foreach ($pivotTables as $pivot) {
            $relationship = [
                'type' => 'belongsToMany',
                'related_table' => $pivot['related_table'],
                'related_model' => $this->tableToModelName($pivot['related_table']),
                'pivot_table' => $pivot['pivot_table'],
                'foreign_pivot_key' => $pivot['foreign_pivot_key'],
                'related_pivot_key' => $pivot['related_pivot_key'],
                'parent_key' => $pivot['parent_key'],
                'related_key' => $pivot['related_key'],
                'method_name' => $this->generateMethodName($pivot['related_table'], true),
                'description' => "多对多关系 {$pivot['related_table']}",
                'confidence' => $pivot['confidence'],
            ];

            $relationships[] = $relationship;
        }

        return $relationships;
    }

    /**
     * 获取表的外键约束
     */
    protected function getForeignKeys(string $tableName): array
    {
        try {
            $sql = "
                SELECT
                    kcu.COLUMN_NAME as column_name,
                    kcu.REFERENCED_TABLE_NAME as referenced_table,
                    kcu.REFERENCED_COLUMN_NAME as referenced_column_name
                FROM
                    INFORMATION_SCHEMA.KEY_COLUMN_USAGE kcu
                WHERE
                    kcu.TABLE_SCHEMA = DATABASE()
                    AND kcu.TABLE_NAME = ?
                    AND kcu.REFERENCED_TABLE_NAME IS NOT NULL
            ";

            return Db::select($sql, [$tableName]);
        } catch (\Exception $e) {
            return [];
        }
    }

    /**
     * 分析约定命名的外键
     */
    protected function analyzeConventionForeignKeys(string $tableName): array
    {
        $foreignKeys = [];
        $columns = $this->getTableColumns($tableName);

        foreach ($columns as $column) {
            $columnName = $column['COLUMN_NAME'];

            // 检查是否符合外键命名约定
            if (preg_match('/^(.+)_id$/', $columnName, $matches)) {
                $referencedTable = $matches[1];

                // 检查引用的表是否存在
                if ($this->tableExists($referencedTable)) {
                    $confidence = 90; // 高置信度
                } else {
                    // 尝试复数形式
                    $referencedTable = $this->pluralize($referencedTable);
                    if ($this->tableExists($referencedTable)) {
                        $confidence = 80; // 中等置信度
                    } else {
                        continue; // 跳过不存在的表
                    }
                }

                $foreignKeys[] = [
                    'column_name' => $columnName,
                    'referenced_table' => $referencedTable,
                    'referenced_column_name' => 'id',
                    'confidence' => $confidence,
                ];
            }
        }

        return $foreignKeys;
    }

    /**
     * 获取反向关系
     */
    protected function getReverseRelationships(string $tableName): array
    {
        $relationships = [];
        $allTables = $this->getAllTables();

        foreach ($allTables as $table) {
            if ($table === $tableName) {
                continue;
            }

            // 检查该表是否有指向当前表的外键
            $foreignKeys = $this->getForeignKeys($table);
            foreach ($foreignKeys as $fk) {
                if ($fk['referenced_table'] === $tableName) {
                    $relationships[] = [
                        'table' => $table,
                        'foreign_key' => $fk['column_name'],
                        'local_key' => $fk['referenced_column_name'],
                        'confidence' => 100,
                    ];
                }
            }

            // 检查约定命名的外键
            $conventionForeignKeys = $this->analyzeConventionForeignKeys($table);
            foreach ($conventionForeignKeys as $cfk) {
                if ($cfk['referenced_table'] === $tableName) {
                    $relationships[] = [
                        'table' => $table,
                        'foreign_key' => $cfk['column_name'],
                        'local_key' => $cfk['referenced_column_name'],
                        'confidence' => $cfk['confidence'],
                    ];
                }
            }
        }

        return $relationships;
    }

    /**
     * 查找中间表 (多对多关系)
     */
    protected function findPivotTables(string $tableName): array
    {
        $pivotTables = [];
        $allTables = $this->getAllTables();

        foreach ($allTables as $table) {
            if ($table === $tableName) {
                continue;
            }

            // 检查是否为中间表
            if ($this->isPivotTable($table, $tableName)) {
                $relatedTable = $this->getRelatedTableFromPivot($table, $tableName);
                if ($relatedTable) {
                    $pivotTables[] = [
                        'pivot_table' => $table,
                        'related_table' => $relatedTable,
                        'foreign_pivot_key' => $this->singularize($tableName) . '_id',
                        'related_pivot_key' => $this->singularize($relatedTable) . '_id',
                        'parent_key' => 'id',
                        'related_key' => 'id',
                        'confidence' => 85,
                    ];
                }
            }
        }

        return $pivotTables;
    }

    /**
     * 判断是否为中间表
     */
    protected function isPivotTable(string $tableName, string $currentTable): bool
    {
        $columns = $this->getTableColumns($tableName);
        $foreignKeyCount = 0;
        $hasForeignKeyToCurrent = false;

        foreach ($columns as $column) {
            $columnName = $column['COLUMN_NAME'];

            // 跳过主键和时间戳字段
            if (in_array($columnName, ['id', 'created_at', 'updated_at', 'deleted_at'])) {
                continue;
            }

            // 检查是否为外键
            if (preg_match('/^(.+)_id$/', $columnName, $matches)) {
                $foreignKeyCount++;
                $referencedTable = $matches[1];

                if ($referencedTable === $this->singularize($currentTable)) {
                    $hasForeignKeyToCurrent = true;
                }
            }
        }

        // 中间表通常有2个外键，且其中一个指向当前表
        return $foreignKeyCount >= 2 && $hasForeignKeyToCurrent;
    }

    /**
     * 从中间表获取关联的表
     */
    protected function getRelatedTableFromPivot(string $pivotTable, string $currentTable): ?string
    {
        $columns = $this->getTableColumns($pivotTable);
        $currentTableSingular = $this->singularize($currentTable);

        foreach ($columns as $column) {
            $columnName = $column['COLUMN_NAME'];

            if (preg_match('/^(.+)_id$/', $columnName, $matches)) {
                $referencedTable = $matches[1];

                if ($referencedTable !== $currentTableSingular) {
                    // 尝试复数形式
                    $pluralTable = $this->pluralize($referencedTable);
                    if ($this->tableExists($pluralTable)) {
                        return $pluralTable;
                    }
                }
            }
        }

        return null;
    }

    /**
     * 判断是否为唯一关系 (hasOne)
     */
    protected function isUniqueRelation(string $tableName, string $foreignKey): bool
    {
        try {
            $sql = "
                SELECT COUNT(*) as index_count
                FROM INFORMATION_SCHEMA.STATISTICS
                WHERE TABLE_SCHEMA = DATABASE()
                AND TABLE_NAME = ?
                AND COLUMN_NAME = ?
                AND NON_UNIQUE = 0
            ";

            $result = Db::selectOne($sql, [$tableName, $foreignKey]);
            return $result && $result->index_count > 0;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * 生成方法名
     */
    protected function generateMethodName(string $tableName, bool $plural = false): string
    {
        $name = $this->singularize($tableName);
        $name = $this->toCamelCase($name);

        return $plural ? $this->pluralize($name) : $name;
    }

    /**
     * 表名转模型名
     */
    protected function tableToModelName(string $tableName): string
    {
        $name = $this->singularize($tableName);
        return $this->toPascalCase($name);
    }

    /**
     * 获取表的所有列
     */
    protected function getTableColumns(string $tableName): array
    {
        $cacheKey = "columns_{$tableName}";
        if (isset($this->tableCache[$cacheKey])) {
            return $this->tableCache[$cacheKey];
        }

        try {
            $sql = "
                SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT, COLUMN_KEY
                FROM INFORMATION_SCHEMA.COLUMNS
                WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = ?
                ORDER BY ORDINAL_POSITION
            ";

            $columns = Db::select($sql, [$tableName]);
            $this->tableCache[$cacheKey] = $columns;
            return $columns;
        } catch (\Exception $e) {
            return [];
        }
    }

    /**
     * 获取所有表名
     */
    protected function getAllTables(): array
    {
        $cacheKey = 'all_tables';
        if (isset($this->tableCache[$cacheKey])) {
            return $this->tableCache[$cacheKey];
        }

        try {
            $sql = "
                SELECT TABLE_NAME
                FROM INFORMATION_SCHEMA.TABLES
                WHERE TABLE_SCHEMA = DATABASE() AND TABLE_TYPE = 'BASE TABLE'
            ";

            $tables = array_column(Db::select($sql), 'TABLE_NAME');
            $this->tableCache[$cacheKey] = $tables;
            return $tables;
        } catch (\Exception $e) {
            return [];
        }
    }

    /**
     * 检查表是否存在
     */
    protected function tableExists(string $tableName): bool
    {
        $allTables = $this->getAllTables();
        return in_array($tableName, $allTables);
    }

    /**
     * 单数化
     */
    protected function singularize(string $word): string
    {
        $rules = [
            '/ies$/' => 'y',
            '/ves$/' => 'f',
            '/ses$/' => 's',
            '/s$/' => '',
        ];

        foreach ($rules as $pattern => $replacement) {
            if (preg_match($pattern, $word)) {
                return preg_replace($pattern, $replacement, $word);
            }
        }

        return $word;
    }

    /**
     * 复数化
     */
    protected function pluralize(string $word): string
    {
        $rules = [
            '/y$/' => 'ies',
            '/f$/' => 'ves',
            '/s$/' => 'ses',
            '/$/' => 's',
        ];

        foreach ($rules as $pattern => $replacement) {
            if (preg_match($pattern, $word)) {
                return preg_replace($pattern, $replacement, $word);
            }
        }

        return $word . 's';
    }

    /**
     * 转换为驼峰命名
     */
    protected function toCamelCase(string $string): string
    {
        return lcfirst($this->toPascalCase($string));
    }

    /**
     * 转换为帕斯卡命名
     */
    protected function toPascalCase(string $string): string
    {
        return str_replace(' ', '', ucwords(str_replace(['_', '-'], ' ', $string)));
    }

    /**
     * 计算置信度 (优化版)
     */
    protected function calculateConfidence(array $relationship): int
    {
        $confidence = 0;
        $factors = [];

        // 外键约束存在 +40分 (最高权重)
        if (!empty($relationship['has_foreign_key'])) {
            $confidence += 40;
            $factors[] = '外键约束(+40)';
        }

        // 命名约定匹配 +30分
        if (!empty($relationship['naming_match'])) {
            $confidence += 30;
            $factors[] = '命名约定(+30)';
        }

        // 字段类型匹配 +20分
        if (!empty($relationship['type_match'])) {
            $confidence += 20;
            $factors[] = '类型匹配(+20)';
        }

        // 字段长度匹配 +10分
        if (!empty($relationship['length_match'])) {
            $confidence += 10;
            $factors[] = '长度匹配(+10)';
        }

        // 索引存在 +15分 (新增)
        if (!empty($relationship['has_index'])) {
            $confidence += 15;
            $factors[] = '索引存在(+15)';
        }

        // 表名相似度 +5-15分 (新增)
        if (!empty($relationship['table_similarity'])) {
            $similarityScore = min(15, $relationship['table_similarity'] * 15);
            $confidence += $similarityScore;
            $factors[] = "表名相似度(+{$similarityScore})";
        }

        // 字段注释匹配 +5分 (新增)
        if (!empty($relationship['comment_match'])) {
            $confidence += 5;
            $factors[] = '注释匹配(+5)';
        }

        // 数据一致性检查 +10分 (新增)
        if (!empty($relationship['data_consistency'])) {
            $confidence += 10;
            $factors[] = '数据一致性(+10)';
        }

        // 记录置信度计算因子
        $relationship['confidence_factors'] = $factors;
        $relationship['confidence_details'] = [
            'total_score' => min(100, $confidence),
            'max_possible' => 100,
            'factors' => $factors,
            'calculation_method' => 'weighted_sum'
        ];

        return min(100, $confidence);
    }

    /**
     * 检查数据一致性 (新增方法)
     */
    protected function checkDataConsistency(string $localTable, string $localField, string $foreignTable, string $foreignField): bool
    {
        try {
            // 检查是否存在孤立的外键值
            $sql = "
                SELECT COUNT(*) as orphan_count
                FROM {$localTable} l
                LEFT JOIN {$foreignTable} f ON l.{$localField} = f.{$foreignField}
                WHERE l.{$localField} IS NOT NULL AND f.{$foreignField} IS NULL
            ";

            $result = Db::select($sql);
            return ($result[0]->orphan_count ?? 0) == 0;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * 计算表名相似度 (新增方法)
     */
    protected function calculateTableSimilarity(string $table1, string $table2): float
    {
        // 移除表前缀
        $table1 = preg_replace('/^[a-z]+_/', '', $table1);
        $table2 = preg_replace('/^[a-z]+_/', '', $table2);

        // 计算编辑距离
        $distance = levenshtein(strtolower($table1), strtolower($table2));
        $maxLength = max(strlen($table1), strlen($table2));

        if ($maxLength == 0) return 1.0;

        return 1 - ($distance / $maxLength);
    }

    /**
     * 检查字段注释匹配 (新增方法)
     */
    protected function checkCommentMatch(array $localField, array $foreignField): bool
    {
        $localComment = strtolower($localField['COLUMN_COMMENT'] ?? '');
        $foreignComment = strtolower($foreignField['COLUMN_COMMENT'] ?? '');

        if (empty($localComment) || empty($foreignComment)) {
            return false;
        }

        // 检查注释中是否包含相关关键词
        $keywords = ['id', 'key', 'ref', 'reference', '引用', '关联', '外键'];

        foreach ($keywords as $keyword) {
            if (strpos($localComment, $keyword) !== false && strpos($foreignComment, $keyword) !== false) {
                return true;
            }
        }

        return false;
    }

    /**
     * 检查索引存在 (新增方法)
     */
    protected function checkIndexExists(string $tableName, string $fieldName): bool
    {
        try {
            $sql = "
                SELECT COUNT(*) as index_count
                FROM INFORMATION_SCHEMA.STATISTICS
                WHERE TABLE_SCHEMA = DATABASE()
                AND TABLE_NAME = ?
                AND COLUMN_NAME = ?
            ";

            $result = Db::select($sql, [$tableName, $fieldName]);
            return ($result[0]->index_count ?? 0) > 0;
        } catch (\Exception $e) {
            return false;
        }
    }
}
