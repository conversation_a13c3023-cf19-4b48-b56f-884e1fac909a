/**
 * CURD 实时代码生成器
 * 在用户配置字段时实时生成代码预览
 */

layui.use(['layer'], function() {
    var layer = layui.layer;

    // 实时生成器类
    var RealtimeGenerator = {
        // 配置
        config: {
            autoGenerate: true,
            debounceDelay: 500,
            enabledFileTypes: ['controller', 'model', 'view', 'js']
        },

        // 防抖定时器
        debounceTimer: null,

        // 初始化
        init: function() {
            this.bindEvents();
            this.setupAutoGeneration();
        },

        // 绑定事件
        bindEvents: function() {
            var self = this;

            // 字段配置变化时触发实时生成
            $(document).on('change', '.field-config-table input, .field-config-table select', function() {
                if (self.config.autoGenerate) {
                    self.triggerRealtimeGeneration();
                }
            });

            // 表名变化时触发
            $(document).on('input', 'input[name="table_name"]', function() {
                if (self.config.autoGenerate) {
                    self.triggerRealtimeGeneration();
                }
            });

            // 开关自动生成
            $(document).on('click', '#auto-generate-toggle', function() {
                self.toggleAutoGeneration();
            });

            // 手动刷新
            $(document).on('click', '#manual-refresh-btn', function() {
                self.generateAllFiles();
            });

            // 文件类型选择变化
            $(document).on('change', '.file-type-checkbox', function() {
                var fileType = $(this).val();
                var enabled = $(this).is(':checked');
                
                if (enabled) {
                    self.config.enabledFileTypes.push(fileType);
                } else {
                    var index = self.config.enabledFileTypes.indexOf(fileType);
                    if (index > -1) {
                        self.config.enabledFileTypes.splice(index, 1);
                    }
                }
                
                if (self.config.autoGenerate) {
                    self.triggerRealtimeGeneration();
                }
            });
        },

        // 设置自动生成
        setupAutoGeneration: function() {
            // 在页面上添加自动生成控制面板
            this.addAutoGenerationPanel();
        },

        // 添加自动生成控制面板
        addAutoGenerationPanel: function() {
            var panel = `
                <div class="realtime-control-panel">
                    <div class="panel-header">
                        <h5><i class="layui-icon layui-icon-refresh"></i> 实时生成</h5>
                        <div class="panel-controls">
                            <input type="checkbox" id="auto-generate-toggle" lay-skin="switch" lay-text="开|关" checked>
                        </div>
                    </div>
                    <div class="panel-body">
                        <div class="file-type-controls">
                            <label>生成文件类型：</label>
                            <div class="checkbox-group">
                                <input type="checkbox" class="file-type-checkbox" value="controller" checked> 控制器
                                <input type="checkbox" class="file-type-checkbox" value="model" checked> 模型
                                <input type="checkbox" class="file-type-checkbox" value="view" checked> 视图
                                <input type="checkbox" class="file-type-checkbox" value="js" checked> JavaScript
                            </div>
                        </div>
                        <div class="generation-status">
                            <span id="generation-status-text">就绪</span>
                            <div class="generation-progress" id="generation-progress" style="display: none;">
                                <div class="progress-bar"></div>
                            </div>
                        </div>
                        <div class="manual-controls">
                            <button class="layui-btn layui-btn-xs" id="manual-refresh-btn">
                                <i class="layui-icon layui-icon-refresh"></i> 手动刷新
                            </button>
                        </div>
                    </div>
                </div>
            `;

            // 将面板添加到步骤4的侧边栏
            if ($('#step-4 .step-sidebar').length > 0) {
                $('#step-4 .step-sidebar').prepend(panel);
            } else {
                // 如果没有侧边栏，添加到步骤4的顶部
                $('#step-4 .step-content').prepend(panel);
            }
        },

        // 触发实时生成（带防抖）
        triggerRealtimeGeneration: function() {
            var self = this;
            
            // 清除之前的定时器
            if (this.debounceTimer) {
                clearTimeout(this.debounceTimer);
            }

            // 设置新的定时器
            this.debounceTimer = setTimeout(function() {
                self.generateAllFiles();
            }, this.config.debounceDelay);

            // 更新状态
            this.updateStatus('准备生成...', 'preparing');
        },

        // 生成所有文件
        generateAllFiles: function() {
            var self = this;

            if (!window.tableInfo || !window.fieldConfigs) {
                this.updateStatus('请先分析表结构', 'error');
                return;
            }

            this.updateStatus('正在生成...', 'generating');
            this.showProgress();

            var totalFiles = this.config.enabledFileTypes.length;
            var completedFiles = 0;

            // 逐个生成文件
            this.config.enabledFileTypes.forEach(function(fileType, index) {
                setTimeout(function() {
                    self.generateSingleFile(fileType);
                    completedFiles++;
                    
                    // 更新进度
                    var progress = (completedFiles / totalFiles) * 100;
                    self.updateProgress(progress);

                    // 如果全部完成
                    if (completedFiles === totalFiles) {
                        setTimeout(function() {
                            self.hideProgress();
                            self.updateStatus('生成完成', 'completed');
                            
                            // 3秒后恢复就绪状态
                            setTimeout(function() {
                                self.updateStatus('就绪', 'ready');
                            }, 3000);
                        }, 200);
                    }
                }, index * 100); // 错开生成时间
            });
        },

        // 生成单个文件
        generateSingleFile: function(fileType) {
            try {
                var code = '';
                
                // 根据文件类型生成代码
                switch (fileType) {
                    case 'controller':
                        code = this.generateController();
                        break;
                    case 'model':
                        code = this.generateModel();
                        break;
                    case 'view':
                        code = this.generateView();
                        break;
                    case 'js':
                        code = this.generateJavaScript();
                        break;
                    default:
                        code = '// 暂不支持的文件类型: ' + fileType;
                }

                // 更新代码预览
                if (window.CurdCodePreview) {
                    window.CurdCodePreview.updateCode(fileType, code);
                }

                // 更新文件状态
                this.updateFileStatus(fileType, 'generated');

            } catch (error) {
                console.error('生成文件失败:', fileType, error);
                this.updateFileStatus(fileType, 'error');
            }
        },

        // 生成控制器代码
        generateController: function() {
            var tableName = window.tableInfo.name;
            var className = this.toPascalCase(tableName);
            var fields = window.fieldConfigs || [];

            return `<?php

namespace app\\admin\\controller;

use support\\Request;
use support\\Response;
use app\\common\\model\\${className};

/**
 * ${window.tableInfo.comment || className} 控制器
 * 自动生成时间: ${new Date().toLocaleString()}
 */
class ${className}Controller
{
    /**
     * 列表页面
     */
    public function index(Request $request): Response
    {
        if ($request->method() === 'POST') {
            $page = $request->input('page', 1);
            $limit = $request->input('limit', 15);
            
            $query = ${className}::query();
            
            // 搜索条件
${this.generateSearchConditions(fields)}
            
            $total = $query->count();
            $list = $query->offset(($page - 1) * $limit)
                         ->limit($limit)
                         ->orderBy('id', 'desc')
                         ->get();
            
            return json(['code' => 1, 'msg' => '获取成功', 'count' => $total, 'data' => $list]);
        }
        
        return view('admin/${tableName}/index');
    }

    /**
     * 添加/编辑
     */
    public function save(Request $request): Response
    {
        $id = $request->input('id');
        $data = $request->only([
${this.generateFieldList(fields)}
        ]);
        
        try {
            if ($id) {
                ${className}::where('id', $id)->update($data);
                return json(['code' => 1, 'msg' => '更新成功']);
            } else {
                ${className}::create($data);
                return json(['code' => 1, 'msg' => '添加成功']);
            }
        } catch (\\Exception $e) {
            return json(['code' => 0, 'msg' => '操作失败: ' . $e->getMessage()]);
        }
    }

    /**
     * 删除
     */
    public function delete(Request $request): Response
    {
        $ids = $request->input('ids');
        
        try {
            ${className}::whereIn('id', $ids)->delete();
            return json(['code' => 1, 'msg' => '删除成功']);
        } catch (\\Exception $e) {
            return json(['code' => 0, 'msg' => '删除失败: ' . $e->getMessage()]);
        }
    }
}`;
        },

        // 生成模型代码
        generateModel: function() {
            var tableName = window.tableInfo.name;
            var className = this.toPascalCase(tableName);
            var fields = window.fieldConfigs || [];

            return `<?php

namespace app\\common\\model;

use support\\Model;

/**
 * ${window.tableInfo.comment || className} 模型
 * 自动生成时间: ${new Date().toLocaleString()}
 */
class ${className} extends Model
{
    protected $table = '${tableName}';
    
    protected $fillable = [
${this.generateFieldList(fields, true)}
    ];

    protected $casts = [
${this.generateCasts(fields)}
    ];
}`;
        },

        // 生成视图代码
        generateView: function() {
            var tableName = window.tableInfo.name;
            var fields = window.fieldConfigs || [];

            return `@extends('admin.layouts.app')

@section('content')
<div class="layui-card">
    <div class="layui-card-header">
        <h3>${window.tableInfo.comment || tableName}管理</h3>
    </div>
    <div class="layui-card-body">
        <!-- 搜索表单 -->
        <form class="layui-form" lay-filter="searchForm">
${this.generateSearchForm(fields)}
            <div class="layui-form-item">
                <button type="submit" class="layui-btn" lay-submit>搜索</button>
                <button type="reset" class="layui-btn layui-btn-primary">重置</button>
            </div>
        </form>

        <!-- 工具栏 -->
        <div class="layui-btn-group">
            <button class="layui-btn" id="add-btn">添加</button>
            <button class="layui-btn layui-btn-danger" id="delete-btn">删除</button>
        </div>

        <!-- 数据表格 -->
        <table class="layui-hide" id="data-table"></table>
    </div>
</div>
@endsection`;
        },

        // 生成JavaScript代码
        generateJavaScript: function() {
            var tableName = window.tableInfo.name;
            var fields = window.fieldConfigs || [];

            return `layui.use(['table', 'form', 'layer'], function() {
    var table = layui.table;
    var form = layui.form;
    var layer = layui.layer;

    // 渲染表格
    table.render({
        elem: '#data-table',
        url: '/admin/${tableName}',
        method: 'POST',
        page: true,
        cols: [[
            {type: 'checkbox'},
            {field: 'id', title: 'ID', width: 80},
${this.generateTableColumns(fields)}
            {title: '操作', width: 150, toolbar: '#toolbar'}
        ]]
    });

    // 搜索
    form.on('submit(searchForm)', function(data) {
        table.reload('data-table', {
            where: data.field,
            page: {curr: 1}
        });
        return false;
    });
});`;
        },

        // 辅助方法
        toPascalCase: function(str) {
            return str.replace(/_([a-z])/g, function(match, letter) {
                return letter.toUpperCase();
            }).replace(/^[a-z]/, function(match) {
                return match.toUpperCase();
            });
        },

        generateSearchConditions: function(fields) {
            var conditions = [];
            fields.forEach(function(field) {
                if (field.show_in_search) {
                    conditions.push(`            if ($request->input('${field.name}')) {
                $query->where('${field.name}', 'like', '%' . $request->input('${field.name}') . '%');
            }`);
                }
            });
            return conditions.join('\n');
        },

        generateFieldList: function(fields, quoted) {
            var list = [];
            fields.forEach(function(field) {
                if (field.name !== 'id' && field.name !== 'created_at' && field.name !== 'updated_at') {
                    list.push(`        ${quoted ? "'" + field.name + "'," : "'" + field.name + "'"}`);
                }
            });
            return list.join('\n');
        },

        generateCasts: function(fields) {
            var casts = [];
            fields.forEach(function(field) {
                if (field.type === 'int' || field.type === 'tinyint') {
                    casts.push(`        '${field.name}' => 'integer',`);
                } else if (field.type === 'datetime') {
                    casts.push(`        '${field.name}' => 'datetime',`);
                }
            });
            return casts.join('\n');
        },

        generateSearchForm: function(fields) {
            var form = [];
            fields.forEach(function(field) {
                if (field.show_in_search) {
                    form.push(`            <div class="layui-inline">
                <label class="layui-form-label">${field.comment || field.name}</label>
                <div class="layui-input-inline">
                    <input type="text" name="${field.name}" class="layui-input">
                </div>
            </div>`);
                }
            });
            return form.join('\n');
        },

        generateTableColumns: function(fields) {
            var columns = [];
            fields.forEach(function(field) {
                if (field.show_in_list && field.name !== 'id') {
                    columns.push(`            {field: '${field.name}', title: '${field.comment || field.name}'},`);
                }
            });
            return columns.join('\n');
        },

        // UI更新方法
        updateStatus: function(text, type) {
            var statusEl = $('#generation-status-text');
            statusEl.text(text);
            
            // 移除所有状态类
            statusEl.removeClass('status-ready status-preparing status-generating status-completed status-error');
            
            // 添加新状态类
            if (type) {
                statusEl.addClass('status-' + type);
            }
        },

        showProgress: function() {
            $('#generation-progress').show();
            this.updateProgress(0);
        },

        hideProgress: function() {
            $('#generation-progress').hide();
        },

        updateProgress: function(percent) {
            $('#generation-progress .progress-bar').css('width', percent + '%');
        },

        updateFileStatus: function(fileType, status) {
            var fileItem = $(`.file-item[data-file="${fileType}"]`);
            var statusEl = fileItem.find('.file-status');
            
            if (statusEl.length === 0) {
                statusEl = $('<div class="file-status"></div>');
                fileItem.append(statusEl);
            }
            
            statusEl.removeClass('generated modified error').addClass(status);
        },

        toggleAutoGeneration: function() {
            this.config.autoGenerate = $('#auto-generate-toggle').is(':checked');
            
            if (this.config.autoGenerate) {
                this.updateStatus('自动生成已开启', 'ready');
                // 立即生成一次
                this.generateAllFiles();
            } else {
                this.updateStatus('自动生成已关闭', 'ready');
            }
        }
    };

    // 暴露到全局
    window.CurdRealtimeGenerator = RealtimeGenerator;

    // 自动初始化
    $(document).ready(function() {
        // 延迟初始化，确保其他模块已加载
        setTimeout(function() {
            RealtimeGenerator.init();
        }, 1000);
    });
});
