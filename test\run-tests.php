<?php
/**
 * 综合测试启动脚本
 * 自动运行所有测试并生成报告
 */

echo "🚀 CURD生成器V2 - 综合测试启动器\n";
echo str_repeat("=", 60) . "\n\n";

// 检查PHP环境
echo "🔍 检查运行环境...\n";
echo "PHP版本: " . PHP_VERSION . "\n";
echo "操作系统: " . PHP_OS . "\n";
echo "当前目录: " . getcwd() . "\n\n";

// 测试文件列表
$testFiles = [
    'scripts/compile-and-test.php' => '编译检查测试',
    'test/simple-api-test.php' => '简化API接口测试',
    'test/curd-v2-automated-test.php' => 'CURD V2自动化测试',
    'test-all-features.php' => '一键功能测试'
];

$testResults = [];
$totalTests = 0;
$passedTests = 0;

// 运行所有测试
foreach ($testFiles as $file => $description) {
    echo "🧪 运行测试: $description\n";
    echo str_repeat("-", 40) . "\n";

    if (!file_exists($file)) {
        echo "❌ 测试文件不存在: $file\n\n";
        $testResults[$description] = ['status' => 'missing', 'output' => '文件不存在'];
        continue;
    }

    $totalTests++;

    // 捕获输出
    ob_start();
    $startTime = microtime(true);

    try {
        include $file;
        $duration = round((microtime(true) - $startTime) * 1000, 2);
        $output = ob_get_clean();

        // 简单判断测试是否通过（基于输出内容）
        $passed = !preg_match('/❌|错误|失败|异常/', $output);

        if ($passed) {
            echo "✅ $description 测试通过 ({$duration}ms)\n";
            $passedTests++;
            $testResults[$description] = ['status' => 'passed', 'duration' => $duration, 'output' => $output];
        } else {
            echo "❌ $description 测试失败 ({$duration}ms)\n";
            $testResults[$description] = ['status' => 'failed', 'duration' => $duration, 'output' => $output];
        }

    } catch (Exception $e) {
        $duration = round((microtime(true) - $startTime) * 1000, 2);
        $output = ob_get_clean();
        echo "💥 $description 测试异常: " . $e->getMessage() . " ({$duration}ms)\n";
        $testResults[$description] = ['status' => 'error', 'duration' => $duration, 'output' => $output, 'error' => $e->getMessage()];
    }

    echo "\n";
}

// 生成测试报告
echo "📊 生成测试报告...\n";
echo str_repeat("=", 60) . "\n";

$report = [
    'timestamp' => date('Y-m-d H:i:s'),
    'environment' => [
        'php_version' => PHP_VERSION,
        'os' => PHP_OS,
        'working_directory' => getcwd()
    ],
    'summary' => [
        'total_tests' => $totalTests,
        'passed_tests' => $passedTests,
        'failed_tests' => $totalTests - $passedTests,
        'success_rate' => $totalTests > 0 ? round(($passedTests / $totalTests) * 100, 1) : 0
    ],
    'results' => $testResults
];

// 保存详细报告
$reportFile = 'test/comprehensive-test-report.json';
file_put_contents($reportFile, json_encode($report, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));

// 显示摘要
echo "📈 测试摘要:\n";
echo "  总测试数: {$report['summary']['total_tests']}\n";
echo "  通过测试: {$report['summary']['passed_tests']}\n";
echo "  失败测试: {$report['summary']['failed_tests']}\n";
echo "  成功率: {$report['summary']['success_rate']}%\n\n";

// 显示详细结果
echo "📋 详细结果:\n";
foreach ($testResults as $testName => $result) {
    $status = $result['status'];
    $icon = $status === 'passed' ? '✅' : ($status === 'failed' ? '❌' : '💥');
    $duration = isset($result['duration']) ? " ({$result['duration']}ms)" : '';
    echo "  $icon $testName$duration\n";

    if ($status !== 'passed') {
        if (isset($result['error'])) {
            echo "    错误: {$result['error']}\n";
        }
    }
}

echo "\n";

// 生成下一步建议
echo "💡 下一步建议:\n";
echo str_repeat("-", 30) . "\n";

if ($passedTests === $totalTests) {
    echo "🎉 所有测试都通过了！\n";
    echo "✅ 后端API功能正常\n";
    echo "🔍 如果前端仍有问题，请运行前端测试:\n";
    echo "   1. 访问: http://localhost:8787/test/simple-connection-test.html\n";
    echo "   2. 访问: http://localhost:8787/test/debug-api.html\n";
    echo "   3. 检查浏览器控制台错误\n";
} else {
    echo "⚠️  发现问题，需要修复:\n";

    foreach ($testResults as $testName => $result) {
        if ($result['status'] !== 'passed') {
            echo "   - $testName: {$result['status']}\n";
        }
    }

    echo "\n🔧 修复建议:\n";
    echo "   1. 检查服务器是否启动: php start.php start\n";
    echo "   2. 检查路由配置是否正确\n";
    echo "   3. 查看详细报告: $reportFile\n";
    echo "   4. 运行单独的API测试: php test/api-test.php\n";
}

echo "\n";

// CURL测试提示
echo "🌐 额外测试选项:\n";
echo str_repeat("-", 30) . "\n";
echo "如果需要更详细的网络测试，可以运行:\n";

if (PHP_OS_FAMILY === 'Windows') {
    echo "  Windows: test\\curl-test.bat\n";
} else {
    echo "  Linux/Mac: chmod +x test/curl-test.sh && ./test/curl-test.sh\n";
}

echo "  或者直接使用curl命令:\n";
echo "  curl -X POST -H \"Content-Type: application/x-www-form-urlencoded\" \\\n";
echo "       -d \"connection=mysql_second\" \\\n";
echo "       http://localhost:8787/curdtest/tables\n";

echo "\n";

// 保存简化报告
$simpleReport = [
    'timestamp' => $report['timestamp'],
    'total_tests' => $report['summary']['total_tests'],
    'passed_tests' => $report['summary']['passed_tests'],
    'success_rate' => $report['summary']['success_rate'],
    'all_passed' => $passedTests === $totalTests,
    'next_steps' => $passedTests === $totalTests ? 'frontend_testing' : 'backend_fixing'
];

file_put_contents('test/test-summary.json', json_encode($simpleReport, JSON_PRETTY_PRINT));

echo "📄 报告文件:\n";
echo "  详细报告: $reportFile\n";
echo "  简化报告: test/test-summary.json\n";

echo "\n" . str_repeat("=", 60) . "\n";
echo "综合测试完成！\n";

// 返回退出码
exit($passedTests === $totalTests ? 0 : 1);
?>
