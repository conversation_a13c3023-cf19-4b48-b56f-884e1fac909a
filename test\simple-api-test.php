<?php
/**
 * 简化API测试脚本 - 直接使用HTTP请求测试
 */

echo "🔧 CURD生成器V2 - 简化API测试\n";
echo str_repeat("=", 50) . "\n\n";

// 配置
$baseUrl = 'http://localhost:8787';
$apiUrl = $baseUrl . '/curdtest/tables';
$analyzeUrl = $baseUrl . '/curdtest/analyze';

// 测试函数
function makeRequest($url, $data = []) {
    $postData = http_build_query($data);
    
    $context = stream_context_create([
        'http' => [
            'method' => 'POST',
            'header' => "Content-Type: application/x-www-form-urlencoded\r\n" .
                       "Content-Length: " . strlen($postData) . "\r\n",
            'content' => $postData,
            'timeout' => 10
        ]
    ]);
    
    $result = @file_get_contents($url, false, $context);
    
    if ($result === false) {
        return ['error' => '请求失败', 'http_code' => 0];
    }
    
    // 获取HTTP状态码
    $httpCode = 200;
    if (isset($http_response_header)) {
        foreach ($http_response_header as $header) {
            if (preg_match('/HTTP\/\d\.\d\s+(\d+)/', $header, $matches)) {
                $httpCode = (int)$matches[1];
                break;
            }
        }
    }
    
    return [
        'body' => $result,
        'http_code' => $httpCode,
        'headers' => $http_response_header ?? []
    ];
}

// 测试1: 检查服务器状态
echo "🌐 测试1: 检查服务器状态\n";
echo str_repeat("-", 30) . "\n";

$result = makeRequest($baseUrl);
if (isset($result['error'])) {
    echo "❌ 服务器连接失败: {$result['error']}\n";
    echo "💡 请确保服务器已启动: php start.php start\n\n";
    exit(1);
} else {
    echo "✅ 服务器运行正常 (HTTP {$result['http_code']})\n\n";
}

// 测试2: 测试所有数据库连接
echo "🗄️  测试2: 测试各数据库连接\n";
echo str_repeat("-", 30) . "\n";

$connections = ['mysql', 'mysql_read', 'mysql_second', 'mysql_log', 'mysql_cache'];
$testResults = [];

foreach ($connections as $connection) {
    echo "测试连接: $connection\n";
    
    $startTime = microtime(true);
    $result = makeRequest($apiUrl, ['connection' => $connection]);
    $duration = round((microtime(true) - $startTime) * 1000, 2);
    
    if (isset($result['error'])) {
        echo "  ❌ 请求失败: {$result['error']}\n";
        $testResults[$connection] = ['status' => 'error', 'error' => $result['error']];
    } else if ($result['http_code'] !== 200) {
        echo "  ❌ HTTP错误: {$result['http_code']}\n";
        echo "  📄 响应: " . substr($result['body'], 0, 100) . "...\n";
        $testResults[$connection] = ['status' => 'http_error', 'code' => $result['http_code']];
    } else {
        $data = json_decode($result['body'], true);
        if ($data && isset($data['code'])) {
            if ($data['code'] == 1) {
                $tableCount = count($data['data'] ?? []);
                echo "  ✅ 成功 ({$duration}ms) - 获取到 $tableCount 个表\n";
                
                if (strpos($data['msg'], '演示数据') !== false) {
                    echo "  ⚠️  使用演示数据模式\n";
                }
                
                $testResults[$connection] = [
                    'status' => 'success',
                    'duration' => $duration,
                    'table_count' => $tableCount,
                    'tables' => array_column($data['data'] ?? [], 'name'),
                    'is_demo' => strpos($data['msg'], '演示数据') !== false
                ];
                
                // 显示前3个表
                if (!empty($data['data'])) {
                    $tables = array_slice($data['data'], 0, 3);
                    foreach ($tables as $table) {
                        echo "    - {$table['name']} ({$table['comment']})\n";
                    }
                    if (count($data['data']) > 3) {
                        echo "    - ... 还有 " . (count($data['data']) - 3) . " 个表\n";
                    }
                }
            } else {
                echo "  ❌ API错误: {$data['msg']}\n";
                $testResults[$connection] = ['status' => 'api_error', 'message' => $data['msg']];
            }
        } else {
            echo "  ❌ 响应格式错误\n";
            echo "  📄 原始响应: " . substr($result['body'], 0, 100) . "...\n";
            $testResults[$connection] = ['status' => 'format_error', 'body' => substr($result['body'], 0, 200)];
        }
    }
    echo "\n";
}

// 测试3: 重点测试mysql_second
echo "🎯 测试3: 重点测试mysql_second连接\n";
echo str_repeat("-", 30) . "\n";

if (isset($testResults['mysql_second']) && $testResults['mysql_second']['status'] === 'success') {
    $result = $testResults['mysql_second'];
    echo "✅ mysql_second 连接测试成功！\n";
    echo "📊 详细信息:\n";
    echo "  - 响应时间: {$result['duration']}ms\n";
    echo "  - 表数量: {$result['table_count']}\n";
    echo "  - 演示模式: " . ($result['is_demo'] ? '是' : '否') . "\n";
    
    echo "🔍 检查期望的表:\n";
    $expectedTables = ['admin', 'member', 'shop_product', 'shop_order', 'article', 'business'];
    $actualTables = $result['tables'];
    
    $allFound = true;
    foreach ($expectedTables as $expected) {
        if (in_array($expected, $actualTables)) {
            echo "  ✅ $expected\n";
        } else {
            echo "  ❌ $expected (缺失)\n";
            $allFound = false;
        }
    }
    
    $extra = array_diff($actualTables, $expectedTables);
    if (!empty($extra)) {
        echo "  ⚠️  额外的表: " . implode(', ', $extra) . "\n";
    }
    
    if ($allFound && count($actualTables) === 6) {
        echo "🎉 mysql_second 表列表完全正确！\n";
    }
} else {
    echo "❌ mysql_second 连接测试失败\n";
    if (isset($testResults['mysql_second'])) {
        $error = $testResults['mysql_second'];
        echo "错误详情: " . json_encode($error, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n";
    }
}

echo "\n";

// 测试4: 测试表分析功能
echo "🔍 测试4: 测试表分析功能\n";
echo str_repeat("-", 30) . "\n";

$result = makeRequest($analyzeUrl, [
    'table_name' => 'admin',
    'table_prefix' => '',
    'connection' => 'mysql_second'
]);

if (isset($result['error'])) {
    echo "❌ 表分析请求失败: {$result['error']}\n";
} else if ($result['http_code'] !== 200) {
    echo "❌ 表分析HTTP错误: {$result['http_code']}\n";
    echo "📄 响应: " . substr($result['body'], 0, 100) . "...\n";
} else {
    $data = json_decode($result['body'], true);
    if ($data && $data['code'] == 1) {
        echo "✅ 表分析成功\n";
        $tableInfo = $data['data'];
        echo "📋 表信息:\n";
        echo "  - 表名: {$tableInfo['name']}\n";
        echo "  - 注释: {$tableInfo['comment']}\n";
        echo "  - 字段数: " . count($tableInfo['fields']) . "\n";
        
        echo "📋 字段列表:\n";
        foreach (array_slice($tableInfo['fields'], 0, 5) as $field) {
            echo "  - {$field['name']} ({$field['type']}) - {$field['comment']}\n";
        }
        if (count($tableInfo['fields']) > 5) {
            echo "  - ... 还有 " . (count($tableInfo['fields']) - 5) . " 个字段\n";
        }
    } else {
        echo "❌ 表分析失败: " . ($data['msg'] ?? '未知错误') . "\n";
        echo "📄 原始响应: " . substr($result['body'], 0, 200) . "...\n";
    }
}

echo "\n";

// 总结
echo "📊 测试总结\n";
echo str_repeat("=", 50) . "\n";

$successCount = 0;
$totalCount = count($connections);

foreach ($testResults as $conn => $result) {
    if ($result['status'] === 'success') {
        $successCount++;
    }
}

echo "📈 连接测试结果:\n";
echo "  总连接数: $totalCount\n";
echo "  成功连接: $successCount\n";
echo "  失败连接: " . ($totalCount - $successCount) . "\n";
echo "  成功率: " . round(($successCount / $totalCount) * 100, 1) . "%\n\n";

if ($successCount === $totalCount) {
    echo "🎉 所有API测试都通过了！\n";
    echo "✅ 后端功能完全正常\n";
    echo "🔍 如果前端仍有问题，请检查:\n";
    echo "   1. 浏览器控制台错误\n";
    echo "   2. JavaScript事件绑定\n";
    echo "   3. 浏览器缓存问题\n";
} else {
    echo "⚠️  发现API问题，需要修复:\n";
    foreach ($testResults as $conn => $result) {
        if ($result['status'] !== 'success') {
            echo "   - $conn: {$result['status']}\n";
        }
    }
}

echo "\n💡 下一步:\n";
echo "   - 访问简化测试页面: http://localhost:8787/test/simple-connection-test.html\n";
echo "   - 访问调试页面: http://localhost:8787/test/debug-api.html\n";
echo "   - 访问实际页面: http://localhost:8787/admin/system/curdgeneratev2\n";

echo "\n" . str_repeat("=", 50) . "\n";
echo "简化API测试完成！\n";
?>
