<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CURD生成器V2 - 功能测试页面</title>
    <link rel="stylesheet" href="https://unpkg.com/layui@2.8.18/dist/css/layui.css">
    <link rel="stylesheet" href="../public/static/admin/css/curd-generator-v2.css">
    <style>
        .test-container {
            padding: 20px;
            max-width: 1200px;
            margin: 0 auto;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e8e8e8;
            border-radius: 8px;
            background: #fff;
        }
        .test-results {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            max-height: 300px;
            overflow-y: auto;
        }
        .test-btn {
            margin-right: 10px;
            margin-bottom: 10px;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-success { background: #52c41a; }
        .status-error { background: #ff4d4f; }
        .status-warning { background: #faad14; }
        .status-info { background: #1890ff; }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="layui-card">
            <div class="layui-card-header">
                <h2>🧪 CURD生成器V2 - 功能测试套件</h2>
                <p>这个页面用于测试CURD生成器V2的所有功能模块</p>
            </div>
            <div class="layui-card-body">
                <!-- 快速测试区域 -->
                <div class="test-section">
                    <h3>🚀 快速测试</h3>
                    <button class="layui-btn test-btn" id="run-all-tests">运行所有测试</button>
                    <button class="layui-btn layui-btn-primary test-btn" id="run-performance-test">性能测试</button>
                    <button class="layui-btn layui-btn-normal test-btn" id="run-memory-test">内存测试</button>
                    <button class="layui-btn layui-btn-warm test-btn" id="clear-results">清空结果</button>
                    <div id="quick-test-results" class="test-results" style="display: none;"></div>
                </div>

                <!-- 模块测试区域 -->
                <div class="test-section">
                    <h3>🔧 模块测试</h3>
                    <button class="layui-btn layui-btn-sm test-btn" onclick="runModuleTest('basic')">基础功能</button>
                    <button class="layui-btn layui-btn-sm test-btn" onclick="runModuleTest('navigation')">步骤导航</button>
                    <button class="layui-btn layui-btn-sm test-btn" onclick="runModuleTest('tableAnalysis')">表结构分析</button>
                    <button class="layui-btn layui-btn-sm test-btn" onclick="runModuleTest('fieldConfig')">字段配置</button>
                    <button class="layui-btn layui-btn-sm test-btn" onclick="runModuleTest('codePreview')">代码预览</button>
                    <button class="layui-btn layui-btn-sm test-btn" onclick="runModuleTest('qualityCheck')">质量检查</button>
                    <button class="layui-btn layui-btn-sm test-btn" onclick="runModuleTest('dataManager')">数据管理</button>
                    <button class="layui-btn layui-btn-sm test-btn" onclick="runModuleTest('responsive')">响应式</button>
                    <div id="module-test-results" class="test-results" style="display: none;"></div>
                </div>

                <!-- 功能演示区域 -->
                <div class="test-section">
                    <h3>🎯 功能演示</h3>
                    <button class="layui-btn layui-btn-sm layui-btn-primary test-btn" onclick="demoTableAnalysis()">表结构分析演示</button>
                    <button class="layui-btn layui-btn-sm layui-btn-primary test-btn" onclick="demoFieldConfig()">字段配置演示</button>
                    <button class="layui-btn layui-btn-sm layui-btn-primary test-btn" onclick="demoCodePreview()">代码预览演示</button>
                    <button class="layui-btn layui-btn-sm layui-btn-primary test-btn" onclick="demoQualityCheck()">质量检查演示</button>
                    <button class="layui-btn layui-btn-sm layui-btn-primary test-btn" onclick="demoDataImportExport()">数据导入导出演示</button>
                    <div id="demo-results" class="test-results" style="display: none;"></div>
                </div>

                <!-- 压力测试区域 -->
                <div class="test-section">
                    <h3>⚡ 压力测试</h3>
                    <button class="layui-btn layui-btn-sm layui-btn-danger test-btn" onclick="stressTestLargeTable()">大表测试(100字段)</button>
                    <button class="layui-btn layui-btn-sm layui-btn-danger test-btn" onclick="stressTestBatchOperations()">批量操作测试</button>
                    <button class="layui-btn layui-btn-sm layui-btn-danger test-btn" onclick="stressTestCodeGeneration()">代码生成压力测试</button>
                    <button class="layui-btn layui-btn-sm layui-btn-danger test-btn" onclick="stressTestMemoryUsage()">内存使用压力测试</button>
                    <div id="stress-test-results" class="test-results" style="display: none;"></div>
                </div>

                <!-- 兼容性测试区域 -->
                <div class="test-section">
                    <h3>🌐 兼容性测试</h3>
                    <div class="layui-row layui-col-space15">
                        <div class="layui-col-md6">
                            <h4>浏览器信息</h4>
                            <p><strong>用户代理:</strong> <span id="user-agent"></span></p>
                            <p><strong>屏幕分辨率:</strong> <span id="screen-resolution"></span></p>
                            <p><strong>视口大小:</strong> <span id="viewport-size"></span></p>
                            <p><strong>设备像素比:</strong> <span id="device-pixel-ratio"></span></p>
                        </div>
                        <div class="layui-col-md6">
                            <h4>功能支持</h4>
                            <p><span class="status-indicator" id="local-storage-status"></span>本地存储</p>
                            <p><span class="status-indicator" id="clipboard-status"></span>剪贴板API</p>
                            <p><span class="status-indicator" id="file-api-status"></span>文件API</p>
                            <p><span class="status-indicator" id="touch-status"></span>触摸支持</p>
                            <p><span class="status-indicator" id="css3-status"></span>CSS3支持</p>
                        </div>
                    </div>
                    <button class="layui-btn layui-btn-sm test-btn" onclick="runCompatibilityTest()">运行兼容性测试</button>
                    <div id="compatibility-results" class="test-results" style="display: none;"></div>
                </div>

                <!-- 测试统计 -->
                <div class="test-section">
                    <h3>📊 测试统计</h3>
                    <div class="layui-row layui-col-space15">
                        <div class="layui-col-md3">
                            <div class="layui-card">
                                <div class="layui-card-body" style="text-align: center;">
                                    <h2 id="total-tests" style="color: #1890ff;">0</h2>
                                    <p>总测试数</p>
                                </div>
                            </div>
                        </div>
                        <div class="layui-col-md3">
                            <div class="layui-card">
                                <div class="layui-card-body" style="text-align: center;">
                                    <h2 id="passed-tests" style="color: #52c41a;">0</h2>
                                    <p>通过测试</p>
                                </div>
                            </div>
                        </div>
                        <div class="layui-col-md3">
                            <div class="layui-card">
                                <div class="layui-card-body" style="text-align: center;">
                                    <h2 id="failed-tests" style="color: #ff4d4f;">0</h2>
                                    <p>失败测试</p>
                                </div>
                            </div>
                        </div>
                        <div class="layui-col-md3">
                            <div class="layui-card">
                                <div class="layui-card-body" style="text-align: center;">
                                    <h2 id="success-rate" style="color: #faad14;">0%</h2>
                                    <p>成功率</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 隐藏的CURD生成器容器 -->
    <div id="curd-generator-container" style="display: none;">
        <div class="step-nav"></div>
        <div class="step-panel" id="step-1"></div>
        <div class="step-panel" id="step-2"></div>
        <div class="step-panel" id="step-3"></div>
        <div class="step-panel" id="step-4"></div>
        <div class="step-panel" id="step-5"></div>
        <div id="progress-fill"></div>
        <div id="prev-btn"></div>
        <div id="next-btn"></div>
        <div id="generate-btn"></div>
        <div id="field-config-tbody"></div>
        <div id="preview-code"></div>
        <div id="info-table-name"></div>
        <div id="info-field-count"></div>
        <div id="list-fields-stat"></div>
        <div id="form-fields-stat"></div>
    </div>

    <!-- 引入JavaScript库 -->
    <script src="https://unpkg.com/layui@2.8.18/dist/layui.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/components/prism-core.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/components/prism-php.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/components/prism-javascript.min.js"></script>
    
    <!-- 引入CURD生成器模块 -->
    <script src="../public/static/admin/js/curd-generator-v2.js"></script>
    <script src="../public/static/admin/js/curd-code-preview.js"></script>
    <script src="../public/static/admin/js/curd-field-config.js"></script>
    <script src="../public/static/admin/js/curd-quality-checker.js"></script>
    <script src="../public/static/admin/js/curd-data-manager.js"></script>
    <script src="../public/static/admin/js/curd-test-suite.js"></script>

    <script>
        // 测试页面JavaScript代码
        layui.use(['layer'], function() {
            var layer = layui.layer;
            
            // 初始化页面
            initTestPage();
            
            // 绑定事件
            bindTestEvents();
            
            // 显示浏览器信息
            displayBrowserInfo();
            
            // 检查功能支持
            checkFeatureSupport();
        });

        // 初始化测试页面
        function initTestPage() {
            console.log('🧪 CURD生成器V2测试页面已加载');
            console.log('📋 可用的测试命令:');
            console.log('  - runAllTests(): 运行所有测试');
            console.log('  - runModuleTest(name): 运行指定模块测试');
            console.log('  - CurdTestSuite.run(): 使用测试套件');
        }

        // 绑定测试事件
        function bindTestEvents() {
            // 运行所有测试
            $('#run-all-tests').on('click', function() {
                runAllTests();
            });

            // 性能测试
            $('#run-performance-test').on('click', function() {
                runPerformanceTest();
            });

            // 内存测试
            $('#run-memory-test').on('click', function() {
                runMemoryTest();
            });

            // 清空结果
            $('#clear-results').on('click', function() {
                $('.test-results').hide().empty();
                resetTestStats();
            });
        }

        // 运行所有测试
        function runAllTests() {
            showResults('quick-test-results', '🚀 开始运行所有测试...\n');
            
            if (typeof CurdTestSuite !== 'undefined') {
                var results = CurdTestSuite.run();
                updateTestStats(results);
                appendResults('quick-test-results', '\n✅ 所有测试完成！\n');
                appendResults('quick-test-results', '📊 测试结果: ' + results.passed + '/' + results.total + ' 通过\n');
                if (results.errors.length > 0) {
                    appendResults('quick-test-results', '❌ 失败的测试:\n');
                    results.errors.forEach(function(error) {
                        appendResults('quick-test-results', '  - ' + error + '\n');
                    });
                }
            } else {
                appendResults('quick-test-results', '❌ 测试套件未加载\n');
            }
        }

        // 运行模块测试
        function runModuleTest(moduleName) {
            showResults('module-test-results', '🔧 运行模块测试: ' + moduleName + '\n');
            
            if (typeof CurdTestSuite !== 'undefined') {
                var results = CurdTestSuite.runSuite(moduleName);
                appendResults('module-test-results', '📊 模块测试结果: ' + results.passed + '/' + (results.passed + results.failed) + ' 通过\n');
            } else {
                appendResults('module-test-results', '❌ 测试套件未加载\n');
            }
        }

        // 运行性能测试
        function runPerformanceTest() {
            showResults('quick-test-results', '⚡ 开始性能测试...\n');
            
            if (typeof CurdTestSuite !== 'undefined') {
                CurdTestSuite.runPerformance();
                appendResults('quick-test-results', '✅ 性能测试完成，请查看控制台\n');
            } else {
                appendResults('quick-test-results', '❌ 测试套件未加载\n');
            }
        }

        // 运行内存测试
        function runMemoryTest() {
            showResults('quick-test-results', '💾 开始内存测试...\n');
            
            if (typeof CurdTestSuite !== 'undefined') {
                CurdTestSuite.runMemory();
                appendResults('quick-test-results', '✅ 内存测试完成，请查看控制台\n');
            } else {
                appendResults('quick-test-results', '❌ 测试套件未加载\n');
            }
        }

        // 显示测试结果
        function showResults(containerId, content) {
            var container = $('#' + containerId);
            container.text(content).show();
        }

        // 追加测试结果
        function appendResults(containerId, content) {
            var container = $('#' + containerId);
            container.append(content);
            container.scrollTop(container[0].scrollHeight);
        }

        // 更新测试统计
        function updateTestStats(results) {
            $('#total-tests').text(results.total);
            $('#passed-tests').text(results.passed);
            $('#failed-tests').text(results.failed);
            $('#success-rate').text(((results.passed / results.total) * 100).toFixed(1) + '%');
        }

        // 重置测试统计
        function resetTestStats() {
            $('#total-tests').text('0');
            $('#passed-tests').text('0');
            $('#failed-tests').text('0');
            $('#success-rate').text('0%');
        }

        // 显示浏览器信息
        function displayBrowserInfo() {
            $('#user-agent').text(navigator.userAgent);
            $('#screen-resolution').text(screen.width + 'x' + screen.height);
            $('#viewport-size').text(window.innerWidth + 'x' + window.innerHeight);
            $('#device-pixel-ratio').text(window.devicePixelRatio || 1);
        }

        // 检查功能支持
        function checkFeatureSupport() {
            // 本地存储
            var hasLocalStorage = typeof Storage !== 'undefined';
            $('#local-storage-status').addClass(hasLocalStorage ? 'status-success' : 'status-error');

            // 剪贴板API
            var hasClipboard = navigator.clipboard !== undefined;
            $('#clipboard-status').addClass(hasClipboard ? 'status-success' : 'status-warning');

            // 文件API
            var hasFileAPI = window.File && window.FileReader && window.FileList && window.Blob;
            $('#file-api-status').addClass(hasFileAPI ? 'status-success' : 'status-error');

            // 触摸支持
            var hasTouch = 'ontouchstart' in window || navigator.maxTouchPoints > 0;
            $('#touch-status').addClass(hasTouch ? 'status-success' : 'status-info');

            // CSS3支持
            var hasCSS3 = typeof document.body.style.transform !== 'undefined';
            $('#css3-status').addClass(hasCSS3 ? 'status-success' : 'status-warning');
        }

        // 演示函数（这些函数会在后续实现）
        function demoTableAnalysis() {
            showResults('demo-results', '🎯 表结构分析演示\n正在模拟表结构分析...\n');
            // 实现演示逻辑
        }

        function demoFieldConfig() {
            showResults('demo-results', '🎯 字段配置演示\n正在演示字段配置功能...\n');
            // 实现演示逻辑
        }

        function demoCodePreview() {
            showResults('demo-results', '🎯 代码预览演示\n正在生成代码预览...\n');
            // 实现演示逻辑
        }

        function demoQualityCheck() {
            showResults('demo-results', '🎯 质量检查演示\n正在运行质量检查...\n');
            // 实现演示逻辑
        }

        function demoDataImportExport() {
            showResults('demo-results', '🎯 数据导入导出演示\n正在演示导入导出功能...\n');
            // 实现演示逻辑
        }

        // 压力测试函数
        function stressTestLargeTable() {
            showResults('stress-test-results', '⚡ 大表压力测试\n正在生成100个字段的测试数据...\n');
            // 实现压力测试逻辑
        }

        function stressTestBatchOperations() {
            showResults('stress-test-results', '⚡ 批量操作压力测试\n正在测试批量操作性能...\n');
            // 实现压力测试逻辑
        }

        function stressTestCodeGeneration() {
            showResults('stress-test-results', '⚡ 代码生成压力测试\n正在测试代码生成性能...\n');
            // 实现压力测试逻辑
        }

        function stressTestMemoryUsage() {
            showResults('stress-test-results', '⚡ 内存使用压力测试\n正在监控内存使用情况...\n');
            // 实现压力测试逻辑
        }

        // 兼容性测试
        function runCompatibilityTest() {
            showResults('compatibility-results', '🌐 兼容性测试\n正在检查浏览器兼容性...\n');
            
            var tests = [
                { name: 'ES5支持', test: function() { return typeof Array.prototype.forEach === 'function'; } },
                { name: 'JSON支持', test: function() { return typeof JSON !== 'undefined'; } },
                { name: 'querySelector支持', test: function() { return typeof document.querySelector === 'function'; } },
                { name: 'addEventListener支持', test: function() { return typeof document.addEventListener === 'function'; } },
                { name: 'XMLHttpRequest支持', test: function() { return typeof XMLHttpRequest !== 'undefined'; } }
            ];
            
            tests.forEach(function(test) {
                var result = test.test();
                var status = result ? '✅' : '❌';
                appendResults('compatibility-results', status + ' ' + test.name + ': ' + (result ? '支持' : '不支持') + '\n');
            });
            
            appendResults('compatibility-results', '\n🎯 兼容性测试完成\n');
        }
    </script>
</body>
</html>
