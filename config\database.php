<?php

// 引入辅助函数
if (file_exists(__DIR__ . '/../bootstrap/helpers.php')) {
    require_once __DIR__ . '/../bootstrap/helpers.php';
}

/**
 * 优化的多数据库配置文件
 * 支持多数据库连接、读写分离、连接池等高级功能
 */

return [
    // 默认数据库连接
    'default' => env('DB_CONNECTION', 'mysql'),

    // 数据库连接配置
    'connections' => [
        // 主数据库连接（写库）
        'mysql' => [
            'driver'      => 'mysql',
            'host'        => env('DB_HOST', '127.0.0.1'),
            'port'        => env('DB_PORT', 3306),
            'database'    => env('DB_DATABASE', 'easyadmin8'),
            'username'    => env('DB_USERNAME', 'root'),
            'password'    => env('DB_PASSWORD', 'root'),
            'unix_socket' => env('DB_SOCKET', ''),
            'charset'     => env('DB_CHARSET', 'utf8mb4'),
            'collation'   => env('DB_COLLATION', 'utf8mb4_general_ci'),
            'prefix'      => env('DB_PREFIX', 'ea8_'),
            'strict'      => env('DB_STRICT', true),
            'engine'      => env('DB_ENGINE', null),
            'options'     => [
                \PDO::ATTR_TIMEOUT => env('DB_TIMEOUT', 3),
                \PDO::ATTR_ERRMODE => \PDO::ERRMODE_EXCEPTION,
                \PDO::MYSQL_ATTR_USE_BUFFERED_QUERY => true,
                \PDO::MYSQL_ATTR_INIT_COMMAND => "SET sql_mode='STRICT_TRANS_TABLES,ERROR_FOR_DIVISION_BY_ZERO,NO_AUTO_CREATE_USER,NO_ENGINE_SUBSTITUTION'",
            ]
        ],

        // 从数据库连接（读库）
        'mysql_read' => [
            'driver'      => 'mysql',
            'host'        => env('DB_READ_HOST', env('DB_HOST', '127.0.0.1')),
            'port'        => env('DB_READ_PORT', env('DB_PORT', 3306)),
            'database'    => env('DB_READ_DATABASE', env('DB_DATABASE', 'easyadmin8')),
            'username'    => env('DB_READ_USERNAME', env('DB_USERNAME', 'root')),
            'password'    => env('DB_READ_PASSWORD', env('DB_PASSWORD', 'root')),
            'unix_socket' => env('DB_READ_SOCKET', ''),
            'charset'     => env('DB_CHARSET', 'utf8mb4'),
            'collation'   => env('DB_COLLATION', 'utf8mb4_general_ci'),
            'prefix'      => env('DB_PREFIX', 'ea8_'),
            'strict'      => env('DB_STRICT', true),
            'engine'      => env('DB_ENGINE', null),
            'options'     => [
                \PDO::ATTR_TIMEOUT => env('DB_TIMEOUT', 3),
                \PDO::ATTR_ERRMODE => \PDO::ERRMODE_EXCEPTION,
                \PDO::MYSQL_ATTR_USE_BUFFERED_QUERY => true,
            ]
        ],

        // 无前缀数据库连接
        'mysql_without_prefix' => [
            'driver'      => 'mysql',
            'host'        => env('DB_HOST', '127.0.0.1'),
            'port'        => env('DB_PORT', 3306),
            'database'    => env('DB_DATABASE', 'easyadmin8'),
            'username'    => env('DB_USERNAME', 'root'),
            'password'    => env('DB_PASSWORD', 'root'),
            'unix_socket' => env('DB_SOCKET', ''),
            'charset'     => env('DB_CHARSET', 'utf8mb4'),
            'collation'   => env('DB_COLLATION', 'utf8mb4_general_ci'),
            'prefix'      => '',
            'strict'      => env('DB_STRICT', true),
            'engine'      => env('DB_ENGINE', null),
            'options'     => [
                \PDO::ATTR_TIMEOUT => env('DB_TIMEOUT', 3),
                \PDO::ATTR_ERRMODE => \PDO::ERRMODE_EXCEPTION,
                \PDO::MYSQL_ATTR_USE_BUFFERED_QUERY => true,
            ]
        ],

        // 第二数据库连接
        'mysql_second' => [
            'driver'      => 'mysql',
            'host'        => '127.0.0.1',
            'port'        => 3306,
            'database'    => 'hejiang',
            'username'    => 'root',
            'password'    => '5GeNi1v7P7Xcur5W',
            'unix_socket' => '',
            'charset'     => 'utf8mb4',
            'collation'   => 'utf8mb4_general_ci',
            'prefix'      => 'ddwx_',
            'strict'      => true,
            'engine'      => null,
            'options'     => [
                \PDO::ATTR_TIMEOUT => 3,
                \PDO::ATTR_ERRMODE => \PDO::ERRMODE_EXCEPTION,
                \PDO::MYSQL_ATTR_USE_BUFFERED_QUERY => true,
            ]
        ],

        // 日志数据库连接
        'mysql_log' => [
            'driver'      => 'mysql',
            'host'        => env('DB_LOG_HOST', env('DB_HOST', '127.0.0.1')),
            'port'        => env('DB_LOG_PORT', env('DB_PORT', 3306)),
            'database'    => env('DB_LOG_DATABASE', 'easyadmin8_log'),
            'username'    => env('DB_LOG_USERNAME', env('DB_USERNAME', 'root')),
            'password'    => env('DB_LOG_PASSWORD', env('DB_PASSWORD', 'root')),
            'unix_socket' => env('DB_LOG_SOCKET', ''),
            'charset'     => env('DB_CHARSET', 'utf8mb4'),
            'collation'   => env('DB_COLLATION', 'utf8mb4_general_ci'),
            'prefix'      => env('DB_LOG_PREFIX', 'log_'),
            'strict'      => env('DB_STRICT', true),
            'engine'      => env('DB_ENGINE', null),
            'options'     => [
                \PDO::ATTR_TIMEOUT => env('DB_TIMEOUT', 3),
                \PDO::ATTR_ERRMODE => \PDO::ERRMODE_EXCEPTION,
                \PDO::MYSQL_ATTR_USE_BUFFERED_QUERY => true,
            ]
        ],

        // 缓存数据库连接
        'mysql_cache' => [
            'driver'      => 'mysql',
            'host'        => env('DB_CACHE_HOST', env('DB_HOST', '127.0.0.1')),
            'port'        => env('DB_CACHE_PORT', env('DB_PORT', 3306)),
            'database'    => env('DB_CACHE_DATABASE', 'easyadmin8_cache'),
            'username'    => env('DB_CACHE_USERNAME', env('DB_USERNAME', 'root')),
            'password'    => env('DB_CACHE_PASSWORD', env('DB_PASSWORD', 'root')),
            'unix_socket' => env('DB_CACHE_SOCKET', ''),
            'charset'     => env('DB_CHARSET', 'utf8mb4'),
            'collation'   => env('DB_COLLATION', 'utf8mb4_general_ci'),
            'prefix'      => env('DB_CACHE_PREFIX', 'cache_'),
            'strict'      => env('DB_STRICT', true),
            'engine'      => env('DB_ENGINE', null),
            'options'     => [
                \PDO::ATTR_TIMEOUT => env('DB_TIMEOUT', 3),
                \PDO::ATTR_ERRMODE => \PDO::ERRMODE_EXCEPTION,
                \PDO::MYSQL_ATTR_USE_BUFFERED_QUERY => true,
            ]
        ],

        // PostgreSQL 数据库连接
        'pgsql' => [
            'driver'   => 'pgsql',
            'host'     => env('PGSQL_HOST', '127.0.0.1'),
            'port'     => env('PGSQL_PORT', 5432),
            'database' => env('PGSQL_DATABASE', 'easyadmin8'),
            'username' => env('PGSQL_USERNAME', 'postgres'),
            'password' => env('PGSQL_PASSWORD', 'root'),
            'charset'  => env('PGSQL_CHARSET', 'utf8'),
            'prefix'   => env('PGSQL_PREFIX', 'ea8_'),
            'schema'   => env('PGSQL_SCHEMA', 'public'),
            'sslmode'  => env('PGSQL_SSLMODE', 'prefer'),
            'options'  => [
                \PDO::ATTR_TIMEOUT => env('DB_TIMEOUT', 3),
                \PDO::ATTR_ERRMODE => \PDO::ERRMODE_EXCEPTION,
            ]
        ],

        // SQLite 数据库连接
        'sqlite' => [
            'driver'   => 'sqlite',
            'database' => env('SQLITE_DATABASE', __DIR__ . '/../database/database.sqlite'),
            'prefix'   => env('SQLITE_PREFIX', ''),
            'foreign_key_constraints' => env('SQLITE_FOREIGN_KEYS', true),
            'options'  => [
                \PDO::ATTR_TIMEOUT => env('DB_TIMEOUT', 3),
                \PDO::ATTR_ERRMODE => \PDO::ERRMODE_EXCEPTION,
            ]
        ],

        // SQL Server 数据库连接
        'sqlsrv' => [
            'driver'   => 'sqlsrv',
            'host'     => env('SQLSRV_HOST', '127.0.0.1'),
            'port'     => env('SQLSRV_PORT', 1433),
            'database' => env('SQLSRV_DATABASE', 'easyadmin8'),
            'username' => env('SQLSRV_USERNAME', 'sa'),
            'password' => env('SQLSRV_PASSWORD', 'root'),
            'charset'  => env('SQLSRV_CHARSET', 'utf8'),
            'prefix'   => env('SQLSRV_PREFIX', 'ea8_'),
            'options'  => [
                \PDO::ATTR_TIMEOUT => env('DB_TIMEOUT', 3),
                \PDO::ATTR_ERRMODE => \PDO::ERRMODE_EXCEPTION,
            ]
        ],
    ],

    // 数据库连接池配置
    'pool' => [
        'enable' => env('DB_POOL_ENABLE', false),
        'max_connections' => env('DB_POOL_MAX_CONNECTIONS', 10),
        'min_connections' => env('DB_POOL_MIN_CONNECTIONS', 1),
        'max_idle_time' => env('DB_POOL_MAX_IDLE_TIME', 60),
        'max_wait_time' => env('DB_POOL_MAX_WAIT_TIME', 3),
        'retry_interval' => env('DB_POOL_RETRY_INTERVAL', 100),
    ],

    // 读写分离配置
    'read_write_separation' => [
        'enable' => env('DB_READ_WRITE_SEPARATION', false),
        'write_connection' => env('DB_WRITE_CONNECTION', 'mysql'),
        'read_connections' => explode(',', env('DB_READ_CONNECTIONS', 'mysql_read')),
        'sticky' => env('DB_STICKY', false),
        'weight' => [
            'mysql_read' => env('DB_READ_WEIGHT', 1),
        ],
    ],

    // 数据库迁移配置
    'migrations' => [
        'table' => env('DB_MIGRATIONS_TABLE', 'migrations'),
        'update_date_on_publish' => true,
    ],

    // Redis 配置
    'redis' => [
        'client' => env('REDIS_CLIENT', 'phpredis'),
        'options' => [
            'cluster' => env('REDIS_CLUSTER', 'redis'),
            'prefix' => env('REDIS_PREFIX', 'easyadmin8_database_'),
        ],
        'default' => [
            'url' => env('REDIS_URL'),
            'host' => env('REDIS_HOST', '127.0.0.1'),
            'password' => env('REDIS_PASSWORD', null),
            'port' => env('REDIS_PORT', 6379),
            'database' => env('REDIS_DB', 0),
            'read_timeout' => env('REDIS_READ_TIMEOUT', 60),
            'context' => [],
        ],
        'cache' => [
            'url' => env('REDIS_URL'),
            'host' => env('REDIS_HOST', '127.0.0.1'),
            'password' => env('REDIS_PASSWORD', null),
            'port' => env('REDIS_PORT', 6379),
            'database' => env('REDIS_CACHE_DB', 1),
            'read_timeout' => env('REDIS_READ_TIMEOUT', 60),
            'context' => [],
        ],
        'session' => [
            'url' => env('REDIS_URL'),
            'host' => env('REDIS_HOST', '127.0.0.1'),
            'password' => env('REDIS_PASSWORD', null),
            'port' => env('REDIS_PORT', 6379),
            'database' => env('REDIS_SESSION_DB', 2),
            'read_timeout' => env('REDIS_READ_TIMEOUT', 60),
            'context' => [],
        ],
    ],
];