<?php

namespace app\admin\controller\system;

/**
 * 性能监控控制器
 * 提供系统性能监控、实时指标、告警管理等功能
 */
class MonitorController
{
    /**
     * 监控首页
     */
    public function index($request = null)
    {
        if (!$request) {
            $request = new \RequestMock();
        }

        $systemMetrics = $this->getSystemMetrics();
        $apiMetrics = $this->getApiMetrics();
        $alerts = $this->getActiveAlerts();

        $html = $this->renderMonitorPage($systemMetrics, $apiMetrics, $alerts);
        return response($html);
    }

    /**
     * 获取系统指标
     */
    public function getSystemMetrics()
    {
        return [
            'cpu_usage' => 23.5,
            'memory_usage' => 67.8,
            'disk_usage' => 45.2,
            'network_in' => 1.2,
            'network_out' => 0.8,
            'load_average' => [0.85, 0.92, 1.05],
            'uptime' => 2847,
            'processes' => 156,
            'connections' => 89,
            'response_time' => 125,
            'throughput' => 1250,
            'error_rate' => 0.8
        ];
    }

    /**
     * 获取API指标
     */
    public function getApiMetrics()
    {
        return [
            'total_requests' => 125847,
            'successful_requests' => 123456,
            'failed_requests' => 2391,
            'avg_response_time' => 145,
            'p95_response_time' => 285,
            'p99_response_time' => 456,
            'requests_per_second' => 85.6,
            'active_connections' => 234,
            'cache_hit_rate' => 94.2,
            'database_queries' => 1567,
            'slow_queries' => 12,
            'api_endpoints' => 45
        ];
    }

    /**
     * 获取活跃告警
     */
    public function getActiveAlerts()
    {
        return [
            [
                'id' => 1,
                'level' => 'warning',
                'title' => 'API响应时间过高',
                'message' => '/api/users接口平均响应时间超过500ms',
                'time' => '2025-01-20 10:30:00',
                'status' => 'active'
            ],
            [
                'id' => 2,
                'level' => 'info',
                'title' => '缓存命中率下降',
                'message' => '数据库查询缓存命中率降至85%',
                'time' => '2025-01-20 10:25:00',
                'status' => 'active'
            ],
            [
                'id' => 3,
                'level' => 'critical',
                'title' => '磁盘空间不足',
                'message' => '系统磁盘使用率达到90%',
                'time' => '2025-01-20 10:20:00',
                'status' => 'resolved'
            ]
        ];
    }

    /**
     * 获取实时指标
     */
    public function getRealTimeMetrics($request = null)
    {
        if (!$request) {
            $request = new \RequestMock();
        }

        $metrics = [
            'timestamp' => time(),
            'cpu' => rand(20, 80) / 10,
            'memory' => rand(500, 900) / 10,
            'requests' => rand(80, 120),
            'response_time' => rand(100, 300),
            'errors' => rand(0, 5),
            'active_users' => rand(50, 200)
        ];

        return response(json_encode([
            'code' => 200,
            'msg' => 'success',
            'data' => $metrics
        ]));
    }

    /**
     * 获取历史数据
     */
    public function getHistoricalData($request = null)
    {
        if (!$request) {
            $request = new \RequestMock();
        }

        $period = $request->get('period', '1h');
        $metric = $request->get('metric', 'response_time');

        $data = $this->generateHistoricalData($period, $metric);

        return response(json_encode([
            'code' => 200,
            'msg' => 'success',
            'data' => $data
        ]));
    }

    /**
     * 生成历史数据
     */
    private function generateHistoricalData($period, $metric)
    {
        $points = [];
        $now = time();
        
        switch ($period) {
            case '1h':
                $interval = 300; // 5分钟
                $count = 12;
                break;
            case '24h':
                $interval = 3600; // 1小时
                $count = 24;
                break;
            case '7d':
                $interval = 86400; // 1天
                $count = 7;
                break;
            default:
                $interval = 300;
                $count = 12;
        }

        for ($i = $count; $i >= 0; $i--) {
            $timestamp = $now - ($i * $interval);
            $value = $this->generateMetricValue($metric);
            
            $points[] = [
                'timestamp' => $timestamp,
                'value' => $value,
                'formatted_time' => date('H:i', $timestamp)
            ];
        }

        return [
            'period' => $period,
            'metric' => $metric,
            'points' => $points,
            'summary' => [
                'min' => min(array_column($points, 'value')),
                'max' => max(array_column($points, 'value')),
                'avg' => round(array_sum(array_column($points, 'value')) / count($points), 2)
            ]
        ];
    }

    /**
     * 生成指标值
     */
    private function generateMetricValue($metric)
    {
        switch ($metric) {
            case 'response_time':
                return rand(80, 300);
            case 'cpu_usage':
                return rand(10, 80);
            case 'memory_usage':
                return rand(40, 90);
            case 'requests_per_second':
                return rand(50, 150);
            case 'error_rate':
                return rand(0, 50) / 10;
            default:
                return rand(0, 100);
        }
    }

    /**
     * 创建告警规则
     */
    public function createAlert($request = null)
    {
        if (!$request) {
            $request = new \RequestMock();
        }

        $alertData = [
            'name' => $request->post('name', '响应时间告警'),
            'metric' => $request->post('metric', 'response_time'),
            'condition' => $request->post('condition', '>'),
            'threshold' => $request->post('threshold', 500),
            'duration' => $request->post('duration', 300),
            'severity' => $request->post('severity', 'warning')
        ];

        $alert = $this->simulateAlertCreation($alertData);

        return response(json_encode([
            'code' => 200,
            'msg' => '告警规则创建成功',
            'data' => $alert
        ]));
    }

    /**
     * 模拟告警创建
     */
    private function simulateAlertCreation($alertData)
    {
        return array_merge($alertData, [
            'id' => rand(1000, 9999),
            'status' => 'active',
            'created_at' => date('Y-m-d H:i:s'),
            'last_triggered' => null,
            'trigger_count' => 0
        ]);
    }

    /**
     * 系统健康检查
     */
    public function healthCheck($request = null)
    {
        $checks = [
            'database' => $this->checkDatabase(),
            'cache' => $this->checkCache(),
            'storage' => $this->checkStorage(),
            'api' => $this->checkApi(),
            'external_services' => $this->checkExternalServices()
        ];

        $overallStatus = $this->calculateOverallHealth($checks);

        return response(json_encode([
            'code' => 200,
            'msg' => 'success',
            'data' => [
                'overall_status' => $overallStatus,
                'checks' => $checks,
                'timestamp' => date('Y-m-d H:i:s')
            ]
        ]));
    }

    /**
     * 检查数据库
     */
    private function checkDatabase()
    {
        return [
            'status' => 'healthy',
            'response_time' => rand(5, 20),
            'connections' => rand(10, 50),
            'slow_queries' => rand(0, 3),
            'details' => '数据库连接正常，查询性能良好'
        ];
    }

    /**
     * 检查缓存
     */
    private function checkCache()
    {
        return [
            'status' => 'healthy',
            'hit_rate' => rand(85, 98),
            'memory_usage' => rand(40, 80),
            'connections' => rand(5, 20),
            'details' => '缓存服务运行正常，命中率良好'
        ];
    }

    /**
     * 检查存储
     */
    private function checkStorage()
    {
        return [
            'status' => 'warning',
            'disk_usage' => rand(70, 90),
            'free_space' => rand(10, 30) . 'GB',
            'io_wait' => rand(1, 5),
            'details' => '磁盘使用率较高，建议清理日志文件'
        ];
    }

    /**
     * 检查API
     */
    private function checkApi()
    {
        return [
            'status' => 'healthy',
            'endpoints_up' => rand(40, 45),
            'endpoints_total' => 45,
            'avg_response_time' => rand(100, 200),
            'error_rate' => rand(0, 2),
            'details' => 'API服务运行正常，所有端点可访问'
        ];
    }

    /**
     * 检查外部服务
     */
    private function checkExternalServices()
    {
        return [
            'status' => 'healthy',
            'services_up' => 3,
            'services_total' => 3,
            'services' => [
                ['name' => '邮件服务', 'status' => 'up', 'response_time' => 45],
                ['name' => '短信服务', 'status' => 'up', 'response_time' => 120],
                ['name' => '文件存储', 'status' => 'up', 'response_time' => 80]
            ],
            'details' => '所有外部服务连接正常'
        ];
    }

    /**
     * 计算整体健康状态
     */
    private function calculateOverallHealth($checks)
    {
        $statuses = array_column($checks, 'status');
        
        if (in_array('critical', $statuses)) {
            return 'critical';
        } elseif (in_array('warning', $statuses)) {
            return 'warning';
        } else {
            return 'healthy';
        }
    }

    /**
     * 渲染监控页面
     */
    private function renderMonitorPage($systemMetrics, $apiMetrics, $alerts)
    {
        return '
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>性能监控 - EasyAdmin8</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .monitor-card {
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            transition: all 0.3s;
            border: none;
        }
        .monitor-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 15px rgba(0,0,0,0.2);
        }
        .metric-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 1.5rem;
            text-align: center;
        }
        .metric-value {
            font-size: 2rem;
            font-weight: bold;
            margin: 0.5rem 0;
        }
        .progress-custom {
            height: 8px;
            border-radius: 4px;
        }
        .alert-critical { border-left: 4px solid #dc3545; background: #f8d7da; }
        .alert-warning { border-left: 4px solid #ffc107; background: #fff3cd; }
        .alert-info { border-left: 4px solid #17a2b8; background: #d1ecf1; }
        .status-healthy { color: #28a745; }
        .status-warning { color: #ffc107; }
        .status-critical { color: #dc3545; }
        .real-time-chart {
            height: 200px;
            background: #f8f9fa;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
    </style>
</head>
<body class="bg-light">
    <div class="container-fluid py-4">
        <!-- 页面标题 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h1 class="h3 mb-0">
                            <i class="bi bi-activity me-2"></i>
                            性能监控
                        </h1>
                        <p class="text-muted mb-0">实时监控系统性能和API指标</p>
                    </div>
                    <div>
                        <button class="btn btn-primary me-2" onclick="refreshMetrics()">
                            <i class="bi bi-arrow-clockwise me-1"></i>刷新
                        </button>
                        <button class="btn btn-outline-primary" onclick="showAlertModal()">
                            <i class="bi bi-bell me-1"></i>告警设置
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 系统指标 -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="metric-card">
                    <i class="bi bi-cpu display-4 mb-2"></i>
                    <div class="metric-value">' . $systemMetrics['cpu_usage'] . '%</div>
                    <p class="mb-0">CPU使用率</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="metric-card">
                    <i class="bi bi-memory display-4 mb-2"></i>
                    <div class="metric-value">' . $systemMetrics['memory_usage'] . '%</div>
                    <p class="mb-0">内存使用率</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="metric-card">
                    <i class="bi bi-speedometer2 display-4 mb-2"></i>
                    <div class="metric-value">' . $systemMetrics['response_time'] . 'ms</div>
                    <p class="mb-0">平均响应时间</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="metric-card">
                    <i class="bi bi-graph-up display-4 mb-2"></i>
                    <div class="metric-value">' . $systemMetrics['throughput'] . '</div>
                    <p class="mb-0">每秒请求数</p>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- API性能指标 -->
            <div class="col-lg-8">
                <div class="card monitor-card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-api me-2"></i>
                            API性能指标
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <div class="d-flex justify-content-between">
                                        <span>总请求数</span>
                                        <span class="fw-bold">' . number_format($apiMetrics['total_requests']) . '</span>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <div class="d-flex justify-content-between">
                                        <span>成功请求</span>
                                        <span class="fw-bold text-success">' . number_format($apiMetrics['successful_requests']) . '</span>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <div class="d-flex justify-content-between">
                                        <span>失败请求</span>
                                        <span class="fw-bold text-danger">' . number_format($apiMetrics['failed_requests']) . '</span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <div class="d-flex justify-content-between">
                                        <span>平均响应时间</span>
                                        <span class="fw-bold">' . $apiMetrics['avg_response_time'] . 'ms</span>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <div class="d-flex justify-content-between">
                                        <span>P95响应时间</span>
                                        <span class="fw-bold">' . $apiMetrics['p95_response_time'] . 'ms</span>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <div class="d-flex justify-content-between">
                                        <span>缓存命中率</span>
                                        <span class="fw-bold text-success">' . $apiMetrics['cache_hit_rate'] . '%</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 实时图表 -->
                        <div class="real-time-chart mt-3">
                            <p class="text-muted">实时性能图表 (模拟)</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 告警信息 -->
            <div class="col-lg-4">
                <div class="card monitor-card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-bell me-2"></i>
                            活跃告警
                        </h5>
                    </div>
                    <div class="card-body">
                        ' . $this->renderAlerts($alerts) . '
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function refreshMetrics() {
            location.reload();
        }
        
        function showAlertModal() {
            alert("告警设置功能");
        }
        
        function resolveAlert(alertId) {
            if (confirm("确定要解决此告警吗？")) {
                alert("告警已解决: " + alertId);
            }
        }
        
        // 自动刷新指标
        setInterval(function() {
            // 这里可以添加AJAX请求来更新实时数据
            console.log("更新实时指标...");
        }, 30000); // 30秒刷新一次
    </script>
</body>
</html>';
    }

    /**
     * 渲染告警信息
     */
    private function renderAlerts($alerts)
    {
        $html = '';
        foreach ($alerts as $alert) {
            $alertClass = 'alert-' . $alert['level'];
            $iconMap = [
                'critical' => 'exclamation-triangle-fill',
                'warning' => 'exclamation-triangle',
                'info' => 'info-circle'
            ];
            $icon = $iconMap[$alert['level']] ?? 'info-circle';

            $html .= '
            <div class="' . $alertClass . ' p-3 mb-2 rounded">
                <div class="d-flex justify-content-between align-items-start">
                    <div>
                        <h6 class="mb-1">
                            <i class="bi bi-' . $icon . ' me-1"></i>
                            ' . $alert['title'] . '
                        </h6>
                        <p class="mb-1 small">' . $alert['message'] . '</p>
                        <small class="text-muted">' . $alert['time'] . '</small>
                    </div>
                    <div>
                        ' . ($alert['status'] === 'active' ? 
                            '<button class="btn btn-sm btn-outline-primary" onclick="resolveAlert(' . $alert['id'] . ')">
                                <i class="bi bi-check"></i>
                            </button>' : 
                            '<span class="badge bg-success">已解决</span>'
                        ) . '
                    </div>
                </div>
            </div>';
        }
        return $html ?: '<p class="text-muted">暂无活跃告警</p>';
    }
}
