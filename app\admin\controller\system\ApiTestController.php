<?php

namespace app\admin\controller\system;

/**
 * 增强型API测试控制器
 * 提供强大的API测试功能，包括批量测试、性能测试、自动化测试等
 */
class ApiTestController
{
    /**
     * API测试首页
     */
    public function index($request = null)
    {
        if (!$request) {
            $request = new \RequestMock();
        }

        $testSuites = $this->getTestSuites();
        $recentTests = $this->getRecentTests();
        $statistics = $this->getTestStatistics();

        $html = $this->renderTestPage($testSuites, $recentTests, $statistics);
        return response($html);
    }

    /**
     * 获取测试套件列表
     */
    public function getTestSuites()
    {
        return [
            [
                'id' => 1,
                'name' => '用户管理API测试',
                'description' => '测试用户相关的所有API接口',
                'api_count' => 8,
                'last_run' => '2025-01-20 10:30:00',
                'success_rate' => 95.5,
                'status' => 'passed'
            ],
            [
                'id' => 2,
                'name' => '文章管理API测试',
                'description' => '测试文章CRUD操作和搜索功能',
                'api_count' => 12,
                'last_run' => '2025-01-20 09:15:00',
                'success_rate' => 88.2,
                'status' => 'warning'
            ],
            [
                'id' => 3,
                'name' => '权限系统API测试',
                'description' => '测试权限验证和角色管理',
                'api_count' => 6,
                'last_run' => '2025-01-20 08:45:00',
                'success_rate' => 100.0,
                'status' => 'passed'
            ]
        ];
    }

    /**
     * 获取最近测试记录
     */
    public function getRecentTests()
    {
        return [
            [
                'id' => 101,
                'api_name' => 'GET /api/users',
                'test_time' => '2025-01-20 10:35:00',
                'response_time' => 125,
                'status_code' => 200,
                'result' => 'success'
            ],
            [
                'id' => 102,
                'api_name' => 'POST /api/articles',
                'test_time' => '2025-01-20 10:32:00',
                'response_time' => 890,
                'status_code' => 500,
                'result' => 'failed'
            ],
            [
                'id' => 103,
                'api_name' => 'PUT /api/users/123',
                'test_time' => '2025-01-20 10:28:00',
                'response_time' => 245,
                'status_code' => 200,
                'result' => 'success'
            ]
        ];
    }

    /**
     * 获取测试统计信息
     */
    public function getTestStatistics()
    {
        return [
            'total_tests' => 1250,
            'passed_tests' => 1156,
            'failed_tests' => 94,
            'success_rate' => 92.5,
            'avg_response_time' => 285,
            'total_apis' => 45,
            'tested_apis' => 42,
            'coverage' => 93.3
        ];
    }

    /**
     * 执行单个API测试
     */
    public function testSingleApi($request = null)
    {
        if (!$request) {
            $request = new \RequestMock();
        }

        $apiUrl = $request->post('url', '/api/users');
        $method = $request->post('method', 'GET');
        $headers = json_decode($request->post('headers', '{}'), true);
        $body = $request->post('body', '');

        // 模拟API测试
        $result = $this->simulateApiTest($apiUrl, $method, $headers, $body);

        return response(json_encode([
            'code' => 200,
            'msg' => 'success',
            'data' => $result
        ]));
    }

    /**
     * 模拟API测试
     */
    private function simulateApiTest($url, $method, $headers, $body)
    {
        // 模拟不同的测试结果
        $scenarios = [
            [
                'status_code' => 200,
                'response_time' => rand(100, 300),
                'response_size' => rand(500, 2000),
                'success' => true,
                'response_body' => '{"code":200,"msg":"success","data":{"id":1,"name":"测试用户"}}',
                'headers' => [
                    'Content-Type' => 'application/json',
                    'X-Response-Time' => '150ms'
                ]
            ],
            [
                'status_code' => 404,
                'response_time' => rand(50, 150),
                'response_size' => rand(100, 500),
                'success' => false,
                'response_body' => '{"code":404,"msg":"资源不存在"}',
                'headers' => [
                    'Content-Type' => 'application/json'
                ]
            ],
            [
                'status_code' => 500,
                'response_time' => rand(1000, 3000),
                'response_size' => rand(200, 800),
                'success' => false,
                'response_body' => '{"code":500,"msg":"服务器内部错误"}',
                'headers' => [
                    'Content-Type' => 'application/json'
                ]
            ]
        ];

        $scenario = $scenarios[array_rand($scenarios)];

        return [
            'test_id' => uniqid(),
            'url' => $url,
            'method' => $method,
            'timestamp' => date('Y-m-d H:i:s'),
            'status_code' => $scenario['status_code'],
            'response_time' => $scenario['response_time'],
            'response_size' => $scenario['response_size'],
            'success' => $scenario['success'],
            'response_body' => $scenario['response_body'],
            'response_headers' => $scenario['headers'],
            'request_headers' => $headers,
            'request_body' => $body,
            'assertions' => [
                [
                    'type' => 'status_code',
                    'expected' => 200,
                    'actual' => $scenario['status_code'],
                    'passed' => $scenario['status_code'] === 200
                ],
                [
                    'type' => 'response_time',
                    'expected' => '< 500ms',
                    'actual' => $scenario['response_time'] . 'ms',
                    'passed' => $scenario['response_time'] < 500
                ],
                [
                    'type' => 'content_type',
                    'expected' => 'application/json',
                    'actual' => 'application/json',
                    'passed' => true
                ]
            ]
        ];
    }

    /**
     * 批量测试API
     */
    public function batchTest($request = null)
    {
        if (!$request) {
            $request = new \RequestMock();
        }

        $testSuiteId = $request->post('test_suite_id', 1);
        $apis = $this->getApiListForSuite($testSuiteId);

        $results = [];
        foreach ($apis as $api) {
            $results[] = $this->simulateApiTest($api['url'], $api['method'], [], '');
        }

        $summary = $this->generateBatchTestSummary($results);

        return response(json_encode([
            'code' => 200,
            'msg' => 'success',
            'data' => [
                'batch_id' => uniqid(),
                'test_suite_id' => $testSuiteId,
                'total_tests' => count($results),
                'results' => $results,
                'summary' => $summary
            ]
        ]));
    }

    /**
     * 获取测试套件的API列表
     */
    private function getApiListForSuite($suiteId)
    {
        $suites = [
            1 => [
                ['url' => '/api/users', 'method' => 'GET'],
                ['url' => '/api/users', 'method' => 'POST'],
                ['url' => '/api/users/1', 'method' => 'GET'],
                ['url' => '/api/users/1', 'method' => 'PUT'],
                ['url' => '/api/users/1', 'method' => 'DELETE']
            ],
            2 => [
                ['url' => '/api/articles', 'method' => 'GET'],
                ['url' => '/api/articles', 'method' => 'POST'],
                ['url' => '/api/articles/1', 'method' => 'GET'],
                ['url' => '/api/articles/search', 'method' => 'POST']
            ]
        ];

        return $suites[$suiteId] ?? [];
    }

    /**
     * 生成批量测试摘要
     */
    private function generateBatchTestSummary($results)
    {
        $total = count($results);
        $passed = count(array_filter($results, function($r) { return $r['success']; }));
        $failed = $total - $passed;
        
        $totalTime = array_sum(array_column($results, 'response_time'));
        $avgTime = $total > 0 ? round($totalTime / $total, 2) : 0;

        return [
            'total_tests' => $total,
            'passed' => $passed,
            'failed' => $failed,
            'success_rate' => $total > 0 ? round(($passed / $total) * 100, 2) : 0,
            'total_time' => $totalTime,
            'average_time' => $avgTime,
            'fastest_test' => $total > 0 ? min(array_column($results, 'response_time')) : 0,
            'slowest_test' => $total > 0 ? max(array_column($results, 'response_time')) : 0
        ];
    }

    /**
     * 性能测试
     */
    public function performanceTest($request = null)
    {
        if (!$request) {
            $request = new \RequestMock();
        }

        $apiUrl = $request->post('url', '/api/users');
        $concurrent = $request->post('concurrent', 10);
        $duration = $request->post('duration', 60);

        // 模拟性能测试结果
        $result = $this->simulatePerformanceTest($apiUrl, $concurrent, $duration);

        return response(json_encode([
            'code' => 200,
            'msg' => 'success',
            'data' => $result
        ]));
    }

    /**
     * 模拟性能测试
     */
    private function simulatePerformanceTest($url, $concurrent, $duration)
    {
        $totalRequests = $concurrent * $duration;
        $successRequests = round($totalRequests * 0.95);
        $failedRequests = $totalRequests - $successRequests;

        return [
            'test_id' => uniqid(),
            'url' => $url,
            'concurrent_users' => $concurrent,
            'test_duration' => $duration,
            'total_requests' => $totalRequests,
            'successful_requests' => $successRequests,
            'failed_requests' => $failedRequests,
            'requests_per_second' => round($totalRequests / $duration, 2),
            'average_response_time' => rand(150, 300),
            'min_response_time' => rand(50, 100),
            'max_response_time' => rand(500, 1000),
            'percentiles' => [
                '50%' => rand(150, 200),
                '90%' => rand(250, 350),
                '95%' => rand(350, 450),
                '99%' => rand(450, 800)
            ],
            'error_rate' => round(($failedRequests / $totalRequests) * 100, 2),
            'throughput' => round(($successRequests * 1024) / $duration, 2) . ' KB/s'
        ];
    }

    /**
     * 渲染测试页面
     */
    private function renderTestPage($testSuites, $recentTests, $statistics)
    {
        return '
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API测试工具 - EasyAdmin8</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .test-card {
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            transition: all 0.3s;
            border: none;
        }
        .test-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 15px rgba(0,0,0,0.2);
        }
        .status-passed { color: #28a745; }
        .status-failed { color: #dc3545; }
        .status-warning { color: #ffc107; }
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 1.5rem;
        }
        .test-result-success { background: #d4edda; border-left: 4px solid #28a745; }
        .test-result-failed { background: #f8d7da; border-left: 4px solid #dc3545; }
        .response-time-good { color: #28a745; }
        .response-time-slow { color: #ffc107; }
        .response-time-bad { color: #dc3545; }
    </style>
</head>
<body class="bg-light">
    <div class="container-fluid py-4">
        <!-- 页面标题 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h1 class="h3 mb-0">
                            <i class="bi bi-bug me-2"></i>
                            API测试工具
                        </h1>
                        <p class="text-muted mb-0">强大的API测试、性能测试和自动化测试工具</p>
                    </div>
                    <div>
                        <button class="btn btn-primary me-2" onclick="showTestModal()">
                            <i class="bi bi-play-circle me-1"></i>新建测试
                        </button>
                        <button class="btn btn-outline-primary" onclick="showBatchTestModal()">
                            <i class="bi bi-collection me-1"></i>批量测试
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 统计信息 -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="stats-card text-center">
                    <i class="bi bi-check-circle display-4 mb-2"></i>
                    <h3>' . $statistics['success_rate'] . '%</h3>
                    <p class="mb-0">成功率</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card text-center">
                    <i class="bi bi-speedometer2 display-4 mb-2"></i>
                    <h3>' . $statistics['avg_response_time'] . 'ms</h3>
                    <p class="mb-0">平均响应时间</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card text-center">
                    <i class="bi bi-graph-up display-4 mb-2"></i>
                    <h3>' . $statistics['total_tests'] . '</h3>
                    <p class="mb-0">总测试数</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card text-center">
                    <i class="bi bi-shield-check display-4 mb-2"></i>
                    <h3>' . $statistics['coverage'] . '%</h3>
                    <p class="mb-0">API覆盖率</p>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- 测试套件 -->
            <div class="col-lg-8">
                <div class="card test-card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-collection me-2"></i>
                            测试套件
                        </h5>
                    </div>
                    <div class="card-body">
                        ' . $this->renderTestSuites($testSuites) . '
                    </div>
                </div>
            </div>

            <!-- 最近测试 -->
            <div class="col-lg-4">
                <div class="card test-card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-clock-history me-2"></i>
                            最近测试
                        </h5>
                    </div>
                    <div class="card-body">
                        ' . $this->renderRecentTests($recentTests) . '
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function showTestModal() {
            alert("新建测试功能");
        }
        
        function showBatchTestModal() {
            alert("批量测试功能");
        }
        
        function runTestSuite(suiteId) {
            alert("运行测试套件: " + suiteId);
        }
    </script>
</body>
</html>';
    }

    /**
     * 渲染测试套件
     */
    private function renderTestSuites($testSuites)
    {
        $html = '';
        foreach ($testSuites as $suite) {
            $statusIcon = $suite['status'] === 'passed' ? 'check-circle' : 
                         ($suite['status'] === 'warning' ? 'exclamation-triangle' : 'x-circle');
            $statusClass = 'status-' . $suite['status'];

            $html .= '
            <div class="row mb-3 p-3 border rounded">
                <div class="col-md-8">
                    <h6 class="mb-1">' . $suite['name'] . '</h6>
                    <p class="text-muted small mb-2">' . $suite['description'] . '</p>
                    <small class="text-muted">
                        <i class="bi bi-api me-1"></i>' . $suite['api_count'] . ' APIs
                        <span class="ms-3">
                            <i class="bi bi-clock me-1"></i>' . $suite['last_run'] . '
                        </span>
                    </small>
                </div>
                <div class="col-md-4 text-end">
                    <div class="mb-2">
                        <i class="bi bi-' . $statusIcon . ' ' . $statusClass . ' me-1"></i>
                        <span class="' . $statusClass . '">' . $suite['success_rate'] . '%</span>
                    </div>
                    <button class="btn btn-sm btn-primary" onclick="runTestSuite(' . $suite['id'] . ')">
                        <i class="bi bi-play me-1"></i>运行
                    </button>
                </div>
            </div>';
        }
        return $html;
    }

    /**
     * 渲染最近测试
     */
    private function renderRecentTests($recentTests)
    {
        $html = '';
        foreach ($recentTests as $test) {
            $resultClass = $test['result'] === 'success' ? 'test-result-success' : 'test-result-failed';
            $timeClass = $test['response_time'] < 200 ? 'response-time-good' : 
                        ($test['response_time'] < 500 ? 'response-time-slow' : 'response-time-bad');

            $html .= '
            <div class="' . $resultClass . ' p-2 mb-2 rounded">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <small class="fw-bold">' . $test['api_name'] . '</small>
                        <br>
                        <small class="text-muted">' . $test['test_time'] . '</small>
                    </div>
                    <div class="text-end">
                        <small class="' . $timeClass . '">' . $test['response_time'] . 'ms</small>
                        <br>
                        <small>HTTP ' . $test['status_code'] . '</small>
                    </div>
                </div>
            </div>';
        }
        return $html;
    }
}
