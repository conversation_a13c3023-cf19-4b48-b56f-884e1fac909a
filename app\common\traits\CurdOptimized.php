<?php

namespace app\common\traits;

use app\common\exceptions\ValidationException;
use app\common\exceptions\ModelException;
use app\common\services\ExportService;
use Respect\Validation\Validator;
use support\Request;
use support\Response;
use app\common\services\annotation\NodeAnnotation;

/**
 * 优化后的CURD操作特性
 */
trait CurdOptimized
{
    /**
     * 列表页面
     */
    #[NodeAnnotation(title: '列表', auth: true)]
    public function index(Request $request): Response
    {
        if (!$request->isAjax()) {
            return $this->fetch();
        }

        if ($request->input('selectFields')) {
            return $this->selectList();
        }

        try {
            [$page, $limit, $where] = $this->buildTableParams();
            $result = $this->getListData($where, $limit);
            
            return json([
                'code' => 0,
                'msg' => '',
                'count' => $result['count'],
                'data' => $result['data'],
            ]);
        } catch (\Exception $e) {
            return json([
                'code' => 1,
                'msg' => '获取数据失败: ' . $e->getMessage(),
                'count' => 0,
                'data' => [],
            ]);
        }
    }

    /**
     * 添加记录
     */
    #[NodeAnnotation(title: '添加', auth: true)]
    public function add(Request $request): Response
    {
        if ($request->isAjax()) {
            try {
                $this->validateAddData($request->post());
                $result = $this->createRecord($request->post());
                
                return $result 
                    ? $this->success('保存成功') 
                    : $this->error('保存失败');
                    
            } catch (ValidationException $e) {
                return $this->error('数据验证失败: ' . $e->getMessage());
            } catch (ModelException $e) {
                return $this->error('数据保存失败: ' . $e->getMessage());
            } catch (\Exception $e) {
                logger()->error('添加记录失败', [
                    'model' => get_class($this->model),
                    'data' => $request->post(),
                    'exception' => $e->getMessage()
                ]);
                return $this->error('系统错误，请联系管理员');
            }
        }
        
        return $this->fetch();
    }

    /**
     * 编辑记录
     */
    #[NodeAnnotation(title: '编辑', auth: true)]
    public function edit(Request $request): Response
    {
        $id = (int)$request->input('id');
        
        try {
            $row = $this->findRecord($id);
            
            if ($request->isAjax()) {
                $this->validateEditData($request->post(), $id);
                $result = $this->updateRecord($row, $request->post());
                
                return $result 
                    ? $this->success('保存成功') 
                    : $this->error('保存失败');
            }
            
            $this->assign(compact('row'));
            return $this->fetch();
            
        } catch (ValidationException $e) {
            return $this->error('数据验证失败: ' . $e->getMessage());
        } catch (ModelException $e) {
            return $this->error('数据操作失败: ' . $e->getMessage());
        } catch (\Exception $e) {
            logger()->error('编辑记录失败', [
                'model' => get_class($this->model),
                'id' => $id,
                'exception' => $e->getMessage()
            ]);
            return $this->error('系统错误，请联系管理员');
        }
    }

    /**
     * 删除记录
     */
    #[NodeAnnotation(title: '删除', auth: true)]
    public function delete(Request $request): Response
    {
        if (!$request->isAjax()) {
            return $this->error('非法请求');
        }

        try {
            $ids = $this->parseIds($request->input('id'));
            $this->validateDeleteData($ids);
            $result = $this->deleteRecords($ids);
            
            return $result 
                ? $this->success('删除成功') 
                : $this->error('删除失败');
                
        } catch (ValidationException $e) {
            return $this->error('数据验证失败: ' . $e->getMessage());
        } catch (ModelException $e) {
            return $this->error('删除失败: ' . $e->getMessage());
        } catch (\Exception $e) {
            logger()->error('删除记录失败', [
                'model' => get_class($this->model),
                'ids' => $request->input('id'),
                'exception' => $e->getMessage()
            ]);
            return $this->error('系统错误，请联系管理员');
        }
    }

    /**
     * 导出数据
     */
    #[NodeAnnotation(title: '导出', auth: true)]
    public function export(Request $request): Response
    {
        if (env('EASYADMIN.IS_DEMO', false)) {
            return $this->error('演示环境下不允许操作');
        }

        try {
            [$page, $limit, $where] = $this->buildTableParams();
            $exportService = new ExportService();
            
            return $exportService->exportModel($this->model, $where, [
                'exclude_fields' => $this->noExportFields ?? [],
                'limit' => 100000,
                'filename' => $this->getExportFilename(),
            ]);
            
        } catch (\Exception $e) {
            logger()->error('导出数据失败', [
                'model' => get_class($this->model),
                'exception' => $e->getMessage()
            ]);
            return $this->error('导出失败: ' . $e->getMessage());
        }
    }

    /**
     * 属性修改
     */
    #[NodeAnnotation(title: '属性修改', auth: true)]
    public function modify(Request $request): Response
    {
        if (!$request->isAjax()) {
            return $this->error('非法请求');
        }

        try {
            $post = $request->post();
            $this->validateModifyData($post);
            
            $row = $this->findRecord($post['id']);
            $result = $this->modifyField($row, $post['field'], $post['value']);
            
            return $result 
                ? $this->success('保存成功') 
                : $this->error('保存失败');
                
        } catch (ValidationException $e) {
            return $this->error('数据验证失败: ' . $e->getMessage());
        } catch (ModelException $e) {
            return $this->error('操作失败: ' . $e->getMessage());
        } catch (\Exception $e) {
            logger()->error('修改属性失败', [
                'model' => get_class($this->model),
                'data' => $request->post(),
                'exception' => $e->getMessage()
            ]);
            return $this->error('系统错误，请联系管理员');
        }
    }

    /**
     * 获取列表数据
     */
    protected function getListData(array $where, int $limit): array
    {
        $count = $this->model->where($where)->count();
        $data = $this->model->where($where)
            ->orderBy($this->order, $this->sort)
            ->paginate($limit)
            ->items();
            
        return compact('count', 'data');
    }

    /**
     * 查找记录
     */
    protected function findRecord(int $id)
    {
        $row = $this->model->find($id);
        if (empty($row)) {
            throw new ModelException('数据不存在');
        }
        return $row;
    }

    /**
     * 创建记录
     */
    protected function createRecord(array $data): bool
    {
        try {
            return (bool)insertFields($this->model, $data);
        } catch (\Exception $e) {
            throw new ModelException('数据保存失败: ' . $e->getMessage());
        }
    }

    /**
     * 更新记录
     */
    protected function updateRecord($row, array $data): bool
    {
        try {
            return (bool)updateFields($this->model, $row, $data);
        } catch (\Exception $e) {
            throw new ModelException('数据更新失败: ' . $e->getMessage());
        }
    }

    /**
     * 删除记录
     */
    protected function deleteRecords(array $ids): bool
    {
        try {
            return $this->model->whereIn('id', $ids)->delete() > 0;
        } catch (\Exception $e) {
            throw new ModelException('数据删除失败: ' . $e->getMessage());
        }
    }

    /**
     * 修改字段
     */
    protected function modifyField($row, string $field, $value): bool
    {
        try {
            $row->$field = $value;
            return $row->save();
        } catch (\Exception $e) {
            throw new ModelException('字段修改失败: ' . $e->getMessage());
        }
    }

    /**
     * 解析ID参数
     */
    protected function parseIds($ids): array
    {
        if (!is_array($ids)) {
            $ids = (array)$ids;
        }
        return array_filter(array_map('intval', $ids));
    }

    /**
     * 验证添加数据
     */
    protected function validateAddData(array $data): void
    {
        // 子类可以重写此方法进行具体验证
    }

    /**
     * 验证编辑数据
     */
    protected function validateEditData(array $data, int $id): void
    {
        // 子类可以重写此方法进行具体验证
    }

    /**
     * 验证删除数据
     */
    protected function validateDeleteData(array $ids): void
    {
        if (empty($ids)) {
            throw new ValidationException('请选择要删除的数据');
        }
    }

    /**
     * 验证修改数据
     */
    protected function validateModifyData(array $data): void
    {
        Validator::input($data, [
            'id' => Validator::notEmpty()->setName('ID'),
            'field' => Validator::notEmpty()->setName('字段'),
        ]);
    }

    /**
     * 获取导出文件名
     */
    protected function getExportFilename(): string
    {
        return '数据导出_' . date('YmdHis');
    }
}
