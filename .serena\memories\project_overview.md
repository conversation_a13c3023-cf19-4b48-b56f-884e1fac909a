# EasyAdmin8-webman 项目概述

## 项目简介
EasyAdmin8-webman 是基于 webman 框架和 Layui 前端框架的快速开发后台管理系统。这是一个现代化的 PHP 后台管理系统，专注于提供高效的 CURD 生成功能和完整的后台管理解决方案。

## 技术栈
- **后端框架**: webman (基于 Workerman 的高性能 PHP 框架)
- **PHP 版本**: >= 8.1.0
- **前端框架**: Layui v2.9.x
- **数据库**: MySQL >= 5.7
- **模板引擎**: Laravel Blade
- **依赖管理**: Composer
- **缓存**: Redis/File Cache
- **文件上传**: 支持阿里云 OSS、腾讯云 COS、七牛云等

## 核心功能
1. **CURD 生成器**: 自动生成控制器、模型、视图和 JS 文件
2. **权限管理**: 基于角色的权限控制系统
3. **API 文档**: 自动生成和管理 API 文档
4. **系统监控**: 性能监控和日志管理
5. **文件管理**: 多云存储支持
6. **多语言支持**: 国际化功能
7. **缓存管理**: 多种缓存策略

## 项目结构
- `app/`: 应用程序代码
  - `admin/`: 后台管理模块
  - `controller/`: 控制器
  - `model/`: 数据模型
  - `view/`: 视图模板
- `config/`: 配置文件
- `public/`: 公共资源文件
- `database/`: 数据库相关文件
- `runtime/`: 运行时文件

## 特色功能
- **AI 驱动的 CURD 生成**: 智能分析数据表结构，自动生成完整的 CURD 功能
- **可视化界面**: 现代化的管理界面，基于 Layui 构建
- **高性能**: 基于 webman 框架，支持高并发处理
- **模块化设计**: 清晰的模块划分，易于扩展和维护