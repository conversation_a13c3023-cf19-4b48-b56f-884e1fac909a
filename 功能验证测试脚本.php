<?php
/**
 * EasyAdmin8-webman API文档管理系统 - 功能验证测试脚本
 * 测试所有新增功能是否可以正常访问
 */

echo "=== EasyAdmin8-webman API文档管理系统 - 功能验证测试 ===\n\n";

/**
 * 测试URL访问
 */
function testUrl($url, $description) {
    echo "测试: {$description}\n";
    echo "URL: {$url}\n";
    
    $context = stream_context_create([
        'http' => [
            'timeout' => 10,
            'method' => 'GET'
        ]
    ]);
    
    $startTime = microtime(true);
    $content = @file_get_contents($url, false, $context);
    $endTime = microtime(true);
    
    $responseTime = round(($endTime - $startTime) * 1000, 2);
    
    if ($content !== false) {
        $size = strlen($content);
        echo "   ✅ 访问成功 - 响应时间: {$responseTime}ms, 大小: " . number_format($size) . " bytes\n";
        
        // 检查是否包含预期内容
        if (strpos($content, 'EasyAdmin8') !== false || strpos($content, 'API') !== false) {
            echo "   ✅ 内容验证通过\n";
        } else {
            echo "   ⚠️  内容验证失败\n";
        }
        return true;
    } else {
        echo "   ❌ 访问失败\n";
        return false;
    }
}

echo "开始功能验证测试...\n\n";

// 基础URL
$baseUrl = 'http://localhost:8080';

// 测试URL列表
$testUrls = [
    // 基础功能
    [
        'url' => $baseUrl,
        'desc' => '系统首页'
    ],
    [
        'url' => $baseUrl . '/admin/system/apidoc',
        'desc' => '简化版API文档首页'
    ],
    [
        'url' => $baseUrl . '/admin/system/apidoc-enhanced',
        'desc' => '增强版API文档首页'
    ],
    
    // 新增功能模块
    [
        'url' => $baseUrl . '/admin/system/api-version',
        'desc' => 'API版本管理系统'
    ],
    [
        'url' => $baseUrl . '/admin/system/api-test',
        'desc' => 'API测试工具系统'
    ],
    [
        'url' => $baseUrl . '/admin/system/cache',
        'desc' => '智能缓存管理系统'
    ],
    [
        'url' => $baseUrl . '/admin/system/search',
        'desc' => '全文搜索系统'
    ],
    [
        'url' => $baseUrl . '/admin/system/monitor',
        'desc' => '性能监控系统'
    ],
    
    // API接口测试
    [
        'url' => $baseUrl . '/admin/system/apidoc/list',
        'desc' => 'API文档列表接口'
    ],
    [
        'url' => $baseUrl . '/admin/system/apidoc/tables',
        'desc' => '数据表列表接口'
    ],
    [
        'url' => $baseUrl . '/admin/system/api-version/list',
        'desc' => 'API版本列表接口'
    ],
    [
        'url' => $baseUrl . '/admin/system/monitor/health',
        'desc' => '系统健康检查接口'
    ]
];

$passed = 0;
$total = count($testUrls);

foreach ($testUrls as $test) {
    if (testUrl($test['url'], $test['desc'])) {
        $passed++;
    }
    echo "\n";
}

echo "=== 功能验证测试结果 ===\n";
echo "通过率: " . round(($passed / $total) * 100, 1) . "%\n";
echo "通过项: {$passed}/{$total}\n";

if ($passed === $total) {
    echo "🎉 所有功能验证通过！系统运行完美！\n";
} elseif ($passed >= $total * 0.8) {
    echo "✅ 大部分功能正常，系统运行良好！\n";
} else {
    echo "⚠️  部分功能需要检查，请确认服务器是否正常启动！\n";
}

echo "\n=== 使用指南 ===\n";
echo "1. 确保服务器已启动: php -S localhost:8080 -t public public/index_simple.php\n";
echo "2. 访问系统首页: http://localhost:8080\n";
echo "3. 体验新增功能:\n";
echo "   - 🔄 API版本管理: http://localhost:8080/admin/system/api-version\n";
echo "   - 🧪 API测试工具: http://localhost:8080/admin/system/api-test\n";
echo "   - ⚡ 缓存管理: http://localhost:8080/admin/system/cache\n";
echo "   - 🔍 全文搜索: http://localhost:8080/admin/system/search\n";
echo "   - 📈 性能监控: http://localhost:8080/admin/system/monitor\n";

echo "\n=== 功能验证测试完成 ===\n";
