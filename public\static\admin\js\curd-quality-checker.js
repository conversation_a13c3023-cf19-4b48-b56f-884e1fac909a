/**
 * CURD 代码质量检查器
 */

layui.use(['layer'], function() {
    var layer = layui.layer;

    // 质量检查规则
    var qualityRules = {
        // 表结构检查
        table: {
            hasId: {
                name: '主键检查',
                description: '表应该有主键字段',
                level: 'error',
                check: function(tableInfo) {
                    return tableInfo.fields && tableInfo.fields.some(f => f.primary);
                }
            },
            hasTimestamps: {
                name: '时间戳检查',
                description: '建议添加created_at和updated_at字段',
                level: 'warning',
                check: function(tableInfo) {
                    if (!tableInfo.fields) return false;
                    var hasCreated = tableInfo.fields.some(f => f.name === 'created_at');
                    var hasUpdated = tableInfo.fields.some(f => f.name === 'updated_at');
                    return hasCreated && hasUpdated;
                }
            },
            hasComment: {
                name: '表注释检查',
                description: '表应该有注释说明',
                level: 'info',
                check: function(tableInfo) {
                    return tableInfo.comment && tableInfo.comment.trim() !== '';
                }
            }
        },
        
        // 字段配置检查
        fields: {
            hasListFields: {
                name: '列表字段检查',
                description: '至少应该有3个字段显示在列表中',
                level: 'warning',
                check: function(fieldConfigs) {
                    return fieldConfigs.filter(f => f.show_in_list).length >= 3;
                }
            },
            hasSearchFields: {
                name: '搜索字段检查',
                description: '建议至少有1个搜索字段',
                level: 'info',
                check: function(fieldConfigs) {
                    return fieldConfigs.filter(f => f.show_in_search).length >= 1;
                }
            },
            hasFormFields: {
                name: '表单字段检查',
                description: '至少应该有1个表单字段',
                level: 'error',
                check: function(fieldConfigs) {
                    return fieldConfigs.filter(f => f.show_in_form).length >= 1;
                }
            },
            fieldComments: {
                name: '字段注释检查',
                description: '所有字段都应该有注释或标签',
                level: 'warning',
                check: function(fieldConfigs) {
                    return fieldConfigs.every(f => f.comment && f.comment.trim() !== '');
                }
            },
            componentMatch: {
                name: '组件匹配检查',
                description: '字段组件类型应该与字段类型匹配',
                level: 'warning',
                check: function(fieldConfigs) {
                    return fieldConfigs.every(f => isComponentMatchType(f.type, f.component));
                }
            }
        },
        
        // 命名规范检查
        naming: {
            tableNaming: {
                name: '表命名规范',
                description: '表名应该使用下划线命名法',
                level: 'info',
                check: function(tableName) {
                    return /^[a-z][a-z0-9_]*$/.test(tableName);
                }
            },
            fieldNaming: {
                name: '字段命名规范',
                description: '字段名应该使用下划线命名法',
                level: 'info',
                check: function(fieldConfigs) {
                    return fieldConfigs.every(f => /^[a-z][a-z0-9_]*$/.test(f.name));
                }
            }
        },
        
        // 性能优化检查
        performance: {
            indexCheck: {
                name: '索引检查',
                description: '搜索字段建议添加索引',
                level: 'info',
                check: function(fieldConfigs, tableInfo) {
                    var searchFields = fieldConfigs.filter(f => f.show_in_search);
                    if (searchFields.length === 0) return true;
                    
                    var indexes = tableInfo.indexes || [];
                    return searchFields.every(f => 
                        indexes.some(idx => idx.columns && idx.columns.includes(f.name))
                    );
                }
            },
            textFieldsInList: {
                name: 'TEXT字段列表显示',
                description: 'TEXT类型字段不建议在列表中显示',
                level: 'warning',
                check: function(fieldConfigs) {
                    return !fieldConfigs.some(f => 
                        f.type.toLowerCase().includes('text') && f.show_in_list
                    );
                }
            }
        }
    };

    // 初始化质量检查器
    function initQualityChecker() {
        bindQualityEvents();
    }

    // 绑定质量检查相关事件
    function bindQualityEvents() {
        // 自动检查按钮
        $(document).on('click', '#quality-check-btn', function() {
            runQualityCheck();
        });

        // 修复建议按钮
        $(document).on('click', '#auto-fix-btn', function() {
            autoFixIssues();
        });

        // 生成质量报告
        $(document).on('click', '#quality-report-btn', function() {
            generateQualityReport();
        });
    }

    // 运行质量检查
    function runQualityCheck() {
        var tableName = $('select[name="table_name"]').val();
        var tableInfo = window.tableInfo;
        var fieldConfigs = window.fieldConfigs;

        if (!tableName || !tableInfo || !fieldConfigs) {
            layer.msg('请先完成表结构分析和字段配置', {icon: 2});
            return;
        }

        var issues = [];

        // 检查表结构
        Object.keys(qualityRules.table).forEach(function(ruleKey) {
            var rule = qualityRules.table[ruleKey];
            if (!rule.check(tableInfo)) {
                issues.push({
                    type: 'table',
                    rule: ruleKey,
                    name: rule.name,
                    description: rule.description,
                    level: rule.level,
                    fixable: hasAutoFix('table', ruleKey)
                });
            }
        });

        // 检查字段配置
        Object.keys(qualityRules.fields).forEach(function(ruleKey) {
            var rule = qualityRules.fields[ruleKey];
            if (!rule.check(fieldConfigs)) {
                issues.push({
                    type: 'fields',
                    rule: ruleKey,
                    name: rule.name,
                    description: rule.description,
                    level: rule.level,
                    fixable: hasAutoFix('fields', ruleKey)
                });
            }
        });

        // 检查命名规范
        Object.keys(qualityRules.naming).forEach(function(ruleKey) {
            var rule = qualityRules.naming[ruleKey];
            var checkData = ruleKey === 'tableNaming' ? tableName : fieldConfigs;
            if (!rule.check(checkData)) {
                issues.push({
                    type: 'naming',
                    rule: ruleKey,
                    name: rule.name,
                    description: rule.description,
                    level: rule.level,
                    fixable: hasAutoFix('naming', ruleKey)
                });
            }
        });

        // 检查性能优化
        Object.keys(qualityRules.performance).forEach(function(ruleKey) {
            var rule = qualityRules.performance[ruleKey];
            var checkResult = ruleKey === 'indexCheck' ? 
                rule.check(fieldConfigs, tableInfo) : 
                rule.check(fieldConfigs);
            if (!checkResult) {
                issues.push({
                    type: 'performance',
                    rule: ruleKey,
                    name: rule.name,
                    description: rule.description,
                    level: rule.level,
                    fixable: hasAutoFix('performance', ruleKey)
                });
            }
        });

        displayQualityResults(issues);
    }

    // 显示质量检查结果
    function displayQualityResults(issues) {
        var content = '<div class="quality-results">';
        
        if (issues.length === 0) {
            content += '<div class="quality-success">';
            content += '<i class="layui-icon layui-icon-ok-circle" style="font-size: 48px; color: #52c41a;"></i>';
            content += '<h3>质量检查通过</h3>';
            content += '<p>恭喜！您的配置符合所有质量标准。</p>';
            content += '</div>';
        } else {
            var errorCount = issues.filter(i => i.level === 'error').length;
            var warningCount = issues.filter(i => i.level === 'warning').length;
            var infoCount = issues.filter(i => i.level === 'info').length;
            var fixableCount = issues.filter(i => i.fixable).length;

            content += '<div class="quality-summary">';
            content += '<h3>质量检查结果</h3>';
            content += '<div class="quality-stats">';
            content += '<span class="stat-item error">错误: ' + errorCount + '</span>';
            content += '<span class="stat-item warning">警告: ' + warningCount + '</span>';
            content += '<span class="stat-item info">建议: ' + infoCount + '</span>';
            content += '<span class="stat-item fixable">可自动修复: ' + fixableCount + '</span>';
            content += '</div>';
            content += '</div>';

            content += '<div class="quality-issues">';
            issues.forEach(function(issue) {
                content += '<div class="quality-issue ' + issue.level + '">';
                content += '<div class="issue-header">';
                content += '<i class="layui-icon ' + getIssueIcon(issue.level) + '"></i>';
                content += '<span class="issue-name">' + issue.name + '</span>';
                if (issue.fixable) {
                    content += '<button class="layui-btn layui-btn-xs fix-btn" data-type="' + issue.type + '" data-rule="' + issue.rule + '">修复</button>';
                }
                content += '</div>';
                content += '<div class="issue-description">' + issue.description + '</div>';
                content += '</div>';
            });
            content += '</div>';

            if (fixableCount > 0) {
                content += '<div class="quality-actions">';
                content += '<button class="layui-btn layui-btn-normal" id="fix-all-btn">一键修复所有问题</button>';
                content += '</div>';
            }
        }
        
        content += '</div>';

        layer.open({
            type: 1,
            title: '代码质量检查结果',
            content: content,
            area: ['600px', '500px'],
            btn: ['关闭'],
            success: function() {
                // 绑定修复按钮事件
                $('.fix-btn').on('click', function() {
                    var type = $(this).data('type');
                    var rule = $(this).data('rule');
                    fixSingleIssue(type, rule);
                });

                $('#fix-all-btn').on('click', function() {
                    autoFixIssues();
                });
            }
        });
    }

    // 获取问题图标
    function getIssueIcon(level) {
        switch (level) {
            case 'error': return 'layui-icon-close-fill';
            case 'warning': return 'layui-icon-tips-fill';
            case 'info': return 'layui-icon-about-fill';
            default: return 'layui-icon-about';
        }
    }

    // 检查是否有自动修复功能
    function hasAutoFix(type, rule) {
        var autoFixRules = {
            fields: ['hasListFields', 'hasSearchFields', 'textFieldsInList'],
            performance: ['textFieldsInList']
        };
        
        return autoFixRules[type] && autoFixRules[type].includes(rule);
    }

    // 修复单个问题
    function fixSingleIssue(type, rule) {
        switch (type + '.' + rule) {
            case 'fields.hasListFields':
                fixListFields();
                break;
            case 'fields.hasSearchFields':
                fixSearchFields();
                break;
            case 'fields.textFieldsInList':
            case 'performance.textFieldsInList':
                fixTextFieldsInList();
                break;
            default:
                layer.msg('该问题暂不支持自动修复', {icon: 0});
                return;
        }
        
        layer.msg('问题已修复', {icon: 1});
        
        // 重新渲染字段配置
        if (window.CurdGeneratorV2 && window.CurdGeneratorV2.loadFieldConfigs) {
            window.CurdGeneratorV2.loadFieldConfigs();
        }
    }

    // 自动修复所有问题
    function autoFixIssues() {
        layer.confirm('确定要自动修复所有可修复的问题吗？', {icon: 3}, function(index) {
            fixListFields();
            fixSearchFields();
            fixTextFieldsInList();
            
            layer.close(index);
            layer.msg('所有可修复问题已处理', {icon: 1});
            
            // 重新渲染字段配置
            if (window.CurdGeneratorV2 && window.CurdGeneratorV2.loadFieldConfigs) {
                window.CurdGeneratorV2.loadFieldConfigs();
            }
        });
    }

    // 修复列表字段问题
    function fixListFields() {
        if (!window.fieldConfigs) return;
        
        var listFields = window.fieldConfigs.filter(f => f.show_in_list);
        if (listFields.length >= 3) return;
        
        // 自动选择合适的字段显示在列表中
        var candidates = window.fieldConfigs.filter(f => 
            !f.show_in_list && 
            !f.type.toLowerCase().includes('text') &&
            f.name !== 'password' &&
            !f.name.includes('_at')
        );
        
        var needed = 3 - listFields.length;
        for (var i = 0; i < Math.min(needed, candidates.length); i++) {
            candidates[i].show_in_list = true;
        }
    }

    // 修复搜索字段问题
    function fixSearchFields() {
        if (!window.fieldConfigs) return;
        
        var searchFields = window.fieldConfigs.filter(f => f.show_in_search);
        if (searchFields.length >= 1) return;
        
        // 自动选择name或title字段作为搜索字段
        var nameField = window.fieldConfigs.find(f => f.name.includes('name'));
        var titleField = window.fieldConfigs.find(f => f.name.includes('title'));
        
        if (nameField) {
            nameField.show_in_search = true;
        } else if (titleField) {
            titleField.show_in_search = true;
        } else {
            // 选择第一个varchar字段
            var varcharField = window.fieldConfigs.find(f => 
                f.type.toLowerCase().includes('varchar') && !f.primary
            );
            if (varcharField) {
                varcharField.show_in_search = true;
            }
        }
    }

    // 修复TEXT字段在列表中显示的问题
    function fixTextFieldsInList() {
        if (!window.fieldConfigs) return;
        
        window.fieldConfigs.forEach(function(field) {
            if (field.type.toLowerCase().includes('text') && field.show_in_list) {
                field.show_in_list = false;
            }
        });
    }

    // 检查组件类型是否匹配字段类型
    function isComponentMatchType(fieldType, component) {
        var type = fieldType.toLowerCase();
        
        var typeComponentMap = {
            'int': ['number', 'select', 'radio'],
            'varchar': ['input', 'select', 'radio'],
            'text': ['textarea', 'editor'],
            'datetime': ['datetime', 'date'],
            'date': ['date'],
            'decimal': ['number'],
            'float': ['number'],
            'enum': ['select', 'radio'],
            'json': ['textarea', 'editor']
        };
        
        for (var t in typeComponentMap) {
            if (type.includes(t)) {
                return typeComponentMap[t].includes(component);
            }
        }
        
        return true; // 未知类型默认匹配
    }

    // 生成质量报告
    function generateQualityReport() {
        var tableName = $('select[name="table_name"]').val();
        var tableInfo = window.tableInfo;
        var fieldConfigs = window.fieldConfigs;

        if (!tableName || !tableInfo || !fieldConfigs) {
            layer.msg('请先完成表结构分析和字段配置', {icon: 2});
            return;
        }

        var report = {
            table: tableName,
            timestamp: new Date().toLocaleString(),
            summary: {
                totalFields: fieldConfigs.length,
                listFields: fieldConfigs.filter(f => f.show_in_list).length,
                formFields: fieldConfigs.filter(f => f.show_in_form).length,
                searchFields: fieldConfigs.filter(f => f.show_in_search).length,
                requiredFields: fieldConfigs.filter(f => f.required).length
            },
            recommendations: []
        };

        // 生成建议
        if (report.summary.listFields < 3) {
            report.recommendations.push('建议增加列表显示字段，提高数据可读性');
        }
        
        if (report.summary.searchFields === 0) {
            report.recommendations.push('建议添加搜索字段，提高数据查找效率');
        }
        
        var textInList = fieldConfigs.filter(f => 
            f.type.toLowerCase().includes('text') && f.show_in_list
        ).length;
        if (textInList > 0) {
            report.recommendations.push('建议将TEXT类型字段从列表中移除，提高页面加载速度');
        }

        displayQualityReport(report);
    }

    // 显示质量报告
    function displayQualityReport(report) {
        var content = '<div class="quality-report">';
        content += '<div class="report-header">';
        content += '<h3>代码质量报告</h3>';
        content += '<p>表名: ' + report.table + ' | 生成时间: ' + report.timestamp + '</p>';
        content += '</div>';
        
        content += '<div class="report-summary">';
        content += '<h4>配置概览</h4>';
        content += '<div class="summary-grid">';
        content += '<div class="summary-item"><span>总字段数</span><strong>' + report.summary.totalFields + '</strong></div>';
        content += '<div class="summary-item"><span>列表字段</span><strong>' + report.summary.listFields + '</strong></div>';
        content += '<div class="summary-item"><span>表单字段</span><strong>' + report.summary.formFields + '</strong></div>';
        content += '<div class="summary-item"><span>搜索字段</span><strong>' + report.summary.searchFields + '</strong></div>';
        content += '<div class="summary-item"><span>必填字段</span><strong>' + report.summary.requiredFields + '</strong></div>';
        content += '</div>';
        content += '</div>';
        
        if (report.recommendations.length > 0) {
            content += '<div class="report-recommendations">';
            content += '<h4>优化建议</h4>';
            content += '<ul>';
            report.recommendations.forEach(function(rec) {
                content += '<li>' + rec + '</li>';
            });
            content += '</ul>';
            content += '</div>';
        }
        
        content += '</div>';

        layer.open({
            type: 1,
            title: '质量报告',
            content: content,
            area: ['500px', '400px'],
            btn: ['导出报告', '关闭'],
            yes: function() {
                exportQualityReport(report);
            }
        });
    }

    // 导出质量报告
    function exportQualityReport(report) {
        var content = '# 代码质量报告\n\n';
        content += '**表名:** ' + report.table + '\n';
        content += '**生成时间:** ' + report.timestamp + '\n\n';
        content += '## 配置概览\n\n';
        content += '- 总字段数: ' + report.summary.totalFields + '\n';
        content += '- 列表字段: ' + report.summary.listFields + '\n';
        content += '- 表单字段: ' + report.summary.formFields + '\n';
        content += '- 搜索字段: ' + report.summary.searchFields + '\n';
        content += '- 必填字段: ' + report.summary.requiredFields + '\n\n';
        
        if (report.recommendations.length > 0) {
            content += '## 优化建议\n\n';
            report.recommendations.forEach(function(rec, index) {
                content += (index + 1) + '. ' + rec + '\n';
            });
        }

        var blob = new Blob([content], { type: 'text/markdown;charset=utf-8' });
        var url = window.URL.createObjectURL(blob);
        var a = document.createElement('a');
        a.href = url;
        a.download = report.table + '_quality_report.md';
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        window.URL.revokeObjectURL(url);
        
        layer.msg('报告导出成功', {icon: 1});
    }

    // 暴露全局函数
    window.CurdQualityChecker = {
        init: initQualityChecker,
        check: runQualityCheck,
        autoFix: autoFixIssues,
        generateReport: generateQualityReport
    };

    // 自动初始化
    $(document).ready(function() {
        initQualityChecker();
    });
});
