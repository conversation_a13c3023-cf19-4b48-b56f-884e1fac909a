/**
 * CURD 高级代码生成器
 * 支持更复杂的代码结构和模板
 */

layui.use(['layer'], function() {
    var layer = layui.layer;

    // 高级代码生成器类
    var AdvancedGenerator = {
        // 代码模板库
        templates: {
            controller: {
                basic: 'basic_controller_template',
                api: 'api_controller_template',
                resource: 'resource_controller_template'
            },
            model: {
                basic: 'basic_model_template',
                eloquent: 'eloquent_model_template',
                repository: 'repository_model_template'
            },
            view: {
                layui: 'layui_view_template',
                bootstrap: 'bootstrap_view_template',
                vue: 'vue_view_template'
            },
            js: {
                jquery: 'jquery_js_template',
                layui: 'layui_js_template',
                vue: 'vue_js_template'
            }
        },

        // 初始化
        init: function() {
            this.bindEvents();
            this.loadTemplates();
        },

        // 绑定事件
        bindEvents: function() {
            var self = this;

            // 模板选择变化
            $(document).on('change', '.template-selector', function() {
                var fileType = $(this).data('file-type');
                var template = $(this).val();
                self.updatePreview(fileType, template);
            });

            // 高级选项切换
            $(document).on('click', '.advanced-toggle', function() {
                var target = $(this).data('target');
                $('#' + target).toggle();
                $(this).find('i').toggleClass('layui-icon-down layui-icon-up');
            });

            // 代码片段插入
            $(document).on('click', '.snippet-btn', function() {
                var snippet = $(this).data('snippet');
                self.insertSnippet(snippet);
            });

            // 批量生成
            $(document).on('click', '#batch-generate-btn', function() {
                self.batchGenerate();
            });
        },

        // 加载模板
        loadTemplates: function() {
            // 这里可以从服务器加载模板配置
            console.log('加载代码模板...');
        },

        // 更新预览
        updatePreview: function(fileType, template) {
            var self = this;
            
            if (!window.tableInfo || !window.fieldConfigs) {
                layer.msg('请先分析表结构', {icon: 2});
                return;
            }

            var code = self.generateCode(fileType, template);
            
            // 更新代码预览
            if (window.CurdCodePreview) {
                window.CurdCodePreview.updateCode(fileType, code);
            }
        },

        // 生成代码
        generateCode: function(fileType, template) {
            var self = this;
            var code = '';

            switch (fileType) {
                case 'controller':
                    code = self.generateController(template);
                    break;
                case 'model':
                    code = self.generateModel(template);
                    break;
                case 'view':
                    code = self.generateView(template);
                    break;
                case 'js':
                    code = self.generateJavaScript(template);
                    break;
                case 'migration':
                    code = self.generateMigration();
                    break;
                case 'seeder':
                    code = self.generateSeeder();
                    break;
                case 'test':
                    code = self.generateTest();
                    break;
                case 'api':
                    code = self.generateApiController();
                    break;
                case 'css':
                    code = self.generateCSS();
                    break;
                default:
                    code = '// 暂不支持的文件类型: ' + fileType;
            }

            return code;
        },

        // 生成控制器代码
        generateController: function(template) {
            var tableName = window.tableInfo.name;
            var className = this.toPascalCase(tableName);
            var fields = window.fieldConfigs || [];

            var code = `<?php

namespace app\\admin\\controller\\${this.getModuleName()};

use support\\Request;
use support\\Response;
use app\\common\\model\\${className};
use app\\admin\\controller\\AdminController;

/**
 * ${window.tableInfo.comment || className} 控制器
 * 自动生成时间: ${new Date().toLocaleString()}
 */
class ${className}Controller extends AdminController
{
    /**
     * 列表页面
     */
    public function index(Request $request): Response
    {
        if ($request->method() === 'POST') {
            return $this->selectList($request);
        }
        
        return view('admin/${this.getModuleName().toLowerCase()}/${tableName}/index');
    }

    /**
     * 获取列表数据
     */
    protected function selectList(Request $request): Response
    {
        $page = $request->input('page', 1);
        $limit = $request->input('limit', 15);
        
        $query = ${className}::query();
        
        // 搜索条件
${this.generateSearchConditions(fields)}
        
        $total = $query->count();
        $list = $query->offset(($page - 1) * $limit)
                     ->limit($limit)
                     ->orderBy('id', 'desc')
                     ->get();
        
        return $this->success('获取成功', [
            'count' => $total,
            'data' => $list
        ]);
    }

    /**
     * 添加页面
     */
    public function add(Request $request): Response
    {
        if ($request->method() === 'POST') {
            return $this->save($request);
        }
        
        return view('admin/${this.getModuleName().toLowerCase()}/${tableName}/add');
    }

    /**
     * 编辑页面
     */
    public function edit(Request $request): Response
    {
        if ($request->method() === 'POST') {
            return $this->update($request);
        }
        
        $id = $request->input('id');
        $info = ${className}::find($id);
        
        if (!$info) {
            return $this->error('记录不存在');
        }
        
        return view('admin/${this.getModuleName().toLowerCase()}/${tableName}/edit', [
            'info' => $info
        ]);
    }

    /**
     * 保存数据
     */
    protected function save(Request $request): Response
    {
        $data = $request->only([
${this.generateFieldList(fields, '            ')}
        ]);
        
        // 数据验证
        $validate = $this->validate($data, [
${this.generateValidationRules(fields)}
        ]);
        
        if ($validate !== true) {
            return $this->error($validate);
        }
        
        try {
            ${className}::create($data);
            return $this->success('添加成功');
        } catch (\\Exception $e) {
            return $this->error('添加失败: ' . $e->getMessage());
        }
    }

    /**
     * 更新数据
     */
    protected function update(Request $request): Response
    {
        $id = $request->input('id');
        $data = $request->only([
${this.generateFieldList(fields, '            ')}
        ]);
        
        $model = ${className}::find($id);
        if (!$model) {
            return $this->error('记录不存在');
        }
        
        try {
            $model->update($data);
            return $this->success('更新成功');
        } catch (\\Exception $e) {
            return $this->error('更新失败: ' . $e->getMessage());
        }
    }

    /**
     * 删除数据
     */
    public function delete(Request $request): Response
    {
        $ids = $request->input('ids');
        
        if (empty($ids)) {
            return $this->error('请选择要删除的记录');
        }
        
        try {
            ${className}::whereIn('id', $ids)->delete();
            return $this->success('删除成功');
        } catch (\\Exception $e) {
            return $this->error('删除失败: ' . $e->getMessage());
        }
    }
}`;

            return code;
        },

        // 生成模型代码
        generateModel: function(template) {
            var tableName = window.tableInfo.name;
            var className = this.toPascalCase(tableName);
            var fields = window.fieldConfigs || [];

            var code = `<?php

namespace app\\common\\model;

use support\\Model;

/**
 * ${window.tableInfo.comment || className} 模型
 * 自动生成时间: ${new Date().toLocaleString()}
 */
class ${className} extends Model
{
    /**
     * 表名
     */
    protected $table = '${tableName}';

    /**
     * 可批量赋值的属性
     */
    protected $fillable = [
${this.generateFieldList(fields, '        ', true)}
    ];

    /**
     * 属性类型转换
     */
    protected $casts = [
${this.generateCasts(fields)}
    ];

    /**
     * 隐藏的属性
     */
    protected $hidden = [
        'password',
        'deleted_at'
    ];

    /**
     * 日期字段
     */
    protected $dates = [
        'created_at',
        'updated_at'
    ];

${this.generateModelMethods(fields)}
}`;

            return code;
        },

        // 生成视图代码
        generateView: function(template) {
            var tableName = window.tableInfo.name;
            var className = this.toPascalCase(tableName);
            var fields = window.fieldConfigs || [];

            return this.generateLayuiView(tableName, className, fields);
        },

        // 生成JavaScript代码
        generateJavaScript: function(template) {
            var tableName = window.tableInfo.name;
            var fields = window.fieldConfigs || [];

            return this.generateLayuiJS(tableName, fields);
        },

        // 生成数据迁移
        generateMigration: function() {
            var tableName = window.tableInfo.name;
            var fields = window.fieldConfigs || [];

            var code = `<?php

use support\\Db;

/**
 * ${tableName} 表迁移
 * 自动生成时间: ${new Date().toLocaleString()}
 */
return new class {
    /**
     * 执行迁移
     */
    public function up()
    {
        Db::statement("
            CREATE TABLE IF NOT EXISTS \`${tableName}\` (
${this.generateTableSchema(fields)}
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='${window.tableInfo.comment || ''}';
        ");
    }

    /**
     * 回滚迁移
     */
    public function down()
    {
        Db::statement("DROP TABLE IF EXISTS \`${tableName}\`");
    }
};`;

            return code;
        },

        // 生成数据填充
        generateSeeder: function() {
            var tableName = window.tableInfo.name;
            var className = this.toPascalCase(tableName);

            var code = `<?php

namespace database\\seeders;

use app\\common\\model\\${className};

/**
 * ${className} 数据填充
 * 自动生成时间: ${new Date().toLocaleString()}
 */
class ${className}Seeder
{
    /**
     * 运行数据填充
     */
    public function run()
    {
        $data = [
${this.generateSampleData()}
        ];

        foreach ($data as $item) {
            ${className}::create($item);
        }
    }
}`;

            return code;
        },

        // 生成单元测试
        generateTest: function() {
            var tableName = window.tableInfo.name;
            var className = this.toPascalCase(tableName);

            var code = `<?php

namespace tests\\Feature;

use Tests\\TestCase;
use app\\common\\model\\${className};

/**
 * ${className} 功能测试
 * 自动生成时间: ${new Date().toLocaleString()}
 */
class ${className}Test extends TestCase
{
    /**
     * 测试列表页面
     */
    public function testIndex()
    {
        $response = $this->get('/admin/${tableName}');
        $response->assertStatus(200);
    }

    /**
     * 测试创建数据
     */
    public function testCreate()
    {
        $data = [
${this.generateTestData()}
        ];

        $response = $this->post('/admin/${tableName}/add', $data);
        $response->assertStatus(200);
        
        $this->assertDatabaseHas('${tableName}', $data);
    }

    /**
     * 测试更新数据
     */
    public function testUpdate()
    {
        $model = ${className}::factory()->create();
        
        $data = [
${this.generateTestData()}
        ];

        $response = $this->post('/admin/${tableName}/edit', array_merge(['id' => $model->id], $data));
        $response->assertStatus(200);
        
        $this->assertDatabaseHas('${tableName}', $data);
    }

    /**
     * 测试删除数据
     */
    public function testDelete()
    {
        $model = ${className}::factory()->create();
        
        $response = $this->post('/admin/${tableName}/delete', ['ids' => [$model->id]]);
        $response->assertStatus(200);
        
        $this->assertDatabaseMissing('${tableName}', ['id' => $model->id]);
    }
}`;

            return code;
        },

        // 辅助方法
        toPascalCase: function(str) {
            return str.replace(/_([a-z])/g, function(match, letter) {
                return letter.toUpperCase();
            }).replace(/^[a-z]/, function(match) {
                return match.toUpperCase();
            });
        },

        getModuleName: function() {
            return 'System'; // 可以根据需要动态获取
        },

        generateSearchConditions: function(fields) {
            var conditions = [];
            fields.forEach(function(field) {
                if (field.show_in_search) {
                    conditions.push(`        if ($request->input('${field.name}')) {
            $query->where('${field.name}', 'like', '%' . $request->input('${field.name}') . '%');
        }`);
                }
            });
            return conditions.join('\n');
        },

        generateFieldList: function(fields, indent, quoted) {
            indent = indent || '';
            var list = [];
            fields.forEach(function(field) {
                if (field.name !== 'id' && field.name !== 'created_at' && field.name !== 'updated_at') {
                    list.push(indent + (quoted ? "'" + field.name + "'," : "'" + field.name + "'"));
                }
            });
            return list.join('\n');
        },

        generateValidationRules: function(fields) {
            var rules = [];
            fields.forEach(function(field) {
                if (field.required && field.name !== 'id') {
                    rules.push(`            '${field.name}' => 'required',`);
                }
            });
            return rules.join('\n');
        },

        generateCasts: function(fields) {
            var casts = [];
            fields.forEach(function(field) {
                if (field.type === 'int' || field.type === 'tinyint') {
                    casts.push(`        '${field.name}' => 'integer',`);
                } else if (field.type === 'decimal' || field.type === 'float') {
                    casts.push(`        '${field.name}' => 'decimal:2',`);
                } else if (field.type === 'datetime' || field.type === 'timestamp') {
                    casts.push(`        '${field.name}' => 'datetime',`);
                }
            });
            return casts.join('\n');
        },

        generateModelMethods: function(fields) {
            return `    /**
     * 获取格式化的创建时间
     */
    public function getCreatedAtFormattedAttribute()
    {
        return $this->created_at ? $this->created_at->format('Y-m-d H:i:s') : '';
    }

    /**
     * 获取格式化的更新时间
     */
    public function getUpdatedAtFormattedAttribute()
    {
        return $this->updated_at ? $this->updated_at->format('Y-m-d H:i:s') : '';
    }`;
        },

        generateTableSchema: function(fields) {
            var schema = [];
            fields.forEach(function(field) {
                var line = `                \`${field.name}\` `;
                
                switch (field.type) {
                    case 'int':
                        line += 'INT(11)';
                        break;
                    case 'varchar':
                        line += `VARCHAR(${field.length || 255})`;
                        break;
                    case 'text':
                        line += 'TEXT';
                        break;
                    case 'datetime':
                        line += 'DATETIME';
                        break;
                    case 'timestamp':
                        line += 'TIMESTAMP';
                        break;
                    default:
                        line += 'VARCHAR(255)';
                }
                
                if (!field.nullable) {
                    line += ' NOT NULL';
                }
                
                if (field.auto_increment) {
                    line += ' AUTO_INCREMENT';
                }
                
                if (field.default) {
                    line += ` DEFAULT '${field.default}'`;
                }
                
                if (field.comment) {
                    line += ` COMMENT '${field.comment}'`;
                }
                
                line += ',';
                schema.push(line);
            });
            
            // 添加主键
            var primaryKeys = fields.filter(f => f.primary).map(f => f.name);
            if (primaryKeys.length > 0) {
                schema.push(`                PRIMARY KEY (\`${primaryKeys.join('`, `')}\`)`);
            }
            
            return schema.join('\n');
        },

        generateSampleData: function() {
            return `            [
                'name' => '示例数据1',
                'status' => 1,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'name' => '示例数据2',
                'status' => 1,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ]`;
        },

        generateTestData: function() {
            return `            'name' => 'Test Name',
            'status' => 1`;
        },

        // 生成Layui视图
        generateLayuiView: function(tableName, className, fields) {
            // 这里生成Layui风格的视图代码
            return `@extends('admin.layouts.app')

@section('content')
<div class="layui-card">
    <div class="layui-card-header">
        <h3>${window.tableInfo.comment || className}管理</h3>
    </div>
    <div class="layui-card-body">
        <!-- 搜索表单 -->
        <form class="layui-form" lay-filter="searchForm">
            <div class="layui-form-item">
${this.generateSearchForm(fields)}
                <div class="layui-inline">
                    <button type="submit" class="layui-btn" lay-submit lay-filter="search">搜索</button>
                    <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                </div>
            </div>
        </form>

        <!-- 工具栏 -->
        <div class="layui-btn-group">
            <button class="layui-btn" id="add-btn">添加</button>
            <button class="layui-btn layui-btn-danger" id="delete-btn">删除</button>
        </div>

        <!-- 数据表格 -->
        <table class="layui-hide" id="data-table" lay-filter="data-table"></table>
    </div>
</div>
@endsection

@section('scripts')
<script src="/static/admin/js/${tableName}.js"></script>
@endsection`;
        },

        generateSearchForm: function(fields) {
            var form = [];
            fields.forEach(function(field) {
                if (field.show_in_search) {
                    form.push(`                <div class="layui-inline">
                    <label class="layui-form-label">${field.comment || field.name}</label>
                    <div class="layui-input-inline">
                        <input type="text" name="${field.name}" placeholder="请输入${field.comment || field.name}" class="layui-input">
                    </div>
                </div>`);
                }
            });
            return form.join('\n');
        },

        // 生成Layui JavaScript
        generateLayuiJS: function(tableName, fields) {
            return `layui.use(['table', 'form', 'layer'], function() {
    var table = layui.table;
    var form = layui.form;
    var layer = layui.layer;

    // 渲染表格
    table.render({
        elem: '#data-table',
        url: '/admin/${tableName}',
        method: 'POST',
        page: true,
        cols: [[
            {type: 'checkbox', fixed: 'left'},
            {field: 'id', title: 'ID', width: 80, sort: true},
${this.generateTableColumns(fields)}
            {title: '操作', width: 150, align: 'center', toolbar: '#toolbar'}
        ]]
    });

    // 监听搜索
    form.on('submit(search)', function(data) {
        table.reload('data-table', {
            where: data.field,
            page: {curr: 1}
        });
        return false;
    });

    // 添加按钮
    $('#add-btn').on('click', function() {
        layer.open({
            type: 2,
            title: '添加',
            area: ['800px', '600px'],
            content: '/admin/${tableName}/add'
        });
    });

    // 删除按钮
    $('#delete-btn').on('click', function() {
        var checkStatus = table.checkStatus('data-table');
        var data = checkStatus.data;
        
        if (data.length === 0) {
            layer.msg('请选择要删除的数据');
            return;
        }
        
        layer.confirm('确定删除选中的数据吗？', function(index) {
            var ids = data.map(function(item) {
                return item.id;
            });
            
            $.post('/admin/${tableName}/delete', {ids: ids}, function(res) {
                if (res.code === 1) {
                    layer.msg('删除成功');
                    table.reload('data-table');
                } else {
                    layer.msg(res.msg);
                }
            });
            
            layer.close(index);
        });
    });
});`;
        },

        generateTableColumns: function(fields) {
            var columns = [];
            fields.forEach(function(field) {
                if (field.show_in_list && field.name !== 'id') {
                    columns.push(`            {field: '${field.name}', title: '${field.comment || field.name}', width: 120},`);
                }
            });
            return columns.join('\n');
        },

        // 生成CSS样式
        generateCSS: function() {
            return `/* ${window.tableInfo.name} 样式文件 */
/* 自动生成时间: ${new Date().toLocaleString()} */

.${window.tableInfo.name}-container {
    padding: 20px;
}

.${window.tableInfo.name}-form {
    max-width: 600px;
    margin: 0 auto;
}

.${window.tableInfo.name}-table {
    margin-top: 20px;
}

.${window.tableInfo.name}-actions {
    margin-bottom: 15px;
}

.${window.tableInfo.name}-search {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 6px;
    margin-bottom: 20px;
}

.${window.tableInfo.name}-card {
    border: 1px solid #e8e8e8;
    border-radius: 6px;
    padding: 20px;
    margin-bottom: 20px;
}

.${window.tableInfo.name}-status {
    display: inline-block;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
}

.${window.tableInfo.name}-status.active {
    background: #f6ffed;
    color: #52c41a;
    border: 1px solid #b7eb8f;
}

.${window.tableInfo.name}-status.inactive {
    background: #fff2f0;
    color: #ff4d4f;
    border: 1px solid #ffccc7;
}`;
        },

        // 批量生成
        batchGenerate: function() {
            var self = this;
            var selectedFiles = [];
            
            $('.file-item').each(function() {
                if ($(this).hasClass('active') || $(this).find('input[type="checkbox"]').is(':checked')) {
                    selectedFiles.push($(this).data('file'));
                }
            });
            
            if (selectedFiles.length === 0) {
                layer.msg('请选择要生成的文件', {icon: 2});
                return;
            }
            
            layer.msg('正在批量生成代码...', {icon: 16, time: 0});
            
            var results = {};
            selectedFiles.forEach(function(fileType) {
                results[fileType] = self.generateCode(fileType, 'basic');
            });
            
            layer.closeAll('loading');
            
            // 显示生成结果
            self.showBatchResults(results);
        },

        // 显示批量生成结果
        showBatchResults: function(results) {
            var content = '<div class="batch-results">';
            
            Object.keys(results).forEach(function(fileType) {
                content += `<div class="result-item">
                    <h4>${fileType}</h4>
                    <div class="result-actions">
                        <button class="layui-btn layui-btn-xs" onclick="copyCode('${fileType}')">复制</button>
                        <button class="layui-btn layui-btn-xs layui-btn-primary" onclick="downloadFile('${fileType}')">下载</button>
                    </div>
                </div>`;
            });
            
            content += '</div>';
            
            layer.open({
                type: 1,
                title: '批量生成结果',
                content: content,
                area: ['800px', '600px'],
                btn: ['关闭'],
                success: function() {
                    // 保存结果到全局变量
                    window.batchResults = results;
                }
            });
        },

        // 插入代码片段
        insertSnippet: function(snippet) {
            // 实现代码片段插入功能
            console.log('插入代码片段:', snippet);
        }
    };

    // 暴露到全局
    window.CurdAdvancedGenerator = AdvancedGenerator;

    // 自动初始化
    $(document).ready(function() {
        AdvancedGenerator.init();
    });
});
