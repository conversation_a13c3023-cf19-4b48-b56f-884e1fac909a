<?php
/**
 * Simple test page for EasyAdmin8
 */

echo "<h1>EasyAdmin8 项目状态</h1>";
echo "<p>项目路径: " . dirname(__DIR__) . "</p>";
echo "<p>PHP 版本: " . PHP_VERSION . "</p>";
echo "<p>当前时间: " . date('Y-m-d H:i:s') . "</p>";

// 检查关键文件
$files = [
    'vendor/autoload.php' => '自动加载器',
    '.env' => '环境配置',
    'config/app.php' => '应用配置',
    'start.php' => '启动文件'
];

echo "<h2>文件检查</h2>";
foreach ($files as $file => $desc) {
    $path = dirname(__DIR__) . '/' . $file;
    $status = file_exists($path) ? '✅ 存在' : '❌ 缺失';
    echo "<p>{$desc} ({$file}): {$status}</p>";
}

echo "<h2>建议</h2>";
echo "<p>请使用以下命令启动 Webman 服务器：</p>";
echo "<pre>cd " . dirname(__DIR__) . "\nphp windows.php</pre>";
echo "<p>或者在 Windows 上运行：</p>";
echo "<pre>windows.bat</pre>";

echo "<h2>访问地址</h2>";
echo "<p>安装页面: <a href='/install'>http://127.0.0.1:8787/install</a></p>";
echo "<p>管理后台: <a href='/admin'>http://127.0.0.1:8787/admin</a></p>";
