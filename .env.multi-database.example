# ===========================================
# EasyAdmin8-webman 多数据库配置示例
# ===========================================

# 应用基础配置
APP_DEBUG=true
APP_PORT=8787

# 后台系统日志开关
APP_ADMIN_SYSTEM_LOG=true

# ===========================================
# 主数据库配置（默认连接）
# ===========================================
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=easyadmin8
DB_USERNAME=root
DB_PASSWORD=root
DB_PREFIX=ea8_
DB_CHARSET=utf8mb4
DB_COLLATION=utf8mb4_general_ci
DB_TIMEOUT=3
DB_STRICT=true
DB_ENGINE=null
DB_SOCKET=

# ===========================================
# 读写分离配置
# ===========================================
# 是否启用读写分离
DB_READ_WRITE_SEPARATION=false
# 写库连接
DB_WRITE_CONNECTION=mysql
# 读库连接（多个用逗号分隔）
DB_READ_CONNECTIONS=mysql_read
# 是否启用粘性会话
DB_STICKY=false
# 读库权重
DB_READ_WEIGHT=1

# 读库配置（从库）
DB_READ_HOST=127.0.0.1
DB_READ_PORT=3306
DB_READ_DATABASE=easyadmin8
DB_READ_USERNAME=root
DB_READ_PASSWORD=root
DB_READ_SOCKET=

# ===========================================
# 第二数据库配置
# ===========================================
DB_SECOND_HOST=127.0.0.1
DB_SECOND_PORT=3306
DB_SECOND_DATABASE=easyadmin8_second
DB_SECOND_USERNAME=root
DB_SECOND_PASSWORD=root
DB_SECOND_PREFIX=ea8_
DB_SECOND_SOCKET=

# ===========================================
# 日志数据库配置
# ===========================================
DB_LOG_HOST=127.0.0.1
DB_LOG_PORT=3306
DB_LOG_DATABASE=easyadmin8_log
DB_LOG_USERNAME=root
DB_LOG_PASSWORD=root
DB_LOG_PREFIX=log_
DB_LOG_SOCKET=

# ===========================================
# 缓存数据库配置
# ===========================================
DB_CACHE_HOST=127.0.0.1
DB_CACHE_PORT=3306
DB_CACHE_DATABASE=easyadmin8_cache
DB_CACHE_USERNAME=root
DB_CACHE_PASSWORD=root
DB_CACHE_PREFIX=cache_
DB_CACHE_SOCKET=

# ===========================================
# PostgreSQL 数据库配置
# ===========================================
PGSQL_HOST=127.0.0.1
PGSQL_PORT=5432
PGSQL_DATABASE=easyadmin8
PGSQL_USERNAME=postgres
PGSQL_PASSWORD=root
PGSQL_PREFIX=ea8_
PGSQL_CHARSET=utf8
PGSQL_SCHEMA=public
PGSQL_SSLMODE=prefer

# ===========================================
# SQLite 数据库配置
# ===========================================
SQLITE_DATABASE=database/database.sqlite
SQLITE_PREFIX=
SQLITE_FOREIGN_KEYS=true

# ===========================================
# SQL Server 数据库配置
# ===========================================
SQLSRV_HOST=127.0.0.1
SQLSRV_PORT=1433
SQLSRV_DATABASE=easyadmin8
SQLSRV_USERNAME=sa
SQLSRV_PASSWORD=root
SQLSRV_PREFIX=ea8_
SQLSRV_CHARSET=utf8

# ===========================================
# 数据库连接池配置
# ===========================================
DB_POOL_ENABLE=false
DB_POOL_MAX_CONNECTIONS=10
DB_POOL_MIN_CONNECTIONS=1
DB_POOL_MAX_IDLE_TIME=60
DB_POOL_MAX_WAIT_TIME=3
DB_POOL_RETRY_INTERVAL=100

# ===========================================
# 数据库迁移配置
# ===========================================
DB_MIGRATIONS_TABLE=migrations

# ===========================================
# Redis 配置
# ===========================================
REDIS_CLIENT=phpredis
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379
REDIS_DB=0
REDIS_CACHE_DB=1
REDIS_SESSION_DB=2
REDIS_READ_TIMEOUT=60
REDIS_PREFIX=easyadmin8_database_
REDIS_CLUSTER=redis
REDIS_URL=

# ===========================================
# EasyAdmin 系统配置
# ===========================================
# 是否开启后台域名绑定
EASYADMIN.ADMIN_DOMAIN_STATUS=false
# 后台地址域名绑定
EASYADMIN.ADMIN_DOMAIN=admin.xxx.com
# 后台登录验证码开关
EASYADMIN.CAPTCHA=false
# 是否为演示环境
EASYADMIN.IS_DEMO=false
# CDN配置项组
EASYADMIN.CDN=
EASYADMIN.EXAMPLE=true
# 是否开启CSRF过滤
EASYADMIN.IS_CSRF=false
# 静态文件路径前缀
EASYADMIN.STATIC_PATH=/static
# OSS静态文件路径前缀
EASYADMIN.OSS_STATIC_PREFIX=static_easyadmin