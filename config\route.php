<?php

// 引入辅助函数和模拟类
if (file_exists(__DIR__ . '/../bootstrap/helpers.php')) {
    require_once __DIR__ . '/../bootstrap/helpers.php';
}


/**
 * This file is part of webman.
 *
 * Licensed under The MIT License
 * For full copyright and license information, please see the MIT-LICENSE.txt
 * Redistributions of files must retain the above copyright notice.
 *
 * <AUTHOR>
 * @copyright walkor<<EMAIL>>
 * @link      http://www.workerman.net/
 * @license   http://www.opensource.org/licenses/mit-license.php MIT License
 */

use Webman\Route;

// API文档管理路由 (使用简化版，避免依赖问题)
Route::get('/admin/system/apidoc', [app\admin\controller\system\ApiDocControllerSimple::class, 'index']);
Route::get('/admin/system/apidoc/index', [app\admin\controller\system\ApiDocControllerSimple::class, 'index']);
Route::get('/admin/system/apidoc/view', [app\admin\controller\system\ApiDocControllerSimple::class, 'view']);
Route::post('/admin/system/apidoc/generate', [app\admin\controller\system\ApiDocControllerSimple::class, 'generate']);
Route::get('/admin/system/apidoc/export', [app\admin\controller\system\ApiDocControllerSimple::class, 'export']);
Route::post('/admin/system/apidoc/test', [app\admin\controller\system\ApiDocControllerSimple::class, 'test']);

// CURD 测试路由 (临时，无需登录)
Route::any('/curdtest/tables', [app\admin\controller\system\CurdTestController::class, 'getTables']);
Route::any('/curdtest/analyze', [app\admin\controller\system\CurdTestController::class, 'analyzeTable']);
Route::get('/admin/system/apidoc/list', [app\admin\controller\system\ApiDocControllerSimple::class, 'getApiDocList']);
Route::get('/admin/system/apidoc/endpoints', [app\admin\controller\system\ApiDocControllerSimple::class, 'getApiEndpoints']);
Route::get('/admin/system/apidoc/tables', [app\admin\controller\system\ApiDocControllerSimple::class, 'getTableList']);

// API文档管理路由 (增强版 - 独立路径)
Route::get('/admin/system/apidoc-enhanced', [app\admin\controller\system\ApiDocControllerEnhanced::class, 'index']);
Route::get('/admin/system/apidoc-enhanced/dashboard', [app\admin\controller\system\ApiDocControllerEnhanced::class, 'dashboard']);
Route::get('/admin/system/apidoc-enhanced/analytics', [app\admin\controller\system\ApiDocControllerEnhanced::class, 'analytics']);
Route::get('/admin/system/apidoc-enhanced/preview', [app\admin\controller\system\ApiDocControllerEnhanced::class, 'preview']);
Route::get('/admin/system/apidoc-enhanced/search', [app\admin\controller\system\ApiDocControllerEnhanced::class, 'search']);
Route::get('/admin/system/apidoc-enhanced/compare', [app\admin\controller\system\ApiDocControllerEnhanced::class, 'compare']);
Route::post('/admin/system/apidoc-enhanced/batch-generate', [app\admin\controller\system\ApiDocControllerEnhanced::class, 'batchGenerate']);
Route::get('/admin/system/apidoc-enhanced/exportPostman', [app\admin\controller\system\ApiDocControllerEnhanced::class, 'exportPostman']);
Route::get('/admin/system/apidoc-enhanced/exportSwagger', [app\admin\controller\system\ApiDocControllerEnhanced::class, 'exportSwagger']);

// API文档管理路由 (简化版 - 备用)
Route::get('/admin/system/apidoc-simple', [app\admin\controller\system\ApiDocControllerSimple::class, 'index']);
Route::get('/admin/system/apidoc-simple/view', [app\admin\controller\system\ApiDocControllerSimple::class, 'view']);
Route::post('/admin/system/apidoc-simple/generate', [app\admin\controller\system\ApiDocControllerSimple::class, 'generate']);
Route::get('/admin/system/apidoc-simple/export', [app\admin\controller\system\ApiDocControllerSimple::class, 'export']);
Route::post('/admin/system/apidoc-simple/test', [app\admin\controller\system\ApiDocControllerSimple::class, 'test']);
Route::get('/admin/system/apidoc-simple/list', [app\admin\controller\system\ApiDocControllerSimple::class, 'getApiDocList']);
Route::get('/admin/system/apidoc-simple/endpoints', [app\admin\controller\system\ApiDocControllerSimple::class, 'getApiEndpoints']);
Route::get('/admin/system/apidoc-simple/tables', [app\admin\controller\system\ApiDocControllerSimple::class, 'getTableList']);

// API版本管理路由
Route::get('/admin/system/api-version', [app\admin\controller\system\ApiVersionController::class, 'index']);
Route::get('/admin/system/api-version/list', [app\admin\controller\system\ApiVersionController::class, 'getVersionList']);
Route::get('/admin/system/api-version/compare', [app\admin\controller\system\ApiVersionController::class, 'compareVersions']);
Route::post('/admin/system/api-version/create', [app\admin\controller\system\ApiVersionController::class, 'createVersion']);
Route::post('/admin/system/api-version/release', [app\admin\controller\system\ApiVersionController::class, 'releaseVersion']);
Route::post('/admin/system/api-version/rollback', [app\admin\controller\system\ApiVersionController::class, 'rollbackVersion']);
Route::get('/admin/system/api-version/changelog', [app\admin\controller\system\ApiVersionController::class, 'getChangeLog']);

// API测试工具路由
Route::get('/admin/system/api-test', [app\admin\controller\system\ApiTestController::class, 'index']);
Route::post('/admin/system/api-test/single', [app\admin\controller\system\ApiTestController::class, 'testSingleApi']);
Route::post('/admin/system/api-test/batch', [app\admin\controller\system\ApiTestController::class, 'batchTest']);
Route::post('/admin/system/api-test/performance', [app\admin\controller\system\ApiTestController::class, 'performanceTest']);

// 缓存管理路由
Route::get('/admin/system/cache', [app\admin\controller\system\CacheController::class, 'index']);
Route::post('/admin/system/cache/clear', [app\admin\controller\system\CacheController::class, 'clearCache']);
Route::post('/admin/system/cache/warmup', [app\admin\controller\system\CacheController::class, 'warmupCache']);
Route::get('/admin/system/cache/details', [app\admin\controller\system\CacheController::class, 'getCacheDetails']);
Route::get('/admin/system/cache/suggestions', [app\admin\controller\system\CacheController::class, 'getOptimizationSuggestions']);

// 搜索功能路由
Route::get('/admin/system/search', [app\admin\controller\system\SearchController::class, 'index']);
Route::get('/admin/system/search/query', [app\admin\controller\system\SearchController::class, 'search']);
Route::get('/admin/system/search/suggest', [app\admin\controller\system\SearchController::class, 'suggest']);
Route::get('/admin/system/search/analytics', [app\admin\controller\system\SearchController::class, 'analytics']);

// 性能监控路由
Route::get('/admin/system/monitor', [app\admin\controller\system\MonitorController::class, 'index']);
Route::get('/admin/system/monitor/metrics', [app\admin\controller\system\MonitorController::class, 'getRealTimeMetrics']);
Route::get('/admin/system/monitor/history', [app\admin\controller\system\MonitorController::class, 'getHistoricalData']);
Route::post('/admin/system/monitor/alert', [app\admin\controller\system\MonitorController::class, 'createAlert']);
Route::get('/admin/system/monitor/health', [app\admin\controller\system\MonitorController::class, 'healthCheck']);

// CURD 生成器 V2 路由
Route::any('/admin/system/curdgeneratev2', [app\admin\controller\system\CurdGenerateV2Controller::class, 'index']);

// 数据库连接测试路由
Route::any('/admin/system/databasetest', [app\admin\controller\system\DatabaseTestController::class, 'index']);

Route::group('/common', function () {
    Route::any('/[{path:.+}]', function ($request) {
        return view('404');
    });
});