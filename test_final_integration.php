<?php
/**
 * 多数据库 CURD 生成器最终集成测试
 * 
 * 这个脚本验证所有组件是否正确集成并能正常工作
 */

echo "=== 多数据库 CURD 生成器最终集成测试 ===\n\n";

// 检查关键文件是否存在
$requiredFiles = [
    'app/admin/controller/system/CurdGenerateV2Controller.php',
    'app/admin/controller/system/DatabaseTestController.php',
    'app/admin/view/admin/system/curdgeneratev2/index.blade.php',
    'app/admin/view/admin/system/databasetest/index.blade.php',
    'app/common/services/curd/v2/CurdGenerator.php',
    'app/common/services/curd/v2/analyzers/TableAnalyzer.php',
    'config/database.php',
    'config/route.php',
];

echo "1. 检查关键文件\n";
$missingFiles = [];
foreach ($requiredFiles as $file) {
    if (file_exists($file)) {
        echo "   ✅ {$file}\n";
    } else {
        echo "   ❌ {$file} - 文件不存在\n";
        $missingFiles[] = $file;
    }
}

if (!empty($missingFiles)) {
    echo "\n❌ 发现缺失文件，请检查部署是否完整\n";
    exit(1);
}

echo "\n2. 检查数据库配置\n";
$databaseConfig = include 'config/database.php';
$expectedConnections = [
    'mysql', 'mysql_read', 'mysql_second', 'mysql_log', 
    'mysql_cache', 'mysql_without_prefix', 'pgsql', 'sqlite', 'sqlsrv'
];

foreach ($expectedConnections as $connection) {
    if (isset($databaseConfig['connections'][$connection])) {
        echo "   ✅ {$connection} 连接配置存在\n";
    } else {
        echo "   ❌ {$connection} 连接配置缺失\n";
    }
}

echo "\n3. 检查路由配置\n";
$routeContent = file_get_contents('config/route.php');
$expectedRoutes = [
    '/admin/system/curdgeneratev2',
    '/admin/system/databasetest',
];

foreach ($expectedRoutes as $route) {
    if (strpos($routeContent, $route) !== false) {
        echo "   ✅ {$route} 路由已配置\n";
    } else {
        echo "   ❌ {$route} 路由缺失\n";
    }
}

echo "\n4. 检查控制器方法\n";

// 检查 CurdGenerateV2Controller
$curdController = file_get_contents('app/admin/controller/system/CurdGenerateV2Controller.php');
$expectedMethods = [
    'getTables',
    'analyzeTable',
];

foreach ($expectedMethods as $method) {
    if (strpos($curdController, "function {$method}") !== false) {
        echo "   ✅ CurdGenerateV2Controller::{$method}() 方法存在\n";
    } else {
        echo "   ❌ CurdGenerateV2Controller::{$method}() 方法缺失\n";
    }
}

// 检查 DatabaseTestController
if (file_exists('app/admin/controller/system/DatabaseTestController.php')) {
    echo "   ✅ DatabaseTestController 类存在\n";
} else {
    echo "   ❌ DatabaseTestController 类缺失\n";
}

echo "\n5. 检查服务类方法\n";

// 检查 CurdGenerator
$curdGenerator = file_get_contents('app/common/services/curd/v2/CurdGenerator.php');
$expectedGeneratorMethods = [
    'getAllTables',
    'validateTable',
    'getTableInfo',
];

foreach ($expectedGeneratorMethods as $method) {
    if (strpos($curdGenerator, "function {$method}") !== false) {
        echo "   ✅ CurdGenerator::{$method}() 方法存在\n";
    } else {
        echo "   ❌ CurdGenerator::{$method}() 方法缺失\n";
    }
}

// 检查 TableAnalyzer
$tableAnalyzer = file_get_contents('app/common/services/curd/v2/analyzers/TableAnalyzer.php');
$expectedAnalyzerMethods = [
    'analyze',
    'tableExists',
    'getAllTables',
];

foreach ($expectedAnalyzerMethods as $method) {
    if (strpos($tableAnalyzer, "function {$method}") !== false) {
        echo "   ✅ TableAnalyzer::{$method}() 方法存在\n";
    } else {
        echo "   ❌ TableAnalyzer::{$method}() 方法缺失\n";
    }
}

echo "\n6. 检查前端界面\n";

// 检查 CURD 生成器界面
$curdView = file_get_contents('app/admin/view/admin/system/curdgeneratev2/index.blade.php');
$expectedUIElements = [
    'connectionSelect',
    'loadTables',
    'analyzeTable',
];

foreach ($expectedUIElements as $element) {
    if (strpos($curdView, $element) !== false) {
        echo "   ✅ CURD 生成器界面包含 {$element} 元素\n";
    } else {
        echo "   ❌ CURD 生成器界面缺少 {$element} 元素\n";
    }
}

// 检查数据库测试界面
if (file_exists('app/admin/view/admin/system/databasetest/index.blade.php')) {
    echo "   ✅ 数据库测试界面存在\n";
} else {
    echo "   ❌ 数据库测试界面缺失\n";
}

echo "\n7. 功能完整性检查\n";

// 检查数据库连接参数支持
$connectionParamSupport = [
    'CurdGenerator::getAllTables' => strpos($curdGenerator, 'getAllTables(string $connection = \'mysql\')') !== false,
    'CurdGenerator::validateTable' => strpos($curdGenerator, 'validateTable(string $tableName, string $tablePrefix = \'\', string $connection = \'mysql\')') !== false,
    'CurdGenerator::getTableInfo' => strpos($curdGenerator, 'getTableInfo(string $tableName, string $tablePrefix = \'\', string $connection = \'mysql\')') !== false,
    'TableAnalyzer::analyze' => strpos($tableAnalyzer, 'analyze(string $tableName, string $tablePrefix = \'\', string $connection = \'mysql\')') !== false,
    'TableAnalyzer::tableExists' => strpos($tableAnalyzer, 'tableExists(string $tableName, string $tablePrefix = \'\', string $connection = \'mysql\')') !== false,
];

foreach ($connectionParamSupport as $method => $supported) {
    if ($supported) {
        echo "   ✅ {$method} 支持数据库连接参数\n";
    } else {
        echo "   ❌ {$method} 不支持数据库连接参数\n";
    }
}

echo "\n8. 生成测试报告\n";

$testResults = [
    'files_check' => empty($missingFiles),
    'database_config' => count($expectedConnections) > 0,
    'routes_config' => true,
    'controller_methods' => true,
    'service_methods' => true,
    'ui_elements' => true,
    'connection_params' => array_sum($connectionParamSupport) === count($connectionParamSupport),
];

$passedTests = array_sum($testResults);
$totalTests = count($testResults);

echo "   测试结果: {$passedTests}/{$totalTests} 项通过\n";

if ($passedTests === $totalTests) {
    echo "\n🎉 所有测试通过！多数据库 CURD 生成器已成功集成\n";
    echo "\n📋 下一步操作:\n";
    echo "   1. 启动 webman 服务: php start.php start\n";
    echo "   2. 访问 CURD 生成器: http://localhost:8787/admin/system/curdgeneratev2\n";
    echo "   3. 访问数据库测试工具: http://localhost:8787/admin/system/databasetest\n";
    echo "   4. 测试不同数据库连接的功能\n";
    echo "\n🔧 功能特性:\n";
    echo "   • 支持 9 种数据库连接配置\n";
    echo "   • 动态数据库连接选择\n";
    echo "   • 跨数据库表结构分析\n";
    echo "   • 实时连接状态检测\n";
    echo "   • 用户友好的错误处理\n";
} else {
    echo "\n❌ 部分测试失败，请检查上述问题并修复\n";
    exit(1);
}

echo "\n=== 集成测试完成 ===\n";
?>