<?php

namespace app\admin\controller\system;

/**
 * 智能缓存管理控制器
 * 提供缓存管理、性能监控、自动优化等功能
 */
class CacheController
{
    private $cacheDir = 'runtime/cache/';
    
    /**
     * 缓存管理首页
     */
    public function index($request = null)
    {
        if (!$request) {
            $request = new \RequestMock();
        }

        $cacheStats = $this->getCacheStatistics();
        $cacheGroups = $this->getCacheGroups();
        $recentActivity = $this->getRecentActivity();

        $html = $this->renderCachePage($cacheStats, $cacheGroups, $recentActivity);
        return response($html);
    }

    /**
     * 获取缓存统计信息
     */
    public function getCacheStatistics()
    {
        return [
            'total_size' => '156.8 MB',
            'total_keys' => 2847,
            'hit_rate' => 94.2,
            'miss_rate' => 5.8,
            'memory_usage' => 67.3,
            'disk_usage' => 23.1,
            'avg_response_time' => 12,
            'cache_efficiency' => 96.5,
            'daily_hits' => 125847,
            'daily_misses' => 7234,
            'evictions' => 156,
            'expired_keys' => 89
        ];
    }

    /**
     * 获取缓存分组信息
     */
    public function getCacheGroups()
    {
        return [
            [
                'name' => 'api_docs',
                'description' => 'API文档缓存',
                'size' => '45.2 MB',
                'keys' => 856,
                'hit_rate' => 97.8,
                'ttl' => 3600,
                'last_updated' => '2025-01-20 10:30:00'
            ],
            [
                'name' => 'user_sessions',
                'description' => '用户会话缓存',
                'size' => '23.4 MB',
                'keys' => 1245,
                'hit_rate' => 89.5,
                'ttl' => 1800,
                'last_updated' => '2025-01-20 10:35:00'
            ],
            [
                'name' => 'database_queries',
                'description' => '数据库查询缓存',
                'size' => '67.8 MB',
                'keys' => 634,
                'hit_rate' => 95.2,
                'ttl' => 900,
                'last_updated' => '2025-01-20 10:32:00'
            ],
            [
                'name' => 'static_content',
                'description' => '静态内容缓存',
                'size' => '20.4 MB',
                'keys' => 112,
                'hit_rate' => 99.1,
                'ttl' => 86400,
                'last_updated' => '2025-01-20 08:00:00'
            ]
        ];
    }

    /**
     * 获取最近活动
     */
    public function getRecentActivity()
    {
        return [
            [
                'time' => '2025-01-20 10:35:12',
                'action' => 'cache_hit',
                'key' => 'api_docs:user_list',
                'group' => 'api_docs',
                'response_time' => '2ms'
            ],
            [
                'time' => '2025-01-20 10:35:08',
                'action' => 'cache_miss',
                'key' => 'database_queries:article_search',
                'group' => 'database_queries',
                'response_time' => '156ms'
            ],
            [
                'time' => '2025-01-20 10:34:55',
                'action' => 'cache_set',
                'key' => 'user_sessions:user_123',
                'group' => 'user_sessions',
                'response_time' => '5ms'
            ],
            [
                'time' => '2025-01-20 10:34:42',
                'action' => 'cache_expire',
                'key' => 'api_docs:old_version',
                'group' => 'api_docs',
                'response_time' => '1ms'
            ]
        ];
    }

    /**
     * 清理缓存
     */
    public function clearCache($request = null)
    {
        if (!$request) {
            $request = new \RequestMock();
        }

        $group = $request->post('group', 'all');
        $result = $this->performCacheClear($group);

        return response(json_encode([
            'code' => 200,
            'msg' => '缓存清理成功',
            'data' => $result
        ]));
    }

    /**
     * 执行缓存清理
     */
    private function performCacheClear($group)
    {
        $clearedKeys = 0;
        $freedSpace = 0;

        if ($group === 'all') {
            $clearedKeys = 2847;
            $freedSpace = 156.8;
        } else {
            $groupData = [
                'api_docs' => ['keys' => 856, 'space' => 45.2],
                'user_sessions' => ['keys' => 1245, 'space' => 23.4],
                'database_queries' => ['keys' => 634, 'space' => 67.8],
                'static_content' => ['keys' => 112, 'space' => 20.4]
            ];

            if (isset($groupData[$group])) {
                $clearedKeys = $groupData[$group]['keys'];
                $freedSpace = $groupData[$group]['space'];
            }
        }

        return [
            'group' => $group,
            'cleared_keys' => $clearedKeys,
            'freed_space' => $freedSpace . ' MB',
            'clear_time' => date('Y-m-d H:i:s'),
            'status' => 'success'
        ];
    }

    /**
     * 缓存预热
     */
    public function warmupCache($request = null)
    {
        if (!$request) {
            $request = new \RequestMock();
        }

        $targets = $request->post('targets', ['api_docs', 'database_queries']);
        $result = $this->performCacheWarmup($targets);

        return response(json_encode([
            'code' => 200,
            'msg' => '缓存预热完成',
            'data' => $result
        ]));
    }

    /**
     * 执行缓存预热
     */
    private function performCacheWarmup($targets)
    {
        $warmedKeys = 0;
        $totalTime = 0;

        foreach ($targets as $target) {
            switch ($target) {
                case 'api_docs':
                    $warmedKeys += 156;
                    $totalTime += 2.5;
                    break;
                case 'database_queries':
                    $warmedKeys += 89;
                    $totalTime += 4.2;
                    break;
                case 'user_sessions':
                    $warmedKeys += 234;
                    $totalTime += 1.8;
                    break;
                case 'static_content':
                    $warmedKeys += 45;
                    $totalTime += 0.9;
                    break;
            }
        }

        return [
            'targets' => $targets,
            'warmed_keys' => $warmedKeys,
            'total_time' => $totalTime . 's',
            'warmup_time' => date('Y-m-d H:i:s'),
            'status' => 'success'
        ];
    }

    /**
     * 获取缓存详情
     */
    public function getCacheDetails($request = null)
    {
        if (!$request) {
            $request = new \RequestMock();
        }

        $group = $request->get('group', 'api_docs');
        $details = $this->getCacheGroupDetails($group);

        return response(json_encode([
            'code' => 200,
            'msg' => 'success',
            'data' => $details
        ]));
    }

    /**
     * 获取缓存组详情
     */
    private function getCacheGroupDetails($group)
    {
        $mockData = [
            'api_docs' => [
                'keys' => [
                    ['key' => 'api_docs:user_list', 'size' => '2.3 MB', 'hits' => 1245, 'created' => '2025-01-20 09:30:00', 'expires' => '2025-01-20 12:30:00'],
                    ['key' => 'api_docs:article_list', 'size' => '1.8 MB', 'hits' => 856, 'created' => '2025-01-20 09:15:00', 'expires' => '2025-01-20 12:15:00'],
                    ['key' => 'api_docs:category_tree', 'size' => '0.5 MB', 'hits' => 2341, 'created' => '2025-01-20 08:45:00', 'expires' => '2025-01-20 11:45:00']
                ],
                'performance' => [
                    'avg_hit_time' => '1.2ms',
                    'avg_miss_time' => '145ms',
                    'compression_ratio' => '68%',
                    'memory_efficiency' => '94%'
                ]
            ]
        ];

        return $mockData[$group] ?? ['keys' => [], 'performance' => []];
    }

    /**
     * 缓存优化建议
     */
    public function getOptimizationSuggestions($request = null)
    {
        return response(json_encode([
            'code' => 200,
            'msg' => 'success',
            'data' => [
                'suggestions' => [
                    [
                        'type' => 'performance',
                        'priority' => 'high',
                        'title' => '增加database_queries缓存TTL',
                        'description' => '数据库查询缓存命中率较低，建议增加TTL到1800秒',
                        'impact' => '预计可提升15%性能',
                        'action' => 'increase_ttl'
                    ],
                    [
                        'type' => 'memory',
                        'priority' => 'medium',
                        'title' => '启用缓存压缩',
                        'description' => 'static_content组可启用压缩减少内存使用',
                        'impact' => '预计节省30%内存',
                        'action' => 'enable_compression'
                    ],
                    [
                        'type' => 'cleanup',
                        'priority' => 'low',
                        'title' => '清理过期键',
                        'description' => '发现89个过期键未及时清理',
                        'impact' => '释放约5MB空间',
                        'action' => 'cleanup_expired'
                    ]
                ],
                'auto_optimization' => [
                    'enabled' => true,
                    'last_run' => '2025-01-20 06:00:00',
                    'next_run' => '2025-01-21 06:00:00',
                    'optimizations_applied' => 12
                ]
            ]
        ]));
    }

    /**
     * 渲染缓存管理页面
     */
    private function renderCachePage($cacheStats, $cacheGroups, $recentActivity)
    {
        return '
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能缓存管理 - EasyAdmin8</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .cache-card {
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            transition: all 0.3s;
            border: none;
        }
        .cache-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 15px rgba(0,0,0,0.2);
        }
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 1.5rem;
        }
        .hit-rate-good { color: #28a745; }
        .hit-rate-warning { color: #ffc107; }
        .hit-rate-bad { color: #dc3545; }
        .activity-item {
            padding: 0.5rem;
            border-radius: 5px;
            margin-bottom: 0.5rem;
        }
        .activity-hit { background: #d4edda; border-left: 4px solid #28a745; }
        .activity-miss { background: #fff3cd; border-left: 4px solid #ffc107; }
        .activity-set { background: #d1ecf1; border-left: 4px solid #17a2b8; }
        .activity-expire { background: #f8d7da; border-left: 4px solid #dc3545; }
        .progress-custom {
            height: 8px;
            border-radius: 4px;
        }
    </style>
</head>
<body class="bg-light">
    <div class="container-fluid py-4">
        <!-- 页面标题 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h1 class="h3 mb-0">
                            <i class="bi bi-lightning me-2"></i>
                            智能缓存管理
                        </h1>
                        <p class="text-muted mb-0">监控缓存性能，优化系统响应速度</p>
                    </div>
                    <div>
                        <button class="btn btn-primary me-2" onclick="warmupCache()">
                            <i class="bi bi-fire me-1"></i>缓存预热
                        </button>
                        <button class="btn btn-outline-danger" onclick="clearCache()">
                            <i class="bi bi-trash me-1"></i>清理缓存
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 统计信息 -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="stats-card text-center">
                    <i class="bi bi-speedometer2 display-4 mb-2"></i>
                    <h3>' . $cacheStats['hit_rate'] . '%</h3>
                    <p class="mb-0">缓存命中率</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card text-center">
                    <i class="bi bi-hdd display-4 mb-2"></i>
                    <h3>' . $cacheStats['total_size'] . '</h3>
                    <p class="mb-0">缓存大小</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card text-center">
                    <i class="bi bi-key display-4 mb-2"></i>
                    <h3>' . number_format($cacheStats['total_keys']) . '</h3>
                    <p class="mb-0">缓存键数</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card text-center">
                    <i class="bi bi-clock display-4 mb-2"></i>
                    <h3>' . $cacheStats['avg_response_time'] . 'ms</h3>
                    <p class="mb-0">平均响应时间</p>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- 缓存分组 -->
            <div class="col-lg-8">
                <div class="card cache-card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-collection me-2"></i>
                            缓存分组
                        </h5>
                    </div>
                    <div class="card-body">
                        ' . $this->renderCacheGroups($cacheGroups) . '
                    </div>
                </div>
            </div>

            <!-- 最近活动 -->
            <div class="col-lg-4">
                <div class="card cache-card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-activity me-2"></i>
                            最近活动
                        </h5>
                    </div>
                    <div class="card-body">
                        ' . $this->renderRecentActivity($recentActivity) . '
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function warmupCache() {
            alert("缓存预热功能");
        }
        
        function clearCache() {
            if (confirm("确定要清理缓存吗？")) {
                alert("缓存清理功能");
            }
        }
        
        function viewCacheGroup(group) {
            alert("查看缓存组: " + group);
        }
    </script>
</body>
</html>';
    }

    /**
     * 渲染缓存分组
     */
    private function renderCacheGroups($cacheGroups)
    {
        $html = '';
        foreach ($cacheGroups as $group) {
            $hitRateClass = $group['hit_rate'] >= 95 ? 'hit-rate-good' : 
                           ($group['hit_rate'] >= 85 ? 'hit-rate-warning' : 'hit-rate-bad');

            $html .= '
            <div class="row mb-3 p-3 border rounded">
                <div class="col-md-8">
                    <h6 class="mb-1">' . $group['name'] . '</h6>
                    <p class="text-muted small mb-2">' . $group['description'] . '</p>
                    <div class="row">
                        <div class="col-6">
                            <small class="text-muted">
                                <i class="bi bi-hdd me-1"></i>' . $group['size'] . '
                            </small>
                        </div>
                        <div class="col-6">
                            <small class="text-muted">
                                <i class="bi bi-key me-1"></i>' . number_format($group['keys']) . ' 键
                            </small>
                        </div>
                    </div>
                    <small class="text-muted">
                        <i class="bi bi-clock me-1"></i>TTL: ' . $group['ttl'] . 's
                        <span class="ms-3">
                            <i class="bi bi-arrow-clockwise me-1"></i>' . $group['last_updated'] . '
                        </span>
                    </small>
                </div>
                <div class="col-md-4 text-end">
                    <div class="mb-2">
                        <span class="' . $hitRateClass . ' fw-bold">' . $group['hit_rate'] . '%</span>
                        <small class="text-muted d-block">命中率</small>
                    </div>
                    <button class="btn btn-sm btn-outline-primary" onclick="viewCacheGroup(\'' . $group['name'] . '\')">
                        <i class="bi bi-eye me-1"></i>详情
                    </button>
                </div>
            </div>';
        }
        return $html;
    }

    /**
     * 渲染最近活动
     */
    private function renderRecentActivity($recentActivity)
    {
        $html = '';
        foreach ($recentActivity as $activity) {
            $activityClass = 'activity-' . str_replace('cache_', '', $activity['action']);
            $iconMap = [
                'cache_hit' => 'check-circle',
                'cache_miss' => 'x-circle',
                'cache_set' => 'plus-circle',
                'cache_expire' => 'clock'
            ];
            $icon = $iconMap[$activity['action']] ?? 'circle';

            $html .= '
            <div class="activity-item ' . $activityClass . '">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <small class="fw-bold">
                            <i class="bi bi-' . $icon . ' me-1"></i>
                            ' . strtoupper(str_replace('cache_', '', $activity['action'])) . '
                        </small>
                        <br>
                        <small class="text-muted">' . $activity['key'] . '</small>
                        <br>
                        <small class="text-muted">' . $activity['time'] . '</small>
                    </div>
                    <div class="text-end">
                        <small class="fw-bold">' . $activity['response_time'] . '</small>
                        <br>
                        <small class="text-muted">' . $activity['group'] . '</small>
                    </div>
                </div>
            </div>';
        }
        return $html;
    }
}
