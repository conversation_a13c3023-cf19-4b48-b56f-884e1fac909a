<?php

namespace app\common\model;

use support\Model;

/**
 * API文档模型
 * 用于管理API文档的数据存储
 */
class ApiDoc extends Model
{
    /**
     * 表名
     */
    protected $table = 'api_docs';

    /**
     * 主键
     */
    protected $primaryKey = 'id';

    /**
     * 可批量赋值的属性
     */
    protected $fillable = [
        'table_name',
        'display_name',
        'description',
        'version',
        'base_url',
        'auth_type',
        'endpoints',
        'metadata',
        'options',
        'status',
        'share_token',
        'share_expires_at',
        'view_count',
        'test_count',
        'export_count',
        'created_by',
        'updated_by',
        'created_at',
        'updated_at'
    ];

    /**
     * 需要转换为数组的属性
     */
    protected $casts = [
        'endpoints' => 'array',
        'metadata' => 'array',
        'options' => 'array',
        'share_expires_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    /**
     * 状态常量
     */
    const STATUS_ACTIVE = 1;      // 正常
    const STATUS_INACTIVE = 0;    // 停用
    const STATUS_DRAFT = 2;       // 草稿

    /**
     * 认证类型常量
     */
    const AUTH_NONE = 'none';
    const AUTH_JWT = 'jwt';
    const AUTH_OAUTH2 = 'oauth2';
    const AUTH_API_KEY = 'api_key';
    const AUTH_BASIC = 'basic';

    /**
     * 获取状态文本
     */
    public function getStatusTextAttribute(): string
    {
        $statusMap = [
            self::STATUS_ACTIVE => '正常',
            self::STATUS_INACTIVE => '停用',
            self::STATUS_DRAFT => '草稿'
        ];

        return $statusMap[$this->status] ?? '未知';
    }

    /**
     * 获取认证类型文本
     */
    public function getAuthTypeTextAttribute(): string
    {
        $authMap = [
            self::AUTH_NONE => '无认证',
            self::AUTH_JWT => 'JWT Token',
            self::AUTH_OAUTH2 => 'OAuth2',
            self::AUTH_API_KEY => 'API Key',
            self::AUTH_BASIC => 'Basic Auth'
        ];

        return $authMap[$this->auth_type] ?? '未知';
    }

    /**
     * 获取接口数量
     */
    public function getApiCountAttribute(): int
    {
        return is_array($this->endpoints) ? count($this->endpoints) : 0;
    }

    /**
     * 获取文档大小（估算）
     */
    public function getDocSizeAttribute(): string
    {
        $size = strlen(json_encode($this->endpoints)) + strlen($this->description) + 1000;
        
        if ($size < 1024) {
            return $size . 'B';
        } elseif ($size < 1024 * 1024) {
            return round($size / 1024, 1) . 'KB';
        } else {
            return round($size / (1024 * 1024), 1) . 'MB';
        }
    }

    /**
     * 检查是否可以公开分享
     */
    public function canPublicShare(): bool
    {
        return !empty($this->share_token) && 
               ($this->share_expires_at === null || $this->share_expires_at->isFuture());
    }

    /**
     * 生成分享链接
     */
    public function generateShareUrl(): string
    {
        if (!$this->share_token) {
            return '';
        }

        return url('/api-docs/view/' . $this->share_token);
    }

    /**
     * 增加查看次数
     */
    public function incrementViewCount(): void
    {
        $this->increment('view_count');
    }

    /**
     * 增加测试次数
     */
    public function incrementTestCount(): void
    {
        $this->increment('test_count');
    }

    /**
     * 增加导出次数
     */
    public function incrementExportCount(): void
    {
        $this->increment('export_count');
    }

    /**
     * 创建者关联
     */
    public function creator()
    {
        return $this->belongsTo(Admin::class, 'created_by');
    }

    /**
     * 更新者关联
     */
    public function updater()
    {
        return $this->belongsTo(Admin::class, 'updated_by');
    }

    /**
     * 测试历史关联
     */
    public function testHistories()
    {
        return $this->hasMany(ApiTestHistory::class, 'api_doc_id');
    }

    /**
     * 分享记录关联
     */
    public function shareRecords()
    {
        return $this->hasMany(ApiDocShare::class, 'api_doc_id');
    }

    /**
     * 作用域：正常状态
     */
    public function scopeActive($query)
    {
        return $query->where('status', self::STATUS_ACTIVE);
    }

    /**
     * 作用域：按表名搜索
     */
    public function scopeByTable($query, string $tableName)
    {
        return $query->where('table_name', $tableName);
    }

    /**
     * 作用域：按关键词搜索
     */
    public function scopeSearch($query, string $keyword)
    {
        return $query->where(function($q) use ($keyword) {
            $q->where('table_name', 'like', "%{$keyword}%")
              ->orWhere('display_name', 'like', "%{$keyword}%")
              ->orWhere('description', 'like', "%{$keyword}%");
        });
    }

    /**
     * 作用域：按创建者筛选
     */
    public function scopeByCreator($query, int $creatorId)
    {
        return $query->where('created_by', $creatorId);
    }

    /**
     * 作用域：最近创建
     */
    public function scopeRecent($query, int $days = 7)
    {
        return $query->where('created_at', '>=', now()->subDays($days));
    }

    /**
     * 作用域：热门文档（按查看次数排序）
     */
    public function scopePopular($query, int $limit = 10)
    {
        return $query->orderBy('view_count', 'desc')->limit($limit);
    }

    /**
     * 获取统计信息
     */
    public static function getStatistics(): array
    {
        $total = self::count();
        $active = self::active()->count();
        $recent = self::recent()->count();
        $totalViews = self::sum('view_count');
        $totalTests = self::sum('test_count');
        $totalExports = self::sum('export_count');

        return [
            'total_docs' => $total,
            'active_docs' => $active,
            'recent_docs' => $recent,
            'total_views' => $totalViews,
            'total_tests' => $totalTests,
            'total_exports' => $totalExports,
            'avg_views_per_doc' => $total > 0 ? round($totalViews / $total, 1) : 0,
            'avg_tests_per_doc' => $total > 0 ? round($totalTests / $total, 1) : 0,
        ];
    }

    /**
     * 获取热门表格
     */
    public static function getPopularTables(int $limit = 5): array
    {
        return self::select('table_name', 'display_name', 'view_count')
                   ->active()
                   ->orderBy('view_count', 'desc')
                   ->limit($limit)
                   ->get()
                   ->toArray();
    }

    /**
     * 获取最近活动
     */
    public static function getRecentActivity(int $limit = 10): array
    {
        return self::select('table_name', 'display_name', 'created_at', 'updated_at', 'created_by')
                   ->with('creator:id,username')
                   ->orderBy('updated_at', 'desc')
                   ->limit($limit)
                   ->get()
                   ->map(function($doc) {
                       return [
                           'table_name' => $doc->table_name,
                           'display_name' => $doc->display_name,
                           'action' => $doc->created_at->eq($doc->updated_at) ? '创建' : '更新',
                           'time' => $doc->updated_at->diffForHumans(),
                           'user' => $doc->creator->username ?? '未知用户'
                       ];
                   })
                   ->toArray();
    }

    /**
     * 清理过期分享
     */
    public static function cleanupExpiredShares(): int
    {
        return self::where('share_expires_at', '<', now())
                   ->update([
                       'share_token' => null,
                       'share_expires_at' => null
                   ]);
    }

    /**
     * 批量更新状态
     */
    public static function batchUpdateStatus(array $ids, int $status): int
    {
        return self::whereIn('id', $ids)->update(['status' => $status]);
    }

    /**
     * 导出数据
     */
    public function toExportArray(): array
    {
        return [
            'table_name' => $this->table_name,
            'display_name' => $this->display_name,
            'description' => $this->description,
            'version' => $this->version,
            'base_url' => $this->base_url,
            'auth_type' => $this->auth_type,
            'endpoints' => $this->endpoints,
            'metadata' => $this->metadata,
            'created_at' => $this->created_at->toDateTimeString(),
            'updated_at' => $this->updated_at->toDateTimeString()
        ];
    }

    /**
     * 从导入数据创建
     */
    public static function createFromImport(array $data): self
    {
        return self::create([
            'table_name' => $data['table_name'],
            'display_name' => $data['display_name'],
            'description' => $data['description'] ?? '',
            'version' => $data['version'] ?? '1.0.0',
            'base_url' => $data['base_url'] ?? '/api',
            'auth_type' => $data['auth_type'] ?? self::AUTH_JWT,
            'endpoints' => $data['endpoints'] ?? [],
            'metadata' => $data['metadata'] ?? [],
            'status' => self::STATUS_ACTIVE,
            'created_by' => session('admin.id', 0),
            'updated_by' => session('admin.id', 0)
        ]);
    }
}
