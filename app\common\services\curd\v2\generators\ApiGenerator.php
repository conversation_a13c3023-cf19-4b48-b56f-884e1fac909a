<?php

namespace app\common\services\curd\v2\generators;

/**
 * API 代码生成器
 * 生成 RESTful API 相关代码
 */
class ApiGenerator
{
    /**
     * 生成 API 控制器代码
     */
    public function generateApiController(array $endpoints, array $tableInfo): string
    {
        $className = $this->getApiControllerName($tableInfo['name']);
        $modelName = $this->getModelName($tableInfo['name']);
        $resourceName = $this->getResourceName($tableInfo['name']);

        $code = "<?php\n\n";
        $code .= "namespace app\\api\\controller;\n\n";
        $code .= "use app\\common\\controller\\ApiController;\n";
        $code .= "use app\\common\\model\\{$modelName};\n";
        $code .= "use support\\Request;\n";
        $code .= "use support\\Response;\n\n";

        $code .= "/**\n";
        $code .= " * {$tableInfo['comment']} API 控制器\n";
        $code .= " * 自动生成的 RESTful API 接口\n";
        $code .= " */\n";
        $code .= "class {$className} extends ApiController\n";
        $code .= "{\n";
        $code .= "    protected \$model;\n\n";
        $code .= "    public function __construct()\n";
        $code .= "    {\n";
        $code .= "        parent::__construct();\n";
        $code .= "        \$this->model = new {$modelName}();\n";
        $code .= "    }\n\n";

        // 生成各个接口方法
        foreach ($endpoints as $endpoint) {
            if (empty($endpoint['relationship'])) {
                $code .= $this->generateControllerMethod($endpoint, $tableInfo);
            } else {
                $code .= $this->generateRelationshipMethod($endpoint, $tableInfo);
            }
        }

        $code .= "}\n";

        return $code;
    }

    /**
     * 生成控制器方法
     */
    protected function generateControllerMethod(array $endpoint, array $tableInfo): string
    {
        $method = $endpoint['controller_method'];
        $description = $endpoint['description'];

        $code = "    /**\n";
        $code .= "     * {$description}\n";
        $code .= "     */\n";
        $code .= "    public function {$method}(Request \$request): Response\n";
        $code .= "    {\n";

        switch ($endpoint['action']) {
            case 'index':
                $code .= $this->generateIndexMethod($tableInfo);
                break;
            case 'show':
                $code .= $this->generateShowMethod($tableInfo);
                break;
            case 'store':
                $code .= $this->generateStoreMethod($tableInfo);
                break;
            case 'update':
                $code .= $this->generateUpdateMethod($tableInfo);
                break;
            case 'destroy':
                $code .= $this->generateDestroyMethod($tableInfo);
                break;
            case 'batch_delete':
                $code .= $this->generateBatchDeleteMethod($tableInfo);
                break;
            case 'batch_update':
                $code .= $this->generateBatchUpdateMethod($tableInfo);
                break;
            case 'search':
                $code .= $this->generateSearchMethod($tableInfo);
                break;
            case 'count':
                $code .= $this->generateCountMethod($tableInfo);
                break;
            case 'export':
                $code .= $this->generateExportMethod($tableInfo);
                break;
            case 'import':
                $code .= $this->generateImportMethod($tableInfo);
                break;
        }

        $code .= "    }\n\n";

        return $code;
    }

    /**
     * 生成关联关系方法
     */
    protected function generateRelationshipMethod(array $endpoint, array $tableInfo): string
    {
        $method = $endpoint['controller_method'];
        $description = $endpoint['description'];
        $relationship = $endpoint['relationship'];

        $code = "    /**\n";
        $code .= "     * {$description}\n";
        $code .= "     */\n";
        $code .= "    public function {$method}(Request \$request): Response\n";
        $code .= "    {\n";

        switch ($endpoint['action']) {
            case strpos($endpoint['action'], 'get_') === 0:
                $code .= $this->generateGetRelationshipMethod($relationship);
                break;
            case strpos($endpoint['action'], 'attach_') === 0:
                $code .= $this->generateAttachMethod($relationship);
                break;
            case strpos($endpoint['action'], 'detach_') === 0:
                $code .= $this->generateDetachMethod($relationship);
                break;
        }

        $code .= "    }\n\n";

        return $code;
    }

    /**
     * 生成列表方法
     */
    protected function generateIndexMethod(array $tableInfo): string
    {
        $code = "        try {\n";
        $code .= "            \$query = \$this->model->newQuery();\n\n";

        // 添加搜索条件
        $searchableFields = array_filter($tableInfo['fields'], function($field) {
            return !empty($field['searchable']);
        });

        if (!empty($searchableFields)) {
            $code .= "            // 搜索条件\n";
            foreach ($searchableFields as $field) {
                $fieldName = $field['name'];
                $code .= "            if (\$request->has('{$fieldName}')) {\n";
                $code .= "                \$query->where('{$fieldName}', 'like', '%' . \$request->input('{$fieldName}') . '%');\n";
                $code .= "            }\n";
            }
            $code .= "\n";
        }

        $code .= "            // 排序\n";
        $code .= "            \$sort = \$request->input('sort', 'id');\n";
        $code .= "            \$order = \$request->input('order', 'desc');\n";
        $code .= "            \$query->orderBy(\$sort, \$order);\n\n";

        $code .= "            // 分页\n";
        $code .= "            \$perPage = \$request->input('per_page', 15);\n";
        $code .= "            \$data = \$query->paginate(\$perPage);\n\n";

        $code .= "            return \$this->success('获取成功', \$data);\n";
        $code .= "        } catch (\\Exception \$e) {\n";
        $code .= "            return \$this->error('获取失败: ' . \$e->getMessage());\n";
        $code .= "        }\n";

        return $code;
    }

    /**
     * 生成详情方法
     */
    protected function generateShowMethod(array $tableInfo): string
    {
        $code = "        try {\n";
        $code .= "            \$id = \$request->input('id');\n";
        $code .= "            \$data = \$this->model->find(\$id);\n\n";
        $code .= "            if (!\$data) {\n";
        $code .= "                return \$this->error('记录不存在', [], 404);\n";
        $code .= "            }\n\n";
        $code .= "            return \$this->success('获取成功', \$data);\n";
        $code .= "        } catch (\\Exception \$e) {\n";
        $code .= "            return \$this->error('获取失败: ' . \$e->getMessage());\n";
        $code .= "        }\n";

        return $code;
    }

    /**
     * 生成创建方法
     */
    protected function generateStoreMethod(array $tableInfo): string
    {
        $code = "        try {\n";
        $code .= "            \$data = \$request->only([\n";

        $fillableFields = array_filter($tableInfo['fields'], function($field) {
            return !in_array($field['name'], ['id', 'created_at', 'updated_at', 'deleted_at']);
        });

        foreach ($fillableFields as $field) {
            $code .= "                '{$field['name']}',\n";
        }

        $code .= "            ]);\n\n";

        // 添加验证
        $code .= "            \$validator = \\support\\facade\\Validator::make(\$data, [\n";
        foreach ($fillableFields as $field) {
            if (!empty($field['required'])) {
                $code .= "                '{$field['name']}' => 'required',\n";
            }
        }
        $code .= "            ]);\n\n";

        $code .= "            if (\$validator->fails()) {\n";
        $code .= "                return \$this->error('验证失败', \$validator->errors(), 422);\n";
        $code .= "            }\n\n";

        $code .= "            \$result = \$this->model->create(\$data);\n\n";
        $code .= "            return \$this->success('创建成功', \$result);\n";
        $code .= "        } catch (\\Exception \$e) {\n";
        $code .= "            return \$this->error('创建失败: ' . \$e->getMessage());\n";
        $code .= "        }\n";

        return $code;
    }

    /**
     * 生成更新方法
     */
    protected function generateUpdateMethod(array $tableInfo): string
    {
        $code = "        try {\n";
        $code .= "            \$id = \$request->input('id');\n";
        $code .= "            \$model = \$this->model->find(\$id);\n\n";
        $code .= "            if (!\$model) {\n";
        $code .= "                return \$this->error('记录不存在', [], 404);\n";
        $code .= "            }\n\n";

        $code .= "            \$data = \$request->only([\n";

        $fillableFields = array_filter($tableInfo['fields'], function($field) {
            return !in_array($field['name'], ['id', 'created_at', 'updated_at', 'deleted_at']);
        });

        foreach ($fillableFields as $field) {
            $code .= "                '{$field['name']}',\n";
        }

        $code .= "            ]);\n\n";

        $code .= "            \$model->update(\$data);\n\n";
        $code .= "            return \$this->success('更新成功', \$model);\n";
        $code .= "        } catch (\\Exception \$e) {\n";
        $code .= "            return \$this->error('更新失败: ' . \$e->getMessage());\n";
        $code .= "        }\n";

        return $code;
    }

    /**
     * 生成删除方法
     */
    protected function generateDestroyMethod(array $tableInfo): string
    {
        $code = "        try {\n";
        $code .= "            \$id = \$request->input('id');\n";
        $code .= "            \$model = \$this->model->find(\$id);\n\n";
        $code .= "            if (!\$model) {\n";
        $code .= "                return \$this->error('记录不存在', [], 404);\n";
        $code .= "            }\n\n";

        // 检查是否支持软删除
        if ($tableInfo['has_soft_delete']) {
            $code .= "            \$model->delete(); // 软删除\n";
        } else {
            $code .= "            \$model->delete();\n";
        }

        $code .= "\n            return \$this->success('删除成功');\n";
        $code .= "        } catch (\\Exception \$e) {\n";
        $code .= "            return \$this->error('删除失败: ' . \$e->getMessage());\n";
        $code .= "        }\n";

        return $code;
    }

    /**
     * 生成批量删除方法
     */
    protected function generateBatchDeleteMethod(array $tableInfo): string
    {
        $code = "        try {\n";
        $code .= "            \$ids = \$request->input('ids', []);\n\n";
        $code .= "            if (empty(\$ids)) {\n";
        $code .= "                return \$this->error('请选择要删除的记录');\n";
        $code .= "            }\n\n";

        if ($tableInfo['has_soft_delete']) {
            $code .= "            \$count = \$this->model->whereIn('id', \$ids)->delete(); // 软删除\n";
        } else {
            $code .= "            \$count = \$this->model->whereIn('id', \$ids)->delete();\n";
        }

        $code .= "\n            return \$this->success(\"成功删除 {\$count} 条记录\");\n";
        $code .= "        } catch (\\Exception \$e) {\n";
        $code .= "            return \$this->error('批量删除失败: ' . \$e->getMessage());\n";
        $code .= "        }\n";

        return $code;
    }

    /**
     * 生成批量更新方法
     */
    protected function generateBatchUpdateMethod(array $tableInfo): string
    {
        $code = "        try {\n";
        $code .= "            \$ids = \$request->input('ids', []);\n";
        $code .= "            \$data = \$request->except(['ids']);\n\n";
        $code .= "            if (empty(\$ids)) {\n";
        $code .= "                return \$this->error('请选择要更新的记录');\n";
        $code .= "            }\n\n";
        $code .= "            \$count = \$this->model->whereIn('id', \$ids)->update(\$data);\n\n";
        $code .= "            return \$this->success(\"成功更新 {\$count} 条记录\");\n";
        $code .= "        } catch (\\Exception \$e) {\n";
        $code .= "            return \$this->error('批量更新失败: ' . \$e->getMessage());\n";
        $code .= "        }\n";

        return $code;
    }

    /**
     * 生成搜索方法
     */
    protected function generateSearchMethod(array $tableInfo): string
    {
        $code = "        try {\n";
        $code .= "            \$keyword = \$request->input('keyword');\n";
        $code .= "            \$query = \$this->model->newQuery();\n\n";

        $searchableFields = array_filter($tableInfo['fields'], function($field) {
            return !empty($field['searchable']);
        });

        if (!empty($searchableFields)) {
            $code .= "            if (\$keyword) {\n";
            $code .= "                \$query->where(function(\$q) use (\$keyword) {\n";
            foreach ($searchableFields as $index => $field) {
                $method = $index === 0 ? 'where' : 'orWhere';
                $code .= "                    \$q->{$method}('{$field['name']}', 'like', \"%{\$keyword}%\");\n";
            }
            $code .= "                });\n";
            $code .= "            }\n\n";
        }

        $code .= "            \$perPage = \$request->input('per_page', 15);\n";
        $code .= "            \$data = \$query->paginate(\$perPage);\n\n";
        $code .= "            return \$this->success('搜索成功', \$data);\n";
        $code .= "        } catch (\\Exception \$e) {\n";
        $code .= "            return \$this->error('搜索失败: ' . \$e->getMessage());\n";
        $code .= "        }\n";

        return $code;
    }

    /**
     * 生成统计方法
     */
    protected function generateCountMethod(array $tableInfo): string
    {
        $code = "        try {\n";
        $code .= "            \$total = \$this->model->count();\n";

        if ($tableInfo['has_soft_delete']) {
            $code .= "            \$deleted = \$this->model->onlyTrashed()->count();\n";
            $code .= "            \$active = \$total - \$deleted;\n\n";
            $code .= "            return \$this->success('统计成功', [\n";
            $code .= "                'total' => \$total,\n";
            $code .= "                'active' => \$active,\n";
            $code .= "                'deleted' => \$deleted,\n";
            $code .= "            ]);\n";
        } else {
            $code .= "\n            return \$this->success('统计成功', ['total' => \$total]);\n";
        }

        $code .= "        } catch (\\Exception \$e) {\n";
        $code .= "            return \$this->error('统计失败: ' . \$e->getMessage());\n";
        $code .= "        }\n";

        return $code;
    }

    /**
     * 生成导出方法
     */
    protected function generateExportMethod(array $tableInfo): string
    {
        $code = "        try {\n";
        $code .= "            \$query = \$this->model->newQuery();\n";
        $code .= "            \$data = \$query->get();\n\n";
        $code .= "            // TODO: 实现具体的导出逻辑\n";
        $code .= "            // 可以使用 Excel、CSV 等格式导出\n\n";
        $code .= "            return \$this->success('导出成功', \$data);\n";
        $code .= "        } catch (\\Exception \$e) {\n";
        $code .= "            return \$this->error('导出失败: ' . \$e->getMessage());\n";
        $code .= "        }\n";

        return $code;
    }

    /**
     * 生成导入方法
     */
    protected function generateImportMethod(array $tableInfo): string
    {
        $code = "        try {\n";
        $code .= "            \$file = \$request->file('file');\n\n";
        $code .= "            if (!\$file) {\n";
        $code .= "                return \$this->error('请选择要导入的文件');\n";
        $code .= "            }\n\n";
        $code .= "            // TODO: 实现具体的导入逻辑\n";
        $code .= "            // 可以解析 Excel、CSV 等格式文件\n\n";
        $code .= "            return \$this->success('导入成功');\n";
        $code .= "        } catch (\\Exception \$e) {\n";
        $code .= "            return \$this->error('导入失败: ' . \$e->getMessage());\n";
        $code .= "        }\n";

        return $code;
    }

    /**
     * 生成获取关联关系方法
     */
    protected function generateGetRelationshipMethod(array $relationship): string
    {
        $relationName = $relationship['method_name'];

        $code = "        try {\n";
        $code .= "            \$id = \$request->input('id');\n";
        $code .= "            \$model = \$this->model->find(\$id);\n\n";
        $code .= "            if (!\$model) {\n";
        $code .= "                return \$this->error('记录不存在', [], 404);\n";
        $code .= "            }\n\n";

        if (in_array($relationship['type'], ['hasMany', 'belongsToMany'])) {
            $code .= "            \$perPage = \$request->input('per_page', 15);\n";
            $code .= "            \$data = \$model->{$relationName}()->paginate(\$perPage);\n";
        } else {
            $code .= "            \$data = \$model->{$relationName};\n";
        }

        $code .= "\n            return \$this->success('获取成功', \$data);\n";
        $code .= "        } catch (\\Exception \$e) {\n";
        $code .= "            return \$this->error('获取失败: ' . \$e->getMessage());\n";
        $code .= "        }\n";

        return $code;
    }

    /**
     * 生成附加关联方法
     */
    protected function generateAttachMethod(array $relationship): string
    {
        $relationName = $relationship['method_name'];

        $code = "        try {\n";
        $code .= "            \$id = \$request->input('id');\n";
        $code .= "            \$relatedIds = \$request->input('related_ids', []);\n\n";
        $code .= "            \$model = \$this->model->find(\$id);\n\n";
        $code .= "            if (!\$model) {\n";
        $code .= "                return \$this->error('记录不存在', [], 404);\n";
        $code .= "            }\n\n";
        $code .= "            \$model->{$relationName}()->attach(\$relatedIds);\n\n";
        $code .= "            return \$this->success('关联成功');\n";
        $code .= "        } catch (\\Exception \$e) {\n";
        $code .= "            return \$this->error('关联失败: ' . \$e->getMessage());\n";
        $code .= "        }\n";

        return $code;
    }

    /**
     * 生成分离关联方法
     */
    protected function generateDetachMethod(array $relationship): string
    {
        $relationName = $relationship['method_name'];

        $code = "        try {\n";
        $code .= "            \$id = \$request->input('id');\n";
        $code .= "            \$relatedId = \$request->input('related_id');\n\n";
        $code .= "            \$model = \$this->model->find(\$id);\n\n";
        $code .= "            if (!\$model) {\n";
        $code .= "                return \$this->error('记录不存在', [], 404);\n";
        $code .= "            }\n\n";
        $code .= "            \$model->{$relationName}()->detach(\$relatedId);\n\n";
        $code .= "            return \$this->success('取消关联成功');\n";
        $code .= "        } catch (\\Exception \$e) {\n";
        $code .= "            return \$this->error('取消关联失败: ' . \$e->getMessage());\n";
        $code .= "        }\n";

        return $code;
    }

    /**
     * 生成路由配置
     */
    public function generateApiRoutes(array $endpoints, array $tableInfo): string
    {
        $resourceName = $this->getResourceName($tableInfo['name']);
        $controllerName = $this->getApiControllerName($tableInfo['name']);

        $code = "<?php\n\n";
        $code .= "/**\n";
        $code .= " * {$tableInfo['comment']} API 路由\n";
        $code .= " * 自动生成的 RESTful API 路由配置\n";
        $code .= " */\n\n";

        $code .= "use Webman\\Route;\n\n";

        // 资源路由组
        $code .= "Route::group('/api/{$resourceName}', function () {\n";

        foreach ($endpoints as $endpoint) {
            $method = strtolower($endpoint['method']);
            $path = str_replace("/api/{$resourceName}", '', $endpoint['path']);
            $action = $endpoint['controller_method'];

            if (empty($path)) {
                $path = '/';
            }

            $code .= "    Route::{$method}('{$path}', [\\app\\api\\controller\\{$controllerName}::class, '{$action}'])";
            
            if (!empty($endpoint['name'])) {
                $code .= "->name('{$endpoint['name']}')";
            }

            if (!empty($endpoint['middleware'])) {
                $middlewareStr = "'" . implode("', '", $endpoint['middleware']) . "'";
                $code .= "->middleware([{$middlewareStr}])";
            }

            $code .= "; // {$endpoint['description']}\n";
        }

        $code .= "});\n";

        return $code;
    }

    /**
     * 生成 OpenAPI 文档
     */
    public function generateOpenApiDoc(array $endpoints, array $tableInfo): array
    {
        $resourceName = $this->getResourceName($tableInfo['name']);

        $doc = [
            'openapi' => '3.0.0',
            'info' => [
                'title' => "{$tableInfo['comment']} API",
                'description' => "自动生成的 {$tableInfo['comment']} RESTful API 文档",
                'version' => '1.0.0',
            ],
            'servers' => [
                ['url' => '/api', 'description' => 'API 服务器'],
            ],
            'paths' => [],
            'components' => [
                'schemas' => [
                    $this->getModelName($tableInfo['name']) => $this->generateSchema($tableInfo),
                ],
                'securitySchemes' => [
                    'bearerAuth' => [
                        'type' => 'http',
                        'scheme' => 'bearer',
                        'bearerFormat' => 'JWT',
                    ],
                ],
            ],
            'security' => [
                ['bearerAuth' => []],
            ],
        ];

        // 生成路径文档
        foreach ($endpoints as $endpoint) {
            $path = $endpoint['path'];
            $method = strtolower($endpoint['method']);

            if (!isset($doc['paths'][$path])) {
                $doc['paths'][$path] = [];
            }

            $doc['paths'][$path][$method] = [
                'summary' => $endpoint['description'],
                'tags' => [$resourceName],
                'parameters' => $this->formatOpenApiParameters($endpoint['parameters']),
                'responses' => $endpoint['responses'],
            ];

            if (in_array($endpoint['action'], ['store', 'update'])) {
                $doc['paths'][$path][$method]['requestBody'] = [
                    'required' => true,
                    'content' => [
                        'application/json' => [
                            'schema' => [
                                '$ref' => "#/components/schemas/{$this->getModelName($tableInfo['name'])}",
                            ],
                        ],
                    ],
                ];
            }
        }

        return $doc;
    }

    /**
     * 格式化 OpenAPI 参数
     */
    protected function formatOpenApiParameters(array $parameters): array
    {
        $formatted = [];

        foreach ($parameters as $name => $param) {
            $formatted[] = [
                'name' => $name,
                'in' => $param['in'] ?? 'query',
                'required' => $param['required'] ?? false,
                'description' => $param['description'] ?? '',
                'schema' => [
                    'type' => $param['type'] ?? 'string',
                ],
            ];
        }

        return $formatted;
    }

    /**
     * 生成模式定义
     */
    protected function generateSchema(array $tableInfo): array
    {
        $properties = [];
        $required = [];

        foreach ($tableInfo['fields'] as $field) {
            $properties[$field['name']] = [
                'type' => $this->getSchemaType($field['type']),
                'description' => $field['comment'] ?: $field['name'],
            ];

            if (!empty($field['required'])) {
                $required[] = $field['name'];
            }
        }

        $schema = [
            'type' => 'object',
            'properties' => $properties,
        ];

        if (!empty($required)) {
            $schema['required'] = $required;
        }

        return $schema;
    }

    /**
     * 获取 API 控制器名称
     */
    protected function getApiControllerName(string $tableName): string
    {
        $name = preg_replace('/^[a-z]+_/', '', $tableName);
        return $this->toPascalCase($name) . 'Controller';
    }

    /**
     * 获取模型名称
     */
    protected function getModelName(string $tableName): string
    {
        $name = preg_replace('/^[a-z]+_/', '', $tableName);
        return $this->toPascalCase($this->singularize($name));
    }

    /**
     * 获取资源名称
     */
    protected function getResourceName(string $tableName): string
    {
        $name = preg_replace('/^[a-z]+_/', '', $tableName);
        return $this->pluralize($this->toCamelCase($name));
    }

    /**
     * 获取模式类型
     */
    protected function getSchemaType(string $dbType): string
    {
        $typeMap = [
            'int' => 'integer',
            'bigint' => 'integer',
            'tinyint' => 'integer',
            'decimal' => 'number',
            'float' => 'number',
            'double' => 'number',
            'varchar' => 'string',
            'text' => 'string',
            'date' => 'string',
            'datetime' => 'string',
            'timestamp' => 'string',
            'json' => 'object',
        ];

        return $typeMap[$dbType] ?? 'string';
    }

    /**
     * 单数化
     */
    protected function singularize(string $word): string
    {
        $rules = [
            '/ies$/' => 'y',
            '/ves$/' => 'f',
            '/ses$/' => 's',
            '/s$/' => '',
        ];

        foreach ($rules as $pattern => $replacement) {
            if (preg_match($pattern, $word)) {
                return preg_replace($pattern, $replacement, $word);
            }
        }

        return $word;
    }

    /**
     * 复数化
     */
    protected function pluralize(string $word): string
    {
        $rules = [
            '/y$/' => 'ies',
            '/f$/' => 'ves',
            '/s$/' => 'ses',
            '/$/' => 's',
        ];

        foreach ($rules as $pattern => $replacement) {
            if (preg_match($pattern, $word)) {
                return preg_replace($pattern, $replacement, $word);
            }
        }

        return $word . 's';
    }

    /**
     * 转换为驼峰命名
     */
    protected function toCamelCase(string $string): string
    {
        return lcfirst($this->toPascalCase($string));
    }

    /**
     * 转换为帕斯卡命名
     */
    protected function toPascalCase(string $string): string
    {
        return str_replace(' ', '', ucwords(str_replace(['_', '-'], ' ', $string)));
    }
}
