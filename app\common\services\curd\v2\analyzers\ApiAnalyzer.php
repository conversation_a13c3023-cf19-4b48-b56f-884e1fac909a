<?php

namespace app\common\services\curd\v2\analyzers;

/**
 * API 接口分析器
 * 分析和设计 RESTful API 接口
 */
class ApiAnalyzer
{
    protected array $standardEndpoints = [
        'index' => ['method' => 'GET', 'path' => '', 'description' => '获取列表'],
        'show' => ['method' => 'GET', 'path' => '/{id}', 'description' => '获取详情'],
        'store' => ['method' => 'POST', 'path' => '', 'description' => '创建记录'],
        'update' => ['method' => 'PUT', 'path' => '/{id}', 'description' => '更新记录'],
        'destroy' => ['method' => 'DELETE', 'path' => '/{id}', 'description' => '删除记录'],
    ];

    protected array $extendedEndpoints = [
        'batch_delete' => ['method' => 'DELETE', 'path' => '/batch', 'description' => '批量删除'],
        'batch_update' => ['method' => 'PUT', 'path' => '/batch', 'description' => '批量更新'],
        'export' => ['method' => 'GET', 'path' => '/export', 'description' => '导出数据'],
        'import' => ['method' => 'POST', 'path' => '/import', 'description' => '导入数据'],
        'search' => ['method' => 'GET', 'path' => '/search', 'description' => '搜索记录'],
        'count' => ['method' => 'GET', 'path' => '/count', 'description' => '统计数量'],
    ];

    /**
     * 分析表的 API 接口设计
     */
    public function analyzeApiEndpoints(array $tableInfo, array $options = []): array
    {
        $tableName = $tableInfo['name'];
        $resourceName = $this->getResourceName($tableName);
        $endpoints = [];

        // 基础 CRUD 接口
        foreach ($this->standardEndpoints as $action => $config) {
            $endpoints[] = $this->buildEndpoint($action, $config, $resourceName, $tableInfo);
        }

        // 扩展接口
        if (!empty($options['enable_batch'])) {
            $endpoints[] = $this->buildEndpoint('batch_delete', $this->extendedEndpoints['batch_delete'], $resourceName, $tableInfo);
            $endpoints[] = $this->buildEndpoint('batch_update', $this->extendedEndpoints['batch_update'], $resourceName, $tableInfo);
        }

        if (!empty($options['enable_export'])) {
            $endpoints[] = $this->buildEndpoint('export', $this->extendedEndpoints['export'], $resourceName, $tableInfo);
        }

        if (!empty($options['enable_import'])) {
            $endpoints[] = $this->buildEndpoint('import', $this->extendedEndpoints['import'], $resourceName, $tableInfo);
        }

        if (!empty($options['enable_search'])) {
            $endpoints[] = $this->buildEndpoint('search', $this->extendedEndpoints['search'], $resourceName, $tableInfo);
        }

        if (!empty($options['enable_statistics'])) {
            $endpoints[] = $this->buildEndpoint('count', $this->extendedEndpoints['count'], $resourceName, $tableInfo);
        }

        // 关联关系接口
        if (!empty($tableInfo['relationships'])) {
            $relationshipEndpoints = $this->analyzeRelationshipEndpoints($tableInfo['relationships'], $resourceName);
            $endpoints = array_merge($endpoints, $relationshipEndpoints);
        }

        return $endpoints;
    }

    /**
     * 构建单个接口端点
     */
    protected function buildEndpoint(string $action, array $config, string $resourceName, array $tableInfo): array
    {
        $endpoint = [
            'action' => $action,
            'method' => $config['method'],
            'path' => "/api/{$resourceName}" . $config['path'],
            'name' => "{$resourceName}.{$action}",
            'description' => $config['description'],
            'controller_method' => $action,
            'middleware' => $this->getMiddleware($action),
            'parameters' => $this->getParameters($action, $tableInfo),
            'responses' => $this->getResponses($action, $tableInfo),
            'validation' => $this->getValidationRules($action, $tableInfo),
        ];

        return $endpoint;
    }

    /**
     * 分析关联关系接口
     */
    protected function analyzeRelationshipEndpoints(array $relationships, string $resourceName): array
    {
        $endpoints = [];

        foreach ($relationships as $relationship) {
            if ($relationship['confidence'] < 70) {
                continue; // 跳过低置信度的关系
            }

            $relationName = $relationship['method_name'];
            $relatedResource = $this->getResourceName($relationship['related_table']);

            switch ($relationship['type']) {
                case 'hasMany':
                case 'belongsToMany':
                    // 获取关联列表
                    $endpoints[] = [
                        'action' => "get_{$relationName}",
                        'method' => 'GET',
                        'path' => "/api/{$resourceName}/{id}/{$relationName}",
                        'name' => "{$resourceName}.{$relationName}.index",
                        'description' => "获取{$relationship['description']}列表",
                        'controller_method' => "get" . ucfirst($relationName),
                        'middleware' => ['auth:api'],
                        'parameters' => $this->getRelationshipParameters($relationship),
                        'responses' => $this->getRelationshipResponses($relationship),
                        'relationship' => $relationship,
                    ];

                    // 关联操作接口
                    if ($relationship['type'] === 'belongsToMany') {
                        // 添加关联
                        $endpoints[] = [
                            'action' => "attach_{$relationName}",
                            'method' => 'POST',
                            'path' => "/api/{$resourceName}/{id}/{$relationName}",
                            'name' => "{$resourceName}.{$relationName}.attach",
                            'description' => "添加{$relationship['description']}关联",
                            'controller_method' => "attach" . ucfirst($relationName),
                            'middleware' => ['auth:api'],
                            'parameters' => $this->getAttachParameters($relationship),
                            'responses' => $this->getAttachResponses($relationship),
                            'relationship' => $relationship,
                        ];

                        // 移除关联
                        $endpoints[] = [
                            'action' => "detach_{$relationName}",
                            'method' => 'DELETE',
                            'path' => "/api/{$resourceName}/{id}/{$relationName}/{related_id}",
                            'name' => "{$resourceName}.{$relationName}.detach",
                            'description' => "移除{$relationship['description']}关联",
                            'controller_method' => "detach" . ucfirst($relationName),
                            'middleware' => ['auth:api'],
                            'parameters' => $this->getDetachParameters($relationship),
                            'responses' => $this->getDetachResponses($relationship),
                            'relationship' => $relationship,
                        ];
                    }
                    break;

                case 'belongsTo':
                case 'hasOne':
                    // 获取关联对象
                    $endpoints[] = [
                        'action' => "get_{$relationName}",
                        'method' => 'GET',
                        'path' => "/api/{$resourceName}/{id}/{$relationName}",
                        'name' => "{$resourceName}.{$relationName}.show",
                        'description' => "获取{$relationship['description']}",
                        'controller_method' => "get" . ucfirst($relationName),
                        'middleware' => ['auth:api'],
                        'parameters' => $this->getRelationshipParameters($relationship),
                        'responses' => $this->getRelationshipResponses($relationship),
                        'relationship' => $relationship,
                    ];
                    break;
            }
        }

        return $endpoints;
    }

    /**
     * 获取资源名称
     */
    protected function getResourceName(string $tableName): string
    {
        // 移除表前缀
        $name = preg_replace('/^[a-z]+_/', '', $tableName);

        // 转换为复数形式的资源名
        return $this->pluralize($this->toCamelCase($name));
    }

    /**
     * 获取中间件
     */
    protected function getMiddleware(string $action): array
    {
        $middleware = ['auth:api'];

        // 根据操作类型添加权限中间件
        switch ($action) {
            case 'store':
            case 'update':
            case 'destroy':
            case 'batch_delete':
            case 'batch_update':
                $middleware[] = 'permission:write';
                break;
            case 'index':
            case 'show':
            case 'search':
            case 'count':
            case 'export':
                $middleware[] = 'permission:read';
                break;
            case 'import':
                $middleware[] = 'permission:import';
                break;
        }

        return $middleware;
    }

    /**
     * 获取参数定义
     */
    protected function getParameters(string $action, array $tableInfo): array
    {
        $parameters = [];

        switch ($action) {
            case 'index':
            case 'search':
                $parameters = [
                    'page' => ['type' => 'integer', 'description' => '页码', 'default' => 1],
                    'per_page' => ['type' => 'integer', 'description' => '每页数量', 'default' => 15],
                    'sort' => ['type' => 'string', 'description' => '排序字段'],
                    'order' => ['type' => 'string', 'description' => '排序方向', 'enum' => ['asc', 'desc']],
                ];

                // 添加可搜索字段
                foreach ($tableInfo['fields'] as $field) {
                    if (!empty($field['searchable'])) {
                        $parameters[$field['name']] = [
                            'type' => $this->getParameterType($field['type']),
                            'description' => "按{$field['comment']}搜索",
                        ];
                    }
                }
                break;

            case 'show':
            case 'update':
            case 'destroy':
                $parameters['id'] = [
                    'type' => 'integer',
                    'description' => '记录ID',
                    'required' => true,
                    'in' => 'path',
                ];
                break;

            case 'store':
            case 'update':
                foreach ($tableInfo['fields'] as $field) {
                    if ($field['name'] === 'id' || in_array($field['name'], ['created_at', 'updated_at', 'deleted_at'])) {
                        continue;
                    }

                    $parameters[$field['name']] = [
                        'type' => $this->getParameterType($field['type']),
                        'description' => $field['comment'] ?: $field['name'],
                        'required' => !empty($field['required']) && $action === 'store',
                    ];
                }
                break;

            case 'batch_delete':
            case 'batch_update':
                $parameters['ids'] = [
                    'type' => 'array',
                    'items' => ['type' => 'integer'],
                    'description' => '记录ID数组',
                    'required' => true,
                ];
                break;
        }

        return $parameters;
    }

    /**
     * 获取响应定义
     */
    protected function getResponses(string $action, array $tableInfo): array
    {
        $responses = [
            '200' => ['description' => '成功'],
            '400' => ['description' => '请求参数错误'],
            '401' => ['description' => '未授权'],
            '403' => ['description' => '权限不足'],
            '500' => ['description' => '服务器错误'],
        ];

        switch ($action) {
            case 'index':
            case 'search':
                $responses['200']['content'] = [
                    'application/json' => [
                        'schema' => [
                            'type' => 'object',
                            'properties' => [
                                'code' => ['type' => 'integer', 'example' => 0],
                                'msg' => ['type' => 'string', 'example' => '获取成功'],
                                'data' => [
                                    'type' => 'object',
                                    'properties' => [
                                        'data' => [
                                            'type' => 'array',
                                            'items' => $this->getResourceSchema($tableInfo),
                                        ],
                                        'total' => ['type' => 'integer'],
                                        'per_page' => ['type' => 'integer'],
                                        'current_page' => ['type' => 'integer'],
                                    ],
                                ],
                            ],
                        ],
                    ],
                ];
                break;

            case 'show':
            case 'store':
            case 'update':
                $responses['200']['content'] = [
                    'application/json' => [
                        'schema' => [
                            'type' => 'object',
                            'properties' => [
                                'code' => ['type' => 'integer', 'example' => 0],
                                'msg' => ['type' => 'string', 'example' => '操作成功'],
                                'data' => $this->getResourceSchema($tableInfo),
                            ],
                        ],
                    ],
                ];
                break;

            case 'destroy':
            case 'batch_delete':
            case 'batch_update':
                $responses['200']['content'] = [
                    'application/json' => [
                        'schema' => [
                            'type' => 'object',
                            'properties' => [
                                'code' => ['type' => 'integer', 'example' => 0],
                                'msg' => ['type' => 'string', 'example' => '操作成功'],
                            ],
                        ],
                    ],
                ];
                break;
        }

        // 添加特定错误响应
        if (in_array($action, ['show', 'update', 'destroy'])) {
            $responses['404'] = ['description' => '记录不存在'];
        }

        if (in_array($action, ['store', 'update'])) {
            $responses['422'] = ['description' => '验证失败'];
        }

        return $responses;
    }

    /**
     * 获取验证规则
     */
    protected function getValidationRules(string $action, array $tableInfo): array
    {
        if (!in_array($action, ['store', 'update'])) {
            return [];
        }

        $rules = [];

        foreach ($tableInfo['fields'] as $field) {
            if ($field['name'] === 'id' || in_array($field['name'], ['created_at', 'updated_at', 'deleted_at'])) {
                continue;
            }

            $fieldRules = [];

            // 必填验证
            if (!empty($field['required']) && $action === 'store') {
                $fieldRules[] = 'required';
            } elseif ($action === 'update') {
                $fieldRules[] = 'sometimes';
            }

            // 类型验证
            switch ($field['type']) {
                case 'int':
                case 'bigint':
                case 'tinyint':
                    $fieldRules[] = 'integer';
                    break;
                case 'decimal':
                case 'float':
                case 'double':
                    $fieldRules[] = 'numeric';
                    break;
                case 'varchar':
                case 'text':
                    $fieldRules[] = 'string';
                    if (!empty($field['length'])) {
                        $fieldRules[] = "max:{$field['length']}";
                    }
                    break;
                case 'date':
                    $fieldRules[] = 'date';
                    break;
                case 'datetime':
                case 'timestamp':
                    $fieldRules[] = 'date_format:Y-m-d H:i:s';
                    break;
                case 'json':
                    $fieldRules[] = 'json';
                    break;
            }

            // 唯一性验证
            if (!empty($field['unique'])) {
                $tableName = $tableInfo['name'];
                if ($action === 'update') {
                    $fieldRules[] = "unique:{$tableName},{$field['name']},{\$id}";
                } else {
                    $fieldRules[] = "unique:{$tableName},{$field['name']}";
                }
            }

            if (!empty($fieldRules)) {
                $rules[$field['name']] = implode('|', $fieldRules);
            }
        }

        return $rules;
    }

    /**
     * 获取关联关系参数
     */
    protected function getRelationshipParameters(array $relationship): array
    {
        $parameters = [
            'id' => [
                'type' => 'integer',
                'description' => '主记录ID',
                'required' => true,
                'in' => 'path',
            ],
        ];

        if (in_array($relationship['type'], ['hasMany', 'belongsToMany'])) {
            $parameters['page'] = ['type' => 'integer', 'description' => '页码', 'default' => 1];
            $parameters['per_page'] = ['type' => 'integer', 'description' => '每页数量', 'default' => 15];
        }

        return $parameters;
    }

    /**
     * 获取关联关系响应
     */
    protected function getRelationshipResponses(array $relationship): array
    {
        $responses = [
            '200' => ['description' => '成功'],
            '404' => ['description' => '记录不存在'],
            '401' => ['description' => '未授权'],
            '403' => ['description' => '权限不足'],
        ];

        return $responses;
    }

    /**
     * 获取资源模式定义
     */
    protected function getResourceSchema(array $tableInfo): array
    {
        $properties = [];

        foreach ($tableInfo['fields'] as $field) {
            $properties[$field['name']] = [
                'type' => $this->getSchemaType($field['type']),
                'description' => $field['comment'] ?: $field['name'],
            ];

            // 添加示例值
            if (!empty($field['example'])) {
                $properties[$field['name']]['example'] = $field['example'];
            }
        }

        return [
            'type' => 'object',
            'properties' => $properties,
        ];
    }

    /**
     * 获取参数类型
     */
    protected function getParameterType(string $dbType): string
    {
        $typeMap = [
            'int' => 'integer',
            'bigint' => 'integer',
            'tinyint' => 'integer',
            'decimal' => 'number',
            'float' => 'number',
            'double' => 'number',
            'varchar' => 'string',
            'text' => 'string',
            'date' => 'string',
            'datetime' => 'string',
            'timestamp' => 'string',
            'json' => 'object',
        ];

        return $typeMap[$dbType] ?? 'string';
    }

    /**
     * 获取模式类型
     */
    protected function getSchemaType(string $dbType): string
    {
        return $this->getParameterType($dbType);
    }

    /**
     * 获取附加参数
     */
    protected function getAttachParameters(array $relationship): array
    {
        return [
            'id' => [
                'type' => 'integer',
                'description' => '主记录ID',
                'required' => true,
                'in' => 'path',
            ],
            'related_ids' => [
                'type' => 'array',
                'items' => ['type' => 'integer'],
                'description' => '关联记录ID数组',
                'required' => true,
            ],
        ];
    }

    /**
     * 获取附加响应
     */
    protected function getAttachResponses(array $relationship): array
    {
        return [
            '200' => ['description' => '关联成功'],
            '404' => ['description' => '记录不存在'],
            '422' => ['description' => '关联失败'],
        ];
    }

    /**
     * 获取分离参数
     */
    protected function getDetachParameters(array $relationship): array
    {
        return [
            'id' => [
                'type' => 'integer',
                'description' => '主记录ID',
                'required' => true,
                'in' => 'path',
            ],
            'related_id' => [
                'type' => 'integer',
                'description' => '关联记录ID',
                'required' => true,
                'in' => 'path',
            ],
        ];
    }

    /**
     * 获取分离响应
     */
    protected function getDetachResponses(array $relationship): array
    {
        return [
            '200' => ['description' => '取消关联成功'],
            '404' => ['description' => '记录不存在'],
        ];
    }

    /**
     * 复数化
     */
    protected function pluralize(string $word): string
    {
        $rules = [
            '/y$/' => 'ies',
            '/f$/' => 'ves',
            '/s$/' => 'ses',
            '/$/' => 's',
        ];

        foreach ($rules as $pattern => $replacement) {
            if (preg_match($pattern, $word)) {
                return preg_replace($pattern, $replacement, $word);
            }
        }

        return $word . 's';
    }

    /**
     * 转换为驼峰命名
     */
    protected function toCamelCase(string $string): string
    {
        return lcfirst(str_replace(' ', '', ucwords(str_replace(['_', '-'], ' ', $string))));
    }

    /**
     * 获取增强的验证规则 (新增方法)
     */
    public function getEnhancedValidationRules(string $action, array $tableInfo): array
    {
        $rules = $this->getValidationRules($action, $tableInfo);
        $messages = [];
        $attributes = [];

        foreach ($tableInfo['fields'] as $field) {
            if (in_array($field['name'], ['id', 'created_at', 'updated_at', 'deleted_at'])) {
                continue;
            }

            $fieldName = $field['name'];
            $displayName = $this->getFieldDisplayName($field);

            // 设置字段显示名称
            $attributes[$fieldName] = $displayName;

            // 生成自定义错误消息
            if (isset($rules[$fieldName])) {
                $fieldRules = explode('|', $rules[$fieldName]);

                foreach ($fieldRules as $rule) {
                    $ruleType = explode(':', $rule)[0];

                    switch ($ruleType) {
                        case 'required':
                            $messages["{$fieldName}.required"] = "{$displayName}不能为空";
                            break;
                        case 'integer':
                            $messages["{$fieldName}.integer"] = "{$displayName}必须是整数";
                            break;
                        case 'numeric':
                            $messages["{$fieldName}.numeric"] = "{$displayName}必须是数字";
                            break;
                        case 'string':
                            $messages["{$fieldName}.string"] = "{$displayName}必须是字符串";
                            break;
                        case 'date':
                            $messages["{$fieldName}.date"] = "{$displayName}必须是有效的日期";
                            break;
                        case 'email':
                            $messages["{$fieldName}.email"] = "{$displayName}格式不正确";
                            break;
                        case 'unique':
                            $messages["{$fieldName}.unique"] = "{$displayName}已经存在";
                            break;
                        case 'max':
                            $maxValue = explode(':', $rule)[1] ?? '';
                            $messages["{$fieldName}.max"] = "{$displayName}不能超过{$maxValue}个字符";
                            break;
                        case 'min':
                            $minValue = explode(':', $rule)[1] ?? '';
                            $messages["{$fieldName}.min"] = "{$displayName}至少需要{$minValue}个字符";
                            break;
                    }
                }
            }

            // 添加智能验证规则
            $enhancedRules = $this->addSmartValidationRules($field, $action);
            if (!empty($enhancedRules)) {
                if (isset($rules[$fieldName])) {
                    $rules[$fieldName] .= '|' . implode('|', $enhancedRules);
                } else {
                    $rules[$fieldName] = implode('|', $enhancedRules);
                }
            }
        }

        return [
            'rules' => $rules,
            'messages' => $messages,
            'attributes' => $attributes,
            'validation_summary' => $this->generateValidationSummary($rules)
        ];
    }

    /**
     * 获取字段显示名称
     */
    protected function getFieldDisplayName(array $field): string
    {
        // 优先使用注释作为显示名称
        if (!empty($field['comment'])) {
            return $field['comment'];
        }

        // 根据字段名生成友好的显示名称
        $displayNames = [
            'name' => '名称',
            'title' => '标题',
            'content' => '内容',
            'description' => '描述',
            'email' => '邮箱',
            'phone' => '手机号',
            'mobile' => '手机号',
            'password' => '密码',
            'status' => '状态',
            'type' => '类型',
            'category' => '分类',
            'sort' => '排序',
            'order' => '排序',
            'url' => '链接',
            'image' => '图片',
            'avatar' => '头像',
            'cover' => '封面',
            'price' => '价格',
            'amount' => '金额',
            'quantity' => '数量',
            'stock' => '库存',
            'weight' => '重量',
            'address' => '地址',
            'city' => '城市',
            'province' => '省份',
            'country' => '国家',
            'zipcode' => '邮编',
            'birthday' => '生日',
            'gender' => '性别',
            'age' => '年龄',
        ];

        $fieldName = $field['name'];

        // 直接匹配
        if (isset($displayNames[$fieldName])) {
            return $displayNames[$fieldName];
        }

        // 模糊匹配
        foreach ($displayNames as $key => $value) {
            if (strpos($fieldName, $key) !== false) {
                return $value;
            }
        }

        // 转换下划线为中文
        return str_replace('_', '', $fieldName);
    }

    /**
     * 添加智能验证规则
     */
    protected function addSmartValidationRules(array $field, string $action): array
    {
        $rules = [];
        $fieldName = $field['name'];

        // 邮箱验证
        if ($this->isEmailField($fieldName)) {
            $rules[] = 'email';
        }

        // 手机号验证
        if ($this->isPhoneField($fieldName)) {
            $rules[] = 'regex:/^1[3-9]\d{9}$/';
        }

        // URL验证
        if ($this->isUrlField($fieldName)) {
            $rules[] = 'url';
        }

        // 密码验证
        if ($this->isPasswordField($fieldName)) {
            $rules[] = 'min:6';
            if ($action === 'store') {
                $rules[] = 'confirmed';
            }
        }

        // 状态字段验证
        if ($this->isStatusField($fieldName)) {
            $statusValues = $this->getStatusValues($field);
            if (!empty($statusValues)) {
                $rules[] = 'in:' . implode(',', $statusValues);
            }
        }

        // 数值范围验证
        if (in_array($field['type'], ['int', 'bigint', 'decimal', 'float'])) {
            if ($this->isPositiveField($fieldName)) {
                $rules[] = 'min:0';
            }
            if ($this->isPriceField($fieldName)) {
                $rules[] = 'min:0';
                $rules[] = 'max:999999.99';
            }
        }

        return $rules;
    }

    /**
     * 判断字段类型的辅助方法
     */
    protected function isEmailField(string $fieldName): bool
    {
        return in_array($fieldName, ['email', 'mail', 'email_address']);
    }

    protected function isPhoneField(string $fieldName): bool
    {
        return in_array($fieldName, ['phone', 'mobile', 'tel', 'telephone', 'phone_number', 'mobile_number']);
    }

    protected function isUrlField(string $fieldName): bool
    {
        return in_array($fieldName, ['url', 'link', 'website', 'homepage', 'site_url']);
    }

    protected function isPasswordField(string $fieldName): bool
    {
        return in_array($fieldName, ['password', 'passwd', 'pwd', 'pass']);
    }

    protected function isStatusField(string $fieldName): bool
    {
        return in_array($fieldName, ['status', 'state', 'is_active', 'is_enabled', 'is_published']);
    }

    protected function isPositiveField(string $fieldName): bool
    {
        return in_array($fieldName, ['quantity', 'stock', 'count', 'number', 'amount', 'price', 'weight', 'height', 'width', 'length']);
    }

    protected function isPriceField(string $fieldName): bool
    {
        return in_array($fieldName, ['price', 'amount', 'cost', 'fee', 'money', 'salary', 'wage']);
    }

    /**
     * 获取状态字段的可选值
     */
    protected function getStatusValues(array $field): array
    {
        $fieldName = $field['name'];

        $statusMappings = [
            'status' => [0, 1, 2], // 0:禁用, 1:启用, 2:待审核
            'state' => [0, 1],
            'is_active' => [0, 1],
            'is_enabled' => [0, 1],
            'is_published' => [0, 1],
        ];

        return $statusMappings[$fieldName] ?? [0, 1];
    }

    /**
     * 生成验证规则总结
     */
    protected function generateValidationSummary(array $rules): array
    {
        $summary = [
            'total_fields' => count($rules),
            'required_fields' => 0,
            'optional_fields' => 0,
            'validation_types' => [],
        ];

        foreach ($rules as $field => $rule) {
            if (strpos($rule, 'required') !== false) {
                $summary['required_fields']++;
            } else {
                $summary['optional_fields']++;
            }

            // 统计验证类型
            $types = explode('|', $rule);
            foreach ($types as $type) {
                $baseType = explode(':', $type)[0];
                if (!isset($summary['validation_types'][$baseType])) {
                    $summary['validation_types'][$baseType] = 0;
                }
                $summary['validation_types'][$baseType]++;
            }
        }

        return $summary;
    }
}
