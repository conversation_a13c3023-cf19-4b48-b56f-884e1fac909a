<?php

namespace app\common\services\curd\v2\analyzers;

/**
 * 版本管理分析器
 * 分析项目的版本管理需求并提供 Git 工作流集成
 */
class VersionAnalyzer
{
    protected array $gitWorkflows = [
        'gitflow' => 'Git Flow 工作流',
        'github_flow' => 'GitHub Flow 工作流',
        'gitlab_flow' => 'GitLab Flow 工作流',
        'simple' => '简单分支工作流',
    ];

    protected array $branchTypes = [
        'feature' => '功能分支',
        'bugfix' => '修复分支',
        'hotfix' => '热修复分支',
        'release' => '发布分支',
        'develop' => '开发分支',
        'master' => '主分支',
    ];

    /**
     * 分析版本管理需求
     */
    public function analyzeVersionRequirements(array $generatedCode, array $tableInfo, array $options = []): array
    {
        $versionRequirements = [];

        // 分析 Git 仓库状态
        $versionRequirements['repository'] = $this->analyzeRepositoryStatus();

        // 分析分支策略需求
        $versionRequirements['branching'] = $this->analyzeBranchingStrategy($generatedCode, $tableInfo);

        // 分析提交规范需求
        $versionRequirements['commits'] = $this->analyzeCommitConventions($generatedCode, $tableInfo);

        // 分析发布管理需求
        $versionRequirements['releases'] = $this->analyzeReleaseManagement($generatedCode, $tableInfo);

        // 分析代码审查需求
        $versionRequirements['code_review'] = $this->analyzeCodeReviewRequirements($generatedCode, $tableInfo);

        // 分析持续集成需求
        $versionRequirements['ci_cd'] = $this->analyzeCiCdRequirements($generatedCode, $tableInfo);

        return [
            'requirements' => $versionRequirements,
            'workflow_recommendation' => $this->recommendWorkflow($versionRequirements),
            'integration_plan' => $this->generateIntegrationPlan($versionRequirements),
            'automation_suggestions' => $this->generateAutomationSuggestions($versionRequirements),
        ];
    }

    /**
     * 分析仓库状态
     */
    protected function analyzeRepositoryStatus(): array
    {
        $status = [
            'is_git_repo' => false,
            'has_remote' => false,
            'current_branch' => null,
            'uncommitted_changes' => false,
            'branch_count' => 0,
            'tag_count' => 0,
            'last_commit' => null,
        ];

        try {
            // 检查是否为 Git 仓库
            if (is_dir('.git') || $this->findGitDir()) {
                $status['is_git_repo'] = true;

                // 获取当前分支
                $currentBranch = $this->executeGitCommand('git rev-parse --abbrev-ref HEAD');
                $status['current_branch'] = trim($currentBranch);

                // 检查是否有远程仓库
                $remotes = $this->executeGitCommand('git remote');
                $status['has_remote'] = !empty(trim($remotes));

                // 检查未提交的更改
                $gitStatus = $this->executeGitCommand('git status --porcelain');
                $status['uncommitted_changes'] = !empty(trim($gitStatus));

                // 获取分支数量
                $branches = $this->executeGitCommand('git branch -a');
                $status['branch_count'] = count(array_filter(explode("\n", $branches)));

                // 获取标签数量
                $tags = $this->executeGitCommand('git tag');
                $status['tag_count'] = count(array_filter(explode("\n", $tags)));

                // 获取最后一次提交
                $lastCommit = $this->executeGitCommand('git log -1 --pretty=format:"%h %s %an %ad" --date=short');
                $status['last_commit'] = trim($lastCommit);
            }
        } catch (\Exception $e) {
            // Git 命令执行失败，保持默认状态
        }

        return $status;
    }

    /**
     * 分析分支策略需求
     */
    protected function analyzeBranchingStrategy(array $generatedCode, array $tableInfo): array
    {
        $strategy = [
            'recommended_workflow' => 'gitflow',
            'branch_naming' => [],
            'protection_rules' => [],
            'merge_strategy' => 'merge_commit',
        ];

        $modelName = $this->getModelName($tableInfo['name']);

        // 推荐分支命名规范
        $strategy['branch_naming'] = [
            'feature' => "feature/{$modelName}-功能描述",
            'bugfix' => "bugfix/{$modelName}-问题描述",
            'hotfix' => "hotfix/{$modelName}-紧急修复",
            'release' => "release/v版本号",
        ];

        // 分支保护规则
        $strategy['protection_rules'] = [
            'master' => [
                'require_pull_request' => true,
                'require_status_checks' => true,
                'require_code_review' => true,
                'dismiss_stale_reviews' => true,
                'restrict_pushes' => true,
            ],
            'develop' => [
                'require_pull_request' => true,
                'require_status_checks' => true,
                'require_code_review' => false,
                'dismiss_stale_reviews' => false,
                'restrict_pushes' => false,
            ],
        ];

        // 合并策略
        $strategy['merge_strategy'] = 'squash_merge'; // 推荐压缩合并保持历史清洁

        return $strategy;
    }

    /**
     * 分析提交规范需求
     */
    protected function analyzeCommitConventions(array $generatedCode, array $tableInfo): array
    {
        $conventions = [
            'format' => 'conventional_commits',
            'types' => [],
            'scopes' => [],
            'examples' => [],
            'validation_rules' => [],
        ];

        $modelName = strtolower($this->getModelName($tableInfo['name']));

        // 提交类型
        $conventions['types'] = [
            'feat' => '新功能',
            'fix' => '修复',
            'docs' => '文档',
            'style' => '格式',
            'refactor' => '重构',
            'test' => '测试',
            'chore' => '构建',
            'perf' => '性能',
            'ci' => '持续集成',
        ];

        // 作用域
        $conventions['scopes'] = [
            $modelName => "{$modelName}模块",
            'api' => 'API接口',
            'ui' => '用户界面',
            'db' => '数据库',
            'config' => '配置',
            'test' => '测试',
            'docs' => '文档',
        ];

        // 提交示例
        $conventions['examples'] = [
            "feat({$modelName}): 添加{$modelName}列表查询功能",
            "fix({$modelName}): 修复{$modelName}创建时的验证问题",
            "docs({$modelName}): 更新{$modelName}API文档",
            "test({$modelName}): 添加{$modelName}单元测试",
            "refactor({$modelName}): 重构{$modelName}查询逻辑",
        ];

        // 验证规则
        $conventions['validation_rules'] = [
            'max_length' => 72,
            'min_length' => 10,
            'require_type' => true,
            'require_scope' => false,
            'require_description' => true,
            'allow_breaking_change' => true,
        ];

        return $conventions;
    }

    /**
     * 分析发布管理需求
     */
    protected function analyzeReleaseManagement(array $generatedCode, array $tableInfo): array
    {
        $releases = [
            'versioning_scheme' => 'semantic',
            'release_branches' => true,
            'changelog_generation' => true,
            'tag_format' => 'v{version}',
            'release_notes' => true,
        ];

        $modelName = $this->getModelName($tableInfo['name']);

        // 版本号方案
        $releases['versioning_scheme'] = 'semantic'; // 语义化版本

        // 发布流程
        $releases['release_process'] = [
            'create_release_branch' => "从 develop 创建 release/{$modelName}-v{version} 分支",
            'update_version' => '更新版本号和变更日志',
            'testing' => '执行完整测试套件',
            'merge_to_master' => '合并到 master 分支',
            'create_tag' => '创建版本标签',
            'merge_back_to_develop' => '合并回 develop 分支',
            'deploy' => '部署到生产环境',
        ];

        // 变更日志格式
        $releases['changelog_format'] = [
            'sections' => [
                'Added' => '新增功能',
                'Changed' => '变更内容',
                'Deprecated' => '废弃功能',
                'Removed' => '移除功能',
                'Fixed' => '修复问题',
                'Security' => '安全更新',
            ],
            'auto_generation' => true,
            'commit_grouping' => true,
        ];

        return $releases;
    }

    /**
     * 分析代码审查需求
     */
    protected function analyzeCodeReviewRequirements(array $generatedCode, array $tableInfo): array
    {
        $codeReview = [
            'required_reviewers' => 1,
            'review_checklist' => [],
            'automated_checks' => [],
            'review_templates' => [],
        ];

        // 审查清单
        $codeReview['review_checklist'] = [
            'code_quality' => '代码质量检查',
            'test_coverage' => '测试覆盖率检查',
            'security_review' => '安全审查',
            'performance_review' => '性能审查',
            'documentation' => '文档完整性检查',
            'api_compatibility' => 'API兼容性检查',
        ];

        // 自动化检查
        $codeReview['automated_checks'] = [
            'lint' => '代码风格检查',
            'test' => '自动化测试',
            'security_scan' => '安全扫描',
            'dependency_check' => '依赖检查',
            'build' => '构建检查',
        ];

        // 审查模板
        $codeReview['review_templates'] = [
            'feature' => '功能审查模板',
            'bugfix' => '修复审查模板',
            'security' => '安全审查模板',
            'performance' => '性能审查模板',
        ];

        return $codeReview;
    }

    /**
     * 分析持续集成需求
     */
    protected function analyzeCiCdRequirements(array $generatedCode, array $tableInfo): array
    {
        $cicd = [
            'platforms' => ['github_actions', 'gitlab_ci', 'jenkins'],
            'triggers' => [],
            'stages' => [],
            'environments' => [],
        ];

        // 触发条件
        $cicd['triggers'] = [
            'push' => '代码推送',
            'pull_request' => '拉取请求',
            'tag' => '标签创建',
            'schedule' => '定时触发',
        ];

        // 构建阶段
        $cicd['stages'] = [
            'install' => '依赖安装',
            'lint' => '代码检查',
            'test' => '运行测试',
            'build' => '构建应用',
            'security' => '安全扫描',
            'deploy' => '部署应用',
        ];

        // 部署环境
        $cicd['environments'] = [
            'development' => '开发环境',
            'staging' => '预发布环境',
            'production' => '生产环境',
        ];

        return $cicd;
    }

    /**
     * 推荐工作流
     */
    protected function recommendWorkflow(array $requirements): array
    {
        $recommendation = [
            'workflow' => 'gitflow',
            'reasons' => [],
            'setup_steps' => [],
        ];

        // 根据项目特点推荐工作流
        if ($requirements['repository']['branch_count'] <= 2) {
            $recommendation['workflow'] = 'github_flow';
            $recommendation['reasons'][] = '项目规模较小，适合简单工作流';
        } else {
            $recommendation['workflow'] = 'gitflow';
            $recommendation['reasons'][] = '项目规模较大，需要完整的分支管理';
        }

        // 设置步骤
        $recommendation['setup_steps'] = $this->getWorkflowSetupSteps($recommendation['workflow']);

        return $recommendation;
    }

    /**
     * 生成集成计划
     */
    protected function generateIntegrationPlan(array $requirements): array
    {
        $plan = [
            'phases' => [],
            'timeline' => [],
            'deliverables' => [],
        ];

        // 集成阶段
        $plan['phases'] = [
            'phase1' => [
                'name' => '基础设置',
                'tasks' => ['初始化Git仓库', '设置分支保护', '配置提交规范'],
                'duration' => '1天',
            ],
            'phase2' => [
                'name' => '工作流配置',
                'tasks' => ['配置CI/CD', '设置代码审查', '创建发布流程'],
                'duration' => '2天',
            ],
            'phase3' => [
                'name' => '自动化集成',
                'tasks' => ['配置自动化测试', '设置自动部署', '监控集成'],
                'duration' => '1天',
            ],
        ];

        return $plan;
    }

    /**
     * 生成自动化建议
     */
    protected function generateAutomationSuggestions(array $requirements): array
    {
        $suggestions = [
            'git_hooks' => [],
            'ci_cd_templates' => [],
            'automation_scripts' => [],
        ];

        // Git 钩子建议
        $suggestions['git_hooks'] = [
            'pre-commit' => '提交前代码检查',
            'commit-msg' => '提交信息格式验证',
            'pre-push' => '推送前测试运行',
        ];

        // CI/CD 模板
        $suggestions['ci_cd_templates'] = [
            'github_actions' => 'GitHub Actions 工作流模板',
            'gitlab_ci' => 'GitLab CI 配置模板',
            'jenkins' => 'Jenkins 流水线模板',
        ];

        // 自动化脚本
        $suggestions['automation_scripts'] = [
            'release_script' => '自动化发布脚本',
            'changelog_generator' => '变更日志生成脚本',
            'version_bumper' => '版本号更新脚本',
        ];

        return $suggestions;
    }

    /**
     * 执行 Git 命令
     */
    protected function executeGitCommand(string $command): string
    {
        $output = '';
        $returnCode = 0;
        
        exec($command . ' 2>&1', $outputArray, $returnCode);
        
        if ($returnCode === 0) {
            $output = implode("\n", $outputArray);
        }
        
        return $output;
    }

    /**
     * 查找 Git 目录
     */
    protected function findGitDir(): bool
    {
        $currentDir = getcwd();
        
        while ($currentDir !== dirname($currentDir)) {
            if (is_dir($currentDir . '/.git')) {
                return true;
            }
            $currentDir = dirname($currentDir);
        }
        
        return false;
    }

    /**
     * 获取工作流设置步骤
     */
    protected function getWorkflowSetupSteps(string $workflow): array
    {
        $steps = [
            'gitflow' => [
                '安装 git-flow 工具',
                '初始化 git flow',
                '设置主分支和开发分支',
                '配置分支保护规则',
                '创建第一个功能分支',
            ],
            'github_flow' => [
                '设置主分支保护',
                '配置拉取请求模板',
                '设置状态检查',
                '配置自动合并规则',
            ],
        ];

        return $steps[$workflow] ?? [];
    }

    /**
     * 获取模型名称
     */
    protected function getModelName(string $tableName): string
    {
        $name = preg_replace('/^[a-z]+_/', '', $tableName);
        return str_replace(' ', '', ucwords(str_replace('_', ' ', $name)));
    }
}
