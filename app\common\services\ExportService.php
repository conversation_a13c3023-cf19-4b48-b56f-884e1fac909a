<?php

namespace app\common\services;

use app\common\exceptions\FileException;
use app\common\services\tool\CommonTool;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use support\Db;
use support\Response;

/**
 * 数据导出服务
 */
class ExportService
{
    protected array $defaultOptions = [
        'exclude_fields' => ['delete_time', 'update_time'],
        'limit' => 100000,
        'filename' => '数据导出',
        'format' => 'xlsx',
    ];

    /**
     * 导出模型数据
     */
    public function exportModel($model, array $where = [], array $options = []): Response
    {
        $options = array_merge($this->defaultOptions, $options);
        
        // 获取表结构信息
        $tableInfo = $this->getTableInfo($model);
        
        // 获取数据
        $data = $this->getData($model, $where, $options['limit']);
        
        if (empty($data)) {
            throw new FileException('暂无数据可导出');
        }
        
        // 生成Excel文件
        return $this->generateExcel($data, $tableInfo, $options);
    }

    /**
     * 获取表结构信息
     */
    protected function getTableInfo($model): array
    {
        $tableName = $model->getTable();
        $tableName = CommonTool::humpToLine(lcfirst($tableName));
        $prefix = config('database.connections.mysql.prefix');
        
        $columns = Db::select("SHOW FULL COLUMNS FROM {$prefix}{$tableName}");
        
        $tableInfo = [];
        foreach ($columns as $column) {
            $tableInfo[] = [
                'field' => $column->Field,
                'comment' => $column->Comment ?: $column->Field,
                'type' => $column->Type,
            ];
        }
        
        return $tableInfo;
    }

    /**
     * 获取数据
     */
    protected function getData($model, array $where, int $limit): array
    {
        return $model->where($where)
            ->limit($limit)
            ->orderByDesc('id')
            ->get()
            ->toArray();
    }

    /**
     * 生成Excel文件
     */
    protected function generateExcel(array $data, array $tableInfo, array $options): Response
    {
        try {
            $spreadsheet = new Spreadsheet();
            $sheet = $spreadsheet->getActiveSheet();
            
            // 过滤字段
            $fields = $this->filterFields($tableInfo, $options['exclude_fields']);
            
            // 设置表头
            $this->setHeaders($sheet, $fields);
            
            // 填充数据
            $this->fillData($sheet, $data, $fields);
            
            // 保存文件
            $filePath = $this->saveFile($spreadsheet, $options['filename']);
            
            // 返回下载响应
            return response()->download($filePath, $options['filename'] . '.xlsx');
            
        } catch (\Exception $e) {
            throw new FileException('Excel生成失败: ' . $e->getMessage());
        }
    }

    /**
     * 过滤字段
     */
    protected function filterFields(array $tableInfo, array $excludeFields): array
    {
        return array_filter($tableInfo, function($field) use ($excludeFields) {
            return !in_array($field['field'], $excludeFields);
        });
    }

    /**
     * 设置表头
     */
    protected function setHeaders($sheet, array $fields): void
    {
        $columnIndex = 1;
        foreach ($fields as $field) {
            $columnLetter = $this->getColumnLetter($columnIndex);
            $sheet->setCellValue($columnLetter . '1', $field['comment']);
            $columnIndex++;
        }
    }

    /**
     * 填充数据
     */
    protected function fillData($sheet, array $data, array $fields): void
    {
        $rowIndex = 2;
        foreach ($data as $row) {
            $columnIndex = 1;
            foreach ($fields as $field) {
                $columnLetter = $this->getColumnLetter($columnIndex);
                $value = $row[$field['field']] ?? '';
                
                // 处理特殊数据类型
                $value = $this->formatCellValue($value, $field['type']);
                
                $sheet->setCellValue($columnLetter . $rowIndex, $value);
                $columnIndex++;
            }
            $rowIndex++;
        }
    }

    /**
     * 格式化单元格值
     */
    protected function formatCellValue($value, string $type): string
    {
        // 处理时间戳
        if (is_numeric($value) && strlen($value) === 10 && $value > 946684800) {
            return date('Y-m-d H:i:s', $value);
        }
        
        // 处理JSON数据
        if (is_array($value) || is_object($value)) {
            return json_encode($value, JSON_UNESCAPED_UNICODE);
        }
        
        // 在数值前添加制表符，防止Excel自动转换格式
        if (is_numeric($value) && strlen($value) > 10) {
            return $value . "\t";
        }
        
        return (string)$value;
    }

    /**
     * 保存文件
     */
    protected function saveFile(Spreadsheet $spreadsheet, string $filename): string
    {
        $writer = new Xlsx($spreadsheet);
        $filePath = runtime_path() . '/' . $filename . '_' . date('YmdHis') . '.xlsx';
        
        $writer->save($filePath);
        
        return $filePath;
    }

    /**
     * 获取Excel列字母
     */
    protected function getColumnLetter(int $columnIndex): string
    {
        $letters = '';
        while ($columnIndex > 0) {
            $columnIndex--;
            $letters = chr(65 + ($columnIndex % 26)) . $letters;
            $columnIndex = intval($columnIndex / 26);
        }
        return $letters;
    }

    /**
     * 导出CSV格式
     */
    public function exportCsv(array $data, array $headers, string $filename): Response
    {
        $filePath = runtime_path() . '/' . $filename . '_' . date('YmdHis') . '.csv';
        
        $file = fopen($filePath, 'w');
        
        // 添加BOM头，解决中文乱码
        fwrite($file, "\xEF\xBB\xBF");
        
        // 写入表头
        fputcsv($file, array_column($headers, 'comment'));
        
        // 写入数据
        foreach ($data as $row) {
            $csvRow = [];
            foreach ($headers as $header) {
                $csvRow[] = $row[$header['field']] ?? '';
            }
            fputcsv($file, $csvRow);
        }
        
        fclose($file);
        
        return response()->download($filePath, $filename . '.csv');
    }
}
