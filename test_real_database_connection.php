<?php
/**
 * 测试真实数据库连接并提供解决方案
 */

echo "=== 数据库连接诊断工具 ===\n\n";

// 检查 MySQL 服务状态
echo "1. 检查 MySQL 服务状态\n";

// Windows 系统检查服务
$services = ['MySQL', 'MySQL80', 'MySQL57', 'MySQL56', 'MYSQL', 'mysqld'];
$mysqlRunning = false;

foreach ($services as $service) {
    $output = [];
    $returnCode = 0;
    exec("sc query {$service} 2>nul", $output, $returnCode);
    
    if ($returnCode === 0) {
        $serviceInfo = implode("\n", $output);
        if (strpos($serviceInfo, 'RUNNING') !== false) {
            echo "   ✅ MySQL 服务 '{$service}' 正在运行\n";
            $mysqlRunning = true;
            break;
        } elseif (strpos($serviceInfo, 'STOPPED') !== false) {
            echo "   ⚠️  MySQL 服务 '{$service}' 已停止\n";
            echo "      尝试启动: net start {$service}\n";
        }
    }
}

if (!$mysqlRunning) {
    echo "   ❌ 未找到运行中的 MySQL 服务\n";
    echo "   💡 解决方案:\n";
    echo "      1. 安装 MySQL: https://dev.mysql.com/downloads/mysql/\n";
    echo "      2. 启动 MySQL 服务: net start mysql\n";
    echo "      3. 使用 XAMPP/WAMP 等集成环境\n";
    echo "      4. 使用 Docker: docker run -d -p 3306:3306 -e MYSQL_ROOT_PASSWORD=root mysql:8.0\n\n";
}

// 检查端口占用
echo "2. 检查端口 3306 占用情况\n";
$output = [];
exec("netstat -an | findstr :3306", $output);

if (!empty($output)) {
    echo "   ✅ 端口 3306 有服务监听:\n";
    foreach ($output as $line) {
        echo "      {$line}\n";
    }
} else {
    echo "   ❌ 端口 3306 没有服务监听\n";
    echo "   💡 可能的原因:\n";
    echo "      1. MySQL 服务未启动\n";
    echo "      2. MySQL 配置了其他端口\n";
    echo "      3. 防火墙阻止了连接\n\n";
}

// 尝试连接测试
echo "\n3. 尝试连接测试\n";

$testConfigs = [
    // 常见的 MySQL 配置
    ['host' => '127.0.0.1', 'port' => 3306, 'user' => 'root', 'pass' => ''],
    ['host' => '127.0.0.1', 'port' => 3306, 'user' => 'root', 'pass' => 'root'],
    ['host' => '127.0.0.1', 'port' => 3306, 'user' => 'root', 'pass' => '123456'],
    ['host' => '127.0.0.1', 'port' => 3306, 'user' => 'root', 'pass' => 'password'],
    ['host' => '127.0.0.1', 'port' => 3306, 'user' => 'root', 'pass' => 'admin'],
    ['host' => 'localhost', 'port' => 3306, 'user' => 'root', 'pass' => ''],
    ['host' => 'localhost', 'port' => 3306, 'user' => 'root', 'pass' => 'root'],
    
    // XAMPP 默认配置
    ['host' => '127.0.0.1', 'port' => 3306, 'user' => 'root', 'pass' => ''],
    ['host' => 'localhost', 'port' => 3306, 'user' => 'root', 'pass' => ''],
    
    // WAMP 默认配置
    ['host' => '127.0.0.1', 'port' => 3306, 'user' => 'root', 'pass' => ''],
    ['host' => 'localhost', 'port' => 3306, 'user' => 'root', 'pass' => ''],
    
    // 其他常见端口
    ['host' => '127.0.0.1', 'port' => 3307, 'user' => 'root', 'pass' => ''],
    ['host' => '127.0.0.1', 'port' => 3308, 'user' => 'root', 'pass' => ''],
];

$workingConfig = null;

foreach ($testConfigs as $config) {
    $passDisplay = $config['pass'] === '' ? '(空密码)' : $config['pass'];
    echo "   测试: {$config['user']}@{$config['host']}:{$config['port']} / {$passDisplay}\n";
    
    try {
        $dsn = "mysql:host={$config['host']};port={$config['port']};charset=utf8mb4";
        $pdo = new PDO($dsn, $config['user'], $config['pass'], [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_TIMEOUT => 3
        ]);
        
        echo "      ✅ 连接成功!\n";
        
        // 获取 MySQL 版本
        $stmt = $pdo->query("SELECT VERSION() as version");
        $version = $stmt->fetch(PDO::FETCH_ASSOC);
        echo "      📊 MySQL 版本: {$version['version']}\n";
        
        // 获取数据库列表
        $stmt = $pdo->query("SHOW DATABASES");
        $databases = $stmt->fetchAll(PDO::FETCH_COLUMN);
        echo "      📋 数据库: " . implode(', ', $databases) . "\n";
        
        $workingConfig = $config;
        $workingConfig['databases'] = $databases;
        break;
        
    } catch (PDOException $e) {
        if (strpos($e->getMessage(), 'Access denied') !== false) {
            echo "      ❌ 认证失败\n";
        } elseif (strpos($e->getMessage(), 'Connection refused') !== false) {
            echo "      ❌ 连接被拒绝 (服务未启动)\n";
        } elseif (strpos($e->getMessage(), 'timed out') !== false) {
            echo "      ❌ 连接超时\n";
        } else {
            echo "      ❌ 错误: " . substr($e->getMessage(), 0, 50) . "...\n";
        }
    }
}

if ($workingConfig) {
    echo "\n🎉 找到可用的数据库连接!\n\n";
    
    echo "📋 连接信息:\n";
    echo "   主机: {$workingConfig['host']}\n";
    echo "   端口: {$workingConfig['port']}\n";
    echo "   用户名: {$workingConfig['user']}\n";
    echo "   密码: " . ($workingConfig['pass'] === '' ? '(空密码)' : $workingConfig['pass']) . "\n";
    echo "   可用数据库: " . implode(', ', $workingConfig['databases']) . "\n\n";
    
    echo "🔧 现在可以运行以下命令来设置数据库:\n";
    echo "   php auto_setup_database.php\n";
    echo "   或者手动更新 config/database.php 文件\n\n";
    
} else {
    echo "\n❌ 无法连接到任何 MySQL 数据库\n\n";
    
    echo "🔧 解决方案:\n\n";
    
    echo "方案 1: 安装并配置 MySQL\n";
    echo "   1. 下载 MySQL: https://dev.mysql.com/downloads/mysql/\n";
    echo "   2. 安装并设置 root 密码\n";
    echo "   3. 启动 MySQL 服务: net start mysql\n\n";
    
    echo "方案 2: 使用 XAMPP (推荐)\n";
    echo "   1. 下载 XAMPP: https://www.apachefriends.org/\n";
    echo "   2. 安装并启动 Apache 和 MySQL\n";
    echo "   3. 默认用户名: root, 密码: (空)\n\n";
    
    echo "方案 3: 使用 Docker\n";
    echo "   1. 安装 Docker Desktop\n";
    echo "   2. 运行: docker run -d -p 3306:3306 -e MYSQL_ROOT_PASSWORD=root --name mysql mysql:8.0\n";
    echo "   3. 用户名: root, 密码: root\n\n";
    
    echo "方案 4: 重置 MySQL root 密码\n";
    echo "   1. 停止 MySQL 服务: net stop mysql\n";
    echo "   2. 以安全模式启动: mysqld --skip-grant-tables\n";
    echo "   3. 连接并重置密码: mysql -u root\n";
    echo "   4. 执行: ALTER USER 'root'@'localhost' IDENTIFIED BY 'newpassword';\n\n";
    
    echo "方案 5: 使用模拟数据 (临时解决)\n";
    echo "   当前系统已支持模拟数据模式\n";
    echo "   可以在没有真实数据库的情况下演示功能\n";
    echo "   访问: http://localhost:8787/admin/system/curdgeneratev2\n\n";
}

echo "4. 系统环境信息\n";
echo "   PHP 版本: " . PHP_VERSION . "\n";
echo "   操作系统: " . PHP_OS . "\n";
echo "   PDO MySQL 扩展: " . (extension_loaded('pdo_mysql') ? '✅ 已安装' : '❌ 未安装') . "\n";

if (!extension_loaded('pdo_mysql')) {
    echo "   💡 安装 PDO MySQL 扩展:\n";
    echo "      1. 编辑 php.ini 文件\n";
    echo "      2. 取消注释: extension=pdo_mysql\n";
    echo "      3. 重启 web 服务器\n";
}

echo "\n=== 诊断完成 ===\n";

if ($workingConfig) {
    echo "✅ 数据库连接可用，可以继续设置真实数据库\n";
} else {
    echo "⚠️  数据库连接不可用，建议使用模拟数据模式进行功能演示\n";
    echo "   或按照上述解决方案安装配置数据库\n";
}
?>
