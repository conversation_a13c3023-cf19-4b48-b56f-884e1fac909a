@extends('admin.layouts.app')

@section('title', '数据库连接测试')

@section('content')
<div class="layui-fluid">
    <div class="layui-row layui-col-space15">
        <div class="layui-col-md12">
            <div class="layui-card">
                <div class="layui-card-header">
                    <h3>🔧 数据库连接测试</h3>
                    <p style="margin: 5px 0 0 0; color: #666;">测试多数据库连接配置和 CURD 生成器集成</p>
                </div>
                <div class="layui-card-body">
                    
                    <!-- 连接测试区域 -->
                    <div class="layui-row layui-col-space15">
                        <div class="layui-col-md6">
                            <fieldset class="layui-elem-field">
                                <legend>数据库连接测试</legend>
                                <div class="layui-field-box">
                                    <div class="layui-form">
                                        <div class="layui-form-item">
                                            <label class="layui-form-label">选择连接</label>
                                            <div class="layui-input-block">
                                                <select id="connectionSelect" lay-filter="connectionSelect">
                                                    <option value="mysql">默认连接 (mysql)</option>
                                                    <option value="mysql_read">读库连接 (mysql_read)</option>
                                                    <option value="mysql_second">第二数据库 (mysql_second)</option>
                                                    <option value="mysql_log">日志数据库 (mysql_log)</option>
                                                    <option value="mysql_cache">缓存数据库 (mysql_cache)</option>
                                                    <option value="mysql_without_prefix">无前缀连接 (mysql_without_prefix)</option>
                                                    <option value="pgsql">PostgreSQL (pgsql)</option>
                                                    <option value="sqlite">SQLite (sqlite)</option>
                                                    <option value="sqlsrv">SQL Server (sqlsrv)</option>
                                                </select>
                                            </div>
                                        </div>
                                        
                                        <div class="layui-form-item">
                                            <div class="layui-input-block">
                                                <button type="button" class="layui-btn" id="testConnectionBtn">
                                                    <i class="layui-icon layui-icon-link"></i> 测试连接
                                                </button>
                                                <button type="button" class="layui-btn layui-btn-normal" id="loadTablesBtn">
                                                    <i class="layui-icon layui-icon-table"></i> 加载数据表
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </fieldset>
                        </div>
                        
                        <div class="layui-col-md6">
                            <fieldset class="layui-elem-field">
                                <legend>表结构分析测试</legend>
                                <div class="layui-field-box">
                                    <div class="layui-form">
                                        <div class="layui-form-item">
                                            <label class="layui-form-label">选择数据表</label>
                                            <div class="layui-input-block">
                                                <select id="tableSelect" lay-search>
                                                    <option value="">请先加载数据表</option>
                                                </select>
                                            </div>
                                        </div>
                                        
                                        <div class="layui-form-item">
                                            <label class="layui-form-label">表前缀</label>
                                            <div class="layui-input-block">
                                                <input type="text" id="tablePrefix" placeholder="可选，如: ea8_" class="layui-input">
                                            </div>
                                        </div>
                                        
                                        <div class="layui-form-item">
                                            <div class="layui-input-block">
                                                <button type="button" class="layui-btn layui-btn-danger" id="analyzeTableBtn">
                                                    <i class="layui-icon layui-icon-search"></i> 分析表结构
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </fieldset>
                        </div>
                    </div>
                    
                    <!-- 结果显示区域 -->
                    <div class="layui-row layui-col-space15" style="margin-top: 20px;">
                        <div class="layui-col-md12">
                            <div class="layui-tab" lay-filter="resultTabs">
                                <ul class="layui-tab-title">
                                    <li class="layui-this">连接测试结果</li>
                                    <li>数据表列表</li>
                                    <li>表结构分析</li>
                                    <li>使用说明</li>
                                </ul>
                                <div class="layui-tab-content">
                                    <div class="layui-tab-item layui-show">
                                        <div id="connectionResult" style="min-height: 300px;">
                                            <div class="layui-text" style="text-align: center; padding: 50px; color: #999;">
                                                <i class="layui-icon layui-icon-link" style="font-size: 48px;"></i>
                                                <p>点击"测试连接"按钮开始测试数据库连接</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="layui-tab-item">
                                        <div id="tablesResult" style="min-height: 300px;">
                                            <div class="layui-text" style="text-align: center; padding: 50px; color: #999;">
                                                <i class="layui-icon layui-icon-table" style="font-size: 48px;"></i>
                                                <p>点击"加载数据表"按钮获取数据表列表</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="layui-tab-item">
                                        <div id="analyzeResult" style="min-height: 300px;">
                                            <div class="layui-text" style="text-align: center; padding: 50px; color: #999;">
                                                <i class="layui-icon layui-icon-search" style="font-size: 48px;"></i>
                                                <p>选择数据表后点击"分析表结构"按钮</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="layui-tab-item">
                                        <div style="padding: 20px; line-height: 1.8;">
                                            <h3>🎯 测试目标</h3>
                                            <p>验证多数据库 CURD 生成器的以下功能：</p>
                                            <ul>
                                                <li>多数据库连接配置的正确性</li>
                                                <li>不同数据库连接的表列表获取</li>
                                                <li>跨数据库的表结构分析</li>
                                                <li>CURD 生成器的数据库连接选择功能</li>
                                            </ul>
                                            
                                            <h3>📋 测试步骤</h3>
                                            <ol>
                                                <li><strong>连接测试：</strong>选择不同的数据库连接，点击"测试连接"验证连接状态</li>
                                                <li><strong>表列表：</strong>点击"加载数据表"获取对应数据库的所有表</li>
                                                <li><strong>表分析：</strong>选择具体的表，设置表前缀（可选），点击"分析表结构"</li>
                                                <li><strong>集成测试：</strong>访问 <a href="/admin/system/curdgeneratev2" target="_blank">CURD 生成器 V2</a> 验证数据库选择功能</li>
                                            </ol>
                                            
                                            <h3>⚠️ 注意事项</h3>
                                            <ul>
                                                <li>确保对应的数据库服务正在运行</li>
                                                <li>检查数据库连接配置是否正确</li>
                                                <li>某些连接可能需要先创建对应的数据库</li>
                                                <li>如果连接失败，请检查 <code>config/database.php</code> 配置文件</li>
                                            </ul>
                                            
                                            <h3>🔧 支持的数据库类型</h3>
                                            <div class="layui-row layui-col-space10">
                                                <div class="layui-col-md3">
                                                    <div class="layui-card">
                                                        <div class="layui-card-body" style="text-align: center;">
                                                            <i class="layui-icon layui-icon-database" style="font-size: 24px; color: #1E9FFF;"></i>
                                                            <p>MySQL</p>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="layui-col-md3">
                                                    <div class="layui-card">
                                                        <div class="layui-card-body" style="text-align: center;">
                                                            <i class="layui-icon layui-icon-database" style="font-size: 24px; color: #336791;"></i>
                                                            <p>PostgreSQL</p>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="layui-col-md3">
                                                    <div class="layui-card">
                                                        <div class="layui-card-body" style="text-align: center;">
                                                            <i class="layui-icon layui-icon-database" style="font-size: 24px; color: #003B57;"></i>
                                                            <p>SQLite</p>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="layui-col-md3">
                                                    <div class="layui-card">
                                                        <div class="layui-card-body" style="text-align: center;">
                                                            <i class="layui-icon layui-icon-database" style="font-size: 24px; color: #CC2927;"></i>
                                                            <p>SQL Server</p>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
layui.use(['form', 'element', 'layer'], function(){
    var form = layui.form;
    var element = layui.element;
    var layer = layui.layer;
    
    let currentConnection = 'mysql';
    let availableTables = [];
    
    // 测试数据库连接
    $('#testConnectionBtn').on('click', function() {
        const connection = $('#connectionSelect').val();
        const btn = $(this);
        const originalText = btn.html();
        
        btn.html('<i class="layui-icon layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop"></i> 测试中...');
        btn.prop('disabled', true);
        
        $.post('/admin/system/databasetest', {
            connection: connection
        }, function(res) {
            btn.html(originalText);
            btn.prop('disabled', false);
            
            if (res.code === 0) {
                $('#connectionResult').html(generateConnectionResultHtml(res.data));
                layer.msg('连接测试成功', {icon: 1});
            } else {
                $('#connectionResult').html(`
                    <div class="layui-card">
                        <div class="layui-card-header" style="background: #FF5722; color: white;">
                            <i class="layui-icon layui-icon-close"></i> 连接测试失败
                        </div>
                        <div class="layui-card-body">
                            <p><strong>连接：</strong>${connection}</p>
                            <p><strong>错误：</strong>${res.msg}</p>
                            <p><strong>时间：</strong>${new Date().toLocaleString()}</p>
                        </div>
                    </div>
                `);
                layer.msg('连接测试失败: ' + res.msg, {icon: 2});
            }
        }).fail(function() {
            btn.html(originalText);
            btn.prop('disabled', false);
            layer.msg('请求失败', {icon: 2});
        });
    });
    
    // 加载数据表列表
    $('#loadTablesBtn').on('click', function() {
        const connection = $('#connectionSelect').val();
        const btn = $(this);
        const originalText = btn.html();
        
        currentConnection = connection;
        btn.html('<i class="layui-icon layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop"></i> 加载中...');
        btn.prop('disabled', true);
        
        $.post('/admin/system/curdgeneratev2', {
            action: 'get_tables',
            connection: connection
        }, function(res) {
            btn.html(originalText);
            btn.prop('disabled', false);
            
            if (res.code === 0) {
                availableTables = res.data;
                $('#tablesResult').html(generateTablesResultHtml(res.data, connection));
                
                // 更新表选择下拉列表
                let tableOptions = '<option value="">请选择数据表</option>';
                res.data.forEach(function(table) {
                    tableOptions += `<option value="${table.name}">${table.name} (${table.comment})</option>`;
                });
                $('#tableSelect').html(tableOptions);
                form.render('select');
                
                // 切换到表列表标签页
                element.tabChange('resultTabs', '1');
                layer.msg('数据表加载成功', {icon: 1});
            } else {
                $('#tablesResult').html(`
                    <div class="layui-card">
                        <div class="layui-card-header" style="background: #FF5722; color: white;">
                            <i class="layui-icon layui-icon-close"></i> 加载失败
                        </div>
                        <div class="layui-card-body">
                            <p><strong>连接：</strong>${connection}</p>
                            <p><strong>错误：</strong>${res.msg}</p>
                        </div>
                    </div>
                `);
                layer.msg('数据表加载失败: ' + res.msg, {icon: 2});
            }
        }).fail(function() {
            btn.html(originalText);
            btn.prop('disabled', false);
            layer.msg('请求失败', {icon: 2});
        });
    });
    
    // 分析表结构
    $('#analyzeTableBtn').on('click', function() {
        const tableName = $('#tableSelect').val();
        const tablePrefix = $('#tablePrefix').val();
        const btn = $(this);
        const originalText = btn.html();
        
        if (!tableName) {
            layer.msg('请先选择数据表', {icon: 0});
            return;
        }
        
        btn.html('<i class="layui-icon layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop"></i> 分析中...');
        btn.prop('disabled', true);
        
        $.post('/admin/system/curdgeneratev2', {
            action: 'analyze_table',
            table_name: tableName,
            table_prefix: tablePrefix,
            connection: currentConnection
        }, function(res) {
            btn.html(originalText);
            btn.prop('disabled', false);
            
            if (res.code === 0) {
                $('#analyzeResult').html(generateAnalyzeResultHtml(res.data, tableName, tablePrefix, currentConnection));
                
                // 切换到分析结果标签页
                element.tabChange('resultTabs', '2');
                layer.msg('表结构分析成功', {icon: 1});
            } else {
                $('#analyzeResult').html(`
                    <div class="layui-card">
                        <div class="layui-card-header" style="background: #FF5722; color: white;">
                            <i class="layui-icon layui-icon-close"></i> 分析失败
                        </div>
                        <div class="layui-card-body">
                            <p><strong>连接：</strong>${currentConnection}</p>
                            <p><strong>表名：</strong>${tableName}</p>
                            <p><strong>前缀：</strong>${tablePrefix || '无'}</p>
                            <p><strong>错误：</strong>${res.msg}</p>
                        </div>
                    </div>
                `);
                layer.msg('表结构分析失败: ' + res.msg, {icon: 2});
            }
        }).fail(function() {
            btn.html(originalText);
            btn.prop('disabled', false);
            layer.msg('请求失败', {icon: 2});
        });
    });
    
    // 连接选择变化时清空结果
    form.on('select(connectionSelect)', function(data) {
        $('#tableSelect').html('<option value="">请先加载数据表</option>');
        form.render('select');
        
        // 清空结果显示
        $('#tablesResult').html(`
            <div class="layui-text" style="text-align: center; padding: 50px; color: #999;">
                <i class="layui-icon layui-icon-table" style="font-size: 48px;"></i>
                <p>点击"加载数据表"按钮获取数据表列表</p>
            </div>
        `);
        $('#analyzeResult').html(`
            <div class="layui-text" style="text-align: center; padding: 50px; color: #999;">
                <i class="layui-icon layui-icon-search" style="font-size: 48px;"></i>
                <p>选择数据表后点击"分析表结构"按钮</p>
            </div>
        `);
    });
    
    // 生成连接测试结果HTML
    function generateConnectionResultHtml(data) {
        return `
            <div class="layui-card">
                <div class="layui-card-header" style="background: #5FB878; color: white;">
                    <i class="layui-icon layui-icon-ok"></i> 连接测试成功
                </div>
                <div class="layui-card-body">
                    <div class="layui-row layui-col-space10">
                        <div class="layui-col-md6">
                            <h4>连接信息</h4>
                            <table class="layui-table" lay-size="sm">
                                <tbody>
                                    <tr><td>连接名称</td><td>${data.connection}</td></tr>
                                    <tr><td>数据库类型</td><td>${data.config.driver}</td></tr>
                                    <tr><td>主机地址</td><td>${data.config.host}</td></tr>
                                    <tr><td>端口</td><td>${data.config.port}</td></tr>
                                    <tr><td>数据库名</td><td>${data.config.database}</td></tr>
                                    <tr><td>用户名</td><td>${data.config.username}</td></tr>
                                    <tr><td>响应时间</td><td>${data.response_time_ms} ms</td></tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="layui-col-md6">
                            <h4>数据库信息</h4>
                            <table class="layui-table" lay-size="sm">
                                <tbody>
                                    <tr><td>版本</td><td>${data.database_info.version || 'unknown'}</td></tr>
                                    <tr><td>当前数据库</td><td>${data.database_info.current_database || 'unknown'}</td></tr>
                                    <tr><td>表数量</td><td>${data.database_info.table_count || 0}</td></tr>
                                    <tr><td>字符集</td><td>${data.database_info.charset || 'unknown'}</td></tr>
                                    <tr><td>时区</td><td>${data.database_info.timezone || 'unknown'}</td></tr>
                                    <tr><td>测试时间</td><td>${data.timestamp}</td></tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }
    
    // 生成表列表结果HTML
    function generateTablesResultHtml(tables, connection) {
        let html = `
            <div class="layui-card">
                <div class="layui-card-header" style="background: #1E9FFF; color: white;">
                    <i class="layui-icon layui-icon-table"></i> 数据表列表 (${connection})
                </div>
                <div class="layui-card-body">
                    <p><strong>表数量：</strong>${tables.length} 个</p>
                    <div style="max-height: 400px; overflow-y: auto;">
                        <table class="layui-table" lay-size="sm">
                            <thead>
                                <tr>
                                    <th>序号</th>
                                    <th>表名</th>
                                    <th>注释</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
        `;
        
        tables.forEach(function(table, index) {
            html += `
                <tr>
                    <td>${index + 1}</td>
                    <td><code>${table.name}</code></td>
                    <td>${table.comment || '无注释'}</td>
                    <td>
                        <button class="layui-btn layui-btn-xs" onclick="selectTable('${table.name}')">
                            <i class="layui-icon layui-icon-choose"></i> 选择
                        </button>
                    </td>
                </tr>
            `;
        });
        
        html += `
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        `;
        
        return html;
    }
    
    // 生成分析结果HTML
    function generateAnalyzeResultHtml(data, tableName, tablePrefix, connection) {
        let html = `
            <div class="layui-card">
                <div class="layui-card-header" style="background: #FF5722; color: white;">
                    <i class="layui-icon layui-icon-search"></i> 表结构分析结果
                </div>
                <div class="layui-card-body">
                    <div class="layui-row layui-col-space10">
                        <div class="layui-col-md12">
                            <h4>基本信息</h4>
                            <table class="layui-table" lay-size="sm">
                                <tbody>
                                    <tr><td>连接</td><td>${connection}</td></tr>
                                    <tr><td>表名</td><td>${tableName}</td></tr>
                                    <tr><td>前缀</td><td>${tablePrefix || '无'}</td></tr>
                                    <tr><td>注释</td><td>${data.comment || '无注释'}</td></tr>
                                    <tr><td>字段数量</td><td>${data.fields ? data.fields.length : 0}</td></tr>
                                    <tr><td>索引数量</td><td>${data.indexes ? data.indexes.length : 0}</td></tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    
                    <div class="layui-row layui-col-space10" style="margin-top: 20px;">
                        <div class="layui-col-md12">
                            <h4>字段信息</h4>
                            <div style="max-height: 300px; overflow-y: auto;">
                                <table class="layui-table" lay-size="sm">
                                    <thead>
                                        <tr>
                                            <th>字段名</th>
                                            <th>类型</th>
                                            <th>长度</th>
                                            <th>允许空值</th>
                                            <th>默认值</th>
                                            <th>注释</th>
                                        </tr>
                                    </thead>
                                    <tbody>
        `;
        
        if (data.fields) {
            data.fields.forEach(function(field) {
                html += `
                    <tr>
                        <td><code>${field.name}</code></td>
                        <td>${field.type}</td>
                        <td>${field.length || '-'}</td>
                        <td>${field.nullable ? '是' : '否'}</td>
                        <td>${field.default || '-'}</td>
                        <td>${field.comment || '无注释'}</td>
                    </tr>
                `;
            });
        }
        
        html += `
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        return html;
    }
    
    // 全局函数：选择表
    window.selectTable = function(tableName) {
        $('#tableSelect').val(tableName);
        form.render('select');
        layer.msg('已选择表: ' + tableName, {icon: 1});
    };
});
</script>
@endsection