@include('admin.layout.head')
<link rel="stylesheet" href="/static/plugs/lay-module/treetable-lay/treetable.css?v={$version}" media="all">
<style>
    .layui-btn:not(.layui-btn-lg ):not(.layui-btn-sm):not(.layui-btn-xs) {
        height: 34px;
        line-height: 34px;
        padding: 0 8px;
    }
</style>
<div class="layuimini-container">
    <div class="layuimini-main">
        <table id="currentTable" class="layui-table layui-hide"
               data-auth-add="{{auths('system/menu/add')}}"
               data-auth-edit="{{auths('system/menu/edit')}}"
               data-auth-delete="{{auths('system/menu/delete')}}"
               lay-filter="currentTable">
        </table>
    </div>
</div>
<script type="text/html" id="toolbar">
    <button class="layui-btn layui-btn-sm layuimini-btn-primary" data-treetable-refresh><i class="fa fa-refresh"></i></button>
    <button class="layui-btn layui-btn-normal layui-btn-sm {if !auths('system/menu/add')}layui-hide{/if}" data-open="system/menu/add" data-title="添加"><i class="fa fa-plus"></i> 添加</button>
    <button class="layui-btn layui-btn-sm layui-btn-danger {if !auths('system/menu/delete')}layui-hide{/if}" data-url="system/menu/delete" data-treetable-delete="currentTableRenderId"><i class="fa fa-trash-o"></i> 删除</button>
</script>

@include('admin.layout.foot')
