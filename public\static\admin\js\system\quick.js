define(["jquery", "easy-admin", "iconPickerFa", "autocomplete"], function ($, ea) {

    var iconPickerFa = layui.iconPickerFa,
        autocomplete = layui.autocomplete;

    var init = {
        table_elem: '#currentTable',
        table_render_id: 'currentTableRenderId',
        index_url: 'system/quick/index',
        add_url: 'system/quick/add',
        edit_url: 'system/quick/edit',
        delete_url: 'system/quick/delete',
        export_url: 'system/quick/export',
        modify_url: 'system/quick/modify',
    };

    var Controller = {

        index: function () {
            ea.table.render({
                init: init,
                cols: [[
                    {type: "checkbox"},
                    {field: 'id', width: 80, title: 'ID', searchOp: '='},
                    {field: 'sort', width: 80, title: '排序', edit: 'text'},
                    {field: 'title', minWidth: 80, title: '权限名称'},
                    {field: 'icon', width: 80, title: '图标', templet: ea.table.icon},
                    {field: 'href', minWidth: 120, title: '快捷链接'},
                    {field: 'remark', minWidth: 80, title: '备注信息'},
                    {field: 'status', title: '状态', width: 85, search: 'select', selectList: {0: '禁用', 1: '启用'}, templet: ea.table.switch},
                    {field: 'create_time', minWidth: 80, title: '创建时间', search: 'range'},
                    {width: 250, title: '操作', templet: ea.table.tool, operat: ['edit', 'delete']}
                ]],
            });

            ea.listen();
        },
        add: function () {
            $(function () {
                iconPickerFa.render({
                    elem: '#icon',
                    url: PATH_CONFIG.iconLess,
                    limit: 12,
                    click: function (data) {
                        $('#icon').val('fa ' + data.icon);
                    },
                    success: function (d) {
                        console.log(d);
                    }
                });
            })
            autocomplete.render({
                elem: $('#href')[0],
                url: ea.url('system/menu/getMenuTips'),
                template_val: '{{d.node}}',
                template_txt: '{{d.node}} <span class="layui-badge">{{d.title}}</span>',
                onselect: function (resp) {
                }
            });

            ea.listen();
        },
        edit: function () {
            $(function () {
                iconPickerFa.render({
                    elem: '#icon',
                    url: PATH_CONFIG.iconLess,
                    limit: 12,
                    click: function (data) {
                        $('#icon').val('fa ' + data.icon);
                    },
                    success: function (d) {
                        console.log(d);
                    }
                });
            })
            autocomplete.render({
                elem: $('#href')[0],
                url: ea.url('system/menu/getMenuTips'),
                template_val: '{{d.node}}',
                template_txt: '{{d.node}} <span class="layui-badge">{{d.title}}</span>',
                onselect: function (resp) {
                }
            });

            ea.listen();
        },
    };
    return Controller;
});
