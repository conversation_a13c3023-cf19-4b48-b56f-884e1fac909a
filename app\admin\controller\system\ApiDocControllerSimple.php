<?php

namespace app\admin\controller\system;

use support\Request;
use support\Response;

/**
 * API文档控制器 (简化版)
 * 展示和管理生成的API文档
 */
class ApiDocControllerSimple
{
    /**
     * 返回JSON响应
     */
    protected function json(array $data, int $status = 200): Response
    {
        return response(json_encode($data), $status, ['Content-Type' => 'application/json']);
    }

    /**
     * 返回错误响应
     */
    protected function error(string $message, int $code = 400): Response
    {
        return $this->json(['code' => $code, 'msg' => $message]);
    }

    /**
     * API文档首页
     */
    public function index(Request $request): Response
    {
        // 获取所有已生成的API文档
        $apiDocs = $this->getGeneratedApiDocs();
        
        // 获取API统计信息
        $apiStats = $this->getApiStatistics($apiDocs);
        
        // 简单的HTML响应
        $html = $this->renderIndexPage($apiDocs, $apiStats);
        
        return response($html, 200, ['Content-Type' => 'text/html']);
    }

    /**
     * 查看API文档详情
     */
    public function view(Request $request): Response
    {
        $tableName = $request->get('table', '');
        
        if (empty($tableName)) {
            return $this->error('表名不能为空');
        }

        // 获取表信息
        $tableInfo = $this->getTableInfo($tableName);
        
        // 生成API文档
        $apiDoc = $this->generateApiDocumentation($tableInfo);
        
        // 简单的HTML响应
        $html = $this->renderViewPage($tableName, $apiDoc);
        
        return response($html, 200, ['Content-Type' => 'text/html']);
    }

    /**
     * 生成API文档
     */
    public function generate(Request $request): Response
    {
        $tableName = $request->post('table_name', '');
        
        if (empty($tableName)) {
            return $this->json(['code' => 400, 'msg' => '表名不能为空']);
        }

        try {
            // 模拟生成过程
            sleep(1); // 模拟处理时间
            
            return $this->json([
                'code' => 200,
                'msg' => 'API文档生成成功',
                'data' => [
                    'table_name' => $tableName,
                    'api_count' => 5,
                    'doc_size' => '15.2KB',
                    'generated_at' => date('Y-m-d H:i:s')
                ]
            ]);
            
        } catch (\Exception $e) {
            return $this->json(['code' => 500, 'msg' => '生成失败：' . $e->getMessage()]);
        }
    }

    /**
     * 导出API文档
     */
    public function export(Request $request): Response
    {
        $tableName = $request->get('table', '');
        $format = $request->get('format', 'html');
        
        if (empty($tableName)) {
            return $this->error('表名不能为空');
        }

        try {
            // 模拟导出过程
            $content = $this->generateExportContent($tableName, $format);
            
            $headers = [
                'Content-Type' => $this->getContentType($format),
                'Content-Disposition' => "attachment; filename=\"{$tableName}_api_doc.{$format}\""
            ];
            
            return response($content, 200, $headers);
            
        } catch (\Exception $e) {
            return $this->error('导出失败：' . $e->getMessage());
        }
    }

    /**
     * 测试API接口
     */
    public function test(Request $request): Response
    {
        $tableName = $request->post('table_name', '');
        $endpoint = $request->post('endpoint', '');
        $method = $request->post('method', 'GET');
        
        if (empty($tableName) || empty($endpoint)) {
            return $this->json(['code' => 400, 'msg' => '参数不完整']);
        }

        try {
            // 模拟API测试
            $testResult = [
                'status' => 'success',
                'response_time' => rand(50, 200) . 'ms',
                'status_code' => 200,
                'response_body' => [
                    'code' => 200,
                    'msg' => 'success',
                    'data' => ['id' => 1, 'name' => '测试数据']
                ]
            ];
            
            return $this->json([
                'code' => 200,
                'msg' => 'API测试完成',
                'data' => $testResult
            ]);
            
        } catch (\Exception $e) {
            return $this->json(['code' => 500, 'msg' => '测试失败：' . $e->getMessage()]);
        }
    }

    /**
     * 获取API文档列表
     */
    public function getApiDocList(Request $request): Response
    {
        try {
            $apiDocs = $this->getGeneratedApiDocs();
            $statistics = $this->getApiStatistics($apiDocs);
            
            return $this->json([
                'code' => 200,
                'msg' => 'success',
                'data' => $apiDocs,
                'statistics' => $statistics
            ]);
        } catch (\Exception $e) {
            return $this->json(['code' => 500, 'msg' => '获取列表失败：' . $e->getMessage()]);
        }
    }

    /**
     * 获取API接口列表
     */
    public function getApiEndpoints(Request $request): Response
    {
        $tableName = $request->get('table', '');
        
        if (empty($tableName)) {
            return $this->json(['code' => 400, 'msg' => '表名不能为空']);
        }

        try {
            $endpoints = $this->generateEndpoints($tableName);
            
            return $this->json([
                'code' => 200,
                'msg' => 'success',
                'data' => $endpoints
            ]);
        } catch (\Exception $e) {
            return $this->json(['code' => 500, 'msg' => '获取接口列表失败：' . $e->getMessage()]);
        }
    }

    /**
     * 获取表列表
     */
    public function getTableList(Request $request): Response
    {
        try {
            $tables = [
                ['name' => 'users', 'comment' => '用户表'],
                ['name' => 'articles', 'comment' => '文章表'],
                ['name' => 'categories', 'comment' => '分类表'],
                ['name' => 'tags', 'comment' => '标签表'],
                ['name' => 'comments', 'comment' => '评论表']
            ];
            
            return $this->json([
                'code' => 200,
                'msg' => 'success',
                'data' => $tables
            ]);
        } catch (\Exception $e) {
            return $this->json(['code' => 500, 'msg' => '获取表列表失败：' . $e->getMessage()]);
        }
    }

    /**
     * 获取已生成的API文档列表
     */
    protected function getGeneratedApiDocs(): array
    {
        return [
            [
                'table_name' => 'users',
                'display_name' => '用户管理',
                'api_count' => 5,
                'doc_size' => '15.2KB',
                'generated_at' => '2025-01-20 10:30:00',
                'status' => 'active'
            ],
            [
                'table_name' => 'articles',
                'display_name' => '文章管理',
                'api_count' => 7,
                'doc_size' => '22.8KB',
                'generated_at' => '2025-01-20 09:15:00',
                'status' => 'active'
            ],
            [
                'table_name' => 'categories',
                'display_name' => '分类管理',
                'api_count' => 4,
                'doc_size' => '8.5KB',
                'generated_at' => '2025-01-19 16:45:00',
                'status' => 'active'
            ]
        ];
    }

    /**
     * 获取API统计信息
     */
    protected function getApiStatistics(array $apiDocs): array
    {
        $totalApis = array_sum(array_column($apiDocs, 'api_count'));
        $totalTables = count($apiDocs);
        
        return [
            'total_tables' => $totalTables,
            'total_apis' => $totalApis,
            'total_size' => '46.5KB',
            'avg_apis_per_table' => $totalTables > 0 ? round($totalApis / $totalTables, 1) : 0,
            'last_generated' => $apiDocs ? max(array_column($apiDocs, 'generated_at')) : '无'
        ];
    }

    /**
     * 获取表信息
     */
    protected function getTableInfo(string $tableName): array
    {
        return [
            'name' => $tableName,
            'comment' => '示例表',
            'fields' => [
                ['name' => 'id', 'type' => 'int', 'comment' => '主键ID'],
                ['name' => 'name', 'type' => 'varchar', 'comment' => '名称'],
                ['name' => 'status', 'type' => 'tinyint', 'comment' => '状态'],
                ['name' => 'created_at', 'type' => 'timestamp', 'comment' => '创建时间'],
                ['name' => 'updated_at', 'type' => 'timestamp', 'comment' => '更新时间']
            ]
        ];
    }

    /**
     * 生成API文档
     */
    protected function generateApiDocumentation(array $tableInfo): array
    {
        $tableName = $tableInfo['name'];
        
        return [
            'title' => "{$tableName} API 文档",
            'description' => "基于 {$tableName} 表自动生成的 RESTful API 接口文档",
            'version' => '1.0.0',
            'base_url' => '/api',
            'endpoints' => $this->generateEndpoints($tableName)
        ];
    }

    /**
     * 生成接口列表
     */
    protected function generateEndpoints(string $tableName): array
    {
        return [
            [
                'method' => 'GET',
                'path' => "/{$tableName}",
                'summary' => "获取{$tableName}列表",
                'description' => "分页获取{$tableName}数据列表"
            ],
            [
                'method' => 'GET',
                'path' => "/{$tableName}/{id}",
                'summary' => "获取{$tableName}详情",
                'description' => "根据ID获取单个{$tableName}的详细信息"
            ],
            [
                'method' => 'POST',
                'path' => "/{$tableName}",
                'summary' => "创建{$tableName}",
                'description' => "创建新的{$tableName}记录"
            ],
            [
                'method' => 'PUT',
                'path' => "/{$tableName}/{id}",
                'summary' => "更新{$tableName}",
                'description' => "根据ID更新{$tableName}记录"
            ],
            [
                'method' => 'DELETE',
                'path' => "/{$tableName}/{id}",
                'summary' => "删除{$tableName}",
                'description' => "根据ID删除{$tableName}记录"
            ]
        ];
    }

    /**
     * 渲染首页
     */
    protected function renderIndexPage(array $apiDocs, array $apiStats): string
    {
        $html = '<!DOCTYPE html><html><head><title>API文档管理</title>';
        $html .= '<meta charset="utf-8"><meta name="viewport" content="width=device-width, initial-scale=1">';
        $html .= '<style>body{font-family:Arial,sans-serif;margin:20px;} .card{border:1px solid #ddd;padding:15px;margin:10px 0;border-radius:5px;} .stats{display:flex;gap:20px;} .stat{background:#f5f5f5;padding:10px;border-radius:5px;text-align:center;} .btn{background:#007bff;color:white;padding:8px 16px;border:none;border-radius:4px;cursor:pointer;text-decoration:none;display:inline-block;}</style>';
        $html .= '</head><body>';
        
        $html .= '<h1>API文档管理</h1>';
        
        // 统计信息
        $html .= '<div class="stats">';
        $html .= '<div class="stat"><h3>' . $apiStats['total_tables'] . '</h3><p>总表数</p></div>';
        $html .= '<div class="stat"><h3>' . $apiStats['total_apis'] . '</h3><p>总接口数</p></div>';
        $html .= '<div class="stat"><h3>' . $apiStats['total_size'] . '</h3><p>文档大小</p></div>';
        $html .= '<div class="stat"><h3>' . $apiStats['avg_apis_per_table'] . '</h3><p>平均接口数</p></div>';
        $html .= '</div>';
        
        // API文档列表
        $html .= '<h2>API文档列表</h2>';
        foreach ($apiDocs as $doc) {
            $html .= '<div class="card">';
            $html .= '<h3>' . $doc['display_name'] . ' (' . $doc['table_name'] . ')</h3>';
            $html .= '<p>接口数量: ' . $doc['api_count'] . ' | 文档大小: ' . $doc['doc_size'] . ' | 生成时间: ' . $doc['generated_at'] . '</p>';
            $html .= '<a href="/admin/system/apidoc/view?table=' . $doc['table_name'] . '" class="btn">查看文档</a> ';
            $html .= '<a href="/admin/system/apidoc/export?table=' . $doc['table_name'] . '&format=html" class="btn">导出HTML</a>';
            $html .= '</div>';
        }
        
        $html .= '</body></html>';
        return $html;
    }

    /**
     * 渲染查看页面
     */
    protected function renderViewPage(string $tableName, array $apiDoc): string
    {
        $html = '<!DOCTYPE html><html><head><title>' . $apiDoc['title'] . '</title>';
        $html .= '<meta charset="utf-8"><meta name="viewport" content="width=device-width, initial-scale=1">';
        $html .= '<style>body{font-family:Arial,sans-serif;margin:20px;} .endpoint{border:1px solid #ddd;padding:15px;margin:10px 0;border-radius:5px;} .method{display:inline-block;padding:4px 8px;border-radius:3px;color:white;font-weight:bold;} .get{background:#28a745;} .post{background:#007bff;} .put{background:#ffc107;color:#000;} .delete{background:#dc3545;}</style>';
        $html .= '</head><body>';
        
        $html .= '<h1>' . $apiDoc['title'] . '</h1>';
        $html .= '<p>' . $apiDoc['description'] . '</p>';
        $html .= '<p><strong>版本:</strong> ' . $apiDoc['version'] . ' | <strong>基础URL:</strong> ' . $apiDoc['base_url'] . '</p>';
        
        $html .= '<h2>接口列表</h2>';
        foreach ($apiDoc['endpoints'] as $endpoint) {
            $html .= '<div class="endpoint">';
            $html .= '<span class="method ' . strtolower($endpoint['method']) . '">' . $endpoint['method'] . '</span> ';
            $html .= '<strong>' . $endpoint['path'] . '</strong>';
            $html .= '<h4>' . $endpoint['summary'] . '</h4>';
            $html .= '<p>' . $endpoint['description'] . '</p>';
            $html .= '</div>';
        }
        
        $html .= '<p><a href="/admin/system/apidoc">返回列表</a></p>';
        $html .= '</body></html>';
        return $html;
    }

    /**
     * 生成导出内容
     */
    protected function generateExportContent(string $tableName, string $format): string
    {
        $tableInfo = $this->getTableInfo($tableName);
        $apiDoc = $this->generateApiDocumentation($tableInfo);
        
        switch ($format) {
            case 'json':
                return json_encode($apiDoc, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
            case 'html':
                return $this->renderViewPage($tableName, $apiDoc);
            default:
                return "# {$apiDoc['title']}\n\n{$apiDoc['description']}\n\n## 接口列表\n\n";
        }
    }

    /**
     * 获取内容类型
     */
    protected function getContentType(string $format): string
    {
        $types = [
            'html' => 'text/html',
            'json' => 'application/json',
            'md' => 'text/markdown'
        ];
        
        return $types[$format] ?? 'text/plain';
    }
}
