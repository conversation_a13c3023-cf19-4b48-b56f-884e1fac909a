<?php

namespace app\common\services\curd\v2\generators;

/**
 * 测试代码生成器
 * 根据测试需求分析结果生成完整的测试代码
 */
class TestGenerator
{
    protected array $testFrameworks = [
        'phpunit' => 'PHPUnit',
        'pest' => 'Pest',
        'codeception' => 'Codeception',
    ];

    /**
     * 生成测试代码
     */
    public function generateTestCode(array $testAnalysis, array $tableInfo, array $options = []): array
    {
        $framework = $options['framework'] ?? 'phpunit';
        $testCode = [];

        // 生成单元测试
        $testCode['unit_tests'] = $this->generateUnitTests($testAnalysis, $tableInfo, $framework);

        // 生成集成测试
        $testCode['integration_tests'] = $this->generateIntegrationTests($testAnalysis, $tableInfo, $framework);

        // 生成功能测试
        $testCode['feature_tests'] = $this->generateFeatureTests($testAnalysis, $tableInfo, $framework);

        // 生成API测试
        $testCode['api_tests'] = $this->generateApiTests($testAnalysis, $tableInfo, $framework);

        // 生成测试数据
        $testCode['test_data'] = $this->generateTestData($testAnalysis, $tableInfo);

        // 生成测试配置
        $testCode['test_config'] = $this->generateTestConfig($testAnalysis, $options);

        return $testCode;
    }

    /**
     * 生成单元测试
     */
    protected function generateUnitTests(array $testAnalysis, array $tableInfo, string $framework): array
    {
        $unitTests = [];
        $modelName = $this->getModelName($tableInfo['name']);

        // 模型单元测试
        $unitTests["Unit/{$modelName}Test.php"] = $this->generateModelUnitTest($testAnalysis, $tableInfo, $framework);

        // 验证单元测试
        $unitTests["Unit/{$modelName}ValidationTest.php"] = $this->generateValidationUnitTest($testAnalysis, $tableInfo, $framework);

        // 关联关系单元测试
        if (!empty($tableInfo['relationships'])) {
            $unitTests["Unit/{$modelName}RelationshipTest.php"] = $this->generateRelationshipUnitTest($testAnalysis, $tableInfo, $framework);
        }

        return $unitTests;
    }

    /**
     * 生成模型单元测试
     */
    protected function generateModelUnitTest(array $testAnalysis, array $tableInfo, string $framework): string
    {
        $modelName = $this->getModelName($tableInfo['name']);
        $tableName = $tableInfo['name'];

        $code = "<?php\n\n";
        $code .= "namespace Tests\\Unit;\n\n";
        $code .= "use Tests\\TestCase;\n";
        $code .= "use App\\Models\\{$modelName};\n";
        $code .= "use Illuminate\\Foundation\\Testing\\RefreshDatabase;\n\n";

        $code .= "/**\n";
        $code .= " * {$modelName} 模型单元测试\n";
        $code .= " * 测试模型的基本 CRUD 操作\n";
        $code .= " */\n";
        $code .= "class {$modelName}Test extends TestCase\n";
        $code .= "{\n";
        $code .= "    use RefreshDatabase;\n\n";

        // 测试模型创建
        $code .= "    /**\n";
        $code .= "     * 测试模型创建\n";
        $code .= "     */\n";
        $code .= "    public function test_can_create_model()\n";
        $code .= "    {\n";
        $code .= "        \$data = {$modelName}::factory()->make()->toArray();\n";
        $code .= "        \$model = {$modelName}::create(\$data);\n\n";
        $code .= "        \$this->assertInstanceOf({$modelName}::class, \$model);\n";
        $code .= "        \$this->assertDatabaseHas('{$tableName}', \$data);\n";
        $code .= "    }\n\n";

        // 测试模型查询
        $code .= "    /**\n";
        $code .= "     * 测试模型查询\n";
        $code .= "     */\n";
        $code .= "    public function test_can_retrieve_model()\n";
        $code .= "    {\n";
        $code .= "        \$model = {$modelName}::factory()->create();\n\n";
        $code .= "        \$found = {$modelName}::find(\$model->id);\n\n";
        $code .= "        \$this->assertInstanceOf({$modelName}::class, \$found);\n";
        $code .= "        \$this->assertEquals(\$model->id, \$found->id);\n";
        $code .= "    }\n\n";

        // 测试模型更新
        $code .= "    /**\n";
        $code .= "     * 测试模型更新\n";
        $code .= "     */\n";
        $code .= "    public function test_can_update_model()\n";
        $code .= "    {\n";
        $code .= "        \$model = {$modelName}::factory()->create();\n";
        $code .= "        \$newData = {$modelName}::factory()->make()->toArray();\n\n";
        $code .= "        \$model->update(\$newData);\n\n";
        $code .= "        \$this->assertDatabaseHas('{$tableName}', array_merge(['id' => \$model->id], \$newData));\n";
        $code .= "    }\n\n";

        // 测试模型删除
        $code .= "    /**\n";
        $code .= "     * 测试模型删除\n";
        $code .= "     */\n";
        $code .= "    public function test_can_delete_model()\n";
        $code .= "    {\n";
        $code .= "        \$model = {$modelName}::factory()->create();\n\n";
        $code .= "        \$model->delete();\n\n";

        if ($tableInfo['has_soft_delete']) {
            $code .= "        \$this->assertSoftDeleted('{$tableName}', ['id' => \$model->id]);\n";
        } else {
            $code .= "        \$this->assertDatabaseMissing('{$tableName}', ['id' => \$model->id]);\n";
        }

        $code .= "    }\n";
        $code .= "}\n";

        return $code;
    }

    /**
     * 生成验证单元测试
     */
    protected function generateValidationUnitTest(array $testAnalysis, array $tableInfo, string $framework): string
    {
        $modelName = $this->getModelName($tableInfo['name']);

        $code = "<?php\n\n";
        $code .= "namespace Tests\\Unit;\n\n";
        $code .= "use Tests\\TestCase;\n";
        $code .= "use App\\Models\\{$modelName};\n";
        $code .= "use Illuminate\\Foundation\\Testing\\RefreshDatabase;\n";
        $code .= "use Illuminate\\Validation\\ValidationException;\n\n";

        $code .= "/**\n";
        $code .= " * {$modelName} 验证规则单元测试\n";
        $code .= " */\n";
        $code .= "class {$modelName}ValidationTest extends TestCase\n";
        $code .= "{\n";
        $code .= "    use RefreshDatabase;\n\n";

        // 为每个必填字段生成验证测试
        foreach ($tableInfo['fields'] as $field) {
            if (!empty($field['required']) && !in_array($field['name'], ['id', 'created_at', 'updated_at'])) {
                $code .= $this->generateFieldValidationTest($field, $modelName);
            }
        }

        $code .= "}\n";

        return $code;
    }

    /**
     * 生成字段验证测试
     */
    protected function generateFieldValidationTest(array $field, string $modelName): string
    {
        $fieldName = $field['name'];
        $methodName = "test_{$fieldName}_is_required";

        $code = "    /**\n";
        $code .= "     * 测试 {$fieldName} 字段必填验证\n";
        $code .= "     */\n";
        $code .= "    public function {$methodName}()\n";
        $code .= "    {\n";
        $code .= "        \$this->expectException(ValidationException::class);\n\n";
        $code .= "        \$data = {$modelName}::factory()->make()->toArray();\n";
        $code .= "        unset(\$data['{$fieldName}']);\n\n";
        $code .= "        {$modelName}::create(\$data);\n";
        $code .= "    }\n\n";

        return $code;
    }

    /**
     * 生成关联关系单元测试
     */
    protected function generateRelationshipUnitTest(array $testAnalysis, array $tableInfo, string $framework): string
    {
        $modelName = $this->getModelName($tableInfo['name']);

        $code = "<?php\n\n";
        $code .= "namespace Tests\\Unit;\n\n";
        $code .= "use Tests\\TestCase;\n";
        $code .= "use App\\Models\\{$modelName};\n";
        $code .= "use Illuminate\\Foundation\\Testing\\RefreshDatabase;\n\n";

        $code .= "/**\n";
        $code .= " * {$modelName} 关联关系单元测试\n";
        $code .= " */\n";
        $code .= "class {$modelName}RelationshipTest extends TestCase\n";
        $code .= "{\n";
        $code .= "    use RefreshDatabase;\n\n";

        // 为每个关联关系生成测试
        foreach ($tableInfo['relationships'] as $relationship) {
            if ($relationship['confidence'] >= 70) {
                $code .= $this->generateRelationshipTest($relationship, $modelName);
            }
        }

        $code .= "}\n";

        return $code;
    }

    /**
     * 生成关联关系测试
     */
    protected function generateRelationshipTest(array $relationship, string $modelName): string
    {
        $relationName = $relationship['method_name'];
        $relationType = $relationship['type'];
        $relatedModel = $this->getModelName($relationship['related_table']);

        $code = "    /**\n";
        $code .= "     * 测试 {$relationName} {$relationType} 关联关系\n";
        $code .= "     */\n";
        $code .= "    public function test_{$relationName}_relationship()\n";
        $code .= "    {\n";
        $code .= "        \$model = {$modelName}::factory()->create();\n";

        switch ($relationType) {
            case 'belongsTo':
                $code .= "        \$related = {$relatedModel}::factory()->create();\n";
                $code .= "        \$model->{$relationship['foreign_key']} = \$related->id;\n";
                $code .= "        \$model->save();\n\n";
                $code .= "        \$this->assertInstanceOf({$relatedModel}::class, \$model->{$relationName});\n";
                $code .= "        \$this->assertEquals(\$related->id, \$model->{$relationName}->id);\n";
                break;

            case 'hasMany':
                $code .= "        \$related = {$relatedModel}::factory()->create(['{$relationship['foreign_key']}' => \$model->id]);\n\n";
                $code .= "        \$this->assertTrue(\$model->{$relationName}->contains(\$related));\n";
                $code .= "        \$this->assertInstanceOf({$relatedModel}::class, \$model->{$relationName}->first());\n";
                break;

            case 'belongsToMany':
                $code .= "        \$related = {$relatedModel}::factory()->create();\n";
                $code .= "        \$model->{$relationName}()->attach(\$related->id);\n\n";
                $code .= "        \$this->assertTrue(\$model->{$relationName}->contains(\$related));\n";
                break;
        }

        $code .= "    }\n\n";

        return $code;
    }

    /**
     * 生成集成测试
     */
    protected function generateIntegrationTests(array $testAnalysis, array $tableInfo, string $framework): array
    {
        $integrationTests = [];
        $modelName = $this->getModelName($tableInfo['name']);

        // 数据库集成测试
        $integrationTests["Integration/{$modelName}DatabaseTest.php"] = $this->generateDatabaseIntegrationTest($testAnalysis, $tableInfo, $framework);

        return $integrationTests;
    }

    /**
     * 生成数据库集成测试
     */
    protected function generateDatabaseIntegrationTest(array $testAnalysis, array $tableInfo, string $framework): string
    {
        $modelName = $this->getModelName($tableInfo['name']);

        $code = "<?php\n\n";
        $code .= "namespace Tests\\Integration;\n\n";
        $code .= "use Tests\\TestCase;\n";
        $code .= "use App\\Models\\{$modelName};\n";
        $code .= "use Illuminate\\Foundation\\Testing\\RefreshDatabase;\n\n";

        $code .= "/**\n";
        $code .= " * {$modelName} 数据库集成测试\n";
        $code .= " */\n";
        $code .= "class {$modelName}DatabaseTest extends TestCase\n";
        $code .= "{\n";
        $code .= "    use RefreshDatabase;\n\n";

        // 测试数据库事务
        $code .= "    /**\n";
        $code .= "     * 测试数据库事务\n";
        $code .= "     */\n";
        $code .= "    public function test_database_transaction()\n";
        $code .= "    {\n";
        $code .= "        \\DB::transaction(function () {\n";
        $code .= "            \$model = {$modelName}::factory()->create();\n";
        $code .= "            \$this->assertDatabaseHas('{$tableInfo['name']}', ['id' => \$model->id]);\n";
        $code .= "        });\n";
        $code .= "    }\n\n";

        // 测试批量操作
        $code .= "    /**\n";
        $code .= "     * 测试批量操作\n";
        $code .= "     */\n";
        $code .= "    public function test_bulk_operations()\n";
        $code .= "    {\n";
        $code .= "        \$models = {$modelName}::factory()->count(10)->create();\n\n";
        $code .= "        \$this->assertCount(10, \$models);\n";
        $code .= "        \$this->assertEquals(10, {$modelName}::count());\n";
        $code .= "    }\n";

        $code .= "}\n";

        return $code;
    }

    /**
     * 生成功能测试
     */
    protected function generateFeatureTests(array $testAnalysis, array $tableInfo, string $framework): array
    {
        $featureTests = [];
        $modelName = $this->getModelName($tableInfo['name']);

        // 控制器功能测试
        $featureTests["Feature/{$modelName}ControllerTest.php"] = $this->generateControllerFeatureTest($testAnalysis, $tableInfo, $framework);

        return $featureTests;
    }

    /**
     * 生成控制器功能测试
     */
    protected function generateControllerFeatureTest(array $testAnalysis, array $tableInfo, string $framework): string
    {
        $modelName = $this->getModelName($tableInfo['name']);
        $routeName = strtolower($modelName);

        $code = "<?php\n\n";
        $code .= "namespace Tests\\Feature;\n\n";
        $code .= "use Tests\\TestCase;\n";
        $code .= "use App\\Models\\{$modelName};\n";
        $code .= "use Illuminate\\Foundation\\Testing\\RefreshDatabase;\n\n";

        $code .= "/**\n";
        $code .= " * {$modelName} 控制器功能测试\n";
        $code .= " */\n";
        $code .= "class {$modelName}ControllerTest extends TestCase\n";
        $code .= "{\n";
        $code .= "    use RefreshDatabase;\n\n";

        // 测试列表页面
        $code .= "    /**\n";
        $code .= "     * 测试列表页面\n";
        $code .= "     */\n";
        $code .= "    public function test_index_page()\n";
        $code .= "    {\n";
        $code .= "        {$modelName}::factory()->count(5)->create();\n\n";
        $code .= "        \$response = \$this->get(route('{$routeName}.index'));\n\n";
        $code .= "        \$response->assertStatus(200);\n";
        $code .= "        \$response->assertViewIs('{$routeName}.index');\n";
        $code .= "    }\n\n";

        // 测试详情页面
        $code .= "    /**\n";
        $code .= "     * 测试详情页面\n";
        $code .= "     */\n";
        $code .= "    public function test_show_page()\n";
        $code .= "    {\n";
        $code .= "        \$model = {$modelName}::factory()->create();\n\n";
        $code .= "        \$response = \$this->get(route('{$routeName}.show', \$model));\n\n";
        $code .= "        \$response->assertStatus(200);\n";
        $code .= "        \$response->assertViewIs('{$routeName}.show');\n";
        $code .= "    }\n\n";

        // 测试创建功能
        $code .= "    /**\n";
        $code .= "     * 测试创建功能\n";
        $code .= "     */\n";
        $code .= "    public function test_store_function()\n";
        $code .= "    {\n";
        $code .= "        \$data = {$modelName}::factory()->make()->toArray();\n\n";
        $code .= "        \$response = \$this->post(route('{$routeName}.store'), \$data);\n\n";
        $code .= "        \$response->assertRedirect();\n";
        $code .= "        \$this->assertDatabaseHas('{$tableInfo['name']}', \$data);\n";
        $code .= "    }\n";

        $code .= "}\n";

        return $code;
    }

    /**
     * 生成API测试
     */
    protected function generateApiTests(array $testAnalysis, array $tableInfo, string $framework): array
    {
        $apiTests = [];
        $modelName = $this->getModelName($tableInfo['name']);

        // API接口测试
        $apiTests["Feature/Api/{$modelName}ApiTest.php"] = $this->generateApiTest($testAnalysis, $tableInfo, $framework);

        return $apiTests;
    }

    /**
     * 生成API测试
     */
    protected function generateApiTest(array $testAnalysis, array $tableInfo, string $framework): string
    {
        $modelName = $this->getModelName($tableInfo['name']);
        $resourceName = strtolower($modelName);

        $code = "<?php\n\n";
        $code .= "namespace Tests\\Feature\\Api;\n\n";
        $code .= "use Tests\\TestCase;\n";
        $code .= "use App\\Models\\{$modelName};\n";
        $code .= "use Illuminate\\Foundation\\Testing\\RefreshDatabase;\n\n";

        $code .= "/**\n";
        $code .= " * {$modelName} API 测试\n";
        $code .= " */\n";
        $code .= "class {$modelName}ApiTest extends TestCase\n";
        $code .= "{\n";
        $code .= "    use RefreshDatabase;\n\n";

        // 测试GET列表接口
        $code .= "    /**\n";
        $code .= "     * 测试GET列表接口\n";
        $code .= "     */\n";
        $code .= "    public function test_get_list_api()\n";
        $code .= "    {\n";
        $code .= "        {$modelName}::factory()->count(3)->create();\n\n";
        $code .= "        \$response = \$this->getJson('/api/{$resourceName}');\n\n";
        $code .= "        \$response->assertStatus(200)\n";
        $code .= "                 ->assertJsonStructure([\n";
        $code .= "                     'data' => [\n";
        $code .= "                         '*' => ['id', 'created_at', 'updated_at']\n";
        $code .= "                     ]\n";
        $code .= "                 ]);\n";
        $code .= "    }\n\n";

        // 测试POST创建接口
        $code .= "    /**\n";
        $code .= "     * 测试POST创建接口\n";
        $code .= "     */\n";
        $code .= "    public function test_post_create_api()\n";
        $code .= "    {\n";
        $code .= "        \$data = {$modelName}::factory()->make()->toArray();\n\n";
        $code .= "        \$response = \$this->postJson('/api/{$resourceName}', \$data);\n\n";
        $code .= "        \$response->assertStatus(201);\n";
        $code .= "        \$this->assertDatabaseHas('{$tableInfo['name']}', \$data);\n";
        $code .= "    }\n";

        $code .= "}\n";

        return $code;
    }

    /**
     * 生成测试数据
     */
    protected function generateTestData(array $testAnalysis, array $tableInfo): array
    {
        $testData = [];
        $modelName = $this->getModelName($tableInfo['name']);

        // 生成模型工厂
        $testData["database/factories/{$modelName}Factory.php"] = $this->generateModelFactory($tableInfo);

        // 生成数据填充器
        $testData["database/seeders/{$modelName}Seeder.php"] = $this->generateModelSeeder($tableInfo);

        return $testData;
    }

    /**
     * 生成模型工厂
     */
    protected function generateModelFactory(array $tableInfo): string
    {
        $modelName = $this->getModelName($tableInfo['name']);

        $code = "<?php\n\n";
        $code .= "namespace Database\\Factories;\n\n";
        $code .= "use App\\Models\\{$modelName};\n";
        $code .= "use Illuminate\\Database\\Eloquent\\Factories\\Factory;\n\n";

        $code .= "/**\n";
        $code .= " * {$modelName} 模型工厂\n";
        $code .= " */\n";
        $code .= "class {$modelName}Factory extends Factory\n";
        $code .= "{\n";
        $code .= "    protected \$model = {$modelName}::class;\n\n";

        $code .= "    public function definition()\n";
        $code .= "    {\n";
        $code .= "        return [\n";

        foreach ($tableInfo['fields'] as $field) {
            if (in_array($field['name'], ['id', 'created_at', 'updated_at', 'deleted_at'])) {
                continue;
            }

            $code .= "            '{$field['name']}' => {$this->getFactoryFieldDefinition($field)},\n";
        }

        $code .= "        ];\n";
        $code .= "    }\n";
        $code .= "}\n";

        return $code;
    }

    /**
     * 生成模型填充器
     */
    protected function generateModelSeeder(array $tableInfo): string
    {
        $modelName = $this->getModelName($tableInfo['name']);

        $code = "<?php\n\n";
        $code .= "namespace Database\\Seeders;\n\n";
        $code .= "use App\\Models\\{$modelName};\n";
        $code .= "use Illuminate\\Database\\Seeder;\n\n";

        $code .= "/**\n";
        $code .= " * {$modelName} 数据填充器\n";
        $code .= " */\n";
        $code .= "class {$modelName}Seeder extends Seeder\n";
        $code .= "{\n";
        $code .= "    public function run()\n";
        $code .= "    {\n";
        $code .= "        {$modelName}::factory()->count(10)->create();\n";
        $code .= "    }\n";
        $code .= "}\n";

        return $code;
    }

    /**
     * 生成测试配置
     */
    protected function generateTestConfig(array $testAnalysis, array $options): array
    {
        $config = [];

        // PHPUnit配置
        $config['phpunit.xml'] = $this->generatePhpUnitConfig($testAnalysis, $options);

        // 测试环境配置
        $config['.env.testing'] = $this->generateTestEnvConfig($options);

        return $config;
    }

    /**
     * 生成PHPUnit配置
     */
    protected function generatePhpUnitConfig(array $testAnalysis, array $options): string
    {
        $xml = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n";
        $xml .= "<phpunit xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\"\n";
        $xml .= "         xsi:noNamespaceSchemaLocation=\"./vendor/phpunit/phpunit/phpunit.xsd\"\n";
        $xml .= "         bootstrap=\"vendor/autoload.php\"\n";
        $xml .= "         colors=\"true\">\n";
        $xml .= "    <testsuites>\n";
        $xml .= "        <testsuite name=\"Unit\">\n";
        $xml .= "            <directory suffix=\"Test.php\">./tests/Unit</directory>\n";
        $xml .= "        </testsuite>\n";
        $xml .= "        <testsuite name=\"Feature\">\n";
        $xml .= "            <directory suffix=\"Test.php\">./tests/Feature</directory>\n";
        $xml .= "        </testsuite>\n";
        $xml .= "    </testsuites>\n";
        $xml .= "    <coverage>\n";
        $xml .= "        <include>\n";
        $xml .= "            <directory suffix=\".php\">./app</directory>\n";
        $xml .= "        </include>\n";
        $xml .= "    </coverage>\n";
        $xml .= "</phpunit>\n";

        return $xml;
    }

    /**
     * 生成测试环境配置
     */
    protected function generateTestEnvConfig(array $options): string
    {
        $config = "APP_ENV=testing\n";
        $config .= "APP_DEBUG=true\n";
        $config .= "APP_KEY=base64:test_key_here\n\n";
        $config .= "DB_CONNECTION=sqlite\n";
        $config .= "DB_DATABASE=:memory:\n\n";
        $config .= "CACHE_DRIVER=array\n";
        $config .= "QUEUE_CONNECTION=sync\n";
        $config .= "SESSION_DRIVER=array\n";

        return $config;
    }

    /**
     * 获取工厂字段定义
     */
    protected function getFactoryFieldDefinition(array $field): string
    {
        switch ($field['type']) {
            case 'varchar':
                return "\$this->faker->words(3, true)";
            case 'text':
                return "\$this->faker->paragraph()";
            case 'int':
                return "\$this->faker->numberBetween(1, 1000)";
            case 'date':
                return "\$this->faker->date()";
            case 'datetime':
                return "\$this->faker->dateTime()";
            case 'decimal':
                return "\$this->faker->randomFloat(2, 0, 999.99)";
            case 'boolean':
                return "\$this->faker->boolean()";
            default:
                return "\$this->faker->word()";
        }
    }

    /**
     * 获取模型名称
     */
    protected function getModelName(string $tableName): string
    {
        $name = preg_replace('/^[a-z]+_/', '', $tableName);
        return str_replace(' ', '', ucwords(str_replace('_', ' ', $name)));
    }
}
