-- CURD 配置模板表
CREATE TABLE `ea8_curd_templates` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '模板ID',
    `name` varchar(100) NOT NULL COMMENT '模板名称',
    `description` text COMMENT '模板描述',
    `table_pattern` varchar(100) DEFAULT NULL COMMENT '适用表名模式',
    `config` longtext NOT NULL COMMENT '配置内容(JSON)',
    `tags` varchar(255) DEFAULT NULL COMMENT '标签',
    `is_public` tinyint(1) DEFAULT 0 COMMENT '是否公开模板',
    `use_count` int(11) DEFAULT 0 COMMENT '使用次数',
    `created_by` int(11) DEFAULT NULL COMMENT '创建者ID',
    `created_at` datetime DEFAULT NULL COMMENT '创建时间',
    `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
    PRIMARY <PERSON> (`id`),
    KEY `idx_created_by` (`created_by`),
    <PERSON>EY `idx_name` (`name`),
    KEY `idx_is_public` (`is_public`),
    KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='CURD配置模板表';

-- 插入一些默认模板
INSERT INTO `ea8_curd_templates` (`name`, `description`, `table_pattern`, `config`, `tags`, `is_public`, `created_by`, `created_at`) VALUES
('基础用户表', '适用于用户相关表的基础配置', 'user%', '{"fields":[{"name":"id","component":"input","show_in_list":true,"show_in_form":false},{"name":"username","component":"input","show_in_list":true,"show_in_form":true,"required":true,"searchable":true},{"name":"email","component":"email","show_in_list":true,"show_in_form":true,"required":true,"searchable":true},{"name":"password","component":"password","show_in_list":false,"show_in_form":true,"required":true},{"name":"status","component":"switch","show_in_list":true,"show_in_form":true,"searchable":true},{"name":"created_at","component":"datetime","show_in_list":true,"show_in_form":false,"sortable":true}],"options":{"generate_controller":true,"generate_model":true,"generate_view":true,"generate_js":true,"enable_export":true,"enable_batch":true,"enable_auth":true}}', '用户,基础', 1, 1, NOW()),

('内容管理表', '适用于文章、新闻等内容管理表', '%content%,%article%,%news%', '{"fields":[{"name":"id","component":"input","show_in_list":true,"show_in_form":false},{"name":"title","component":"input","show_in_list":true,"show_in_form":true,"required":true,"searchable":true},{"name":"content","component":"editor","show_in_list":false,"show_in_form":true,"required":true},{"name":"summary","component":"textarea","show_in_list":true,"show_in_form":true},{"name":"author_id","component":"select","show_in_list":true,"show_in_form":true,"searchable":true},{"name":"category_id","component":"select","show_in_list":true,"show_in_form":true,"searchable":true},{"name":"status","component":"select","show_in_list":true,"show_in_form":true,"searchable":true},{"name":"publish_time","component":"datetime","show_in_list":true,"show_in_form":true,"sortable":true},{"name":"created_at","component":"datetime","show_in_list":true,"show_in_form":false,"sortable":true}],"options":{"generate_controller":true,"generate_model":true,"generate_view":true,"generate_js":true,"enable_export":true,"enable_batch":true,"enable_auth":true}}', '内容,文章,新闻', 1, 1, NOW()),

('商品管理表', '适用于电商商品管理相关表', '%product%,%goods%,%item%', '{"fields":[{"name":"id","component":"input","show_in_list":true,"show_in_form":false},{"name":"name","component":"input","show_in_list":true,"show_in_form":true,"required":true,"searchable":true},{"name":"description","component":"textarea","show_in_list":false,"show_in_form":true},{"name":"price","component":"number","show_in_list":true,"show_in_form":true,"required":true,"sortable":true},{"name":"stock","component":"number","show_in_list":true,"show_in_form":true,"sortable":true},{"name":"image","component":"image","show_in_list":true,"show_in_form":true},{"name":"category_id","component":"select","show_in_list":true,"show_in_form":true,"searchable":true},{"name":"status","component":"select","show_in_list":true,"show_in_form":true,"searchable":true},{"name":"created_at","component":"datetime","show_in_list":true,"show_in_form":false,"sortable":true}],"options":{"generate_controller":true,"generate_model":true,"generate_view":true,"generate_js":true,"enable_export":true,"enable_batch":true,"enable_auth":true}}', '商品,电商,管理', 1, 1, NOW()),

('系统配置表', '适用于系统配置、参数设置等表', '%config%,%setting%,%param%', '{"fields":[{"name":"id","component":"input","show_in_list":true,"show_in_form":false},{"name":"key","component":"input","show_in_list":true,"show_in_form":true,"required":true,"searchable":true},{"name":"value","component":"textarea","show_in_list":true,"show_in_form":true,"required":true},{"name":"description","component":"textarea","show_in_list":true,"show_in_form":true},{"name":"type","component":"select","show_in_list":true,"show_in_form":true,"searchable":true},{"name":"group","component":"input","show_in_list":true,"show_in_form":true,"searchable":true},{"name":"sort","component":"number","show_in_list":true,"show_in_form":true,"sortable":true},{"name":"status","component":"switch","show_in_list":true,"show_in_form":true,"searchable":true}],"options":{"generate_controller":true,"generate_model":true,"generate_view":true,"generate_js":true,"enable_export":false,"enable_batch":true,"enable_auth":true}}', '配置,系统,参数', 1, 1, NOW());

-- 创建模板使用记录表
CREATE TABLE `ea8_curd_template_usage` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '记录ID',
    `template_id` int(11) NOT NULL COMMENT '模板ID',
    `table_name` varchar(100) NOT NULL COMMENT '使用的表名',
    `user_id` int(11) DEFAULT NULL COMMENT '使用者ID',
    `used_at` datetime DEFAULT NULL COMMENT '使用时间',
    PRIMARY KEY (`id`),
    KEY `idx_template_id` (`template_id`),
    KEY `idx_table_name` (`table_name`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_used_at` (`used_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='CURD模板使用记录表';
