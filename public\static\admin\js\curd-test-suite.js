/**
 * CURD 生成器 V2 测试套件
 */

(function() {
    'use strict';

    // 测试配置
    var testConfig = {
        autoRun: false,
        verbose: true,
        timeout: 5000
    };

    // 测试数据
    var testData = {
        sampleTable: {
            name: 'test_users',
            comment: '测试用户表',
            fields: [
                { name: 'id', type: 'int', primary: true, auto_increment: true, comment: '主键ID' },
                { name: 'username', type: 'varchar', length: 50, nullable: false, comment: '用户名' },
                { name: 'email', type: 'varchar', length: 100, nullable: false, comment: '邮箱' },
                { name: 'password', type: 'varchar', length: 255, nullable: false, comment: '密码' },
                { name: 'status', type: 'tinyint', default: 1, comment: '状态' },
                { name: 'created_at', type: 'datetime', nullable: true, comment: '创建时间' },
                { name: 'updated_at', type: 'datetime', nullable: true, comment: '更新时间' }
            ]
        },
        sampleFieldConfigs: [
            { name: 'id', component: 'number', show_in_list: true, show_in_form: false, show_in_search: false, required: false },
            { name: 'username', component: 'input', show_in_list: true, show_in_form: true, show_in_search: true, required: true },
            { name: 'email', component: 'input', show_in_list: true, show_in_form: true, show_in_search: true, required: true },
            { name: 'password', component: 'password', show_in_list: false, show_in_form: true, show_in_search: false, required: true },
            { name: 'status', component: 'switch', show_in_list: true, show_in_form: true, show_in_search: false, required: false },
            { name: 'created_at', component: 'datetime', show_in_list: true, show_in_form: false, show_in_search: false, required: false },
            { name: 'updated_at', component: 'datetime', show_in_list: false, show_in_form: false, show_in_search: false, required: false }
        ]
    };

    // 测试结果
    var testResults = {
        total: 0,
        passed: 0,
        failed: 0,
        errors: []
    };

    // 测试套件
    var testSuite = {
        // 基础功能测试
        basic: {
            name: '基础功能测试',
            tests: [
                {
                    name: '页面初始化',
                    test: function() {
                        return $('#curd-generator-container').length > 0 &&
                               $('.step-nav').length > 0 &&
                               $('.step-panel').length >= 5;
                    }
                },
                {
                    name: 'CSS样式加载',
                    test: function() {
                        var link = $('link[href*="curd-generator-v2.css"]');
                        return link.length > 0;
                    }
                },
                {
                    name: 'JavaScript模块加载',
                    test: function() {
                        return typeof window.CurdGeneratorV2 === 'object' &&
                               typeof window.CurdCodePreview === 'object' &&
                               typeof window.CurdFieldConfig === 'object' &&
                               typeof window.CurdQualityChecker === 'object';
                    }
                },
                {
                    name: '第三方库加载',
                    test: function() {
                        return typeof Sortable !== 'undefined' &&
                               typeof Prism !== 'undefined';
                    }
                }
            ]
        },

        // 步骤导航测试
        navigation: {
            name: '步骤导航测试',
            tests: [
                {
                    name: '步骤切换功能',
                    test: function() {
                        if (typeof window.CurdGeneratorV2.goToStep !== 'function') return false;
                        
                        // 测试步骤切换
                        window.CurdGeneratorV2.goToStep(2);
                        var step2Active = $('#step-2').hasClass('active');
                        
                        window.CurdGeneratorV2.goToStep(1);
                        var step1Active = $('#step-1').hasClass('active');
                        
                        return step2Active && step1Active;
                    }
                },
                {
                    name: '进度条更新',
                    test: function() {
                        window.CurdGeneratorV2.goToStep(3);
                        var progressWidth = $('#progress-fill').css('width');
                        return progressWidth && progressWidth !== '0px';
                    }
                },
                {
                    name: '按钮状态控制',
                    test: function() {
                        window.CurdGeneratorV2.goToStep(1);
                        var prevHidden = $('#prev-btn').is(':hidden');
                        
                        window.CurdGeneratorV2.goToStep(5);
                        var generateVisible = $('#generate-btn').is(':visible');
                        
                        return prevHidden && generateVisible;
                    }
                }
            ]
        },

        // 表结构分析测试
        tableAnalysis: {
            name: '表结构分析测试',
            tests: [
                {
                    name: '模拟表数据设置',
                    test: function() {
                        window.tableInfo = testData.sampleTable;
                        return window.tableInfo && window.tableInfo.name === 'test_users';
                    }
                },
                {
                    name: '表信息显示',
                    test: function() {
                        if (typeof window.CurdGeneratorV2.displayTableInfo === 'function') {
                            window.CurdGeneratorV2.displayTableInfo();
                        }
                        
                        return $('#info-table-name').text() === 'test_users' &&
                               $('#info-field-count').text() === '7';
                    }
                },
                {
                    name: '字段配置生成',
                    test: function() {
                        window.fieldConfigs = testData.sampleFieldConfigs;
                        return window.fieldConfigs && window.fieldConfigs.length === 7;
                    }
                }
            ]
        },

        // 字段配置测试
        fieldConfig: {
            name: '字段配置测试',
            tests: [
                {
                    name: '字段配置加载',
                    test: function() {
                        if (typeof window.CurdGeneratorV2.loadFieldConfigs === 'function') {
                            window.CurdGeneratorV2.loadFieldConfigs();
                            return $('#field-config-tbody tr').length === 7;
                        }
                        return false;
                    }
                },
                {
                    name: '字段统计更新',
                    test: function() {
                        var listCount = parseInt($('#list-fields-stat').text()) || 0;
                        var formCount = parseInt($('#form-fields-stat').text()) || 0;
                        return listCount > 0 && formCount > 0;
                    }
                },
                {
                    name: '批量配置功能',
                    test: function() {
                        return typeof window.CurdFieldConfig === 'object' &&
                               typeof window.CurdFieldConfig.batchConfig === 'function';
                    }
                },
                {
                    name: '模板管理功能',
                    test: function() {
                        return typeof window.CurdFieldConfig.saveTemplate === 'function' &&
                               typeof window.CurdFieldConfig.loadTemplate === 'function';
                    }
                }
            ]
        },

        // 代码预览测试
        codePreview: {
            name: '代码预览测试',
            tests: [
                {
                    name: '代码预览初始化',
                    test: function() {
                        return typeof window.CurdCodePreview === 'object' &&
                               typeof window.CurdCodePreview.init === 'function';
                    }
                },
                {
                    name: '文件切换功能',
                    test: function() {
                        if (typeof window.CurdCodePreview.switchFile === 'function') {
                            window.CurdCodePreview.switchFile('controller');
                            return $('.file-item[data-file="controller"]').hasClass('active');
                        }
                        return false;
                    }
                },
                {
                    name: '代码生成功能',
                    test: function() {
                        if (typeof window.CurdCodePreview.refresh === 'function') {
                            window.CurdCodePreview.refresh();
                            var codeContent = $('#preview-code').text();
                            return codeContent && codeContent.length > 0 && codeContent.indexOf('<?php') !== -1;
                        }
                        return false;
                    }
                },
                {
                    name: '代码复制功能',
                    test: function() {
                        return typeof window.CurdCodePreview.copy === 'function';
                    }
                }
            ]
        },

        // 质量检查测试
        qualityCheck: {
            name: '质量检查测试',
            tests: [
                {
                    name: '质量检查器初始化',
                    test: function() {
                        return typeof window.CurdQualityChecker === 'object' &&
                               typeof window.CurdQualityChecker.check === 'function';
                    }
                },
                {
                    name: '自动修复功能',
                    test: function() {
                        return typeof window.CurdQualityChecker.autoFix === 'function';
                    }
                },
                {
                    name: '质量报告生成',
                    test: function() {
                        return typeof window.CurdQualityChecker.generateReport === 'function';
                    }
                }
            ]
        },

        // 数据管理测试
        dataManager: {
            name: '数据管理测试',
            tests: [
                {
                    name: '数据管理器初始化',
                    test: function() {
                        return typeof window.CurdDataManager === 'object';
                    }
                },
                {
                    name: '导入功能',
                    test: function() {
                        return typeof window.CurdDataManager.import === 'function';
                    }
                },
                {
                    name: '导出功能',
                    test: function() {
                        return typeof window.CurdDataManager.export === 'function';
                    }
                }
            ]
        },

        // 响应式测试
        responsive: {
            name: '响应式设计测试',
            tests: [
                {
                    name: '移动端样式',
                    test: function() {
                        // 模拟移动端视口
                        var originalWidth = window.innerWidth;
                        
                        // 检查是否有响应式样式
                        var hasResponsiveCSS = $('style, link').toArray().some(function(el) {
                            var content = el.textContent || '';
                            return content.indexOf('@media') !== -1 && content.indexOf('768px') !== -1;
                        });
                        
                        return hasResponsiveCSS;
                    }
                },
                {
                    name: '触摸设备支持',
                    test: function() {
                        // 检查是否有触摸事件支持
                        return 'ontouchstart' in window || navigator.maxTouchPoints > 0;
                    }
                }
            ]
        }
    };

    // 运行单个测试
    function runTest(test) {
        testResults.total++;
        
        try {
            var result = test.test();
            if (result) {
                testResults.passed++;
                if (testConfig.verbose) {
                    console.log('✅ ' + test.name + ' - PASSED');
                }
                return true;
            } else {
                testResults.failed++;
                testResults.errors.push(test.name + ' - 测试失败');
                if (testConfig.verbose) {
                    console.log('❌ ' + test.name + ' - FAILED');
                }
                return false;
            }
        } catch (error) {
            testResults.failed++;
            testResults.errors.push(test.name + ' - 错误: ' + error.message);
            if (testConfig.verbose) {
                console.log('💥 ' + test.name + ' - ERROR: ' + error.message);
            }
            return false;
        }
    }

    // 运行测试套件
    function runTestSuite(suiteName) {
        var suite = testSuite[suiteName];
        if (!suite) {
            console.error('测试套件不存在: ' + suiteName);
            return;
        }

        console.log('\n🧪 运行测试套件: ' + suite.name);
        console.log('=' + '='.repeat(suite.name.length + 10));

        var suiteResults = { passed: 0, failed: 0 };
        
        suite.tests.forEach(function(test) {
            if (runTest(test)) {
                suiteResults.passed++;
            } else {
                suiteResults.failed++;
            }
        });

        console.log('\n📊 套件结果: ' + suiteResults.passed + ' 通过, ' + suiteResults.failed + ' 失败');
        return suiteResults;
    }

    // 运行所有测试
    function runAllTests() {
        console.log('🚀 开始运行 CURD 生成器 V2 测试套件');
        console.log('='.repeat(50));

        // 重置结果
        testResults = { total: 0, passed: 0, failed: 0, errors: [] };

        // 运行所有测试套件
        Object.keys(testSuite).forEach(function(suiteName) {
            runTestSuite(suiteName);
        });

        // 显示总结果
        console.log('\n🎯 测试总结');
        console.log('='.repeat(20));
        console.log('总测试数: ' + testResults.total);
        console.log('通过: ' + testResults.passed);
        console.log('失败: ' + testResults.failed);
        console.log('成功率: ' + ((testResults.passed / testResults.total) * 100).toFixed(1) + '%');

        if (testResults.errors.length > 0) {
            console.log('\n❌ 失败的测试:');
            testResults.errors.forEach(function(error) {
                console.log('  - ' + error);
            });
        }

        // 返回结果
        return {
            success: testResults.failed === 0,
            total: testResults.total,
            passed: testResults.passed,
            failed: testResults.failed,
            errors: testResults.errors
        };
    }

    // 性能测试
    function runPerformanceTest() {
        console.log('\n⚡ 性能测试');
        console.log('='.repeat(20));

        var tests = [
            {
                name: '页面加载时间',
                test: function() {
                    var start = performance.now();
                    // 模拟页面初始化
                    if (window.CurdGeneratorV2 && window.CurdGeneratorV2.init) {
                        window.CurdGeneratorV2.init();
                    }
                    var end = performance.now();
                    return end - start;
                }
            },
            {
                name: '字段配置渲染时间',
                test: function() {
                    var start = performance.now();
                    if (window.CurdGeneratorV2 && window.CurdGeneratorV2.loadFieldConfigs) {
                        window.CurdGeneratorV2.loadFieldConfigs();
                    }
                    var end = performance.now();
                    return end - start;
                }
            },
            {
                name: '代码生成时间',
                test: function() {
                    var start = performance.now();
                    if (window.CurdCodePreview && window.CurdCodePreview.refresh) {
                        window.CurdCodePreview.refresh();
                    }
                    var end = performance.now();
                    return end - start;
                }
            }
        ];

        tests.forEach(function(test) {
            try {
                var time = test.test();
                var status = time < 100 ? '🟢' : time < 500 ? '🟡' : '🔴';
                console.log(status + ' ' + test.name + ': ' + time.toFixed(2) + 'ms');
            } catch (error) {
                console.log('🔴 ' + test.name + ': 错误 - ' + error.message);
            }
        });
    }

    // 内存使用测试
    function runMemoryTest() {
        if (performance.memory) {
            console.log('\n💾 内存使用情况');
            console.log('='.repeat(20));
            console.log('已使用: ' + (performance.memory.usedJSHeapSize / 1024 / 1024).toFixed(2) + ' MB');
            console.log('总分配: ' + (performance.memory.totalJSHeapSize / 1024 / 1024).toFixed(2) + ' MB');
            console.log('限制: ' + (performance.memory.jsHeapSizeLimit / 1024 / 1024).toFixed(2) + ' MB');
        }
    }

    // 暴露测试接口
    window.CurdTestSuite = {
        run: runAllTests,
        runSuite: runTestSuite,
        runPerformance: runPerformanceTest,
        runMemory: runMemoryTest,
        config: testConfig,
        data: testData
    };

    // 自动运行测试（如果配置了自动运行）
    if (testConfig.autoRun) {
        $(document).ready(function() {
            setTimeout(runAllTests, 1000);
        });
    }

    // 添加控制台命令提示
    console.log('🧪 CURD 测试套件已加载');
    console.log('使用 CurdTestSuite.run() 运行所有测试');
    console.log('使用 CurdTestSuite.runSuite("basic") 运行特定测试套件');
    console.log('使用 CurdTestSuite.runPerformance() 运行性能测试');

})();
