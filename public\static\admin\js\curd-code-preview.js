/**
 * CURD 代码预览功能
 */

layui.use(['layer'], function() {
    var layer = layui.layer;

    // 全局变量
    var currentPreviewFile = 'controller';
    var previewFiles = {
        controller: { name: '控制器', ext: 'php', icon: 'layui-icon-file', lang: 'php' },
        model: { name: '模型', ext: 'php', icon: 'layui-icon-file', lang: 'php' },
        view: { name: '视图', ext: 'blade.php', icon: 'layui-icon-template-1', lang: 'html' },
        js: { name: 'JavaScript', ext: 'js', icon: 'layui-icon-file', lang: 'javascript' },
        route: { name: '路由', ext: 'php', icon: 'layui-icon-file', lang: 'php' }
    };

    // 初始化代码预览
    function initCodePreview() {
        bindPreviewEvents();
        switchPreviewFile('controller');
    }

    // 绑定预览相关事件
    function bindPreviewEvents() {
        // 文件切换
        $(document).on('click', '.file-item', function() {
            var fileType = $(this).data('file');
            if (fileType && previewFiles[fileType]) {
                switchPreviewFile(fileType);
            }
        });

        // 刷新预览
        $(document).on('click', '#refresh-preview', function() {
            refreshCodePreview();
        });

        // 复制代码
        $(document).on('click', '#copy-code', function() {
            copyCodeToClipboard();
        });

        // 下载代码
        $(document).on('click', '#download-code', function() {
            downloadCurrentFile();
        });

        // 全屏预览
        $(document).on('click', '#fullscreen-preview', function() {
            toggleFullscreen();
        });

        // 格式化代码
        $(document).on('click', '#format-code', function() {
            formatCode();
        });

        // 自动换行
        $(document).on('click', '#wrap-code', function() {
            toggleWordWrap();
        });
    }

    // 切换预览文件
    function switchPreviewFile(fileType) {
        if (!previewFiles[fileType]) return;

        currentPreviewFile = fileType;
        
        // 更新文件树状态
        $('.file-item').removeClass('active');
        $('.file-item[data-file="' + fileType + '"]').addClass('active');
        
        // 更新标题和图标
        var file = previewFiles[fileType];
        $('#current-file-name').text(file.name);
        $('#current-file-title i').attr('class', 'layui-icon ' + file.icon);
        
        // 更新文件路径
        var basePath = getBasePath(fileType);
        $('#current-file-path').text(basePath);
        
        // 刷新代码内容
        refreshCodePreview();
    }

    // 获取基础路径
    function getBasePath(fileType) {
        var paths = {
            controller: 'app/admin/controller/',
            model: 'app/common/model/',
            view: 'app/admin/view/admin/',
            js: 'public/static/admin/js/',
            route: 'config/'
        };
        return paths[fileType] || '';
    }

    // 刷新代码预览
    function refreshCodePreview() {
        var tableName = $('select[name="table_name"]').val();
        if (!tableName) {
            $('#preview-code').text('// 请先选择数据表');
            updateCodeInfo('', 0, 0);
            return;
        }

        // 显示加载状态
        $('#preview-code').text('// 正在生成代码预览...');
        
        // 生成代码
        var code = generatePreviewCode(currentPreviewFile, tableName);
        $('#preview-code').text(code);
        
        // 更新代码信息
        var lines = code.split('\n').length;
        var size = (new Blob([code]).size / 1024).toFixed(1);
        updateCodeInfo(code, lines, size);
        
        // 代码高亮
        highlightCode();
    }

    // 更新代码信息
    function updateCodeInfo(code, lines, size) {
        $('#current-line-count').text(lines + ' 行');
        $('#current-file-size').text(size + ' KB');
        
        // 更新统计
        if (window.CurdGeneratorV2 && window.CurdGeneratorV2.updateCodeStats) {
            window.CurdGeneratorV2.updateCodeStats(lines);
        }
    }

    // 生成预览代码
    function generatePreviewCode(fileType, tableName) {
        var className = tableName.charAt(0).toUpperCase() + tableName.slice(1);
        var tablePrefix = $('input[name="table_prefix"]').val() || '';
        
        switch (fileType) {
            case 'controller':
                return generateControllerCode(tableName, className);
            case 'model':
                return generateModelCode(tableName, className, tablePrefix);
            case 'view':
                return generateViewCode(tableName);
            case 'js':
                return generateJsCode(tableName);
            case 'route':
                return generateRouteCode(tableName, className);
            default:
                return '// 代码生成中...';
        }
    }

    // 生成控制器代码
    function generateControllerCode(tableName, className) {
        var lines = [
            '<?php',
            '',
            'namespace app\\admin\\controller;',
            '',
            'use app\\common\\controller\\AdminController;',
            'use app\\common\\model\\' + className + ';',
            'use support\\Request;',
            'use support\\Response;',
            '',
            '/**',
            ' * ' + tableName + ' 控制器',
            ' * 自动生成时间: ' + new Date().toLocaleString(),
            ' */',
            'class ' + className + 'Controller extends AdminController',
            '{',
            '    /**',
            '     * 列表页面',
            '     */',
            '    public function index(): Response',
            '    {',
            '        return view(\'admin/' + tableName + '/index\');',
            '    }',
            '',
            '    /**',
            '     * 获取列表数据',
            '     */',
            '    public function select(Request $request): Response',
            '    {',
            '        $model = new ' + className + '();',
            '        ',
            '        // 搜索条件',
            '        $keyword = $request->input(\'keyword\');',
            '        if ($keyword) {',
            '            $model = $model->where(\'name\', \'like\', \'%\' . $keyword . \'%\');',
            '        }',
            '        ',
            '        // 分页查询',
            '        $list = $model->paginate($request->input(\'limit\', 15));',
            '        ',
            '        return $this->success(\'获取成功\', $list);',
            '    }',
            '',
            '    /**',
            '     * 添加页面',
            '     */',
            '    public function add(): Response',
            '    {',
            '        return view(\'admin/' + tableName + '/add\');',
            '    }',
            '',
            '    /**',
            '     * 保存数据',
            '     */',
            '    public function insert(Request $request): Response',
            '    {',
            '        $data = $request->only([',
            '            // 字段列表将根据配置生成',
            '        ]);',
            '        ',
            '        $model = new ' + className + '();',
            '        $result = $model->save($data);',
            '        ',
            '        if ($result) {',
            '            return $this->success(\'添加成功\');',
            '        } else {',
            '            return $this->error(\'添加失败\');',
            '        }',
            '    }',
            '',
            '    /**',
            '     * 编辑页面',
            '     */',
            '    public function edit(Request $request): Response',
            '    {',
            '        $id = $request->input(\'id\');',
            '        $model = ' + className + '::find($id);',
            '        ',
            '        if (!$model) {',
            '            return $this->error(\'记录不存在\');',
            '        }',
            '        ',
            '        return view(\'admin/' + tableName + '/edit\', [\'model\' => $model]);',
            '    }',
            '',
            '    /**',
            '     * 更新数据',
            '     */',
            '    public function update(Request $request): Response',
            '    {',
            '        $id = $request->input(\'id\');',
            '        $model = ' + className + '::find($id);',
            '        ',
            '        if (!$model) {',
            '            return $this->error(\'记录不存在\');',
            '        }',
            '        ',
            '        $data = $request->only([',
            '            // 字段列表将根据配置生成',
            '        ]);',
            '        ',
            '        $result = $model->save($data);',
            '        ',
            '        if ($result) {',
            '            return $this->success(\'更新成功\');',
            '        } else {',
            '            return $this->error(\'更新失败\');',
            '        }',
            '    }',
            '',
            '    /**',
            '     * 删除数据',
            '     */',
            '    public function delete(Request $request): Response',
            '    {',
            '        $ids = $request->input(\'ids\');',
            '        ',
            '        if (empty($ids)) {',
            '            return $this->error(\'请选择要删除的记录\');',
            '        }',
            '        ',
            '        $result = ' + className + '::destroy($ids);',
            '        ',
            '        if ($result) {',
            '            return $this->success(\'删除成功\');',
            '        } else {',
            '            return $this->error(\'删除失败\');',
            '        }',
            '    }',
            '}'
        ];
        return lines.join('\n');
    }

    // 生成模型代码
    function generateModelCode(tableName, className, tablePrefix) {
        var lines = [
            '<?php',
            '',
            'namespace app\\common\\model;',
            '',
            'use support\\Model;',
            '',
            '/**',
            ' * ' + tableName + ' 模型',
            ' * 自动生成时间: ' + new Date().toLocaleString(),
            ' */',
            'class ' + className + ' extends Model',
            '{',
            '    /**',
            '     * 表名',
            '     */',
            '    protected $table = \'' + tablePrefix + tableName + '\';',
            '',
            '    /**',
            '     * 可填充字段',
            '     */',
            '    protected $fillable = [',
            '        // 字段列表将根据配置生成',
            '    ];',
            '',
            '    /**',
            '     * 字段类型转换',
            '     */',
            '    protected $casts = [',
            '        // 类型转换将根据字段类型生成',
            '    ];',
            '',
            '    /**',
            '     * 时间戳',
            '     */',
            '    public $timestamps = true;',
            '',
            '    /**',
            '     * 软删除',
            '     */',
            '    // use SoftDeletes;',
            '',
            '    /**',
            '     * 获取器示例',
            '     */',
            '    // public function getStatusTextAttribute()',
            '    // {',
            '    //     $statusMap = [1 => \'启用\', 0 => \'禁用\'];',
            '    //     return $statusMap[$this->status] ?? \'未知\';',
            '    // }',
            '',
            '    /**',
            '     * 修改器示例',
            '     */',
            '    // public function setPasswordAttribute($value)',
            '    // {',
            '    //     $this->attributes[\'password\'] = password_hash($value, PASSWORD_DEFAULT);',
            '    // }',
            '}'
        ];
        return lines.join('\n');
    }

    // 生成视图代码
    function generateViewCode(tableName) {
        var lines = [
            '@extends(\'admin.layout.base\')',
            '',
            '@section(\'content\')',
            '<div class="layui-fluid">',
            '    <div class="layui-card">',
            '        <div class="layui-card-header">',
            '            <h3>' + tableName + '管理</h3>',
            '        </div>',
            '        <div class="layui-card-body">',
            '            <!-- 搜索表单 -->',
            '            <form class="layui-form" lay-filter="search-form">',
            '                <div class="layui-row layui-col-space15">',
            '                    <div class="layui-col-md3">',
            '                        <input type="text" name="keyword" placeholder="请输入关键词" class="layui-input">',
            '                    </div>',
            '                    <div class="layui-col-md2">',
            '                        <button type="submit" class="layui-btn" lay-submit lay-filter="search">',
            '                            <i class="layui-icon layui-icon-search"></i> 搜索',
            '                        </button>',
            '                    </div>',
            '                </div>',
            '            </form>',
            '',
            '            <!-- 工具栏 -->',
            '            <div class="layui-row layui-col-space15" style="margin-top: 15px;">',
            '                <div class="layui-col-md12">',
            '                    <button type="button" class="layui-btn" id="add-btn">',
            '                        <i class="layui-icon layui-icon-add-1"></i> 添加',
            '                    </button>',
            '                    <button type="button" class="layui-btn layui-btn-danger" id="delete-btn">',
            '                        <i class="layui-icon layui-icon-delete"></i> 删除',
            '                    </button>',
            '                </div>',
            '            </div>',
            '',
            '            <!-- 数据表格 -->',
            '            <table class="layui-hide" id="data-table" lay-filter="data-table"></table>',
            '        </div>',
            '    </div>',
            '</div>',
            '',
            '<!-- 行工具栏 -->',
            '<script type="text/html" id="row-toolbar">',
            '    <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>',
            '    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="delete">删除</a>',
            '</script>',
            '@endsection',
            '',
            '@section(\'script\')',
            '<script src="/static/admin/js/' + tableName + '.js"></script>',
            '@endsection'
        ];
        return lines.join('\n');
    }

    // 生成JS代码
    function generateJsCode(tableName) {
        var lines = [
            '/**',
            ' * ' + tableName + ' 管理 JavaScript',
            ' * 自动生成时间: ' + new Date().toLocaleString(),
            ' */',
            '',
            'layui.use([\'table\', \'form\', \'layer\'], function() {',
            '    var table = layui.table;',
            '    var form = layui.form;',
            '    var layer = layui.layer;',
            '',
            '    // 数据表格',
            '    var tableIns = table.render({',
            '        elem: \'#data-table\',',
            '        url: \'/admin/' + tableName + '/select\',',
            '        method: \'POST\',',
            '        cols: [[',
            '            {type: \'checkbox\', fixed: \'left\'},',
            '            {field: \'id\', title: \'ID\', width: 80, sort: true},',
            '            // 其他字段将根据配置生成',
            '            {',
            '                title: \'操作\',',
            '                width: 150,',
            '                align: \'center\',',
            '                toolbar: \'#row-toolbar\',',
            '                fixed: \'right\'',
            '            }',
            '        ]],',
            '        page: true,',
            '        limit: 15,',
            '        limits: [15, 30, 50, 100]',
            '    });',
            '',
            '    // 搜索',
            '    form.on(\'submit(search)\', function(data) {',
            '        tableIns.reload({',
            '            where: data.field,',
            '            page: {curr: 1}',
            '        });',
            '        return false;',
            '    });',
            '',
            '    // 添加',
            '    $(\'#add-btn\').on(\'click\', function() {',
            '        layer.open({',
            '            type: 2,',
            '            title: \'添加' + tableName + '\',',
            '            area: [\'800px\', \'600px\'],',
            '            content: \'/admin/' + tableName + '/add\'',
            '        });',
            '    });',
            '',
            '    // 批量删除',
            '    $(\'#delete-btn\').on(\'click\', function() {',
            '        var checkStatus = table.checkStatus(\'data-table\');',
            '        var data = checkStatus.data;',
            '        ',
            '        if (data.length === 0) {',
            '            layer.msg(\'请选择要删除的数据\');',
            '            return;',
            '        }',
            '        ',
            '        var ids = data.map(function(item) {',
            '            return item.id;',
            '        });',
            '        ',
            '        layer.confirm(\'确定要删除选中的数据吗？\', {icon: 3}, function(index) {',
            '            deleteData(ids);',
            '            layer.close(index);',
            '        });',
            '    });',
            '',
            '    // 行工具栏事件',
            '    table.on(\'tool(data-table)\', function(obj) {',
            '        var data = obj.data;',
            '        ',
            '        switch(obj.event) {',
            '            case \'edit\':',
            '                layer.open({',
            '                    type: 2,',
            '                    title: \'编辑' + tableName + '\',',
            '                    area: [\'800px\', \'600px\'],',
            '                    content: \'/admin/' + tableName + '/edit?id=\' + data.id',
            '                });',
            '                break;',
            '            case \'delete\':',
            '                layer.confirm(\'确定要删除这条数据吗？\', {icon: 3}, function(index) {',
            '                    deleteData([data.id]);',
            '                    layer.close(index);',
            '                });',
            '                break;',
            '        }',
            '    });',
            '',
            '    // 删除数据',
            '    function deleteData(ids) {',
            '        $.post(\'/admin/' + tableName + '/delete\', {',
            '            ids: ids',
            '        }, function(res) {',
            '            if (res.code === 1) {',
            '                layer.msg(\'删除成功\');',
            '                tableIns.reload();',
            '            } else {',
            '                layer.msg(res.msg || \'删除失败\');',
            '            }',
            '        });',
            '    }',
            '});'
        ];
        return lines.join('\n');
    }

    // 生成路由代码
    function generateRouteCode(tableName, className) {
        var lines = [
            '<?php',
            '/**',
            ' * ' + tableName + ' 路由配置',
            ' * 自动生成时间: ' + new Date().toLocaleString(),
            ' */',
            '',
            'use app\\admin\\controller\\' + className + 'Controller;',
            '',
            '// ' + tableName + ' 管理路由',
            'Route::group(\'/admin/' + tableName + '\', function() {',
            '    Route::get(\'/\', [' + className + 'Controller::class, \'index\'])',
            '        ->name(\'admin.' + tableName + '.index\');',
            '    ',
            '    Route::post(\'/select\', [' + className + 'Controller::class, \'select\'])',
            '        ->name(\'admin.' + tableName + '.select\');',
            '    ',
            '    Route::get(\'/add\', [' + className + 'Controller::class, \'add\'])',
            '        ->name(\'admin.' + tableName + '.add\');',
            '    ',
            '    Route::post(\'/insert\', [' + className + 'Controller::class, \'insert\'])',
            '        ->name(\'admin.' + tableName + '.insert\');',
            '    ',
            '    Route::get(\'/edit\', [' + className + 'Controller::class, \'edit\'])',
            '        ->name(\'admin.' + tableName + '.edit\');',
            '    ',
            '    Route::post(\'/update\', [' + className + 'Controller::class, \'update\'])',
            '        ->name(\'admin.' + tableName + '.update\');',
            '    ',
            '    Route::post(\'/delete\', [' + className + 'Controller::class, \'delete\'])',
            '        ->name(\'admin.' + tableName + '.delete\');',
            '});'
        ];
        return lines.join('\n');
    }

    // 代码高亮
    function highlightCode() {
        if (window.Prism) {
            Prism.highlightAll();
        } else if (window.hljs) {
            $('#preview-code').each(function(i, block) {
                hljs.highlightBlock(block);
            });
        }
    }

    // 复制代码到剪贴板
    function copyCodeToClipboard() {
        var code = $('#preview-code').text();
        if (navigator.clipboard) {
            navigator.clipboard.writeText(code).then(function() {
                layer.msg('代码已复制到剪贴板', {icon: 1});
            }).catch(function() {
                fallbackCopyTextToClipboard(code);
            });
        } else {
            fallbackCopyTextToClipboard(code);
        }
    }

    // 兼容性复制方法
    function fallbackCopyTextToClipboard(text) {
        var textArea = document.createElement("textarea");
        textArea.value = text;
        textArea.style.position = "fixed";
        textArea.style.top = "-1000px";
        textArea.style.left = "-1000px";
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();
        
        try {
            var successful = document.execCommand('copy');
            if (successful) {
                layer.msg('代码已复制到剪贴板', {icon: 1});
            } else {
                layer.msg('复制失败，请手动复制', {icon: 2});
            }
        } catch (err) {
            layer.msg('复制失败，请手动复制', {icon: 2});
        }
        
        document.body.removeChild(textArea);
    }

    // 下载当前文件
    function downloadCurrentFile() {
        var code = $('#preview-code').text();
        var fileName = getCurrentFileName();
        var blob = new Blob([code], { type: 'text/plain;charset=utf-8' });
        
        if (window.navigator && window.navigator.msSaveOrOpenBlob) {
            // IE
            window.navigator.msSaveOrOpenBlob(blob, fileName);
        } else {
            // 其他浏览器
            var url = window.URL.createObjectURL(blob);
            var a = document.createElement('a');
            a.href = url;
            a.download = fileName;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);
        }
        
        layer.msg('文件下载已开始', {icon: 1});
    }

    // 获取当前文件名
    function getCurrentFileName() {
        var tableName = $('select[name="table_name"]').val() || 'example';
        var className = tableName.charAt(0).toUpperCase() + tableName.slice(1);
        var file = previewFiles[currentPreviewFile];
        
        var fileNames = {
            controller: className + 'Controller.php',
            model: className + '.php',
            view: 'index.blade.php',
            js: tableName + '.js',
            route: tableName + '_routes.php'
        };
        
        return fileNames[currentPreviewFile] || 'code.txt';
    }

    // 切换全屏
    function toggleFullscreen() {
        var $panel = $('.code-preview-panel');
        var $btn = $('#fullscreen-preview');
        
        if ($panel.hasClass('code-preview-fullscreen')) {
            $panel.removeClass('code-preview-fullscreen');
            $btn.find('i').removeClass('layui-icon-screen-restore').addClass('layui-icon-screen-full');
            $btn.attr('title', '全屏预览');
        } else {
            $panel.addClass('code-preview-fullscreen');
            $btn.find('i').removeClass('layui-icon-screen-full').addClass('layui-icon-screen-restore');
            $btn.attr('title', '退出全屏');
        }
    }

    // 格式化代码
    function formatCode() {
        var code = $('#preview-code').text();
        // 这里可以添加代码格式化逻辑
        layer.msg('代码格式化功能开发中', {icon: 0});
    }

    // 切换自动换行
    function toggleWordWrap() {
        var $code = $('#preview-code');
        var $btn = $('#wrap-code');
        
        if ($code.css('white-space') === 'pre-wrap') {
            $code.css('white-space', 'pre');
            $btn.removeClass('active').attr('title', '开启自动换行');
        } else {
            $code.css('white-space', 'pre-wrap');
            $btn.addClass('active').attr('title', '关闭自动换行');
        }
    }

    // 暴露全局函数
    window.CurdCodePreview = {
        init: initCodePreview,
        switchFile: switchPreviewFile,
        refresh: refreshCodePreview,
        copy: copyCodeToClipboard,
        download: downloadCurrentFile
    };

    // 自动初始化
    $(document).ready(function() {
        if ($('#preview-code').length > 0) {
            initCodePreview();
        }
    });
});
