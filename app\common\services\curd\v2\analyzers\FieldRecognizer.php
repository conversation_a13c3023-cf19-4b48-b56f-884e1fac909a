<?php

namespace app\common\services\curd\v2\analyzers;

use app\common\services\curd\v2\dto\FieldInfo;

/**
 * 字段识别器
 * 基于字段名称、类型等信息智能识别合适的表单组件
 */
class FieldRecognizer
{
    protected array $namePatterns;
    protected array $typePatterns;
    protected array $defaultConfig;

    public function __construct()
    {
        $this->loadConfig();
    }

    /**
     * 识别字段配置
     */
    public function recognize(FieldInfo $fieldInfo): array
    {
        $config = $this->defaultConfig;
        
        // 基于字段名称识别
        $nameConfig = $this->recognizeByName($fieldInfo->getName());
        if ($nameConfig) {
            $config = array_merge($config, $nameConfig);
        }

        // 基于字段类型识别
        $typeConfig = $this->recognizeByType($fieldInfo->getType());
        if ($typeConfig) {
            $config = array_merge($config, $typeConfig);
        }

        // 特殊字段处理
        $specialConfig = $this->recognizeSpecialFields($fieldInfo);
        if ($specialConfig) {
            $config = array_merge($config, $specialConfig);
        }

        return $config;
    }

    /**
     * 基于字段名称识别
     */
    protected function recognizeByName(string $fieldName): ?array
    {
        foreach ($this->namePatterns as $pattern => $config) {
            if (preg_match($pattern, $fieldName)) {
                return $config;
            }
        }
        return null;
    }

    /**
     * 基于字段类型识别
     */
    protected function recognizeByType(string $fieldType): ?array
    {
        // 标准化类型
        $normalizedType = $this->normalizeType($fieldType);
        
        return $this->typePatterns[$normalizedType] ?? null;
    }

    /**
     * 识别特殊字段
     */
    protected function recognizeSpecialFields(FieldInfo $fieldInfo): ?array
    {
        $fieldName = $fieldInfo->getName();
        $fieldType = $fieldInfo->getType();

        // 主键字段
        if ($fieldInfo->isPrimary()) {
            return [
                'show_in_form' => false,
                'show_in_list' => true,
                'sortable' => true,
            ];
        }

        // 时间戳字段
        if (in_array($fieldName, ['created_at', 'updated_at', 'deleted_at'])) {
            return [
                'component' => 'datetime',
                'show_in_form' => false,
                'show_in_list' => true,
                'sortable' => true,
            ];
        }

        // 软删除字段
        if ($fieldName === 'delete_time') {
            return [
                'show_in_form' => false,
                'show_in_list' => false,
            ];
        }

        // 布尔类型字段
        if (preg_match('/tinyint\(1\)/', $fieldType)) {
            return [
                'component' => 'switch',
                'searchable' => true,
            ];
        }

        return null;
    }

    /**
     * 标准化字段类型
     */
    protected function normalizeType(string $fieldType): string
    {
        // 移除长度限制
        $type = preg_replace('/\(\d+\)/', '', $fieldType);
        
        // 移除 unsigned 等修饰符
        $type = preg_replace('/\s+(unsigned|signed|zerofill)/i', '', $type);
        
        return strtolower(trim($type));
    }

    /**
     * 加载配置
     */
    protected function loadConfig(): void
    {
        $this->defaultConfig = [
            'component' => 'input',
            'show_in_list' => true,
            'show_in_form' => true,
            'searchable' => false,
            'sortable' => false,
            'options' => [],
        ];

        $this->namePatterns = [
            // 外键字段
            '/^.*_id$/' => [
                'component' => 'select',
                'searchable' => true,
                'options' => ['remote' => true],
            ],
            
            // 密码字段
            '/^(password|pwd|passwd)$/i' => [
                'component' => 'password',
                'show_in_list' => false,
            ],
            
            // 邮箱字段
            '/^(email|mail)$/i' => [
                'component' => 'email',
                'searchable' => true,
            ],
            
            // 电话字段
            '/^(phone|mobile|tel|telephone)$/i' => [
                'component' => 'tel',
                'searchable' => true,
            ],
            
            // URL字段
            '/^(url|link|website|homepage)$/i' => [
                'component' => 'url',
            ],
            
            // 富文本字段
            '/^(content|description|desc|detail|intro|summary)$/i' => [
                'component' => 'editor',
                'show_in_list' => false,
            ],
            
            // 图片字段
            '/^(image|img|avatar|photo|picture|logo|icon)$/i' => [
                'component' => 'image',
                'show_in_list' => true,
            ],
            
            // 文件字段
            '/^(file|attachment|document|upload)$/i' => [
                'component' => 'file',
                'show_in_list' => false,
            ],
            
            // 排序字段
            '/^(sort|order|position|weight|priority)$/i' => [
                'component' => 'number',
                'sortable' => true,
                'searchable' => false,
            ],
            
            // 状态字段
            '/^(status|state|flag)$/i' => [
                'component' => 'select',
                'searchable' => true,
                'options' => [
                    'options' => [
                        ['value' => 0, 'label' => '禁用'],
                        ['value' => 1, 'label' => '启用'],
                    ]
                ],
            ],
            
            // 性别字段
            '/^(sex|gender)$/i' => [
                'component' => 'radio',
                'searchable' => true,
                'options' => [
                    'options' => [
                        ['value' => 1, 'label' => '男'],
                        ['value' => 2, 'label' => '女'],
                    ]
                ],
            ],
            
            // 颜色字段
            '/^(color|colour)$/i' => [
                'component' => 'color',
            ],
            
            // 标签字段
            '/^(tags|keywords)$/i' => [
                'component' => 'tags',
                'show_in_list' => false,
            ],
        ];

        $this->typePatterns = [
            // 文本类型
            'text' => [
                'component' => 'textarea',
                'show_in_list' => false,
            ],
            'longtext' => [
                'component' => 'editor',
                'show_in_list' => false,
            ],
            'mediumtext' => [
                'component' => 'textarea',
                'show_in_list' => false,
            ],
            
            // 数字类型
            'int' => [
                'component' => 'number',
                'sortable' => true,
            ],
            'bigint' => [
                'component' => 'number',
                'sortable' => true,
            ],
            'smallint' => [
                'component' => 'number',
                'sortable' => true,
            ],
            'decimal' => [
                'component' => 'number',
                'sortable' => true,
                'options' => ['step' => '0.01'],
            ],
            'float' => [
                'component' => 'number',
                'sortable' => true,
                'options' => ['step' => '0.01'],
            ],
            'double' => [
                'component' => 'number',
                'sortable' => true,
                'options' => ['step' => '0.01'],
            ],
            
            // 日期时间类型
            'datetime' => [
                'component' => 'datetime',
                'sortable' => true,
            ],
            'date' => [
                'component' => 'date',
                'sortable' => true,
            ],
            'time' => [
                'component' => 'time',
            ],
            'timestamp' => [
                'component' => 'datetime',
                'sortable' => true,
            ],
            
            // 枚举类型
            'enum' => [
                'component' => 'select',
                'searchable' => true,
            ],
            'set' => [
                'component' => 'checkbox',
            ],
            
            // JSON类型
            'json' => [
                'component' => 'json_editor',
                'show_in_list' => false,
            ],
        ];
    }
}
