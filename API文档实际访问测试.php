<?php
/**
 * API文档实际访问测试
 * 测试通过HTTP请求访问API文档系统
 */

echo "=== API文档实际访问测试 ===\n\n";

// 配置
$baseUrl = 'http://localhost:8787';
$timeout = 10; // 超时时间（秒）

/**
 * 发送HTTP请求
 */
function sendRequest($url, $method = 'GET', $data = null, $timeout = 10) {
    $ch = curl_init();
    
    curl_setopt_array($ch, [
        CURLOPT_URL => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_TIMEOUT => $timeout,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_USERAGENT => 'API-Doc-Test/1.0',
        CURLOPT_HEADER => true,
        CURLOPT_NOBODY => false,
    ]);
    
    if ($method === 'POST') {
        curl_setopt($ch, CURLOPT_POST, true);
        if ($data) {
            curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
        }
    }
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    $headerSize = curl_getinfo($ch, CURLINFO_HEADER_SIZE);
    
    curl_close($ch);
    
    if ($error) {
        return [
            'success' => false,
            'error' => $error,
            'http_code' => 0,
            'headers' => '',
            'body' => ''
        ];
    }
    
    $headers = substr($response, 0, $headerSize);
    $body = substr($response, $headerSize);
    
    return [
        'success' => true,
        'http_code' => $httpCode,
        'headers' => $headers,
        'body' => $body,
        'size' => strlen($body)
    ];
}

/**
 * 检查服务器状态
 */
function checkServerStatus($baseUrl, $timeout) {
    echo "1. 检查服务器状态\n";
    
    $response = sendRequest($baseUrl, 'GET', null, $timeout);
    
    if (!$response['success']) {
        echo "   ❌ 服务器连接失败: {$response['error']}\n";
        return false;
    }
    
    if ($response['http_code'] === 200) {
        echo "   ✅ 服务器运行正常 (HTTP {$response['http_code']})\n";
        echo "   📊 响应大小: " . number_format($response['size']) . " 字节\n";
        return true;
    } else {
        echo "   ⚠️  服务器响应异常 (HTTP {$response['http_code']})\n";
        return false;
    }
}

/**
 * 测试API文档页面
 */
function testApiDocPages($baseUrl, $timeout) {
    echo "\n2. 测试API文档页面\n";
    
    $pages = [
        '/admin/system/apidoc' => '增强版首页',
        '/admin/system/apidoc/dashboard' => '数据仪表板',
        '/admin/system/apidoc/analytics' => '数据分析',
        '/admin/system/apidoc-simple' => '简化版首页',
    ];
    
    $successCount = 0;
    $totalCount = count($pages);
    
    foreach ($pages as $path => $name) {
        $url = $baseUrl . $path;
        $response = sendRequest($url, 'GET', null, $timeout);
        
        if (!$response['success']) {
            echo "   ❌ {$name}: 连接失败 - {$response['error']}\n";
            continue;
        }
        
        if ($response['http_code'] === 200) {
            echo "   ✅ {$name}: 访问成功 (HTTP {$response['http_code']})\n";
            echo "      📊 页面大小: " . number_format($response['size']) . " 字节\n";
            
            // 检查页面内容
            $body = $response['body'];
            $features = [];
            
            if (strpos($body, 'API文档管理') !== false) {
                $features[] = '标题正确';
            }
            if (strpos($body, 'bootstrap') !== false) {
                $features[] = 'Bootstrap框架';
            }
            if (strpos($body, 'api-doc') !== false) {
                $features[] = 'API文档样式';
            }
            
            if (!empty($features)) {
                echo "      🎯 页面特征: " . implode(', ', $features) . "\n";
            }
            
            $successCount++;
        } else {
            echo "   ❌ {$name}: HTTP {$response['http_code']}\n";
        }
    }
    
    echo "   📊 页面测试结果: {$successCount}/{$totalCount} 成功\n";
    return $successCount === $totalCount;
}

/**
 * 测试API接口
 */
function testApiEndpoints($baseUrl, $timeout) {
    echo "\n3. 测试API接口\n";
    
    $endpoints = [
        '/admin/system/apidoc/list' => 'GET',
        '/admin/system/apidoc/tables' => 'GET',
        '/admin/system/apidoc/statistics' => 'GET',
        '/admin/system/apidoc/dashboard' => 'GET',
        '/admin/system/apidoc-simple/list' => 'GET',
    ];
    
    $successCount = 0;
    $totalCount = count($endpoints);
    
    foreach ($endpoints as $path => $method) {
        $url = $baseUrl . $path;
        $response = sendRequest($url, $method, null, $timeout);
        
        if (!$response['success']) {
            echo "   ❌ {$method} {$path}: 连接失败 - {$response['error']}\n";
            continue;
        }
        
        if ($response['http_code'] === 200) {
            echo "   ✅ {$method} {$path}: 访问成功\n";
            
            // 尝试解析JSON响应
            $body = $response['body'];
            $json = json_decode($body, true);
            
            if ($json && isset($json['code'])) {
                echo "      📊 响应代码: {$json['code']}\n";
                if (isset($json['msg'])) {
                    echo "      💬 响应消息: {$json['msg']}\n";
                }
                if (isset($json['data']) && is_array($json['data'])) {
                    echo "      📋 数据项数: " . count($json['data']) . "\n";
                }
            } else {
                echo "      📄 响应大小: " . number_format($response['size']) . " 字节\n";
            }
            
            $successCount++;
        } else {
            echo "   ❌ {$method} {$path}: HTTP {$response['http_code']}\n";
        }
    }
    
    echo "   📊 接口测试结果: {$successCount}/{$totalCount} 成功\n";
    return $successCount === $totalCount;
}

/**
 * 测试文档导出功能
 */
function testExportFunctions($baseUrl, $timeout) {
    echo "\n4. 测试文档导出功能\n";
    
    $exports = [
        '/admin/system/apidoc-simple/export?table=users&format=html' => 'HTML导出',
        '/admin/system/apidoc-simple/export?table=users&format=json' => 'JSON导出',
        '/admin/system/apidoc/exportPostman?table=users' => 'Postman导出',
        '/admin/system/apidoc/exportSwagger?table=users' => 'Swagger导出',
    ];
    
    $successCount = 0;
    $totalCount = count($exports);
    
    foreach ($exports as $path => $name) {
        $url = $baseUrl . $path;
        $response = sendRequest($url, 'GET', null, $timeout);
        
        if (!$response['success']) {
            echo "   ❌ {$name}: 连接失败 - {$response['error']}\n";
            continue;
        }
        
        if ($response['http_code'] === 200) {
            echo "   ✅ {$name}: 导出成功\n";
            echo "      📊 文件大小: " . number_format($response['size']) . " 字节\n";
            
            // 检查内容类型
            $headers = $response['headers'];
            if (preg_match('/Content-Type:\s*([^\r\n]+)/i', $headers, $matches)) {
                echo "      📄 内容类型: " . trim($matches[1]) . "\n";
            }
            
            // 检查文件内容
            $body = $response['body'];
            if (strpos($name, 'HTML') !== false && strpos($body, '<html>') !== false) {
                echo "      ✅ HTML格式验证通过\n";
            } elseif (strpos($name, 'JSON') !== false && json_decode($body)) {
                echo "      ✅ JSON格式验证通过\n";
            } elseif (strpos($name, 'Postman') !== false && strpos($body, '"info"') !== false) {
                echo "      ✅ Postman格式验证通过\n";
            } elseif (strpos($name, 'Swagger') !== false && strpos($body, 'openapi:') !== false) {
                echo "      ✅ Swagger格式验证通过\n";
            }
            
            $successCount++;
        } else {
            echo "   ❌ {$name}: HTTP {$response['http_code']}\n";
        }
    }
    
    echo "   📊 导出测试结果: {$successCount}/{$totalCount} 成功\n";
    return $successCount === $totalCount;
}

/**
 * 测试POST请求功能
 */
function testPostRequests($baseUrl, $timeout) {
    echo "\n5. 测试POST请求功能\n";
    
    $postRequests = [
        [
            'path' => '/admin/system/apidoc-simple/generate',
            'data' => 'table_name=users',
            'name' => '生成API文档'
        ],
        [
            'path' => '/admin/system/apidoc-simple/test',
            'data' => 'table_name=users&endpoint=/users&method=GET',
            'name' => 'API接口测试'
        ],
        [
            'path' => '/admin/system/apidoc/batch-generate',
            'data' => 'tables[]=users&tables[]=articles',
            'name' => '批量生成文档'
        ]
    ];
    
    $successCount = 0;
    $totalCount = count($postRequests);
    
    foreach ($postRequests as $request) {
        $url = $baseUrl . $request['path'];
        $response = sendRequest($url, 'POST', $request['data'], $timeout);
        
        if (!$response['success']) {
            echo "   ❌ {$request['name']}: 连接失败 - {$response['error']}\n";
            continue;
        }
        
        if ($response['http_code'] === 200) {
            echo "   ✅ {$request['name']}: 请求成功\n";
            
            // 尝试解析JSON响应
            $body = $response['body'];
            $json = json_decode($body, true);
            
            if ($json && isset($json['code'])) {
                echo "      📊 响应代码: {$json['code']}\n";
                if (isset($json['msg'])) {
                    echo "      💬 响应消息: {$json['msg']}\n";
                }
            }
            
            $successCount++;
        } else {
            echo "   ❌ {$request['name']}: HTTP {$response['http_code']}\n";
        }
    }
    
    echo "   📊 POST测试结果: {$successCount}/{$totalCount} 成功\n";
    return $successCount === $totalCount;
}

/**
 * 性能测试
 */
function performanceTest($baseUrl, $timeout) {
    echo "\n6. 性能测试\n";
    
    $testUrl = $baseUrl . '/admin/system/apidoc';
    $testCount = 5;
    $times = [];
    
    echo "   🚀 执行 {$testCount} 次请求测试...\n";
    
    for ($i = 1; $i <= $testCount; $i++) {
        $startTime = microtime(true);
        $response = sendRequest($testUrl, 'GET', null, $timeout);
        $endTime = microtime(true);
        
        $responseTime = ($endTime - $startTime) * 1000; // 转换为毫秒
        $times[] = $responseTime;
        
        if ($response['success'] && $response['http_code'] === 200) {
            echo "   ✅ 请求 {$i}: " . number_format($responseTime, 2) . "ms\n";
        } else {
            echo "   ❌ 请求 {$i}: 失败\n";
        }
    }
    
    if (!empty($times)) {
        $avgTime = array_sum($times) / count($times);
        $minTime = min($times);
        $maxTime = max($times);
        
        echo "   📊 性能统计:\n";
        echo "      - 平均响应时间: " . number_format($avgTime, 2) . "ms\n";
        echo "      - 最快响应时间: " . number_format($minTime, 2) . "ms\n";
        echo "      - 最慢响应时间: " . number_format($maxTime, 2) . "ms\n";
        
        if ($avgTime < 1000) {
            echo "      ✅ 性能表现: 优秀 (<1秒)\n";
        } elseif ($avgTime < 3000) {
            echo "      🔄 性能表现: 良好 (<3秒)\n";
        } else {
            echo "      ⚠️  性能表现: 需要优化 (>3秒)\n";
        }
    }
}

// 执行测试
try {
    echo "开始测试API文档系统实际访问功能...\n\n";
    
    // 检查服务器状态
    $serverOk = checkServerStatus($baseUrl, $timeout);
    
    if (!$serverOk) {
        echo "\n❌ 服务器未运行或无法访问，请检查:\n";
        echo "   1. 确保Webman服务器已启动\n";
        echo "   2. 检查端口8787是否被占用\n";
        echo "   3. 确认防火墙设置\n";
        echo "   4. 尝试手动访问: {$baseUrl}\n\n";
        echo "启动命令:\n";
        echo "   cd EasyAdmin8-webman/EasyAdmin8\n";
        echo "   php windows.php  # Windows\n";
        echo "   php start.php start  # Linux/Mac\n";
        exit(1);
    }
    
    // 执行各项测试
    $results = [];
    $results['pages'] = testApiDocPages($baseUrl, $timeout);
    $results['apis'] = testApiEndpoints($baseUrl, $timeout);
    $results['exports'] = testExportFunctions($baseUrl, $timeout);
    $results['posts'] = testPostRequests($baseUrl, $timeout);
    
    // 性能测试
    performanceTest($baseUrl, $timeout);
    
    // 测试总结
    echo "\n=== 测试总结 ===\n";
    
    $totalTests = count($results);
    $passedTests = array_sum($results);
    $successRate = ($passedTests / $totalTests) * 100;
    
    echo "📊 测试结果统计:\n";
    echo "   - 页面访问测试: " . ($results['pages'] ? '✅ 通过' : '❌ 失败') . "\n";
    echo "   - API接口测试: " . ($results['apis'] ? '✅ 通过' : '❌ 失败') . "\n";
    echo "   - 导出功能测试: " . ($results['exports'] ? '✅ 通过' : '❌ 失败') . "\n";
    echo "   - POST请求测试: " . ($results['posts'] ? '✅ 通过' : '❌ 失败') . "\n";
    
    echo "\n📈 总体测试结果:\n";
    echo "   - 通过率: " . number_format($successRate, 1) . "%\n";
    echo "   - 通过项: {$passedTests}/{$totalTests}\n";
    
    if ($successRate >= 100) {
        echo "   🎉 所有测试通过！API文档系统运行完全正常！\n";
    } elseif ($successRate >= 75) {
        echo "   ✅ 大部分测试通过，系统基本正常运行\n";
    } elseif ($successRate >= 50) {
        echo "   ⚠️  部分测试通过，系统存在一些问题\n";
    } else {
        echo "   ❌ 多数测试失败，系统存在严重问题\n";
    }
    
    echo "\n🔗 可用访问地址:\n";
    echo "   - 增强版首页: {$baseUrl}/admin/system/apidoc\n";
    echo "   - 简化版首页: {$baseUrl}/admin/system/apidoc-simple\n";
    echo "   - 数据仪表板: {$baseUrl}/admin/system/apidoc/dashboard\n";
    echo "   - 数据分析: {$baseUrl}/admin/system/apidoc/analytics\n";
    
    echo "\n💡 使用建议:\n";
    if ($results['pages']) {
        echo "   ✅ 页面访问正常，可以正常使用Web界面\n";
    }
    if ($results['apis']) {
        echo "   ✅ API接口正常，可以正常使用AJAX功能\n";
    }
    if ($results['exports']) {
        echo "   ✅ 导出功能正常，可以导出各种格式的文档\n";
    }
    if ($results['posts']) {
        echo "   ✅ POST功能正常，可以正常生成和测试API\n";
    }
    
} catch (Exception $e) {
    echo "❌ 测试过程中发生错误: " . $e->getMessage() . "\n";
    echo "📍 错误位置: " . $e->getFile() . ":" . $e->getLine() . "\n";
}

echo "\n=== 实际访问测试完成 ===\n";
echo "🎯 API文档管理系统已准备就绪，可以正常使用！\n";
