<?php
/**
 * 数据库配置验证脚本
 * 验证多数据库配置是否正确
 */

require_once __DIR__ . '/vendor/autoload.php';

echo "=== EasyAdmin8-webman 多数据库配置验证 ===\n\n";

// 1. 检查配置文件
echo "1. 检查配置文件\n";
$configFiles = [
    'config/database.php' => '原始数据库配置',
    'config/database_optimized.php' => '优化数据库配置',
    '.env.multi-database.example' => '多数据库环境配置示例',
];

foreach ($configFiles as $file => $description) {
    if (file_exists($file)) {
        echo "   ✅ {$description}: {$file}\n";
    } else {
        echo "   ❌ {$description}: {$file} - 文件不存在\n";
    }
}

// 2. 检查服务类文件
echo "\n2. 检查服务类文件\n";
$serviceFiles = [
    'app/common/service/DatabaseManager.php' => '数据库管理器',
    'app/admin/controller/system/DatabaseTestController.php' => '数据库测试控制器',
    'app/admin/view/admin/system/databasetest/index.blade.php' => '数据库测试视图',
];

foreach ($serviceFiles as $file => $description) {
    if (file_exists($file)) {
        echo "   ✅ {$description}: {$file}\n";
    } else {
        echo "   ❌ {$description}: {$file} - 文件不存在\n";
    }
}

// 3. 检查配置内容
echo "\n3. 检查优化配置内容\n";
if (file_exists('config/database_optimized.php')) {
    $config = include 'config/database_optimized.php';
    
    $expectedConnections = [
        'mysql' => '主数据库连接',
        'mysql_read' => '读库连接',
        'mysql_without_prefix' => '无前缀连接',
        'mysql_second' => '第二数据库连接',
        'mysql_log' => '日志数据库连接',
        'mysql_cache' => '缓存数据库连接',
        'pgsql' => 'PostgreSQL连接',
        'sqlite' => 'SQLite连接',
        'sqlsrv' => 'SQL Server连接',
    ];
    
    foreach ($expectedConnections as $name => $description) {
        if (isset($config['connections'][$name])) {
            echo "   ✅ {$description}: {$name}\n";
        } else {
            echo "   ❌ {$description}: {$name} - 配置缺失\n";
        }
    }
    
    // 检查高级功能配置
    $advancedFeatures = [
        'pool' => '连接池配置',
        'read_write_separation' => '读写分离配置',
        'migrations' => '迁移配置',
        'redis' => 'Redis配置',
    ];
    
    echo "\n   高级功能配置:\n";
    foreach ($advancedFeatures as $key => $description) {
        if (isset($config[$key])) {
            echo "     ✅ {$description}: {$key}\n";
        } else {
            echo "     ❌ {$description}: {$key} - 配置缺失\n";
        }
    }
} else {
    echo "   ❌ 优化配置文件不存在\n";
}

// 4. 检查环境变量示例
echo "\n4. 检查环境变量配置\n";
if (file_exists('.env.multi-database.example')) {
    $envContent = file_get_contents('.env.multi-database.example');
    
    $expectedEnvVars = [
        'DB_CONNECTION' => '默认连接',
        'DB_READ_WRITE_SEPARATION' => '读写分离开关',
        'DB_SECOND_HOST' => '第二数据库主机',
        'DB_LOG_DATABASE' => '日志数据库',
        'DB_CACHE_DATABASE' => '缓存数据库',
        'PGSQL_HOST' => 'PostgreSQL主机',
        'SQLITE_DATABASE' => 'SQLite数据库文件',
        'SQLSRV_HOST' => 'SQL Server主机',
        'DB_POOL_ENABLE' => '连接池开关',
        'REDIS_HOST' => 'Redis主机',
    ];
    
    foreach ($expectedEnvVars as $var => $description) {
        if (strpos($envContent, $var) !== false) {
            echo "   ✅ {$description}: {$var}\n";
        } else {
            echo "   ❌ {$description}: {$var} - 变量缺失\n";
        }
    }
} else {
    echo "   ❌ 环境变量示例文件不存在\n";
}

// 5. 生成使用示例
echo "\n5. 使用示例代码\n";
$examples = [
    '基本连接使用' => '
use app\common\service\DatabaseManager;

// 使用默认连接
$defaultConn = DatabaseManager::connection();

// 使用指定连接
$secondConn = DatabaseManager::connection("mysql_second");

// 使用日志数据库
$logConn = DatabaseManager::logConnection();',

    '读写分离使用' => '
// 读操作使用读库
$readConn = DatabaseManager::readConnection();
$users = $readConn->table("users")->get();

// 写操作使用写库
$writeConn = DatabaseManager::writeConnection();
$writeConn->table("users")->insert($data);',

    '事务处理' => '
// 单数据库事务
DatabaseManager::transaction(function() {
    // 事务操作
}, "mysql_second");

// 多数据库事务
DatabaseManager::multiTransaction(function() {
    // 跨数据库事务操作
}, ["mysql", "mysql_second"]);',

    '连接测试' => '
// 测试单个连接
$result = DatabaseManager::testConnection("mysql_log");

// 测试所有连接
$results = DatabaseManager::testAllConnections();

// 获取连接信息
$info = DatabaseManager::getConnectionInfo("mysql_cache");'
];

foreach ($examples as $title => $code) {
    echo "\n   {$title}:\n";
    echo "   " . str_repeat('-', strlen($title)) . "\n";
    echo $code . "\n";
}

// 6. 配置建议
echo "\n6. 配置建议\n";
$recommendations = [
    '生产环境建议' => [
        '启用读写分离以提高性能',
        '配置连接池减少连接开销',
        '使用专用的日志数据库',
        '配置Redis缓存提升性能',
        '设置合适的连接超时时间',
    ],
    '安全建议' => [
        '为不同数据库使用不同的用户账号',
        '限制数据库用户权限',
        '使用SSL连接（生产环境）',
        '定期更换数据库密码',
        '监控数据库连接数',
    ],
    '性能优化建议' => [
        '合理设置连接池大小',
        '使用持久连接',
        '优化查询语句',
        '添加适当的索引',
        '定期清理日志表',
    ]
];

foreach ($recommendations as $category => $items) {
    echo "\n   {$category}:\n";
    foreach ($items as $item) {
        echo "     • {$item}\n";
    }
}

echo "\n=== 验证完成 ===\n";
echo "如果所有检查都显示 ✅，说明多数据库配置已正确设置\n";
echo "接下来可以:\n";
echo "1. 将 config/database_optimized.php 重命名为 config/database.php\n";
echo "2. 根据 .env.multi-database.example 更新你的 .env 文件\n";
echo "3. 访问 /admin/system/databasetest 进行连接测试\n";
echo "4. 在代码中使用 DatabaseManager 类进行数据库操作\n";
?>