# 项目结构详解

## 根目录结构
```
EasyAdmin8-webman/
├── app/                    # 应用程序代码
├── bootstrap/              # 启动引导文件
├── config/                 # 配置文件目录
├── database/               # 数据库相关文件
├── public/                 # 公共资源文件
├── runtime/                # 运行时文件
├── support/                # 支持类库
├── vendor/                 # Composer 依赖包
├── .env                    # 环境配置文件
├── .example.env            # 环境配置示例
├── composer.json           # Composer 配置
├── start.php               # 主启动文件
├── windows.php             # Windows 启动文件
└── webman                  # webman 命令行工具
```

## app/ 目录详解
```
app/
├── admin/                  # 后台管理模块
│   ├── controller/         # 后台控制器
│   │   ├── system/         # 系统管理控制器
│   │   │   ├── AdminController.php         # 管理员管理
│   │   │   ├── AuthController.php          # 权限管理
│   │   │   ├── MenuController.php          # 菜单管理
│   │   │   ├── ConfigController.php        # 配置管理
│   │   │   ├── CurdGenerateController.php  # CURD 生成器
│   │   │   ├── CurdGenerateV2Controller.php # CURD 生成器 V2
│   │   │   ├── ApiDocController.php        # API 文档管理
│   │   │   ├── LogController.php           # 日志管理
│   │   │   ├── CacheController.php         # 缓存管理
│   │   │   ├── MonitorController.php       # 系统监控
│   │   │   └── UploadfileController.php    # 文件上传
│   │   ├── mall/           # 商城模块控制器
│   │   ├── IndexController.php             # 后台首页
│   │   ├── LoginController.php             # 登录控制器
│   │   └── AjaxController.php              # Ajax 请求处理
│   ├── model/              # 后台数据模型
│   └── view/               # 后台视图模板
│       ├── admin/          # 管理员相关视图
│       ├── system/         # 系统管理视图
│       ├── common/         # 公共视图组件
│       └── layouts/        # 布局模板
├── controller/             # 前台控制器
├── model/                  # 通用数据模型
├── middleware/             # 中间件
├── command/                # 命令行工具
├── process/                # 进程处理
├── view/                   # 前台视图模板
└── functions.php           # 全局函数定义
```

## config/ 目录详解
```
config/
├── admin.php               # 后台管理配置
├── api_doc.php             # API 文档配置
├── app.php                 # 应用基础配置
├── auth.php                # 认证配置
├── autoload.php            # 自动加载配置
├── bootstrap.php           # 启动配置
├── cache.php               # 缓存配置
├── container.php           # 容器配置
├── database.php            # 数据库配置
├── dependence.php          # 依赖配置
├── exception.php           # 异常处理配置
├── log.php                 # 日志配置
├── middleware.php          # 中间件配置
├── process.php             # 进程配置
├── redis.php               # Redis 配置
├── route.php               # 路由配置
├── server.php              # 服务器配置
├── session.php             # 会话配置
├── static.php              # 静态资源配置
├── translation.php         # 多语言配置
└── view.php                # 视图配置
```

## public/ 目录详解
```
public/
├── static/                 # 静态资源
│   ├── admin/              # 后台静态资源
│   │   ├── css/            # 后台样式文件
│   │   ├── js/             # 后台 JavaScript 文件
│   │   ├── images/         # 后台图片资源
│   │   └── plugins/        # 后台插件
│   ├── common/             # 公共静态资源
│   │   ├── layui/          # Layui 框架文件
│   │   ├── jquery/         # jQuery 库
│   │   ├── images/         # 公共图片
│   │   └── plugins/        # 公共插件
│   └── uploads/            # 上传文件目录
├── index.php               # 入口文件
└── .htaccess               # Apache 重写规则
```

## 核心功能模块

### CURD 生成器
- **位置**: `app/admin/controller/system/CurdGenerateV2Controller.php`
- **功能**: 自动生成完整的 CURD 功能
- **特性**: AI 驱动的智能代码生成

### API 文档系统
- **位置**: `app/admin/controller/system/ApiDocController.php`
- **功能**: 自动生成和管理 API 文档
- **特性**: 支持多版本 API 文档管理

### 权限管理系统
- **位置**: `app/admin/controller/system/AuthController.php`
- **功能**: 基于角色的权限控制
- **特性**: 细粒度权限控制

### 系统监控
- **位置**: `app/admin/controller/system/MonitorController.php`
- **功能**: 系统性能监控和日志管理
- **特性**: 实时性能指标监控

## 数据库结构
```
数据库表前缀: ea8_
主要数据表:
├── ea8_admin_user          # 管理员用户表
├── ea8_admin_menu          # 后台菜单表
├── ea8_admin_auth          # 权限表
├── ea8_admin_role          # 角色表
├── ea8_admin_log           # 操作日志表
├── ea8_system_config       # 系统配置表
├── ea8_system_uploadfile   # 上传文件表
└── ea8_system_quick        # 快捷操作表
```

## 关键文件说明

### 启动文件
- `start.php`: 主启动文件，用于 Linux/Mac 环境
- `windows.php`: Windows 专用启动文件
- `webman`: 命令行工具入口

### 配置文件
- `.env`: 环境配置文件（需要从 .example.env 复制）
- `composer.json`: 项目依赖和自动加载配置

### 核心类库
- `support/`: webman 框架支持类库
- `app/functions.php`: 全局辅助函数

## 扩展目录
```
runtime/                    # 运行时目录
├── logs/                   # 日志文件
├── cache/                  # 缓存文件
├── sessions/               # 会话文件
└── views/                  # 编译后的视图文件

database/                   # 数据库目录
├── migrations/             # 数据库迁移文件
├── seeds/                  # 数据填充文件
└── sql/                    # SQL 脚本文件
```