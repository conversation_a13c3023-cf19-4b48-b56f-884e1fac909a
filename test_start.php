<?php
echo "Starting EasyAdmin8 webman server...\n";

// 设置错误报告
ini_set('display_errors', 1);
error_reporting(E_ALL);

// 切换到项目目录
chdir(__DIR__);

// 检查必要文件
if (!file_exists('vendor/autoload.php')) {
    die("Error: vendor/autoload.php not found. Please run 'composer install'\n");
}

if (!file_exists('.env')) {
    die("Error: .env file not found. Please copy .example.env to .env\n");
}

echo "Loading autoloader...\n";
require_once __DIR__ . '/vendor/autoload.php';

echo "Loading environment variables...\n";
use Dotenv\Dotenv;
if (class_exists('Dotenv\Dotenv') && file_exists(base_path() . '/.env')) {
    if (method_exists('Dotenv\Dotenv', 'createUnsafeImmutable')) {
        Dotenv::createUnsafeImmutable(base_path())->load();
    } else {
        Dotenv::createMutable(base_path())->load();
    }
}

echo "Loading app configuration...\n";
use support\App;
App::loadAllConfig(['route']);

echo "Starting server on port " . env('APP_PORT', 8787) . "...\n";

// 启动服务器
try {
    include __DIR__ . '/windows.php';
} catch (Exception $e) {
    echo "Error starting server: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
