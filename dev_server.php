<?php
/**
 * 开发服务器启动器
 * 使用 PHP 内置服务器运行 EasyAdmin8
 */

echo "=== EasyAdmin8 开发服务器 ===\n";

// 检查环境
if (!file_exists('vendor/autoload.php')) {
    die("错误: 请先运行 composer install\n");
}

if (!file_exists('.env')) {
    die("错误: 请先配置 .env 文件\n");
}

// 获取配置
require_once 'vendor/autoload.php';

if (class_exists('Dotenv\Dotenv') && file_exists('.env')) {
    if (method_exists('Dotenv\Dotenv', 'createUnsafeImmutable')) {
        Dotenv\Dotenv::createUnsafeImmutable(__DIR__)->load();
    } else {
        Dotenv\Dotenv::createMutable(__DIR__)->load();
    }
}

$port = $_ENV['APP_PORT'] ?? getenv('APP_PORT') ?? 8787;
$host = '127.0.0.1';

echo "启动开发服务器...\n";
echo "地址: http://{$host}:{$port}\n";
echo "文档根目录: " . __DIR__ . "/public\n";
echo "路由文件: " . __DIR__ . "/router.php\n";
echo "\n按 Ctrl+C 停止服务器\n\n";

// 创建路由文件
$router_content = '<?php
/**
 * 开发服务器路由器
 */

// 静态文件处理
$request_uri = $_SERVER["REQUEST_URI"];
$path = parse_url($request_uri, PHP_URL_PATH);

// 处理静态文件
if ($path !== "/" && file_exists(__DIR__ . "/public" . $path)) {
    return false; // 让内置服务器处理静态文件
}

// 设置工作目录
chdir(__DIR__);

// 加载应用
require_once "vendor/autoload.php";

// 加载环境变量
if (class_exists("Dotenv\Dotenv") && file_exists(".env")) {
    if (method_exists("Dotenv\Dotenv", "createUnsafeImmutable")) {
        Dotenv\Dotenv::createUnsafeImmutable(__DIR__)->load();
    } else {
        Dotenv\Dotenv::createMutable(__DIR__)->load();
    }
}

// 加载配置
support\App::loadAllConfig(["route"]);

try {
    // 创建请求和响应对象
    $request = new support\Request();
    $response = new support\Response();
    
    // 处理请求
    $app = new Webman\App($request, $response);
    $result = $app->process($request);
    
    // 输出响应
    if ($result instanceof support\Response) {
        http_response_code($result->getStatusCode());
        
        foreach ($result->getHeaders() as $name => $values) {
            foreach ($values as $value) {
                header("$name: $value", false);
            }
        }
        
        echo $result->getBody();
    } else {
        echo $result;
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo "错误: " . $e->getMessage();
    if ($_ENV["APP_DEBUG"] ?? false) {
        echo "\n\n" . $e->getTraceAsString();
    }
}
';

file_put_contents('router.php', $router_content);

// 启动内置服务器
$command = "php -S {$host}:{$port} -t public router.php";
echo "执行命令: {$command}\n\n";

// 在 Windows 上启动服务器
if (PHP_OS_FAMILY === 'Windows') {
    $descriptorspec = array(
        0 => array("pipe", "r"),
        1 => array("pipe", "w"),
        2 => array("pipe", "w")
    );
    
    $process = proc_open($command, $descriptorspec, $pipes);
    
    if (is_resource($process)) {
        echo "服务器已启动，访问 http://{$host}:{$port}\n";
        echo "安装页面: http://{$host}:{$port}/install\n";
        echo "管理后台: http://{$host}:{$port}/admin\n\n";
        
        // 读取输出
        while (!feof($pipes[1])) {
            echo fread($pipes[1], 1024);
        }
        
        fclose($pipes[0]);
        fclose($pipes[1]);
        fclose($pipes[2]);
        proc_close($process);
    }
} else {
    // Linux/Unix
    passthru($command);
}
