<?php $__env->startSection('content'); ?>
<div class="layui-fluid curd-generator-v2">
    <!-- 顶部导航栏 -->
    <div class="curd-header">
        <div class="layui-row layui-col-space15">
            <div class="layui-col-md8">
                <h1 class="curd-title">
                    <i class="layui-icon layui-icon-component"></i>
                    CURD 可视化生成器 V2
                </h1>
                <p class="curd-subtitle">智能分析数据表结构，一键生成完整的CURD代码</p>
            </div>
            <div class="layui-col-md4">
                <div class="curd-badges">
                    <span class="layui-badge layui-bg-blue">
                        <i class="layui-icon layui-icon-search"></i> 智能识别
                    </span>
                    <span class="layui-badge layui-bg-green">
                        <i class="layui-icon layui-icon-set"></i> 可视化配置
                    </span>
                    <span class="layui-badge layui-bg-orange">
                        <i class="layui-icon layui-icon-code"></i> 代码预览
                    </span>
                    <span class="layui-badge layui-bg-red">
                        <i class="layui-icon layui-icon-download-circle"></i> 一键生成
                    </span>
                </div>
            </div>
        </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="layui-row layui-col-space15">
        <!-- 左侧步骤导航 -->
        <div class="layui-col-md3">
            <div class="layui-card curd-steps-card">
                <div class="layui-card-header">
                    <i class="layui-icon layui-icon-list"></i> 生成步骤
                </div>
                <div class="layui-card-body">
                    <div class="curd-steps-nav">
                        <div class="step-item active" data-step="1" id="nav-step-1">
                            <div class="step-number">
                                <i class="layui-icon layui-icon-table" id="step-icon-1"></i>
                                <span class="step-num">1</span>
                            </div>
                            <div class="step-content">
                                <h4 id="step-title-1">选择数据表</h4>
                                <p id="step-desc-1">选择数据库连接和数据表</p>
                            </div>
                        </div>

                        <div class="step-item" data-step="2" id="nav-step-2">
                            <div class="step-number">
                                <i class="layui-icon layui-icon-edit" id="step-icon-2"></i>
                                <span class="step-num">2</span>
                            </div>
                            <div class="step-content">
                                <h4 id="step-title-2">配置字段</h4>
                                <p id="step-desc-2">设置字段属性和组件</p>
                            </div>
                        </div>

                        <div class="step-item" data-step="3" id="nav-step-3">
                            <div class="step-number">
                                <i class="layui-icon layui-icon-set" id="step-icon-3"></i>
                                <span class="step-num">3</span>
                            </div>
                            <div class="step-content">
                                <h4 id="step-title-3">生成选项</h4>
                                <p id="step-desc-3">选择生成文件和功能</p>
                            </div>
                        </div>

                        <div class="step-item" data-step="4" id="nav-step-4">
                            <div class="step-number">
                                <i class="layui-icon layui-icon-code" id="step-icon-4"></i>
                                <span class="step-num">4</span>
                            </div>
                            <div class="step-content">
                                <h4 id="step-title-4">预览代码</h4>
                                <p id="step-desc-4">查看生成的代码</p>
                            </div>
                        </div>

                        <div class="step-item" data-step="5" id="nav-step-5">
                            <div class="step-number">
                                <i class="layui-icon layui-icon-ok" id="step-icon-5"></i>
                                <span class="step-num">5</span>
                            </div>
                            <div class="step-content">
                                <h4 id="step-title-5">生成文件</h4>
                                <p id="step-desc-5">下载生成的文件</p>
                            </div>
                        </div>
                    </div>

                    <!-- 进度条 -->
                    <div class="curd-progress">
                        <div class="progress-bar">
                            <div class="progress-fill" id="progress-fill" style="width: 20%"></div>
                        </div>
                        <div class="progress-text">
                            <span id="progress-text">步骤 1 / 5</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 右侧内容区域 -->
        <div class="layui-col-md9">
            <div class="layui-card curd-content-card">
                <div class="layui-card-header">
                    <div class="content-header">
                        <h3 id="current-step-title">选择数据表</h3>
                        <div class="step-actions">
                            <button type="button" class="layui-btn layui-btn-primary" id="prev-btn" style="display: none;">
                                <i class="layui-icon layui-icon-left"></i> 上一步
                            </button>
                            <button type="button" class="layui-btn" id="next-btn">
                                下一步 <i class="layui-icon layui-icon-right"></i>
                            </button>
                            <button type="button" class="layui-btn layui-btn-normal" id="generate-btn" style="display: none;">
                                <i class="layui-icon layui-icon-download-circle"></i> 生成代码
                            </button>
                        </div>
                    </div>
                </div>

                    <!-- 步骤内容 -->
                    <div class="curd-content">
                        <!-- 步骤1: 选择数据表 -->
                        <div class="step-content" id="step-content-1">
                            <div class="layui-form">
                                <div class="layui-row layui-col-space15">
                                    <div class="layui-col-md4">
                                        <div class="layui-form-item">
                                            <label class="layui-form-label">数据库连接</label>
                                            <div class="layui-input-block">
                                                <select name="connection" lay-filter="connectionSelect">
                                                    <option value="mysql">默认连接 (mysql)</option>
                                                    <option value="mysql_read">读库连接 (mysql_read)</option>
                                                    <option value="mysql_second">第二数据库 (mysql_second)</option>
                                                    <option value="mysql_log">日志数据库 (mysql_log)</option>
                                                    <option value="mysql_cache">缓存数据库 (mysql_cache)</option>
                                                    <option value="mysql_without_prefix">无前缀连接 (mysql_without_prefix)</option>
                                                    <option value="pgsql">PostgreSQL (pgsql)</option>
                                                    <option value="sqlite">SQLite (sqlite)</option>
                                                    <option value="sqlsrv">SQL Server (sqlsrv)</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="layui-col-md4">
                                        <div class="layui-form-item">
                                            <label class="layui-form-label">数据库表</label>
                                            <div class="layui-input-block">
                                                <select name="table_name" lay-verify="required" lay-search lay-filter="tableSelect">
                                                    <option value="">请选择数据表</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="layui-col-md4">
                                        <div class="layui-form-item">
                                            <label class="layui-form-label">表前缀</label>
                                            <div class="layui-input-block">
                                                <input type="text" name="table_prefix" placeholder="可选，如: ea8_" class="layui-input">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <div class="layui-input-block">
                                        <button type="button" class="layui-btn" id="analyze-table">
                                            <i class="layui-icon layui-icon-search"></i> 分析表结构
                                        </button>
                                        <button type="button" class="layui-btn layui-btn-primary" id="refresh-tables">
                                            <i class="layui-icon layui-icon-refresh"></i> 刷新表列表
                                        </button>
                                        <button type="button" class="layui-btn layui-btn-warm" id="import-config-btn">
                                            <i class="layui-icon layui-icon-upload"></i> 导入配置
                                        </button>
                                        <button type="button" class="layui-btn layui-btn-normal" id="export-config-btn">
                                            <i class="layui-icon layui-icon-download"></i> 导出配置
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- 表信息显示 -->
                            <div id="table-info" style="display: none;">
                                <div class="layui-panel">
                                    <div class="layui-panel-header">表信息</div>
                                    <div class="layui-panel-body">
                                        <div class="layui-row layui-col-space15">
                                            <div class="layui-col-md4">
                                                <div class="layui-card">
                                                    <div class="layui-card-header">基本信息</div>
                                                    <div class="layui-card-body">
                                                        <p><strong>表名:</strong> <span id="table-name-display"></span></p>
                                                        <p><strong>注释:</strong> <span id="table-comment-display"></span></p>
                                                        <p><strong>字段数:</strong> <span id="field-count-display"></span></p>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="layui-col-md4">
                                                <div class="layui-card">
                                                    <div class="layui-card-header">特性检测</div>
                                                    <div class="layui-card-body">
                                                        <p><strong>时间戳:</strong> <span id="has-timestamps"></span></p>
                                                        <p><strong>软删除:</strong> <span id="has-soft-delete"></span></p>
                                                        <p><strong>主键:</strong> <span id="primary-key"></span></p>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="layui-col-md4">
                                                <div class="layui-card">
                                                    <div class="layui-card-header">智能识别</div>
                                                    <div class="layui-card-body">
                                                        <p><strong>列表字段:</strong> <span id="list-fields-count"></span></p>
                                                        <p><strong>表单字段:</strong> <span id="form-fields-count"></span></p>
                                                        <p><strong>搜索字段:</strong> <span id="search-fields-count"></span></p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 步骤2: 配置字段 -->
                        <div class="step-panel" id="step-2" style="display: none;">
                            <div class="step-intro">
                                <div class="intro-icon">
                                    <i class="layui-icon layui-icon-edit"></i>
                                </div>
                                <div class="intro-text">
                                    <h3>配置字段属性和显示方式</h3>
                                    <p>为每个字段设置显示标签、表单组件、验证规则等属性，支持拖拽排序和批量操作</p>
                                </div>
                            </div>

                            <div class="layui-row layui-col-space20">
                                <!-- 左侧工具栏 -->
                                <div class="layui-col-md3">
                                    <div class="config-panel">
                                        <div class="panel-header">
                                            <h4><i class="layui-icon layui-icon-set"></i> 配置工具</h4>
                                        </div>
                                        <div class="panel-body">
                                            <div class="tool-group">
                                                <h5>批量操作</h5>
                                                <button type="button" class="layui-btn layui-btn-fluid layui-btn-sm" id="batch-config">
                                                    <i class="layui-icon layui-icon-set"></i> 批量设置
                                                </button>
                                                <button type="button" class="layui-btn layui-btn-fluid layui-btn-sm layui-btn-primary" id="reset-config">
                                                    <i class="layui-icon layui-icon-refresh"></i> 重置配置
                                                </button>
                                            </div>

                                            <div class="tool-group">
                                                <h5>排序管理</h5>
                                                <button type="button" class="layui-btn layui-btn-fluid layui-btn-sm layui-btn-warm reset-field-order">
                                                    <i class="layui-icon layui-icon-refresh-1"></i> 重置排序
                                                </button>
                                                <button type="button" class="layui-btn layui-btn-fluid layui-btn-sm layui-btn-normal save-field-order">
                                                    <i class="layui-icon layui-icon-ok"></i> 保存排序
                                                </button>
                                            </div>

                                            <div class="tool-group">
                                                <h5>模板管理</h5>
                                                <button type="button" class="layui-btn layui-btn-fluid layui-btn-sm layui-btn-danger save-template-btn">
                                                    <i class="layui-icon layui-icon-download-circle"></i> 保存模板
                                                </button>
                                                <button type="button" class="layui-btn layui-btn-fluid layui-btn-sm layui-btn-primary load-template-btn">
                                                    <i class="layui-icon layui-icon-upload-circle"></i> 加载模板
                                                </button>
                                                <button type="button" class="layui-btn layui-btn-fluid layui-btn-sm recommend-template-btn">
                                                    <i class="layui-icon layui-icon-star"></i> 智能推荐
                                                </button>
                                            </div>

                                            <div class="tool-group">
                                                <h5>质量检查</h5>
                                                <button type="button" class="layui-btn layui-btn-fluid layui-btn-sm layui-btn-normal" id="quality-check-btn">
                                                    <i class="layui-icon layui-icon-survey"></i> 质量检查
                                                </button>
                                                <button type="button" class="layui-btn layui-btn-fluid layui-btn-sm layui-btn-warm" id="auto-fix-btn">
                                                    <i class="layui-icon layui-icon-engine"></i> 自动修复
                                                </button>
                                                <button type="button" class="layui-btn layui-btn-fluid layui-btn-sm layui-btn-primary" id="quality-report-btn">
                                                    <i class="layui-icon layui-icon-chart"></i> 质量报告
                                                </button>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 字段统计 -->
                                    <div class="info-panel" style="margin-top: 20px;">
                                        <div class="panel-header">
                                            <h4><i class="layui-icon layui-icon-chart"></i> 字段统计</h4>
                                        </div>
                                        <div class="panel-body">
                                            <div class="stat-item">
                                                <div class="stat-label">总字段数</div>
                                                <div class="stat-value" id="total-fields-count">0</div>
                                            </div>
                                            <div class="stat-item">
                                                <div class="stat-label">列表字段</div>
                                                <div class="stat-value" id="list-fields-stat">0</div>
                                            </div>
                                            <div class="stat-item">
                                                <div class="stat-label">表单字段</div>
                                                <div class="stat-value" id="form-fields-stat">0</div>
                                            </div>
                                            <div class="stat-item">
                                                <div class="stat-label">搜索字段</div>
                                                <div class="stat-value" id="search-fields-stat">0</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 右侧字段配置表格 -->
                                <div class="layui-col-md9">
                                    <div class="config-panel">
                                        <div class="panel-header">
                                            <h4><i class="layui-icon layui-icon-table"></i> 字段配置</h4>
                                            <div class="panel-actions">
                                                <div class="layui-form-item" style="margin: 0;">
                                                    <input type="checkbox" lay-skin="primary" id="select-all-fields" title="全选">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="panel-body" style="padding: 0;">
                                            <div class="sort-hint">
                                                <i class="layui-icon layui-icon-tips"></i>
                                                拖拽左侧手柄可调整字段在表单中的显示顺序，点击字段名可快速编辑
                                            </div>

                                            <div class="field-config-container">
                                                <table class="layui-table field-config-table" id="field-config-table" lay-skin="nob">
                                                    <thead>
                                                        <tr>
                                                            <th width="40">
                                                                <i class="layui-icon layui-icon-slider" title="拖拽排序"></i>
                                                            </th>
                                                            <th width="40">
                                                                <input type="checkbox" lay-skin="primary" id="select-all-fields-header">
                                                            </th>
                                                            <th width="120">字段名</th>
                                                            <th width="80">类型</th>
                                                            <th width="120">显示标签</th>
                                                            <th width="120">表单组件</th>
                                                            <th width="60">列表</th>
                                                            <th width="60">表单</th>
                                                            <th width="60">搜索</th>
                                                            <th width="60">必填</th>
                                                            <th width="60">排序</th>
                                                            <th>操作</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody id="field-config-tbody">
                                                        <!-- 动态生成字段配置行 -->
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 步骤3: 生成选项 -->
                        <div class="step-panel" id="step-3" style="display: none;">
                            <div class="step-intro">
                                <div class="intro-icon">
                                    <i class="layui-icon layui-icon-set"></i>
                                </div>
                                <div class="intro-text">
                                    <h3>配置生成选项和高级设置</h3>
                                    <p>选择要生成的文件类型，配置代码风格、功能特性和高级选项</p>
                                </div>
                            </div>

                            <div class="layui-row layui-col-space20">
                                <!-- 左侧基础选项 -->
                                <div class="layui-col-md4">
                                    <div class="config-panel">
                                        <div class="panel-header">
                                            <h4><i class="layui-icon layui-icon-file"></i> 生成文件</h4>
                                        </div>
                                        <div class="panel-body">
                                            <div class="layui-form">
                                                <div class="option-group">
                                                    <h5>基础文件</h5>
                                                    <div class="layui-form-item">
                                                        <input type="checkbox" name="generate_controller" checked title="控制器 (Controller)" lay-skin="primary">
                                                    </div>
                                                    <div class="layui-form-item">
                                                        <input type="checkbox" name="generate_model" checked title="模型 (Model)" lay-skin="primary">
                                                    </div>
                                                    <div class="layui-form-item">
                                                        <input type="checkbox" name="generate_view" checked title="视图 (View)" lay-skin="primary">
                                                    </div>
                                                    <div class="layui-form-item">
                                                        <input type="checkbox" name="generate_js" checked title="JavaScript" lay-skin="primary">
                                                    </div>
                                                    <div class="layui-form-item">
                                                        <input type="checkbox" name="generate_route" checked title="路由 (Route)" lay-skin="primary">
                                                    </div>
                                                </div>

                                                <div class="option-group">
                                                    <h5>扩展文件</h5>
                                                    <div class="layui-form-item">
                                                        <input type="checkbox" name="generate_migration" title="数据库迁移" lay-skin="primary">
                                                    </div>
                                                    <div class="layui-form-item">
                                                        <input type="checkbox" name="generate_seeder" title="数据填充" lay-skin="primary">
                                                    </div>
                                                    <div class="layui-form-item">
                                                        <input type="checkbox" name="generate_test" title="单元测试" lay-skin="primary">
                                                    </div>
                                                    <div class="layui-form-item">
                                                        <input type="checkbox" name="generate_api_doc" title="API文档" lay-skin="primary">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 中间功能特性 -->
                                <div class="layui-col-md4">
                                    <div class="config-panel">
                                        <div class="panel-header">
                                            <h4><i class="layui-icon layui-icon-component"></i> 功能特性</h4>
                                        </div>
                                        <div class="panel-body">
                                            <div class="layui-form">
                                                <div class="option-group">
                                                    <h5>基础功能</h5>
                                                    <div class="layui-form-item">
                                                        <input type="checkbox" name="enable_pagination" checked title="分页功能" lay-skin="primary">
                                                    </div>
                                                    <div class="layui-form-item">
                                                        <input type="checkbox" name="enable_search" checked title="搜索功能" lay-skin="primary">
                                                    </div>
                                                    <div class="layui-form-item">
                                                        <input type="checkbox" name="enable_sort" checked title="排序功能" lay-skin="primary">
                                                    </div>
                                                    <div class="layui-form-item">
                                                        <input type="checkbox" name="enable_export" title="导出功能" lay-skin="primary">
                                                    </div>
                                                    <div class="layui-form-item">
                                                        <input type="checkbox" name="enable_import" title="导入功能" lay-skin="primary">
                                                    </div>
                                                </div>

                                                <div class="option-group">
                                                    <h5>高级功能</h5>
                                                    <div class="layui-form-item">
                                                        <input type="checkbox" name="enable_batch" title="批量操作" lay-skin="primary">
                                                    </div>
                                                    <div class="layui-form-item">
                                                        <input type="checkbox" name="enable_cache" title="缓存优化" lay-skin="primary">
                                                    </div>
                                                    <div class="layui-form-item">
                                                        <input type="checkbox" name="enable_log" title="操作日志" lay-skin="primary">
                                                    </div>
                                                    <div class="layui-form-item">
                                                        <input type="checkbox" name="enable_auth" title="权限控制" lay-skin="primary">
                                                    </div>
                                                    <div class="layui-form-item">
                                                        <input type="checkbox" name="enable_soft_delete" title="软删除" lay-skin="primary">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 右侧代码风格 -->
                                <div class="layui-col-md4">
                                    <div class="config-panel">
                                        <div class="panel-header">
                                            <h4><i class="layui-icon layui-icon-template"></i> 代码风格</h4>
                                        </div>
                                        <div class="panel-body">
                                            <div class="layui-form">
                                                <div class="option-group">
                                                    <h5>命名风格</h5>
                                                    <div class="layui-form-item">
                                                        <label class="layui-form-label">类命名</label>
                                                        <div class="layui-input-block">
                                                            <select name="class_naming">
                                                                <option value="PascalCase" selected>PascalCase</option>
                                                                <option value="camelCase">camelCase</option>
                                                            </select>
                                                        </div>
                                                    </div>
                                                    <div class="layui-form-item">
                                                        <label class="layui-form-label">方法命名</label>
                                                        <div class="layui-input-block">
                                                            <select name="method_naming">
                                                                <option value="camelCase" selected>camelCase</option>
                                                                <option value="snake_case">snake_case</option>
                                                            </select>
                                                        </div>
                                                    </div>
                                                </div>

                                                <div class="option-group">
                                                    <h5>代码规范</h5>
                                                    <div class="layui-form-item">
                                                        <input type="checkbox" name="add_comments" checked title="添加注释" lay-skin="primary">
                                                    </div>
                                                    <div class="layui-form-item">
                                                        <input type="checkbox" name="add_type_hints" checked title="类型提示" lay-skin="primary">
                                                    </div>
                                                    <div class="layui-form-item">
                                                        <input type="checkbox" name="add_validation" checked title="数据验证" lay-skin="primary">
                                                    </div>
                                                    <div class="layui-form-item">
                                                        <input type="checkbox" name="add_exception_handling" title="异常处理" lay-skin="primary">
                                                    </div>
                                                </div>

                                                <div class="option-group">
                                                    <h5>输出设置</h5>
                                                    <div class="layui-form-item">
                                                        <label class="layui-form-label">输出目录</label>
                                                        <div class="layui-input-block">
                                                            <input type="text" name="output_path" placeholder="默认路径" class="layui-input">
                                                        </div>
                                                    </div>
                                                    <div class="layui-form-item">
                                                        <input type="checkbox" name="create_backup" checked title="创建备份" lay-skin="primary">
                                                    </div>
                                                    <div class="layui-form-item">
                                                        <input type="checkbox" name="overwrite_existing" title="覆盖已存在文件" lay-skin="primary">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 步骤4: 预览代码 -->
                        <div class="step-panel" id="step-4" style="display: none;">
                            <div class="step-intro">
                                <div class="intro-icon">
                                    <i class="layui-icon layui-icon-code"></i>
                                </div>
                                <div class="intro-text">
                                    <h3>预览生成的代码</h3>
                                    <p>查看即将生成的控制器、模型、视图和JavaScript代码，支持语法高亮和代码复制</p>
                                </div>
                            </div>

                            <div class="layui-row layui-col-space20">
                                <!-- 左侧代码导航 -->
                                <div class="layui-col-md3">
                                    <div class="config-panel">
                                        <div class="panel-header">
                                            <h4><i class="layui-icon layui-icon-file"></i> 文件列表</h4>
                                        </div>
                                        <div class="panel-body">
                                            <div class="file-tree">
                                                <div class="file-item active" data-file="controller">
                                                    <i class="layui-icon layui-icon-file"></i>
                                                    <span>控制器</span>
                                                    <div class="file-info">Controller.php</div>
                                                    <div class="file-badge">PHP</div>
                                                </div>
                                                <div class="file-item" data-file="model">
                                                    <i class="layui-icon layui-icon-file"></i>
                                                    <span>模型</span>
                                                    <div class="file-info">Model.php</div>
                                                    <div class="file-badge">PHP</div>
                                                </div>
                                                <div class="file-item" data-file="view">
                                                    <i class="layui-icon layui-icon-template-1"></i>
                                                    <span>视图</span>
                                                    <div class="file-info">index.blade.php</div>
                                                    <div class="file-badge">HTML</div>
                                                </div>
                                                <div class="file-item" data-file="js">
                                                    <i class="layui-icon layui-icon-file"></i>
                                                    <span>JavaScript</span>
                                                    <div class="file-info">index.js</div>
                                                    <div class="file-badge">JS</div>
                                                </div>
                                                <div class="file-item" data-file="route">
                                                    <i class="layui-icon layui-icon-file"></i>
                                                    <span>路由</span>
                                                    <div class="file-info">route.php</div>
                                                    <div class="file-badge">PHP</div>
                                                </div>
                                                <div class="file-item" data-file="migration">
                                                    <i class="layui-icon layui-icon-file"></i>
                                                    <span>数据迁移</span>
                                                    <div class="file-info">migration.php</div>
                                                    <div class="file-badge">PHP</div>
                                                </div>
                                                <div class="file-item" data-file="seeder">
                                                    <i class="layui-icon layui-icon-file"></i>
                                                    <span>数据填充</span>
                                                    <div class="file-info">seeder.php</div>
                                                    <div class="file-badge">PHP</div>
                                                </div>
                                                <div class="file-item" data-file="test">
                                                    <i class="layui-icon layui-icon-file"></i>
                                                    <span>单元测试</span>
                                                    <div class="file-info">test.php</div>
                                                    <div class="file-badge">PHP</div>
                                                </div>
                                                <div class="file-item" data-file="api">
                                                    <i class="layui-icon layui-icon-file"></i>
                                                    <span>API接口</span>
                                                    <div class="file-info">api.php</div>
                                                    <div class="file-badge">PHP</div>
                                                </div>
                                                <div class="file-item" data-file="css">
                                                    <i class="layui-icon layui-icon-file"></i>
                                                    <span>样式文件</span>
                                                    <div class="file-info">style.css</div>
                                                    <div class="file-badge">CSS</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 代码统计 -->
                                    <div class="info-panel" style="margin-top: 20px;">
                                        <div class="panel-header">
                                            <h4><i class="layui-icon layui-icon-chart"></i> 代码统计</h4>
                                        </div>
                                        <div class="panel-body">
                                            <div class="stat-item">
                                                <div class="stat-label">总行数</div>
                                                <div class="stat-value" id="total-lines">0</div>
                                            </div>
                                            <div class="stat-item">
                                                <div class="stat-label">文件数</div>
                                                <div class="stat-value" id="total-files">4</div>
                                            </div>
                                            <div class="stat-item">
                                                <div class="stat-label">方法数</div>
                                                <div class="stat-value" id="total-methods">0</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 右侧代码预览 -->
                                <div class="layui-col-md9">
                                    <div class="config-panel code-preview-panel">
                                        <div class="panel-header">
                                            <h4 id="current-file-title">
                                                <i class="layui-icon layui-icon-file"></i>
                                                <span id="current-file-name">控制器</span>
                                            </h4>
                                            <div class="panel-actions">
                                                <button type="button" class="layui-btn layui-btn-xs layui-btn-primary" id="refresh-preview">
                                                    <i class="layui-icon layui-icon-refresh"></i> 刷新
                                                </button>
                                                <button type="button" class="layui-btn layui-btn-xs layui-btn-normal" id="copy-code">
                                                    <i class="layui-icon layui-icon-file"></i> 复制
                                                </button>
                                                <button type="button" class="layui-btn layui-btn-xs layui-btn-warm" id="download-code">
                                                    <i class="layui-icon layui-icon-download-circle"></i> 下载
                                                </button>
                                                <button type="button" class="layui-btn layui-btn-xs" id="fullscreen-preview">
                                                    <i class="layui-icon layui-icon-screen-full"></i> 全屏
                                                </button>
                                            </div>
                                        </div>
                                        <div class="panel-body code-preview-body">
                                            <div class="code-container">
                                                <div class="code-header">
                                                    <div class="code-info">
                                                        <span class="file-path" id="current-file-path">app/admin/controller/</span>
                                                        <span class="file-size" id="current-file-size">0 KB</span>
                                                        <span class="line-count" id="current-line-count">0 行</span>
                                                    </div>
                                                    <div class="code-actions">
                                                        <button type="button" class="code-action-btn" id="format-code" title="格式化代码">
                                                            <i class="layui-icon layui-icon-template"></i>
                                                        </button>
                                                        <button type="button" class="code-action-btn" id="wrap-code" title="自动换行">
                                                            <i class="layui-icon layui-icon-cols"></i>
                                                        </button>
                                                    </div>
                                                </div>
                                                <div class="code-content">
                                                    <pre><code class="language-php" id="preview-code"></code></pre>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 步骤5: 生成文件 -->
                        <div class="step-panel" id="step-5" style="display: none;">
                            <div class="step-intro">
                                <div class="intro-icon">
                                    <i class="layui-icon layui-icon-ok"></i>
                                </div>
                                <div class="intro-text">
                                    <h3>生成并下载文件</h3>
                                    <p>确认配置无误后，点击生成按钮创建所有文件，支持打包下载和在线预览</p>
                                </div>
                            </div>

                            <div class="layui-row layui-col-space20">
                                <!-- 左侧生成选项 -->
                                <div class="layui-col-md4">
                                    <div class="config-panel">
                                        <div class="panel-header">
                                            <h4><i class="layui-icon layui-icon-set"></i> 生成选项</h4>
                                        </div>
                                        <div class="panel-body">
                                            <div class="generate-options">
                                                <div class="option-group">
                                                    <h5>文件类型</h5>
                                                    <div class="layui-form">
                                                        <input type="checkbox" name="gen_controller" checked title="控制器" lay-skin="primary">
                                                        <input type="checkbox" name="gen_model" checked title="模型" lay-skin="primary">
                                                        <input type="checkbox" name="gen_view" checked title="视图" lay-skin="primary">
                                                        <input type="checkbox" name="gen_js" checked title="JavaScript" lay-skin="primary">
                                                        <input type="checkbox" name="gen_route" checked title="路由" lay-skin="primary">
                                                    </div>
                                                </div>

                                                <div class="option-group">
                                                    <h5>输出格式</h5>
                                                    <div class="layui-form">
                                                        <input type="radio" name="output_format" value="zip" checked title="ZIP压缩包" lay-skin="primary">
                                                        <input type="radio" name="output_format" value="files" title="独立文件" lay-skin="primary">
                                                    </div>
                                                </div>

                                                <div class="option-group">
                                                    <h5>覆盖选项</h5>
                                                    <div class="layui-form">
                                                        <input type="checkbox" name="force_overwrite" title="强制覆盖" lay-skin="primary">
                                                        <input type="checkbox" name="backup_existing" checked title="备份原文件" lay-skin="primary">
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="generate-actions">
                                                <button type="button" class="layui-btn layui-btn-fluid layui-btn-lg" id="start-generate">
                                                    <i class="layui-icon layui-icon-ok"></i> 开始生成
                                                </button>
                                                <button type="button" class="layui-btn layui-btn-fluid layui-btn-primary" id="preview-all">
                                                    <i class="layui-icon layui-icon-search"></i> 预览所有文件
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 右侧生成结果 -->
                                <div class="layui-col-md8">
                                    <div class="config-panel">
                                        <div class="panel-header">
                                            <h4><i class="layui-icon layui-icon-file"></i> 生成结果</h4>
                                            <div class="panel-actions">
                                                <span class="generate-status" id="generate-status">等待生成</span>
                                            </div>
                                        </div>
                                        <div class="panel-body">
                                            <div id="generate-result">
                                                <div class="empty-result">
                                                    <div class="empty-icon">
                                                        <i class="layui-icon layui-icon-file"></i>
                                                    </div>
                                                    <div class="empty-text">
                                                        <h4>准备生成文件</h4>
                                                        <p>点击"开始生成"按钮创建文件</p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 操作按钮 -->
                    <div class="layui-form-item" style="margin-top: 30px;">
                        <div class="layui-input-block">
                            <button type="button" class="layui-btn layui-btn-primary" id="prev-step" style="display: none;">
                                <i class="layui-icon layui-icon-prev"></i> 上一步
                            </button>
                            <button type="button" class="layui-btn" id="next-step">
                                下一步 <i class="layui-icon layui-icon-next"></i>
                            </button>
                            <button type="button" class="layui-btn layui-btn-normal" id="generate-btn" style="display: none;">
                                <i class="layui-icon layui-icon-ok"></i> 生成代码
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 字段配置弹窗 -->
<div id="field-config-modal" style="display: none; padding: 20px;">
    <form class="layui-form" id="field-config-form">
        <div class="layui-form-item">
            <label class="layui-form-label">字段名</label>
            <div class="layui-input-block">
                <input type="text" name="field_name" readonly class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">显示标签</label>
            <div class="layui-input-block">
                <input type="text" name="field_label" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">表单组件</label>
            <div class="layui-input-block">
                <select name="field_component">
                    <!-- 动态生成组件选项 -->
                </select>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">组件选项</label>
            <div class="layui-input-block">
                <textarea name="field_options" placeholder="JSON格式的组件选项" class="layui-textarea"></textarea>
            </div>
        </div>
        <div class="layui-form-item">
            <div class="layui-input-block">
                <input type="checkbox" name="show_in_list" title="在列表中显示" lay-skin="primary">
                <input type="checkbox" name="show_in_form" title="在表单中显示" lay-skin="primary">
                <input type="checkbox" name="searchable" title="可搜索" lay-skin="primary">
                <input type="checkbox" name="sortable" title="可排序" lay-skin="primary">
                <input type="checkbox" name="required" title="必填" lay-skin="primary">
            </div>
        </div>
    </form>
</div>
<?php $__env->stopSection(); ?>

<!-- 代码预览容器 -->
<div id="code-preview-container" style="display: none;"></div>

<?php $__env->startSection('style'); ?>
<link rel="stylesheet" href="/static/admin/css/curd-generator-v2.css?v=<?php echo e(time()); ?>">
<link rel="stylesheet" href="/static/admin/css/curd-preview.css?v=<?php echo e($version); ?>">
<link rel="stylesheet" href="/static/admin/css/field-sorter.css?v=<?php echo e($version); ?>">
<?php $__env->stopSection(); ?>

<?php $__env->startSection('script'); ?>
<script src="/static/admin/lib/sortable/sortable.min.js?v=<?php echo e($version); ?>"></script>
<script src="/static/admin/js/curd-preview.js?v=<?php echo e($version); ?>"></script>
<script src="/static/admin/js/field-sorter.js?v=<?php echo e($version); ?>"></script>
<script src="/static/admin/js/template-manager.js?v=<?php echo e($version); ?>"></script>
<script src="/static/admin/js/relationship-manager.js?v=<?php echo e($version); ?>"></script>
<script src="/static/admin/js/api-manager.js?v=<?php echo e($version); ?>"></script>
<script src="/static/admin/js/quality-manager.js?v=<?php echo e($version); ?>"></script>
<script>
// CURD 生成器 V2 JavaScript
layui.use(['form', 'layer', 'table', 'element'], function(){
    var $ = layui.$,
        form = layui.form,
        layer = layui.layer,
        table = layui.table,
        element = layui.element;

    // 全局变量
    var currentStep = 1,
        maxStep = 5,
        tableInfo = null,
        fieldConfigs = [],
        availableComponents = {},
        codePreview = null,
        fieldSorter = null,
        templateManager = null,
        relationshipManager = null,
        apiManager = null,
        qualityManager = null,
        isLoading = false;

    // 初始化
    init();

    function init() {
        bindEvents();
        updateStepStatus();
        updateProgressBar();
        initStepNavigation();
        loadTables();
        // 延迟加载其他组件
        setTimeout(function() {
            initCodePreview();
            initFieldSorter();
            initTemplateManager();
            initRelationshipManager();
            initApiManager();
            initQualityManager();
        }, 100);
    }

    // 初始化步骤导航
    function initStepNavigation() {
        $('.step-item').on('click', function() {
            var targetStep = parseInt($(this).data('step'));
            if (targetStep <= currentStep || validateStep(currentStep)) {
                goToStep(targetStep);
            }
        });
    }

    // 更新进度条
    function updateProgressBar() {
        var progress = (currentStep / maxStep) * 100;
        $('#progress-fill').css('width', progress + '%');
        $('#progress-text').text('步骤 ' + currentStep + ' / ' + maxStep);
    }

    // 初始化代码预览
    function initCodePreview() {
        // 动态加载预览组件
        require(['/static/admin/js/curd-preview.js'], function(CodePreview) {
            codePreview = new CodePreview({
                container: '#code-preview-container',
                previewUrl: '/admin/system/curd_generate_v2',
                debounceDelay: 500
            });
        });
    }

    // 初始化字段排序
    function initFieldSorter() {
        // 动态加载排序组件
        require(['/static/admin/js/field-sorter.js'], function(FieldSorter) {
            fieldSorter = new FieldSorter({
                container: '#field-config-table tbody',
                handle: '.drag-handle',
                onSort: function(evt) {
                    // 排序完成后触发预览更新
                    triggerPreview();

                    // 显示排序完成提示
                    layer.msg('字段顺序已调整', {icon: 1, time: 1000});
                }
            });
        });
    }

    // 初始化模板管理器
    function initTemplateManager() {
        // 动态加载模板管理组件
        require(['/static/admin/js/template-manager.js'], function(TemplateManager) {
            templateManager = new TemplateManager({
                apiUrl: '/admin/system/curd_generate_v2',
                enableAutoSave: true
            });
        });
    }

    // 初始化关联关系管理器
    function initRelationshipManager() {
        // 动态加载关联关系管理组件
        require(['/static/admin/js/relationship-manager.js'], function(RelationshipManager) {
            relationshipManager = new RelationshipManager({
                apiUrl: '/admin/system/curd_generate_v2',
                enableAutoAnalyze: true,
                confidenceThreshold: 70
            });
        });
    }

    // 初始化 API 管理器
    function initApiManager() {
        // 动态加载 API 管理组件
        require(['/static/admin/js/api-manager.js'], function(ApiManager) {
            apiManager = new ApiManager({
                apiUrl: '/admin/system/curd_generate_v2',
                enableAutoAnalyze: true
            });
        });
    }

    // 初始化质量管理器
    function initQualityManager() {
        // 动态加载质量管理组件
        require(['/static/admin/js/quality-manager.js'], function(QualityManager) {
            qualityManager = new QualityManager({
                apiUrl: '/admin/system/curd_generate_v2',
                enableAutoAnalyze: true,
                enableAutoOptimize: false
            });
        });
    }

    // 绑定事件
    function bindEvents() {
        // 分析表结构
        $('#analyze-btn').on('click', function() {
            if (!isLoading) analyzeTable();
        });

        // 刷新表列表
        $('#refresh-tables-btn').on('click', function() {
            var connection = $('select[name="connection"]').val();
            if (connection) {
                showLoading('正在刷新表列表...');
                loadTables();
            } else {
                layer.msg('请先选择数据库连接', {icon: 2});
            }
        });

        // 测试连接
        $('#test-connection-btn').on('click', function() {
            var connection = $('select[name="connection"]').val();
            if (connection) {
                testConnection(connection);
            } else {
                layer.msg('请先选择数据库连接', {icon: 2});
            }
        });

        // 步骤导航
        $('#next-btn').on('click', function() {
            if (!isLoading && validateStep(currentStep)) {
                nextStep();
            }
        });

        $('#prev-btn').on('click', function() {
            if (!isLoading && currentStep > 1) {
                prevStep();
            }
        });

        // 生成代码
        $('#generate-btn').on('click', function() {
            if (!isLoading) generateCode();
        });

        // 预览代码
        $('#refresh-preview').on('click', previewCode);

        // 数据库连接选择变化
        form.on('select(connection)', function(data){
            if (data.value) {
                showLoading('正在加载表列表...');
                loadTables();
                updateConnectionStatus(data.value);
            }
        });

        // 表选择变化
        form.on('select(table)', function(data){
            if (data.value) {
                // 自动设置表前缀
                var connection = $('select[name="connection"]').val();
                var prefix = getTablePrefix(connection);
                $('input[name="table_prefix"]').val(prefix);

                // 显示分析按钮
                $('#analyze-btn').removeClass('layui-btn-disabled');
            }
        });

        // 键盘快捷键
        $(document).on('keydown', function(e) {
            if (e.ctrlKey && !isLoading) {
                switch(e.keyCode) {
                    case 37: // Ctrl + 左箭头
                        e.preventDefault();
                        if (currentStep > 1) prevStep();
                        break;
                    case 39: // Ctrl + 右箭头
                        e.preventDefault();
                        if (currentStep < maxStep && validateStep(currentStep)) nextStep();
                        break;
                    case 13: // Ctrl + Enter
                        e.preventDefault();
                        if (currentStep === maxStep) generateCode();
                        break;
                }
            }
        });
    }

    // 显示加载状态
    function showLoading(message) {
        isLoading = true;
        $('.curd-content-card .layui-card-body').append(
            '<div class="loading-overlay"><div class="loading-spinner"></div><div style="margin-top: 15px;">' + (message || '加载中...') + '</div></div>'
        );
    }

    // 隐藏加载状态
    function hideLoading() {
        isLoading = false;
        $('.loading-overlay').remove();
    }

    // 验证步骤
    function validateStep(step) {
        switch(step) {
            case 1:
                var tableName = $('select[name="table_name"]').val();
                if (!tableName) {
                    layer.msg('请选择数据表', {icon: 2});
                    return false;
                }
                if (!tableInfo) {
                    layer.msg('请先分析表结构', {icon: 2});
                    return false;
                }
                return true;
            case 2:
                return fieldConfigs && fieldConfigs.length > 0;
            case 3:
                return true; // 生成选项步骤总是可以通过
            case 4:
                return true; // 预览步骤总是可以通过
            default:
                return true;
        }
    }

    // 跳转到指定步骤
    function goToStep(step) {
        if (step < 1 || step > maxStep) return;

        // 更新当前步骤
        currentStep = step;

        // 更新步骤导航状态
        $('.step-item').removeClass('active completed');
        for (var i = 1; i <= maxStep; i++) {
            var $stepItem = $('#nav-step-' + i);
            if (i < currentStep) {
                $stepItem.addClass('completed');
            } else if (i === currentStep) {
                $stepItem.addClass('active');
            }
        }

        // 更新进度条
        updateProgressBar();

        // 显示对应的步骤内容
        $('.step-panel').removeClass('active').hide();
        $('#step-' + step).addClass('active').show();

        // 更新标题
        var stepTitles = ['', '选择数据表', '配置字段', '生成选项', '预览代码', '生成文件'];
        $('#current-step-title').text(stepTitles[step]);

        // 更新按钮状态
        updateStepButtons();
    }

    // 更新步骤按钮状态
    function updateStepButtons() {
        $('#prev-btn').toggle(currentStep > 1);
        $('#next-btn').toggle(currentStep < maxStep);
        $('#generate-btn').toggle(currentStep === maxStep);
    }

    // 获取表前缀
    function getTablePrefix(connection) {
        var prefixes = {
            'mysql': 'ea8_',
            'mysql_read': 'ea8_',
            'mysql_second': 'ddwx_',
            'mysql_log': 'ea8_',
            'mysql_cache': 'ea8_'
        };
        return prefixes[connection] || '';
    }

    // 更新连接状态
    function updateConnectionStatus(connection) {
        var $statusPanel = $('#connection-status-panel');
        var $statusItem = $('#connection-status');

        $statusItem.removeClass('connected error')
                   .addClass('connecting')
                   .find('.status-text').text('连接中...');

        // 这里可以添加实际的连接测试逻辑
        setTimeout(function() {
            $statusItem.removeClass('connecting')
                       .addClass('connected')
                       .find('.status-text').text('已连接 (' + connection + ')');
        }, 1000);
    }

    // 测试数据库连接
    function testConnection(connection) {
        var loadIndex = layer.load(2, {content: '测试连接中...'});

        $.post('/curdtest/tables', {
            connection: connection
        }, function(res) {
            layer.close(loadIndex);
            if (res.code === 1) {
                layer.msg('连接测试成功！找到 ' + res.data.length + ' 个表', {icon: 1, time: 3000});
                updateConnectionStatus(connection);
            } else {
                layer.msg('连接测试失败: ' + res.msg, {icon: 2, time: 3000});
                $('#connection-status').removeClass('connected connecting')
                                      .addClass('error')
                                      .find('.status-text').text('连接失败');
            }
        }).fail(function() {
            layer.close(loadIndex);
            layer.msg('网络请求失败', {icon: 2});
            $('#connection-status').removeClass('connected connecting')
                                  .addClass('error')
                                  .find('.status-text').text('网络错误');
        });
    }

    // 加载数据表列表
    function loadTables() {
        var connection = $('select[name="connection"]').val() || 'mysql';

        $.post('/curdtest/tables', {
            connection: connection
        }, function(res) {
            hideLoading();
            if (res.code === 1) {
                var html = '<option value="">请选择数据表</option>';
                $.each(res.data, function(i, table) {
                    html += '<option value="' + table.name + '">' + table.name + ' (' + table.comment + ')</option>';
                });
                $('select[name="table_name"]').html(html);
                form.render('select');

                // 更新表数量信息
                $('#table-count-info').text('共找到 ' + res.data.length + ' 个表');

                // 显示成功消息
                var message = '数据表加载成功 (' + res.data.length + ' 个表)';
                if (res.msg.indexOf('演示数据') !== -1) {
                    message += ' [演示模式]';
                }
                layer.msg(message, {icon: 1, time: 2000});
            } else {
                layer.msg('加载失败: ' + res.msg, {icon: 2});
                $('select[name="table_name"]').html('<option value="">加载失败</option>');
                form.render('select');
                $('#table-count-info').text('');
            }
        }).fail(function() {
            hideLoading();
            layer.msg('网络请求失败', {icon: 2});
            $('select[name="table_name"]').html('<option value="">网络错误</option>');
            form.render('select');
            $('#table-count-info').text('');
        });
    }

    // 分析表结构
    function analyzeTable() {
        var tableName = $('select[name="table_name"]').val();
        var tablePrefix = $('input[name="table_prefix"]').val();
        var connection = $('select[name="connection"]').val() || 'mysql';

        if (!tableName) {
            layer.msg('请选择数据表', {icon: 2});
            return;
        }

        showLoading('正在分析表结构...');

        $.post('/curdtest/analyze', {
            table_name: tableName,
            table_prefix: tablePrefix,
            connection: connection
        }, function(res) {
            hideLoading();
            if (res.code === 1) {
                tableInfo = res.data;
                displayTableInfo();
                loadComponents();

                // 显示表信息面板
                $('#table-info-panel').show();
                $('#analyze-status').removeClass('status-badge').addClass('status-badge success').text('已分析');

                // 启用下一步按钮
                $('#next-btn').removeClass('layui-btn-disabled');

                // 设置关联关系管理器的当前表
                if (relationshipManager) {
                    relationshipManager.setCurrentTable(tableName);
                }

                // 设置 API 管理器的当前表
                if (apiManager) {
                    apiManager.setCurrentTable(tableName);
                }

                // 显示成功消息
                var message = '表结构分析完成';
                if (res.msg.indexOf('演示数据') !== -1) {
                    message += ' [演示模式]';
                }
                layer.msg(message, {icon: 1, time: 2000});

                // 自动跳转到下一步（可选）
                setTimeout(function() {
                    if (currentStep === 1) {
                        layer.confirm('表结构分析完成，是否继续配置字段？', {
                            btn: ['继续', '稍后'],
                            icon: 3
                        }, function(index) {
                            layer.close(index);
                            nextStep();
                        });
                    }
                }, 1000);

            } else {
                layer.msg('分析失败: ' + res.msg, {icon: 2, time: 3000});
                $('#analyze-status').removeClass('status-badge success').addClass('status-badge error').text('分析失败');
            }
        }).fail(function() {
            hideLoading();
            layer.msg('网络请求失败', {icon: 2});
            $('#analyze-status').removeClass('status-badge success').addClass('status-badge error').text('网络错误');
        });
    }

    // 显示表信息
    function displayTableInfo() {
        if (!tableInfo) return;

        // 更新基本信息 - 适配新的HTML结构
        $('#info-table-name').text(tableInfo.name || '-');
        $('#info-table-comment').text(tableInfo.comment || '无注释');
        $('#info-field-count').text(tableInfo.fields ? tableInfo.fields.length : 0);
        $('#info-connection').text($('select[name="connection"]').val() || '-');

        // 显示主键
        var primaryKeys = [];
        if (tableInfo.fields) {
            $.each(tableInfo.fields, function(i, field) {
                if (field.primary) {
                    primaryKeys.push(field.name);
                }
            });
        }
        $('#info-primary-key').text(primaryKeys.length > 0 ? primaryKeys.join(', ') : '无主键');

        // 显示索引数
        $('#info-index-count').text(tableInfo.indexes ? tableInfo.indexes.length : 0);

        // 兼容旧的HTML结构
        if ($('#table-name-display').length) {
            $('#table-name-display').text(tableInfo.full_name || tableInfo.name);
            $('#table-comment-display').text(tableInfo.comment);
            $('#field-count-display').text(tableInfo.fields ? tableInfo.fields.length : 0);
            $('#has-timestamps').html(tableInfo.has_timestamps ? '<span class="layui-badge layui-bg-green">是</span>' : '<span class="layui-badge">否</span>');
            $('#has-soft-delete').html(tableInfo.has_soft_delete ? '<span class="layui-badge layui-bg-green">是</span>' : '<span class="layui-badge">否</span>');
            $('#primary-key').text(primaryKeys.length > 0 ? primaryKeys.join(', ') : '无');
        }

        // 统计字段类型
        var listFields = 0, formFields = 0, searchFields = 0;
        if (tableInfo.fields) {
            $.each(tableInfo.fields, function(i, field) {
                if (field.show_in_list) listFields++;
                if (field.show_in_form) formFields++;
                if (field.searchable) searchFields++;
            });
        }

        if ($('#list-fields-count').length) {
            $('#list-fields-count').text(listFields);
            $('#form-fields-count').text(formFields);
            $('#search-fields-count').text(searchFields);
        }

        // 添加动画效果
        $('#table-info-panel .info-item').each(function(index) {
            $(this).css('opacity', '0').delay(index * 100).animate({opacity: 1}, 300);
        });

        // 更新字段配置
        if (tableInfo.fields) {
            fieldConfigs = [];
            $.each(tableInfo.fields, function(i, field) {
                fieldConfigs.push({
                    name: field.name,
                    type: field.type,
                    length: field.length,
                    nullable: field.nullable,
                    default: field.default,
                    comment: field.comment,
                    primary: field.primary,
                    auto_increment: field.auto_increment,
                    component: field.component || getDefaultComponent(field.type),
                    show_in_list: !field.primary && field.name !== 'created_at' && field.name !== 'updated_at',
                    show_in_form: !field.primary && !field.auto_increment,
                    show_in_search: field.name.indexOf('name') !== -1 || field.name.indexOf('title') !== -1,
                    required: !field.nullable && !field.auto_increment,
                    sortable: true
                });
            });
        }

        // 触发代码预览
        triggerPreview();
    }

    // 获取默认组件类型
    function getDefaultComponent(fieldType) {
        var type = fieldType.toLowerCase();
        if (type.indexOf('int') !== -1) return 'number';
        if (type.indexOf('decimal') !== -1 || type.indexOf('float') !== -1) return 'number';
        if (type.indexOf('text') !== -1) return 'textarea';
        if (type.indexOf('date') !== -1) return 'date';
        if (type.indexOf('time') !== -1) return 'datetime';
        if (type.indexOf('enum') !== -1) return 'select';
        if (type.indexOf('json') !== -1) return 'textarea';
        return 'input';
    }

    // 触发代码预览
    function triggerPreview() {
        if (codePreview && tableInfo) {
            var config = collectFormData();
            codePreview.onConfigChange(config);

            // 更新模板管理器的当前配置
            if (templateManager) {
                templateManager.updateCurrentConfig(config);
            }
        }
    }

    // 应用模板配置 (全局函数，供模板管理器调用)
    window.applyTemplateConfig = function(templateConfig) {
        if (!templateConfig || !templateConfig.fields) {
            layer.msg('模板配置无效');
            return;
        }

        // 应用字段配置
        if (tableInfo && tableInfo.fields) {
            $.each(tableInfo.fields, function(i, field) {
                var templateField = templateConfig.fields.find(f => f.name === field.name);
                if (templateField) {
                    // 应用模板字段配置
                    field.component = templateField.component || field.component;
                    field.show_in_list = templateField.show_in_list !== undefined ? templateField.show_in_list : field.show_in_list;
                    field.show_in_form = templateField.show_in_form !== undefined ? templateField.show_in_form : field.show_in_form;
                    field.searchable = templateField.searchable !== undefined ? templateField.searchable : field.searchable;
                    field.required = templateField.required !== undefined ? templateField.required : field.required;
                    field.sortable = templateField.sortable !== undefined ? templateField.sortable : field.sortable;
                    field.order = templateField.order !== undefined ? templateField.order : i;
                }
            });

            // 重新构建字段配置表格
            buildFieldConfigTable();
        }

        // 应用生成选项
        if (templateConfig.options) {
            var options = templateConfig.options;
            $('input[name="generate_controller"]').prop('checked', options.generate_controller !== false);
            $('input[name="generate_model"]').prop('checked', options.generate_model !== false);
            $('input[name="generate_view"]').prop('checked', options.generate_view !== false);
            $('input[name="generate_js"]').prop('checked', options.generate_js !== false);
            $('input[name="enable_export"]').prop('checked', options.enable_export !== false);
            $('input[name="enable_batch"]').prop('checked', options.enable_batch !== false);
            $('input[name="enable_auth"]').prop('checked', options.enable_auth !== false);
            $('input[name="enable_cache"]').prop('checked', options.enable_cache === true);

            form.render('checkbox');
        }

        layer.msg('模板应用成功', {icon: 1});
    };

    // 加载组件配置
    function loadComponents() {
        $.post('', {action: 'get_components'}, function(res) {
            if (res.code === 0) {
                availableComponents = res.data.components;
                buildFieldConfigTable();
            }
        });
    }

    // 构建字段配置表格
    function buildFieldConfigTable() {
        var html = '';
        fieldConfigs = tableInfo.fields;

        // 为字段添加排序索引
        $.each(fieldConfigs, function(i, field) {
            field.order = field.order || i;
        });

        // 按排序索引排序
        fieldConfigs.sort(function(a, b) {
            return (a.order || 0) - (b.order || 0);
        });

        $.each(fieldConfigs, function(i, field) {
            html += '<tr data-field="' + field.name + '">';
            // 拖拽手柄将由 FieldSorter 自动添加
            html += '<td><input type="checkbox" lay-skin="primary" data-field="' + field.name + '"></td>';
            html += '<td>' + field.name + '</td>';
            html += '<td><span class="layui-badge layui-bg-gray">' + field.type + '</span></td>';
            html += '<td><input type="text" class="layui-input layui-input-sm" value="' + field.comment + '" data-field="' + field.name + '" data-prop="comment"></td>';
            html += '<td>' + buildComponentSelect(field) + '</td>';
            html += '<td>' + buildCheckbox(field.show_in_list, field.name, 'show_in_list') + '</td>';
            html += '<td>' + buildCheckbox(field.show_in_form, field.name, 'show_in_form') + '</td>';
            html += '<td>' + buildCheckbox(field.searchable, field.name, 'searchable') + '</td>';
            html += '<td>' + buildCheckbox(field.required, field.name, 'required') + '</td>';
            html += '<td>' + buildCheckbox(field.sortable, field.name, 'sortable') + '</td>';
            html += '<td><button type="button" class="layui-btn layui-btn-xs" onclick="configField(\'' + field.name + '\')">配置</button></td>';
            html += '</tr>';
        });

        $('#field-config-tbody').html(html);
        form.render();

        // 重新初始化拖拽排序
        if (fieldSorter) {
            fieldSorter.destroy();
            initFieldSorter();
        }
    }

    // 构建组件选择框
    function buildComponentSelect(field) {
        var html = '<select class="layui-select layui-select-sm" data-field="' + field.name + '" data-prop="component">';
        $.each(availableComponents, function(key, component) {
            var selected = key === field.component ? ' selected' : '';
            html += '<option value="' + key + '"' + selected + '>' + component.name + '</option>';
        });
        html += '</select>';
        return html;
    }

    // 构建复选框
    function buildCheckbox(checked, fieldName, prop) {
        var checkedAttr = checked ? ' checked' : '';
        return '<input type="checkbox" lay-skin="primary" data-field="' + fieldName + '" data-prop="' + prop + '"' + checkedAttr + '>';
    }

    // 下一步
    function nextStep() {
        if (currentStep < maxStep) {
            if (validateStep(currentStep)) {
                currentStep++;
                showStep(currentStep);
                updateStepStatus();

                // 特殊处理
                if (currentStep === 4) {
                    previewCode();
                }
            }
        }
    }

    // 上一步
    function prevStep() {
        if (currentStep > 1) {
            currentStep--;
            showStep(currentStep);
            updateStepStatus();
        }
    }

    // 显示指定步骤
    function showStep(step) {
        $('.step-content').hide();
        $('#step-content-' + step).show();
    }

    // 更新步骤状态
    function updateStepStatus() {
        // 更新按钮状态
        $('#prev-step').toggle(currentStep > 1);
        $('#next-step').toggle(currentStep < maxStep);
        $('#generate-btn').toggle(currentStep === maxStep);

        // 更新步骤图标
        for (var i = 1; i <= maxStep; i++) {
            var icon = $('#step-icon-' + i);
            var title = $('#step-title-' + i);

            if (i < currentStep) {
                icon.removeClass().addClass('layui-icon layui-timeline-axis layui-icon-ok layui-anim layui-anim-scale');
                title.addClass('layui-text-success');
            } else if (i === currentStep) {
                icon.removeClass().addClass('layui-icon layui-timeline-axis layui-icon-loading layui-anim layui-anim-rotate');
                title.removeClass('layui-text-success').addClass('layui-text-primary');
            } else {
                icon.removeClass().addClass('layui-icon layui-timeline-axis layui-icon-circle-dot');
                title.removeClass('layui-text-success layui-text-primary');
            }
        }
    }

    // 验证步骤
    function validateStep(step) {
        switch (step) {
            case 1:
                if (!tableInfo) {
                    layer.msg('请先分析表结构');
                    return false;
                }
                break;
            case 2:
                // 验证字段配置
                break;
            case 3:
                // 验证生成选项
                break;
        }
        return true;
    }

    // 预览代码
    function previewCode() {
        var data = collectFormData();
        var loading = layer.load(2);

        $.post('', {
            action: 'preview_code',
            ...data
        }, function(res) {
            layer.close(loading);
            if (res.code === 0) {
                displayPreview(res.data);
            } else {
                layer.msg(res.msg);
            }
        });
    }

    // 显示预览
    function displayPreview(files) {
        $('#preview-controller').text(files.controller || '');
        $('#preview-model').text(files.model || '');
        $('#preview-view').text(files.view || '');
        $('#preview-js').text(files.js || '');

        // 代码高亮
        if (window.Prism) {
            Prism.highlightAll();
        }
    }

    // 生成代码
    function generateCode() {
        var data = collectFormData();
        var loading = layer.load(2);

        $.post('', {
            action: 'generate',
            ...data
        }, function(res) {
            layer.close(loading);
            if (res.code === 0) {
                displayGenerateResult(res.data);
                layer.msg('生成成功');
            } else if (res.code === -1) {
                // 文件已存在，询问是否覆盖
                layer.confirm(res.msg, {
                    btn: ['强制覆盖', '取消']
                }, function() {
                    data.force = true;
                    generateCodeForce(data);
                });
            } else {
                layer.msg(res.msg);
            }
        });
    }

    // 强制生成代码
    function generateCodeForce(data) {
        var loading = layer.load(2);
        $.post('', {
            action: 'generate',
            ...data
        }, function(res) {
            layer.close(loading);
            if (res.code === 0) {
                displayGenerateResult(res.data);
                layer.msg('生成成功');
            } else {
                layer.msg(res.msg);
            }
        });
    }

    // 显示生成结果
    function displayGenerateResult(data) {
        var html = '<div class="layui-panel">';
        html += '<div class="layui-panel-header">生成的文件</div>';
        html += '<div class="layui-panel-body">';
        html += '<ul class="layui-timeline">';

        $.each(data.files || [], function(i, file) {
            html += '<li class="layui-timeline-item">';
            html += '<i class="layui-icon layui-timeline-axis layui-icon-ok"></i>';
            html += '<div class="layui-timeline-content layui-text">';
            html += '<h3 class="layui-timeline-title">' + file.name + '</h3>';
            html += '<p>' + file.path + '</p>';
            html += '</div>';
            html += '</li>';
        });

        html += '</ul>';
        html += '</div>';
        html += '</div>';

        $('#generate-result').html(html);
    }

    // 收集表单数据
    function collectFormData() {
        var data = {
            table_name: tableInfo.name,
            table_prefix: tableInfo.prefix,
            options: {
                fields: fieldConfigs,
                generate_controller: $('input[name="generate_controller"]').prop('checked'),
                generate_model: $('input[name="generate_model"]').prop('checked'),
                generate_view: $('input[name="generate_view"]').prop('checked'),
                generate_js: $('input[name="generate_js"]').prop('checked'),
                enable_export: $('input[name="enable_export"]').prop('checked'),
                enable_batch: $('input[name="enable_batch"]').prop('checked'),
                enable_auth: $('input[name="enable_auth"]').prop('checked'),
                enable_cache: $('input[name="enable_cache"]').prop('checked')
            }
        };
        return data;
    }

    // 全局函数
    window.configField = function(fieldName) {
        layer.open({
            type: 1,
            title: '配置字段: ' + fieldName,
            content: $('#field-config-modal'),
            area: ['600px', '500px'],
            btn: ['保存', '取消'],
            yes: function(index) {
                layer.close(index);
            }
        });
    };
});
</script>

<!-- 引入独立的JavaScript文件 -->
<script src="/static/admin/js/curd-generator-v2.js?v=<?php echo e(time()); ?>"></script>
<script src="/static/admin/js/curd-code-preview.js?v=<?php echo e(time()); ?>"></script>
<script src="/static/admin/js/curd-field-config.js?v=<?php echo e(time()); ?>"></script>
<script src="/static/admin/js/curd-quality-checker.js?v=<?php echo e(time()); ?>"></script>
<script src="/static/admin/js/curd-data-manager.js?v=<?php echo e(time()); ?>"></script>
<script src="/static/admin/js/curd-advanced-generator.js?v=<?php echo e(time()); ?>"></script>
<script src="/static/admin/js/curd-template-manager.js?v=<?php echo e(time()); ?>"></script>
<script src="/static/admin/js/curd-realtime-generator.js?v=<?php echo e(time()); ?>"></script>
<script src="/static/admin/js/curd-code-optimizer.js?v=<?php echo e(time()); ?>"></script>

<!-- 开发环境测试套件 -->
<?php if(config('app.debug')): ?>
<script src="/static/admin/js/curd-test-suite.js?v=<?php echo e(time()); ?>"></script>
<?php endif; ?>

<!-- 引入第三方库 -->
<script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/components/prism-core.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/components/prism-php.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/components/prism-javascript.min.js"></script>
<link href="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/themes/prism.min.css" rel="stylesheet">
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.layout.base', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\wwwroot\127.0.0.1\EasyAdmin8-webman\app\admin\view/admin\system\curdgeneratev2\index.blade.php ENDPATH**/ ?>