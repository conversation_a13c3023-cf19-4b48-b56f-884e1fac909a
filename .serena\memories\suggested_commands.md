# 建议的开发命令

## 项目启动命令

### Windows 环境
```bash
# 启动开发服务器 (推荐)
start_server.bat

# 或者直接使用 PHP 命令
php windows.php

# 或者使用 webman 标准启动
php start.php start
```

### Linux/Mac 环境
```bash
# 启动服务器
php start.php start

# 后台运行
php start.php start -d

# 停止服务器
php start.php stop

# 重启服务器
php start.php restart

# 查看状态
php start.php status
```

## CURD 生成命令
```bash
# 生成指定表的 CURD 功能
php webman curd -t table_name

# 示例：生成 test_goods 表的 CURD
php webman curd -t test_goods

# 生成带前缀的表
php webman curd -t ea8_users
```

## 数据库相关命令
```bash
# 数据库迁移
php webman migrate

# 数据库填充
php webman seed
```

## 缓存管理命令
```bash
# 清除缓存
php webman cache:clear

# 清除配置缓存
php webman config:clear

# 清除路由缓存
php webman route:clear
```

## 开发工具命令
```bash
# 查看路由列表
php webman route:list

# 生成应用密钥
php webman key:generate

# 查看帮助
php webman --help
```

## 项目管理命令
```bash
# 安装依赖
composer install

# 更新依赖
composer update

# 安装依赖（忽略平台要求）
composer install --ignore-platform-reqs

# 自动加载优化
composer dump-autoload -o
```

## 环境配置
```bash
# 复制环境配置文件
cp .example.env .env

# 编辑环境配置
nano .env
```

## 测试命令
```bash
# 运行项目测试脚本
php 测试脚本.php

# 运行功能验证测试
php 功能验证测试脚本.php

# 运行完整环境修复脚本
php 完整环境修复脚本.php
```

## Windows 系统工具命令
```cmd
# 查看目录内容
dir

# 切换目录
cd path\to\directory

# 查看文件内容
type filename.txt

# 查找文件
findstr "pattern" filename.txt

# 网络测试
ping domain.com

# 端口测试
telnet localhost 8787
```

## Git 版本控制
```bash
# 克隆项目
git clone https://github.com/EasyAdmin8/EasyAdmin8-webman

# 或者从 Gitee 克隆
git clone https://gitee.com/EasyAdmin8/EasyAdmin8-webman

# 查看状态
git status

# 提交更改
git add .
git commit -m "commit message"

# 推送更改
git push origin main
```

## 服务器配置 (Nginx 反向代理)
```nginx
upstream webman {
    server 127.0.0.1:8787;
    keepalive 10240;
}

server {
    server_name your-domain.com;
    listen 80;
    access_log off;
    root /your/webman/public;

    location ^~ / {
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header Host $host;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_http_version 1.1;
        proxy_set_header Connection "";
        if (!-f $request_filename){
            proxy_pass http://webman;
        }
    }
}
```