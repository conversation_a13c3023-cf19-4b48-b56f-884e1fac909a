/**
 * CURD 代码预览组件样式
 */

/* 预览容器 */
.code-preview-wrapper {
    position: fixed;
    top: 60px;
    right: 20px;
    width: 45%;
    height: calc(100vh - 80px);
    background: #fff;
    border: 1px solid #e6e6e6;
    border-radius: 6px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    display: flex;
    flex-direction: column;
    transition: all 0.3s ease;
}

/* 分屏模式 */
.code-preview-wrapper.split-view {
    width: 60%;
    right: 10px;
}

/* 预览头部 */
.preview-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background: #f8f9fa;
    border-bottom: 1px solid #e6e6e6;
    border-radius: 6px 6px 0 0;
}

.preview-title {
    font-size: 14px;
    font-weight: 500;
    color: #333;
}

.preview-title i {
    margin-right: 6px;
    color: #1890ff;
}

.preview-controls {
    display: flex;
    gap: 6px;
}

.preview-controls .layui-btn {
    height: 28px;
    line-height: 28px;
    padding: 0 10px;
    font-size: 12px;
}

/* 标签栏 */
.preview-tabs {
    display: flex;
    background: #fafafa;
    border-bottom: 1px solid #e6e6e6;
    padding: 0 16px;
}

.tab-item {
    padding: 10px 16px;
    cursor: pointer;
    font-size: 13px;
    color: #666;
    border-bottom: 2px solid transparent;
    transition: all 0.2s ease;
    user-select: none;
}

.tab-item:hover {
    color: #1890ff;
    background: rgba(24, 144, 255, 0.05);
}

.tab-item.active {
    color: #1890ff;
    border-bottom-color: #1890ff;
    background: #fff;
}

.tab-item i {
    margin-right: 4px;
}

/* 预览内容 */
.preview-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    position: relative;
    overflow: hidden;
}

/* 加载遮罩 */
.loading-mask {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    color: #666;
    z-index: 10;
    display: none;
}

.loading-mask i {
    margin-right: 8px;
    font-size: 16px;
}

/* 代码容器 */
.code-container {
    flex: 1;
    overflow: auto;
    background: #f8f9fa;
}

.code-block {
    margin: 0;
    padding: 16px;
    background: #f8f9fa;
    border: none;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 13px;
    line-height: 1.5;
    overflow: auto;
    height: 100%;
}

.code-block code {
    background: none;
    padding: 0;
    font-family: inherit;
    font-size: inherit;
    color: #333;
}

/* 代码高亮样式 */
.token.comment {
    color: #999;
    font-style: italic;
}

.token.keyword {
    color: #d73a49;
    font-weight: bold;
}

.token.string {
    color: #032f62;
}

.token.number {
    color: #005cc5;
}

.token.function {
    color: #6f42c1;
}

.token.class-name {
    color: #6f42c1;
    font-weight: bold;
}

.token.variable {
    color: #e36209;
}

.token.operator {
    color: #d73a49;
}

.token.punctuation {
    color: #24292e;
}

/* 预览信息 */
.preview-info {
    display: flex;
    justify-content: space-between;
    padding: 8px 16px;
    background: #f0f0f0;
    border-top: 1px solid #e6e6e6;
    font-size: 12px;
    color: #666;
}

.info-item {
    display: flex;
    align-items: center;
}

.info-label {
    margin-right: 4px;
}

.info-value {
    color: #333;
    font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .code-preview-wrapper {
        width: 50%;
    }
    
    .code-preview-wrapper.split-view {
        width: 65%;
    }
}

@media (max-width: 768px) {
    .code-preview-wrapper {
        width: 90%;
        right: 5%;
        top: 80px;
        height: calc(100vh - 100px);
    }
    
    .code-preview-wrapper.split-view {
        width: 95%;
        right: 2.5%;
    }
    
    .preview-tabs {
        padding: 0 8px;
    }
    
    .tab-item {
        padding: 8px 12px;
        font-size: 12px;
    }
    
    .code-block {
        padding: 12px;
        font-size: 12px;
    }
}

/* 滚动条样式 */
.code-container::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}

.code-container::-webkit-scrollbar-track {
    background: #f1f1f1;
}

.code-container::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.code-container::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 动画效果 */
@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

.code-preview-wrapper {
    animation: slideInRight 0.3s ease-out;
}

/* 暗色主题支持 */
@media (prefers-color-scheme: dark) {
    .code-preview-wrapper {
        background: #1e1e1e;
        border-color: #333;
        color: #fff;
    }
    
    .preview-header {
        background: #2d2d2d;
        border-bottom-color: #333;
    }
    
    .preview-tabs {
        background: #252526;
        border-bottom-color: #333;
    }
    
    .tab-item {
        color: #ccc;
    }
    
    .tab-item:hover {
        color: #1890ff;
        background: rgba(24, 144, 255, 0.1);
    }
    
    .tab-item.active {
        color: #1890ff;
        background: #1e1e1e;
    }
    
    .code-container {
        background: #1e1e1e;
    }
    
    .code-block {
        background: #1e1e1e;
    }
    
    .code-block code {
        color: #d4d4d4;
    }
    
    .preview-info {
        background: #2d2d2d;
        border-top-color: #333;
        color: #ccc;
    }
    
    .info-value {
        color: #fff;
    }
}
