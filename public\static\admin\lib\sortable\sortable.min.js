/**
 * 简化版 Sortable 实现
 * 支持基本的拖拽排序功能
 */
(function(global) {
    'use strict';

    function Sortable(element, options) {
        this.el = element;
        this.options = options || {};
        this.init();
    }

    Sortable.prototype = {
        init: function() {
            this.bindEvents();
        },

        bindEvents: function() {
            var self = this;
            var handle = this.options.handle;

            this.el.addEventListener('mousedown', function(e) {
                var target = handle ? e.target.closest(handle) : e.target;
                if (target && (handle ? target.matches(handle) : true)) {
                    self.onMouseDown(e);
                }
            });
        },

        onMouseDown: function(e) {
            e.preventDefault();
            var self = this;
            var dragEl = e.target.closest('tr');
            var startY = e.clientY;
            var startIndex = Array.from(dragEl.parentNode.children).indexOf(dragEl);

            if (this.options.onStart) {
                this.options.onStart({item: dragEl, oldIndex: startIndex});
            }

            dragEl.classList.add('sortable-chosen');

            function onMouseMove(e) {
                var currentY = e.clientY;
                var deltaY = currentY - startY;

                // 简单的拖拽效果
                dragEl.style.transform = 'translateY(' + deltaY + 'px)';
                dragEl.style.zIndex = '1000';

                // 检测插入位置
                var siblings = Array.from(dragEl.parentNode.children);
                var targetEl = null;

                for (var i = 0; i < siblings.length; i++) {
                    var sibling = siblings[i];
                    if (sibling === dragEl) continue;

                    var rect = sibling.getBoundingClientRect();
                    if (currentY >= rect.top && currentY <= rect.bottom) {
                        targetEl = sibling;
                        break;
                    }
                }

                if (targetEl) {
                    var targetRect = targetEl.getBoundingClientRect();
                    var insertBefore = currentY < targetRect.top + targetRect.height / 2;

                    if (insertBefore) {
                        targetEl.parentNode.insertBefore(dragEl, targetEl);
                    } else {
                        targetEl.parentNode.insertBefore(dragEl, targetEl.nextSibling);
                    }
                }
            }

            function onMouseUp(e) {
                document.removeEventListener('mousemove', onMouseMove);
                document.removeEventListener('mouseup', onMouseUp);

                dragEl.style.transform = '';
                dragEl.style.zIndex = '';
                dragEl.classList.remove('sortable-chosen');

                var newIndex = Array.from(dragEl.parentNode.children).indexOf(dragEl);

                if (self.options.onEnd) {
                    self.options.onEnd({item: dragEl, oldIndex: startIndex, newIndex: newIndex});
                }

                if (self.options.onSort && startIndex !== newIndex) {
                    self.options.onSort({item: dragEl, oldIndex: startIndex, newIndex: newIndex});
                }
            }

            document.addEventListener('mousemove', onMouseMove);
            document.addEventListener('mouseup', onMouseUp);
        },

        option: function(key, value) {
            if (arguments.length === 1) {
                return this.options[key];
            }
            this.options[key] = value;
        },

        destroy: function() {
            // 清理事件监听器
        }
    };

    global.Sortable = Sortable;

})(window);