<?php

namespace app\common\services\curd\v2\managers;

use app\common\services\curd\v2\dto\GenerateConfig;

/**
 * 文件管理器
 * 负责创建、删除和管理生成的文件
 */
class FileManager
{
    protected string $basePath;
    protected array $stats = [];

    public function __construct(string $basePath = null)
    {
        $this->basePath = $basePath ?: base_path();
        $this->loadStats();
    }

    /**
     * 创建文件
     */
    public function create(array $files, bool $force = false): array
    {
        $createdFiles = [];
        $errors = [];
        
        foreach ($files as $type => $content) {
            try {
                $filePath = $this->getFilePath($type, $content);
                
                // 检查文件是否存在
                if (!$force && file_exists($filePath)) {
                    $errors[] = "文件已存在: {$filePath}";
                    continue;
                }
                
                // 创建目录
                $directory = dirname($filePath);
                if (!is_dir($directory)) {
                    if (!mkdir($directory, 0755, true)) {
                        $errors[] = "无法创建目录: {$directory}";
                        continue;
                    }
                }
                
                // 写入文件
                if (file_put_contents($filePath, $content) !== false) {
                    $createdFiles[] = [
                        'type' => $type,
                        'path' => $filePath,
                        'size' => strlen($content),
                        'name' => basename($filePath),
                    ];
                    
                    $this->updateStats('created');
                } else {
                    $errors[] = "无法写入文件: {$filePath}";
                }
                
            } catch (\Exception $e) {
                $errors[] = "创建文件失败 ({$type}): " . $e->getMessage();
            }
        }
        
        return [
            'files' => $createdFiles,
            'errors' => $errors,
            'success_count' => count($createdFiles),
            'error_count' => count($errors),
        ];
    }

    /**
     * 删除文件
     */
    public function delete(array $filePaths): array
    {
        $deletedFiles = [];
        $errors = [];
        
        foreach ($filePaths as $filePath) {
            try {
                if (file_exists($filePath)) {
                    if (unlink($filePath)) {
                        $deletedFiles[] = [
                            'path' => $filePath,
                            'name' => basename($filePath),
                        ];
                        
                        $this->updateStats('deleted');
                    } else {
                        $errors[] = "无法删除文件: {$filePath}";
                    }
                } else {
                    $errors[] = "文件不存在: {$filePath}";
                }
            } catch (\Exception $e) {
                $errors[] = "删除文件失败: " . $e->getMessage();
            }
        }
        
        return [
            'files' => $deletedFiles,
            'errors' => $errors,
            'success_count' => count($deletedFiles),
            'error_count' => count($errors),
        ];
    }

    /**
     * 检查文件是否存在
     */
    public function checkFilesExist(array $filePaths): array
    {
        $existingFiles = [];
        
        foreach ($filePaths as $filePath) {
            if (file_exists($filePath)) {
                $existingFiles[] = [
                    'path' => $filePath,
                    'name' => basename($filePath),
                    'size' => filesize($filePath),
                    'modified' => date('Y-m-d H:i:s', filemtime($filePath)),
                ];
            }
        }
        
        return $existingFiles;
    }

    /**
     * 获取文件路径
     */
    protected function getFilePath(string $type, string $content): string
    {
        // 从内容中提取类名和命名空间
        $className = $this->extractClassName($content);
        $namespace = $this->extractNamespace($content);
        
        switch ($type) {
            case 'controller':
                return $this->basePath . '/app/admin/controller/' . $className . '.php';
                
            case 'model':
                return $this->basePath . '/app/admin/model/' . $className . '.php';
                
            case 'view_index':
                $tableName = $this->extractTableName($content);
                return $this->basePath . '/app/admin/view/admin/' . $tableName . '/index.blade.php';
                
            case 'view_form':
                $tableName = $this->extractTableName($content);
                return $this->basePath . '/app/admin/view/admin/' . $tableName . '/form.blade.php';
                
            case 'js':
                $tableName = $this->extractTableName($content);
                return $this->basePath . '/public/static/admin/js/' . $tableName . '.js';
                
            default:
                throw new \Exception("未知的文件类型: {$type}");
        }
    }

    /**
     * 从内容中提取类名
     */
    protected function extractClassName(string $content): string
    {
        if (preg_match('/class\s+(\w+)/', $content, $matches)) {
            return $matches[1];
        }
        
        return 'UnknownClass';
    }

    /**
     * 从内容中提取命名空间
     */
    protected function extractNamespace(string $content): string
    {
        if (preg_match('/namespace\s+([\w\\\\]+)/', $content, $matches)) {
            return $matches[1];
        }
        
        return '';
    }

    /**
     * 从内容中提取表名
     */
    protected function extractTableName(string $content): string
    {
        // 这里可以根据实际模板内容来提取表名
        // 暂时返回一个默认值
        return 'unknown_table';
    }

    /**
     * 备份文件
     */
    public function backup(string $filePath): string
    {
        if (!file_exists($filePath)) {
            throw new \Exception("文件不存在: {$filePath}");
        }
        
        $backupPath = $filePath . '.backup.' . date('YmdHis');
        
        if (copy($filePath, $backupPath)) {
            return $backupPath;
        } else {
            throw new \Exception("备份文件失败: {$filePath}");
        }
    }

    /**
     * 恢复文件
     */
    public function restore(string $backupPath, string $originalPath): bool
    {
        if (!file_exists($backupPath)) {
            throw new \Exception("备份文件不存在: {$backupPath}");
        }
        
        return copy($backupPath, $originalPath);
    }

    /**
     * 获取文件信息
     */
    public function getFileInfo(string $filePath): array
    {
        if (!file_exists($filePath)) {
            throw new \Exception("文件不存在: {$filePath}");
        }
        
        return [
            'path' => $filePath,
            'name' => basename($filePath),
            'size' => filesize($filePath),
            'modified' => date('Y-m-d H:i:s', filemtime($filePath)),
            'created' => date('Y-m-d H:i:s', filectime($filePath)),
            'readable' => is_readable($filePath),
            'writable' => is_writable($filePath),
            'extension' => pathinfo($filePath, PATHINFO_EXTENSION),
        ];
    }

    /**
     * 清理临时文件
     */
    public function cleanupTempFiles(): int
    {
        $tempDir = $this->basePath . '/runtime/curd_temp';
        $count = 0;
        
        if (is_dir($tempDir)) {
            $iterator = new \RecursiveIteratorIterator(
                new \RecursiveDirectoryIterator($tempDir),
                \RecursiveIteratorIterator::CHILD_FIRST
            );
            
            foreach ($iterator as $file) {
                if ($file->isFile()) {
                    // 删除超过1天的临时文件
                    if (time() - $file->getMTime() > 86400) {
                        unlink($file->getPathname());
                        $count++;
                    }
                }
            }
        }
        
        return $count;
    }

    /**
     * 更新统计信息
     */
    protected function updateStats(string $action): void
    {
        $this->stats[$action] = ($this->stats[$action] ?? 0) + 1;
        $this->stats['last_' . $action . '_time'] = time();
        $this->saveStats();
    }

    /**
     * 加载统计信息
     */
    protected function loadStats(): void
    {
        $statsFile = $this->basePath . '/runtime/curd_stats.json';
        
        if (file_exists($statsFile)) {
            $this->stats = json_decode(file_get_contents($statsFile), true) ?: [];
        }
    }

    /**
     * 保存统计信息
     */
    protected function saveStats(): void
    {
        $statsFile = $this->basePath . '/runtime/curd_stats.json';
        $statsDir = dirname($statsFile);
        
        if (!is_dir($statsDir)) {
            mkdir($statsDir, 0755, true);
        }
        
        file_put_contents($statsFile, json_encode($this->stats, JSON_PRETTY_PRINT));
    }

    /**
     * 获取总生成数
     */
    public function getTotalGenerated(): int
    {
        return $this->stats['created'] ?? 0;
    }

    /**
     * 获取最后生成时间
     */
    public function getLastGenerateTime(): ?string
    {
        $timestamp = $this->stats['last_created_time'] ?? null;
        return $timestamp ? date('Y-m-d H:i:s', $timestamp) : null;
    }

    /**
     * 获取统计信息
     */
    public function getStats(): array
    {
        return $this->stats;
    }

    /**
     * 验证文件路径
     */
    protected function validatePath(string $path): bool
    {
        // 防止路径遍历攻击
        $realPath = realpath(dirname($path));
        $basePath = realpath($this->basePath);
        
        return $realPath && $basePath && strpos($realPath, $basePath) === 0;
    }
}
