/**
 * 字段拖拽排序组件样式
 */

/* 拖拽手柄样式 */
.drag-handle {
    width: 50px !important;
    text-align: center;
    cursor: grab;
    background: #f8f9fa;
    border-right: 1px solid #e6e6e6;
    user-select: none;
    position: relative;
}

.drag-handle:hover {
    background: #e9ecef;
}

.drag-handle:active {
    cursor: grabbing;
}

.drag-handle i {
    font-size: 16px;
    color: #999;
    transition: color 0.2s ease;
}

.drag-handle:hover i {
    color: #1890ff;
}

/* 表头拖拽手柄 */
.drag-handle-header {
    width: 50px !important;
    text-align: center;
    background: #fafafa;
    border-right: 1px solid #e6e6e6;
}

.drag-handle-header i {
    font-size: 14px;
    color: #666;
}

/* 排序状态样式 */
.sorting-active {
    position: relative;
}

.sorting-active .drag-handle {
    background: #e6f7ff;
    border-color: #91d5ff;
}

.sorting-active .drag-handle i {
    color: #1890ff;
}

/* 拖拽中的样式 */
.field-sort-ghost {
    opacity: 0.5;
    background: #f0f0f0 !important;
}

.field-sort-chosen {
    background: #e6f7ff !important;
    box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
}

.field-sort-drag {
    background: #fff !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transform: rotate(2deg);
}

/* 占位符样式 */
.field-sort-placeholder {
    background: #e6f7ff !important;
    border: 2px dashed #91d5ff !important;
    opacity: 0.8;
}

.field-sort-placeholder td {
    border: none !important;
    height: 40px;
}

/* 排序控制按钮 */
.field-sort-controls {
    margin: 10px 0;
    text-align: right;
}

.field-sort-controls .layui-btn {
    margin-left: 8px;
}

/* 排序提示信息 */
.sort-hint {
    background: #f6ffed;
    border: 1px solid #b7eb8f;
    border-radius: 4px;
    padding: 8px 12px;
    margin: 10px 0;
    font-size: 12px;
    color: #52c41a;
    display: none;
}

.sorting-active + .sort-hint {
    display: block;
}

/* 禁用状态 */
.sorting-disabled .drag-handle {
    cursor: not-allowed;
    background: #f5f5f5;
    opacity: 0.6;
}

.sorting-disabled .drag-handle i {
    color: #ccc;
}

/* 表格行拖拽状态 */
tr.sortable-ghost {
    opacity: 0.4;
}

tr.sortable-chosen {
    background: #e6f7ff;
}

/* 拖拽时的全局样式 */
body.field-sorting {
    user-select: none;
}

body.field-sorting * {
    cursor: grabbing !important;
}

/* 拖拽指示器 */
.drag-indicator {
    position: absolute;
    left: -10px;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 20px;
    background: #1890ff;
    border-radius: 2px;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.drag-handle:hover .drag-indicator {
    opacity: 1;
}

/* 排序动画 */
.field-config-table tbody tr {
    transition: all 0.3s ease;
}

.field-config-table tbody tr:hover {
    background: #fafafa;
}

/* 排序编号 */
.field-order-number {
    position: absolute;
    top: 2px;
    right: 2px;
    background: #1890ff;
    color: #fff;
    font-size: 10px;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.sorting-active .field-order-number {
    opacity: 1;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .drag-handle {
        width: 40px !important;
    }

    .drag-handle-header {
        width: 40px !important;
    }

    .drag-handle i {
        font-size: 14px;
    }

    .field-sort-controls {
        text-align: center;
    }

    .field-sort-controls .layui-btn {
        margin: 4px;
        font-size: 12px;
        padding: 0 8px;
        height: 28px;
        line-height: 28px;
    }
}

/* 触摸设备优化 */
@media (pointer: coarse) {
    .drag-handle {
        width: 60px !important;
        padding: 8px 0;
    }

    .drag-handle i {
        font-size: 18px;
    }
}

/* 暗色主题支持 */
@media (prefers-color-scheme: dark) {
    .drag-handle {
        background: #2d2d2d;
        border-color: #404040;
    }

    .drag-handle:hover {
        background: #404040;
    }

    .drag-handle i {
        color: #ccc;
    }

    .drag-handle:hover i {
        color: #1890ff;
    }

    .drag-handle-header {
        background: #1e1e1e;
        border-color: #404040;
    }

    .sorting-active .drag-handle {
        background: #003a8c;
        border-color: #1890ff;
    }

    .field-sort-ghost {
        background: #404040 !important;
    }

    .field-sort-chosen {
        background: #003a8c !important;
    }

    .field-sort-placeholder {
        background: #003a8c !important;
        border-color: #1890ff !important;
    }

    .sort-hint {
        background: #162312;
        border-color: #389e0d;
        color: #73d13d;
    }
}

/* 拖拽手柄动画效果 */
@keyframes dragPulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

.drag-handle:active {
    animation: dragPulse 0.3s ease;
}

/* 排序完成提示动画 */
@keyframes sortComplete {
    0% { background: #f6ffed; }
    50% { background: #d9f7be; }
    100% { background: #f6ffed; }
}

.sort-complete-animation {
    animation: sortComplete 1s ease;
}

/* 字段行高亮效果 */
.field-highlight {
    background: linear-gradient(90deg, #e6f7ff 0%, transparent 100%) !important;
    border-left: 3px solid #1890ff !important;
}

/* 批量操作时的样式 */
.batch-sorting .drag-handle {
    background: #fff2e8;
    border-color: #ffbb96;
}

.batch-sorting .drag-handle i {
    color: #fa8c16;
}

/* 排序统计信息 */
.sort-stats {
    font-size: 12px;
    color: #666;
    margin-top: 8px;
    text-align: center;
}

.sort-stats .stat-item {
    margin: 0 8px;
}

.sort-stats .stat-value {
    font-weight: bold;
    color: #1890ff;
}

/* 模板管理样式 */
.template-list-container {
    max-height: 400px;
    overflow-y: auto;
}

.template-tabs {
    display: flex;
    border-bottom: 1px solid #e6e6e6;
    margin-bottom: 15px;
}

.template-tabs .tab-item {
    padding: 8px 16px;
    cursor: pointer;
    border-bottom: 2px solid transparent;
    transition: all 0.2s ease;
}

.template-tabs .tab-item:hover {
    color: #1890ff;
}

.template-tabs .tab-item.active {
    color: #1890ff;
    border-bottom-color: #1890ff;
}

.template-search {
    margin-bottom: 15px;
}

.template-item {
    border: 1px solid #e6e6e6;
    border-radius: 4px;
    padding: 12px;
    margin-bottom: 10px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.template-item:hover {
    border-color: #1890ff;
    box-shadow: 0 2px 8px rgba(24, 144, 255, 0.1);
}

.template-item.selected {
    border-color: #1890ff;
    background: #e6f7ff;
}

.template-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.template-header h4 {
    margin: 0;
    font-size: 14px;
    font-weight: 500;
}

.template-meta {
    font-size: 12px;
    color: #999;
}

.template-description {
    font-size: 12px;
    color: #666;
    margin-bottom: 8px;
    line-height: 1.4;
}

.template-tags {
    margin-bottom: 8px;
}

.template-tags .tag {
    display: inline-block;
    background: #f0f0f0;
    color: #666;
    padding: 2px 6px;
    border-radius: 2px;
    font-size: 11px;
    margin-right: 4px;
}

.template-stats {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 11px;
    color: #999;
}

.public-badge {
    background: #52c41a;
    color: #fff;
    padding: 2px 6px;
    border-radius: 2px;
}

/* 推荐模板样式 */
.recommendation-list {
    max-height: 300px;
    overflow-y: auto;
}

.recommendation-item {
    border: 1px solid #e6e6e6;
    border-radius: 4px;
    padding: 12px;
    margin-bottom: 10px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.recommendation-item:hover {
    border-color: #1890ff;
}

.recommendation-item.selected {
    border-color: #1890ff;
    background: #e6f7ff;
}

.recommendation-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.recommendation-header h4 {
    margin: 0;
    font-size: 14px;
}

.match-score {
    background: #1890ff;
    color: #fff;
    padding: 2px 8px;
    border-radius: 10px;
    font-size: 11px;
}

.recommendation-description {
    font-size: 12px;
    color: #666;
    margin-bottom: 6px;
}

.recommendation-reason {
    font-size: 11px;
    color: #999;
    font-style: italic;
}

/* 关联关系管理样式 */
.relationship-manager {
    margin-top: 20px;
    border: 1px solid #e6e6e6;
    border-radius: 4px;
    background: #fff;
}

.relationship-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid #e6e6e6;
    background: #fafafa;
}

.relationship-header h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 500;
}

.relationship-actions {
    display: flex;
    gap: 10px;
}

.relationship-content {
    padding: 20px;
}

/* 统计信息样式 */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 15px;
    margin-bottom: 20px;
}

.stat-item {
    text-align: center;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 4px;
    border: 1px solid #e9ecef;
}

.stat-value {
    font-size: 24px;
    font-weight: bold;
    color: #1890ff;
    margin-bottom: 5px;
}

.stat-label {
    font-size: 12px;
    color: #666;
}

/* 关联关系列表样式 */
.relationships-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 15px;
}

.relationship-card {
    border: 1px solid #e6e6e6;
    border-radius: 6px;
    background: #fff;
    transition: all 0.2s ease;
    overflow: hidden;
}

.relationship-card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.relationship-card.confidence-high {
    border-left: 4px solid #52c41a;
}

.relationship-card.confidence-medium {
    border-left: 4px solid #fa8c16;
}

.relationship-card.confidence-low {
    border-left: 4px solid #ff4d4f;
}

.relationship-card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 15px;
    background: #fafafa;
    border-bottom: 1px solid #e6e6e6;
}

.relationship-type {
    display: flex;
    align-items: center;
    gap: 8px;
}

.type-icon {
    font-size: 18px;
    font-weight: bold;
}

.type-name {
    font-size: 14px;
    font-weight: 500;
}

.relationship-confidence {
    display: flex;
    align-items: center;
    gap: 8px;
}

.confidence-value {
    font-size: 12px;
    font-weight: bold;
    color: #666;
}

.confidence-bar {
    width: 60px;
    height: 4px;
    background: #e6e6e6;
    border-radius: 2px;
    overflow: hidden;
}

.confidence-fill {
    height: 100%;
    background: linear-gradient(90deg, #ff4d4f 0%, #fa8c16 50%, #52c41a 100%);
    transition: width 0.3s ease;
}

.relationship-card-body {
    padding: 15px;
}

.relationship-info {
    margin-bottom: 12px;
}

.related-table {
    font-size: 16px;
    font-weight: 500;
    color: #1890ff;
    margin-bottom: 4px;
}

.relationship-description {
    font-size: 12px;
    color: #666;
}

.relationship-details {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 8px;
}

.detail-item {
    font-size: 11px;
}

.detail-label {
    color: #999;
    margin-right: 4px;
}

.detail-value {
    color: #333;
    font-weight: 500;
}

.relationship-card-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 15px;
    background: #fafafa;
    border-top: 1px solid #e6e6e6;
}

/* 无关联关系提示 */
.no-relationships {
    text-align: center;
    padding: 40px;
    color: #999;
    font-size: 14px;
}

/* 关联关系详情样式 */
.relationship-detail {
    max-height: 500px;
    overflow-y: auto;
}

.detail-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #e6e6e6;
}

.detail-header h4 {
    margin: 0;
    font-size: 18px;
}

.confidence-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: bold;
    color: #fff;
}

.confidence-badge.confidence-high {
    background: #52c41a;
}

.confidence-badge.confidence-medium {
    background: #fa8c16;
}

.confidence-badge.confidence-low {
    background: #ff4d4f;
}

.detail-section {
    margin-bottom: 20px;
}

.detail-section h5 {
    margin: 0 0 10px 0;
    font-size: 14px;
    font-weight: 500;
    color: #333;
}

.detail-section .layui-table {
    margin: 0;
}

.detail-section .layui-table td {
    padding: 8px 12px;
    font-size: 12px;
}

.detail-section pre {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 15px;
    margin: 0;
    font-size: 12px;
    line-height: 1.5;
    overflow-x: auto;
}

/* 生成代码样式 */
.generated-code {
    height: 600px;
}

.generated-code .layui-tab-content {
    height: calc(100% - 40px);
    overflow: hidden;
}

.generated-code .layui-tab-item {
    height: 100%;
    overflow-y: auto;
}

.generated-code pre {
    height: 100%;
    margin: 0;
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 20px;
    font-size: 13px;
    line-height: 1.6;
    overflow: auto;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .relationships-grid {
        grid-template-columns: 1fr;
    }

    .relationship-header {
        flex-direction: column;
        gap: 10px;
        align-items: flex-start;
    }

    .relationship-actions {
        width: 100%;
        justify-content: flex-end;
    }

    .relationship-details {
        grid-template-columns: 1fr;
    }
}

/* 暗色主题支持 */
@media (prefers-color-scheme: dark) {
    .relationship-manager {
        background: #1f1f1f;
        border-color: #333;
    }

    .relationship-header {
        background: #2a2a2a;
        border-color: #333;
        color: #fff;
    }

    .stat-item {
        background: #2a2a2a;
        border-color: #333;
        color: #fff;
    }

    .relationship-card {
        background: #1f1f1f;
        border-color: #333;
    }

    .relationship-card-header,
    .relationship-card-footer {
        background: #2a2a2a;
        border-color: #333;
    }

    .related-table {
        color: #40a9ff;
    }

    .detail-value {
        color: #fff;
    }
}
