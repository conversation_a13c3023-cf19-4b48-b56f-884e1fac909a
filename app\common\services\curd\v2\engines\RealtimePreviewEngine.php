<?php

namespace app\common\services\curd\v2\engines;

use app\common\services\curd\v2\dto\GenerateConfig;
use app\common\services\curd\v2\engines\TemplateEngine;

/**
 * 实时代码预览引擎
 * 负责实时生成和预览代码
 * Source: 基于WebSocket实时通信技术实现
 */
class RealtimePreviewEngine
{
    protected TemplateEngine $templateEngine;
    protected array $previewCache = [];
    protected array $websocketClients = [];

    public function __construct(TemplateEngine $templateEngine = null)
    {
        $this->templateEngine = $templateEngine ?: new TemplateEngine();
    }

    /**
     * 生成实时预览代码
     * 支持WebSocket实时推送
     */
    public function generatePreview(GenerateConfig $config, string $fileType = 'controller'): array
    {
        $cacheKey = $this->getCacheKey($config, $fileType);

        // 检查缓存
        if (isset($this->previewCache[$cacheKey])) {
            return $this->previewCache[$cacheKey];
        }

        try {
            // 根据文件类型生成预览
            $preview = match($fileType) {
                'controller' => $this->generateControllerPreview($config),
                'model' => $this->generateModelPreview($config),
                'view' => $this->generateViewPreview($config),
                'js' => $this->generateJsPreview($config),
                'api' => $this->generateApiPreview($config),
                default => ['error' => '不支持的文件类型']
            };

            // 缓存结果
            $this->previewCache[$cacheKey] = $preview;

            // 实时推送到WebSocket客户端
            $this->broadcastPreview($preview, $fileType);

            return $preview;

        } catch (\Exception $e) {
            return [
                'error' => $e->getMessage(),
                'code' => '',
                'highlight' => 'php'
            ];
        }
    }

    /**
     * 生成控制器预览
     */
    protected function generateControllerPreview(GenerateConfig $config): array
    {
        $template = $this->templateEngine->loadTemplate('controller/controller.tpl');
        $code = $this->templateEngine->renderTemplate($template, $config->toArray());

        return [
            'code' => $code,
            'highlight' => 'php',
            'type' => 'controller',
            'filename' => $config->getControllerName() . '.php',
            'lines' => substr_count($code, "\n") + 1,
            'size' => strlen($code)
        ];
    }

    /**
     * 生成模型预览
     */
    protected function generateModelPreview(GenerateConfig $config): array
    {
        $template = $this->templateEngine->loadTemplate('model/model.tpl');
        $code = $this->templateEngine->renderTemplate($template, $config->toArray());

        return [
            'code' => $code,
            'highlight' => 'php',
            'type' => 'model',
            'filename' => $config->getModelName() . '.php',
            'lines' => substr_count($code, "\n") + 1,
            'size' => strlen($code)
        ];
    }

    /**
     * 生成视图预览
     */
    protected function generateViewPreview(GenerateConfig $config): array
    {
        $template = $this->templateEngine->loadTemplate('view/index.tpl');
        $code = $this->templateEngine->renderTemplate($template, $config->toArray());

        return [
            'code' => $code,
            'highlight' => 'html',
            'type' => 'view',
            'filename' => 'index.blade.php',
            'lines' => substr_count($code, "\n") + 1,
            'size' => strlen($code)
        ];
    }

    /**
     * 生成JS预览
     */
    protected function generateJsPreview(GenerateConfig $config): array
    {
        $template = $this->templateEngine->loadTemplate('js/index.tpl');
        $code = $this->templateEngine->renderTemplate($template, $config->toArray());

        return [
            'code' => $code,
            'highlight' => 'javascript',
            'type' => 'js',
            'filename' => 'index.js',
            'lines' => substr_count($code, "\n") + 1,
            'size' => strlen($code)
        ];
    }

    /**
     * 生成API预览
     */
    protected function generateApiPreview(GenerateConfig $config): array
    {
        // 生成RESTful API接口代码
        $apiCode = $this->generateRestfulApi($config);

        return [
            'code' => $apiCode,
            'highlight' => 'php',
            'type' => 'api',
            'filename' => $config->getTableName() . '_api.php',
            'lines' => substr_count($apiCode, "\n") + 1,
            'size' => strlen($apiCode)
        ];
    }

    /**
     * 生成RESTful API代码
     */
    protected function generateRestfulApi(GenerateConfig $config): string
    {
        $tableName = $config->getTableName();
        $modelName = $config->getModelName();

        return <<<PHP
<?php
/**
 * {$tableName} RESTful API接口
 * 自动生成时间: {date('Y-m-d H:i:s')}
 */

namespace app\\api\\controller;

use app\\common\\controller\\ApiController;
use app\\common\\model\\{$modelName};
use support\\Request;
use support\\Response;

class {$modelName}ApiController extends ApiController
{
    /**
     * 获取列表
     * GET /api/{$tableName}
     */
    public function index(Request \$request): Response
    {
        \$page = \$request->get('page', 1);
        \$limit = \$request->get('limit', 15);

        \$query = {$modelName}::query();

        // 搜索条件
        if (\$keyword = \$request->get('keyword')) {
            \$query->where('name', 'like', "%{\$keyword}%");
        }

        \$total = \$query->count();
        \$list = \$query->offset((\$page - 1) * \$limit)
                      ->limit(\$limit)
                      ->get();

        return \$this->success([
            'list' => \$list,
            'total' => \$total,
            'page' => \$page,
            'limit' => \$limit
        ]);
    }

    /**
     * 获取详情
     * GET /api/{$tableName}/{id}
     */
    public function show(Request \$request, \$id): Response
    {
        \$model = {$modelName}::find(\$id);
        if (!\$model) {
            return \$this->error('记录不存在');
        }

        return \$this->success(\$model);
    }

    /**
     * 创建记录
     * POST /api/{$tableName}
     */
    public function store(Request \$request): Response
    {
        \$data = \$request->post();

        try {
            \$model = {$modelName}::create(\$data);
            return \$this->success(\$model, '创建成功');
        } catch (\\Exception \$e) {
            return \$this->error('创建失败: ' . \$e->getMessage());
        }
    }

    /**
     * 更新记录
     * PUT /api/{$tableName}/{id}
     */
    public function update(Request \$request, \$id): Response
    {
        \$model = {$modelName}::find(\$id);
        if (!\$model) {
            return \$this->error('记录不存在');
        }

        \$data = \$request->post();

        try {
            \$model->update(\$data);
            return \$this->success(\$model, '更新成功');
        } catch (\\Exception \$e) {
            return \$this->error('更新失败: ' . \$e->getMessage());
        }
    }

    /**
     * 删除记录
     * DELETE /api/{$tableName}/{id}
     */
    public function destroy(Request \$request, \$id): Response
    {
        \$model = {$modelName}::find(\$id);
        if (!\$model) {
            return \$this->error('记录不存在');
        }

        try {
            \$model->delete();
            return \$this->success(null, '删除成功');
        } catch (\\Exception \$e) {
            return \$this->error('删除失败: ' . \$e->getMessage());
        }
    }
}
PHP;
    }

    /**
     * 广播预览到WebSocket客户端
     */
    protected function broadcastPreview(array $preview, string $fileType): void
    {
        $message = json_encode([
            'type' => 'preview_update',
            'fileType' => $fileType,
            'data' => $preview,
            'timestamp' => time()
        ]);

        // 这里将集成WebSocket服务器进行实时推送
        // 暂时记录到日志，后续集成WebSocket
        error_log("Preview broadcast: " . $message);
    }

    /**
     * 获取缓存键
     */
    protected function getCacheKey(GenerateConfig $config, string $fileType): string
    {
        return md5(serialize($config->toArray()) . $fileType);
    }

    /**
     * 清除预览缓存
     */
    public function clearCache(): void
    {
        $this->previewCache = [];
    }

    /**
     * 获取支持的文件类型
     */
    public function getSupportedTypes(): array
    {
        return ['controller', 'model', 'view', 'js', 'api'];
    }

    /**
     * 获取预览统计信息
     */
    public function getPreviewStats(GenerateConfig $config): array
    {
        $stats = [];
        foreach ($this->getSupportedTypes() as $type) {
            $preview = $this->generatePreview($config, $type);
            $stats[$type] = [
                'lines' => $preview['lines'] ?? 0,
                'size' => $preview['size'] ?? 0,
                'filename' => $preview['filename'] ?? ''
            ];
        }

        return [
            'files' => $stats,
            'total_lines' => array_sum(array_column($stats, 'lines')),
            'total_size' => array_sum(array_column($stats, 'size')),
            'file_count' => count($stats)
        ];
    }

    /**
     * 注册WebSocket客户端
     */
    public function registerWebSocketClient(string $clientId, $connection): void
    {
        $this->websocketClients[$clientId] = $connection;
    }

    /**
     * 移除WebSocket客户端
     */
    public function removeWebSocketClient(string $clientId): void
    {
        unset($this->websocketClients[$clientId]);
    }

    /**
     * 获取连接的客户端数量
     */
    public function getConnectedClientsCount(): int
    {
        return count($this->websocketClients);
    }
}
