@echo off
echo === MySQL 密码重置工具 ===
echo.

echo 1. 停止 MySQL 服务...
net stop mysql
if %errorlevel% neq 0 (
    echo 尝试停止其他 MySQL 服务...
    net stop mysql80
    net stop mysql57
    net stop mysql56
)

echo.
echo 2. 创建临时初始化文件...
echo ALTER USER 'root'@'localhost' IDENTIFIED BY ''; > %TEMP%\mysql_reset.sql
echo FLUSH PRIVILEGES; >> %TEMP%\mysql_reset.sql

echo.
echo 3. 以安全模式启动 MySQL...
echo 请在新的命令行窗口中运行以下命令:
echo.
echo mysqld --init-file=%TEMP%\mysql_reset.sql --console
echo.
echo 等待看到 "ready for connections" 消息后，按任意键继续...
pause

echo.
echo 4. 停止安全模式的 MySQL...
taskkill /f /im mysqld.exe 2>nul

echo.
echo 5. 正常启动 MySQL 服务...
net start mysql
if %errorlevel% neq 0 (
    echo 尝试启动其他 MySQL 服务...
    net start mysql80
    if %errorlevel% neq 0 (
        net start mysql57
        if %errorlevel% neq 0 (
            net start mysql56
        )
    )
)

echo.
echo 6. 清理临时文件...
del %TEMP%\mysql_reset.sql

echo.
echo === 密码重置完成 ===
echo root 用户密码已重置为空密码
echo 现在可以运行: php auto_setup_database.php
echo.
pause
