/**
 * API文档管理器
 * 用于管理和展示生成的API文档
 */
class ApiDocManager {
    constructor() {
        this.baseUrl = '/admin/system/apidoc';
        this.currentTable = null;
        this.apiEndpoints = [];
        this.testResults = new Map();
        
        this.init();
    }

    /**
     * 初始化
     */
    init() {
        this.bindEvents();
        this.loadApiDocs();
    }

    /**
     * 绑定事件
     */
    bindEvents() {
        // 生成API文档按钮
        $(document).on('click', '.btn-generate-api', () => {
            this.showGenerateModal();
        });

        // 查看API文档按钮
        $(document).on('click', '.btn-view-api', (e) => {
            const tableName = $(e.target).data('table');
            this.viewApiDoc(tableName);
        });

        // 测试API按钮
        $(document).on('click', '.btn-test-api', (e) => {
            const tableName = $(e.target).data('table');
            this.showTestModal(tableName);
        });

        // 导出API文档按钮
        $(document).on('click', '.btn-export-api', (e) => {
            const tableName = $(e.target).data('table');
            const format = $(e.target).data('format');
            this.exportApiDoc(tableName, format);
        });

        // 搜索框
        $('#api-search').on('input', (e) => {
            this.searchApiDocs($(e.target).val());
        });

        // 刷新按钮
        $('.btn-refresh').on('click', () => {
            this.loadApiDocs();
        });
    }

    /**
     * 加载API文档列表
     */
    async loadApiDocs() {
        try {
            this.showLoading();
            
            const response = await fetch(`${this.baseUrl}/list`);
            const data = await response.json();
            
            if (data.code === 200) {
                this.renderApiDocList(data.data);
                this.updateStatistics(data.statistics);
            } else {
                this.showError('加载API文档列表失败：' + data.msg);
            }
        } catch (error) {
            console.error('Error loading API docs:', error);
            this.showError('加载API文档列表失败');
        } finally {
            this.hideLoading();
        }
    }

    /**
     * 渲染API文档列表
     */
    renderApiDocList(apiDocs) {
        const container = $('#api-doc-list');
        
        if (apiDocs.length === 0) {
            container.html(this.getEmptyState());
            return;
        }

        let html = '';
        apiDocs.forEach(doc => {
            html += this.getApiDocCard(doc);
        });
        
        container.html(html);
    }

    /**
     * 获取API文档卡片HTML
     */
    getApiDocCard(doc) {
        const statusBadge = doc.status === 'active' 
            ? '<span class="badge badge-soft-success">正常</span>'
            : '<span class="badge badge-soft-warning">待更新</span>';

        return `
            <div class="col-md-6 col-lg-4 mb-3">
                <div class="card api-doc-card" data-table="${doc.table_name}">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-start mb-3">
                            <h5 class="card-title mb-1">${doc.display_name}</h5>
                            ${statusBadge}
                        </div>
                        
                        <p class="text-muted mb-3">
                            <small><i class="mdi mdi-table"></i> ${doc.table_name}</small>
                        </p>
                        
                        <div class="row text-center mb-3">
                            <div class="col-4">
                                <div class="text-primary">
                                    <h4 class="mb-1">${doc.api_count}</h4>
                                    <small class="text-muted">接口数</small>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="text-success">
                                    <h4 class="mb-1">${doc.doc_size}</h4>
                                    <small class="text-muted">文档大小</small>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="text-info">
                                    <h4 class="mb-1">${doc.test_coverage || '0%'}</h4>
                                    <small class="text-muted">测试覆盖</small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="text-muted mb-3">
                            <small><i class="mdi mdi-clock-outline"></i> ${doc.generated_at}</small>
                        </div>
                        
                        <div class="btn-group w-100" role="group">
                            <button type="button" class="btn btn-soft-primary btn-view-api" 
                                    data-table="${doc.table_name}" title="查看文档">
                                <i class="mdi mdi-eye"></i>
                            </button>
                            <button type="button" class="btn btn-soft-success btn-test-api" 
                                    data-table="${doc.table_name}" title="测试接口">
                                <i class="mdi mdi-play"></i>
                            </button>
                            <div class="btn-group" role="group">
                                <button type="button" class="btn btn-soft-info dropdown-toggle" 
                                        data-bs-toggle="dropdown" title="导出文档">
                                    <i class="mdi mdi-download"></i>
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item btn-export-api" href="javascript:void(0)" 
                                           data-table="${doc.table_name}" data-format="html">HTML格式</a></li>
                                    <li><a class="dropdown-item btn-export-api" href="javascript:void(0)" 
                                           data-table="${doc.table_name}" data-format="markdown">Markdown格式</a></li>
                                    <li><a class="dropdown-item btn-export-api" href="javascript:void(0)" 
                                           data-table="${doc.table_name}" data-format="json">JSON格式</a></li>
                                </ul>
                            </div>
                            <button type="button" class="btn btn-soft-warning" 
                                    onclick="apiDocManager.regenerateDoc('${doc.table_name}')" title="重新生成">
                                <i class="mdi mdi-refresh"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 获取空状态HTML
     */
    getEmptyState() {
        return `
            <div class="col-12">
                <div class="text-center py-5">
                    <i class="mdi mdi-api mdi-48px text-muted"></i>
                    <h5 class="mt-3">暂无API文档</h5>
                    <p class="text-muted">点击"生成API文档"开始创建</p>
                    <button type="button" class="btn btn-primary btn-generate-api">
                        <i class="mdi mdi-plus"></i> 生成API文档
                    </button>
                </div>
            </div>
        `;
    }

    /**
     * 更新统计信息
     */
    updateStatistics(stats) {
        $('#total-tables').text(stats.total_tables || 0);
        $('#total-apis').text(stats.total_apis || 0);
        $('#total-size').text(stats.total_size || '0KB');
        $('#avg-apis').text(stats.avg_apis_per_table || 0);
    }

    /**
     * 显示生成模态框
     */
    showGenerateModal() {
        $('#generateApiModal').modal('show');
        this.loadTableList();
    }

    /**
     * 加载表列表
     */
    async loadTableList() {
        try {
            const response = await fetch('/admin/system/table/list');
            const data = await response.json();
            
            if (data.code === 200) {
                const select = $('#table_name');
                select.empty().append('<option value="">请选择数据表</option>');
                
                data.data.forEach(table => {
                    select.append(`<option value="${table.name}">${table.name} - ${table.comment || '无注释'}</option>`);
                });
            }
        } catch (error) {
            console.error('Error loading table list:', error);
        }
    }

    /**
     * 生成API文档
     */
    async generateApiDoc(formData) {
        try {
            this.showLoading('生成中...');
            
            const response = await fetch(`${this.baseUrl}/generate`, {
                method: 'POST',
                body: formData
            });
            
            const data = await response.json();
            
            if (data.code === 200) {
                this.showSuccess('API文档生成成功');
                $('#generateApiModal').modal('hide');
                this.loadApiDocs();
            } else {
                this.showError('生成失败：' + data.msg);
            }
        } catch (error) {
            console.error('Error generating API doc:', error);
            this.showError('生成失败，请重试');
        } finally {
            this.hideLoading();
        }
    }

    /**
     * 查看API文档
     */
    viewApiDoc(tableName) {
        window.open(`${this.baseUrl}/view?table=${tableName}`, '_blank');
    }

    /**
     * 显示测试模态框
     */
    async showTestModal(tableName) {
        this.currentTable = tableName;
        
        try {
            const response = await fetch(`${this.baseUrl}/endpoints?table=${tableName}`);
            const data = await response.json();
            
            if (data.code === 200) {
                this.apiEndpoints = data.data;
                this.renderTestInterface();
                $('#apiTestModal').modal('show');
            } else {
                this.showError('加载API接口失败：' + data.msg);
            }
        } catch (error) {
            console.error('Error loading API endpoints:', error);
            this.showError('加载API接口失败');
        }
    }

    /**
     * 渲染测试界面
     */
    renderTestInterface() {
        const container = $('#api-test-content');
        let html = '';
        
        this.apiEndpoints.forEach((endpoint, index) => {
            html += this.getTestEndpointHtml(endpoint, index);
        });
        
        container.html(html);
    }

    /**
     * 获取测试接口HTML
     */
    getTestEndpointHtml(endpoint, index) {
        const methodClass = `method-${endpoint.method.toLowerCase()}`;
        
        return `
            <div class="card mb-3">
                <div class="card-header">
                    <h6 class="mb-0">
                        <span class="badge badge-${methodClass}">${endpoint.method}</span>
                        <code class="ms-2">${endpoint.path}</code>
                        <span class="ms-2 text-muted">${endpoint.summary}</span>
                    </h6>
                </div>
                <div class="card-body">
                    <form class="api-test-form" data-index="${index}">
                        ${this.getParameterInputs(endpoint.parameters)}
                        
                        <div class="d-flex justify-content-between align-items-center mt-3">
                            <button type="button" class="btn btn-primary" onclick="apiDocManager.testEndpoint(${index})">
                                <i class="mdi mdi-send"></i> 发送请求
                            </button>
                            <button type="button" class="btn btn-secondary" onclick="apiDocManager.clearTestForm(${index})">
                                <i class="mdi mdi-refresh"></i> 清空
                            </button>
                        </div>
                    </form>
                    
                    <div class="test-result mt-3" id="test-result-${index}" style="display: none;">
                        <h6>测试结果:</h6>
                        <div class="result-content"></div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 获取参数输入框HTML
     */
    getParameterInputs(parameters) {
        if (!parameters || parameters.length === 0) {
            return '<p class="text-muted">此接口无需参数</p>';
        }
        
        let html = '<div class="row">';
        
        parameters.forEach(param => {
            const required = param.required ? 'required' : '';
            const requiredMark = param.required ? '<span class="text-danger">*</span>' : '';
            
            html += `
                <div class="col-md-6 mb-3">
                    <label class="form-label">
                        ${param.name} ${requiredMark}
                        <small class="text-muted">(${param.type})</small>
                    </label>
                    <input type="text" class="form-control" name="${param.name}" 
                           placeholder="${param.description}" ${required}>
                    <small class="text-muted">${param.description}</small>
                </div>
            `;
        });
        
        html += '</div>';
        return html;
    }

    /**
     * 测试接口
     */
    async testEndpoint(index) {
        const endpoint = this.apiEndpoints[index];
        const form = document.querySelector(`[data-index="${index}"]`);
        const formData = new FormData(form);
        
        const params = {};
        for (let [key, value] of formData.entries()) {
            if (value.trim() !== '') {
                params[key] = value;
            }
        }
        
        try {
            const response = await fetch(`${this.baseUrl}/test`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: JSON.stringify({
                    table_name: this.currentTable,
                    endpoint: endpoint.path,
                    method: endpoint.method,
                    params: params
                })
            });
            
            const data = await response.json();
            this.showTestResult(index, data);
            
        } catch (error) {
            console.error('Error testing endpoint:', error);
            this.showTestResult(index, {
                code: 500,
                msg: '请求失败',
                data: { error: error.message }
            });
        }
    }

    /**
     * 显示测试结果
     */
    showTestResult(index, result) {
        const resultDiv = document.getElementById(`test-result-${index}`);
        const resultContent = resultDiv.querySelector('.result-content');
        
        if (result.code === 200) {
            resultContent.innerHTML = `
                <div class="alert alert-success">
                    <h6 class="mb-2">✓ 测试成功</h6>
                    <p><strong>响应时间:</strong> ${result.data.response_time}</p>
                    <p><strong>状态码:</strong> ${result.data.status_code}</p>
                    <p><strong>响应内容:</strong></p>
                    <pre class="bg-light p-2 rounded">${JSON.stringify(result.data.response_body, null, 2)}</pre>
                </div>
            `;
        } else {
            resultContent.innerHTML = `
                <div class="alert alert-danger">
                    <h6 class="mb-2">✗ 测试失败</h6>
                    <p>${result.msg}</p>
                </div>
            `;
        }
        
        resultDiv.style.display = 'block';
        this.testResults.set(index, result);
    }

    /**
     * 清空测试表单
     */
    clearTestForm(index) {
        const form = document.querySelector(`[data-index="${index}"]`);
        form.reset();
        
        const resultDiv = document.getElementById(`test-result-${index}`);
        resultDiv.style.display = 'none';
    }

    /**
     * 导出API文档
     */
    exportApiDoc(tableName, format) {
        const url = `${this.baseUrl}/export?table=${tableName}&format=${format}`;
        window.open(url, '_blank');
    }

    /**
     * 重新生成文档
     */
    async regenerateDoc(tableName) {
        if (!confirm('确定要重新生成API文档吗？这将覆盖现有文档。')) {
            return;
        }
        
        try {
            this.showLoading('重新生成中...');
            
            const formData = new FormData();
            formData.append('table_name', tableName);
            
            const response = await fetch(`${this.baseUrl}/generate`, {
                method: 'POST',
                body: formData
            });
            
            const data = await response.json();
            
            if (data.code === 200) {
                this.showSuccess('重新生成成功');
                this.loadApiDocs();
            } else {
                this.showError('重新生成失败：' + data.msg);
            }
        } catch (error) {
            console.error('Error regenerating doc:', error);
            this.showError('重新生成失败，请重试');
        } finally {
            this.hideLoading();
        }
    }

    /**
     * 搜索API文档
     */
    searchApiDocs(keyword) {
        const cards = document.querySelectorAll('.api-doc-card');
        
        cards.forEach(card => {
            const tableName = card.dataset.table;
            const displayName = card.querySelector('.card-title').textContent;
            
            if (tableName.includes(keyword) || displayName.includes(keyword)) {
                card.parentElement.style.display = 'block';
            } else {
                card.parentElement.style.display = 'none';
            }
        });
    }

    /**
     * 显示加载状态
     */
    showLoading(message = '加载中...') {
        // 实现加载状态显示
        if (typeof toastr !== 'undefined') {
            toastr.info(message);
        }
    }

    /**
     * 隐藏加载状态
     */
    hideLoading() {
        // 实现加载状态隐藏
    }

    /**
     * 显示成功消息
     */
    showSuccess(message) {
        if (typeof toastr !== 'undefined') {
            toastr.success(message);
        } else {
            alert(message);
        }
    }

    /**
     * 显示错误消息
     */
    showError(message) {
        if (typeof toastr !== 'undefined') {
            toastr.error(message);
        } else {
            alert(message);
        }
    }
}

// 全局实例
let apiDocManager;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    apiDocManager = new ApiDocManager();
});
