/**
 * CURD 生成器 V2 JavaScript
 */

layui.use(['form', 'layer', 'element'], function() {
    var form = layui.form;
    var layer = layui.layer;
    var element = layui.element;

    // 全局变量
    var currentStep = 1,
        maxStep = 5,
        tableInfo = null,
        fieldConfigs = [],
        isLoading = false,
        currentPreviewFile = 'controller';

    // 初始化
    init();

    function init() {
        bindEvents();
        updateStepStatus();
        updateProgressBar();
        initStepNavigation();
        loadTables();
    }

    // 初始化步骤导航
    function initStepNavigation() {
        $('.step-item').on('click', function() {
            var targetStep = parseInt($(this).data('step'));
            if (targetStep <= currentStep || validateStep(currentStep)) {
                goToStep(targetStep);
            }
        });
    }

    // 更新进度条
    function updateProgressBar() {
        var progress = (currentStep / maxStep) * 100;
        $('#progress-fill').css('width', progress + '%');
        $('#progress-text').text('步骤 ' + currentStep + ' / ' + maxStep);
    }

    // 绑定事件
    function bindEvents() {
        // 分析表结构
        $('#analyze-btn').on('click', function() {
            if (!isLoading) analyzeTable();
        });

        // 刷新表列表
        $('#refresh-tables-btn').on('click', function() {
            var connection = $('select[name="connection"]').val();
            if (connection) {
                showLoading('正在刷新表列表...');
                loadTables();
            } else {
                layer.msg('请先选择数据库连接', {icon: 2});
            }
        });

        // 测试连接
        $('#test-connection-btn').on('click', function() {
            var connection = $('select[name="connection"]').val();
            if (connection) {
                testConnection(connection);
            } else {
                layer.msg('请先选择数据库连接', {icon: 2});
            }
        });

        // 步骤导航
        $('#next-btn').on('click', function() {
            if (!isLoading && validateStep(currentStep)) {
                nextStep();
            }
        });

        $('#prev-btn').on('click', function() {
            if (!isLoading && currentStep > 1) {
                prevStep();
            }
        });

        // 生成代码
        $('#generate-btn').on('click', function() {
            if (!isLoading) generateCode();
        });

        // 数据库连接选择变化
        form.on('select(connectionSelect)', function(data){
            console.log('数据库连接选择事件触发:', data.value);
            if (data.value) {
                showLoading('正在加载表列表...');
                loadTables();
                updateConnectionStatus(data.value);
            }
        });

        // 表选择变化
        form.on('select(tableSelect)', function(data){
            if (data.value) {
                var connection = $('select[name="connection"]').val();
                var prefix = getTablePrefix(connection);
                $('input[name="table_prefix"]').val(prefix);
                $('#analyze-btn').removeClass('layui-btn-disabled');
            }
        });

        // 文件切换
        $(document).on('click', '.file-item', function() {
            var fileType = $(this).data('file');
            if (fileType) {
                switchPreviewFile(fileType);
            }
        });

        // 复制代码
        $(document).on('click', '#copy-code', function() {
            var code = $('#preview-code').text();
            if (navigator.clipboard) {
                navigator.clipboard.writeText(code).then(function() {
                    layer.msg('代码已复制到剪贴板', {icon: 1});
                });
            }
        });

        // 全屏预览
        $(document).on('click', '#fullscreen-preview', function() {
            var $panel = $('.code-preview-panel');
            if ($panel.hasClass('code-preview-fullscreen')) {
                $panel.removeClass('code-preview-fullscreen');
                $(this).find('i').removeClass('layui-icon-screen-restore').addClass('layui-icon-screen-full');
            } else {
                $panel.addClass('code-preview-fullscreen');
                $(this).find('i').removeClass('layui-icon-screen-full').addClass('layui-icon-screen-restore');
            }
        });

        // 键盘快捷键
        $(document).on('keydown', function(e) {
            if (e.ctrlKey && !isLoading) {
                switch(e.keyCode) {
                    case 37: // Ctrl + 左箭头
                        e.preventDefault();
                        if (currentStep > 1) prevStep();
                        break;
                    case 39: // Ctrl + 右箭头
                        e.preventDefault();
                        if (currentStep < maxStep && validateStep(currentStep)) nextStep();
                        break;
                    case 13: // Ctrl + Enter
                        e.preventDefault();
                        if (currentStep === maxStep) generateCode();
                        break;
                }
            }
        });
    }

    // 显示加载状态
    function showLoading(message) {
        isLoading = true;
        $('.curd-content-card .layui-card-body').append(
            '<div class="loading-overlay"><div class="loading-spinner"></div><div style="margin-top: 15px;">' + (message || '加载中...') + '</div></div>'
        );
    }

    // 隐藏加载状态
    function hideLoading() {
        isLoading = false;
        $('.loading-overlay').remove();
    }

    // 验证步骤
    function validateStep(step) {
        switch(step) {
            case 1:
                var tableName = $('select[name="table_name"]').val();
                if (!tableName) {
                    layer.msg('请选择数据表', {icon: 2});
                    return false;
                }
                if (!tableInfo) {
                    layer.msg('请先分析表结构', {icon: 2});
                    return false;
                }
                return true;
            case 2:
                return fieldConfigs && fieldConfigs.length > 0;
            case 3:
                return true;
            case 4:
                return true;
            default:
                return true;
        }
    }

    // 跳转到指定步骤
    function goToStep(step) {
        if (step < 1 || step > maxStep) return;

        currentStep = step;

        // 更新步骤导航状态
        $('.step-item').removeClass('active completed');
        for (var i = 1; i <= maxStep; i++) {
            var $stepItem = $('#nav-step-' + i);
            if (i < currentStep) {
                $stepItem.addClass('completed');
            } else if (i === currentStep) {
                $stepItem.addClass('active');
            }
        }

        updateProgressBar();
        showStepContent();
        updateStepButtons();

        // 特殊处理
        if (currentStep === 2) {
            loadFieldConfigs();
        } else if (currentStep === 4) {
            refreshCodePreview();
        }
    }

    // 下一步
    function nextStep() {
        if (currentStep < maxStep && validateStep(currentStep)) {
            goToStep(currentStep + 1);
        }
    }

    // 上一步
    function prevStep() {
        if (currentStep > 1) {
            goToStep(currentStep - 1);
        }
    }

    // 显示步骤内容
    function showStepContent() {
        $('.step-panel').removeClass('active').hide();
        $('#step-' + currentStep).addClass('active').show();

        var stepTitles = ['', '选择数据表', '配置字段', '生成选项', '预览代码', '生成文件'];
        $('#current-step-title').text(stepTitles[currentStep]);
    }

    // 更新步骤按钮状态
    function updateStepButtons() {
        $('#prev-btn').toggle(currentStep > 1);
        $('#next-btn').toggle(currentStep < maxStep);
        $('#generate-btn').toggle(currentStep === maxStep);
    }

    // 更新步骤状态
    function updateStepStatus() {
        // 兼容旧版本
    }

    // 获取表前缀
    function getTablePrefix(connection) {
        var prefixes = {
            'mysql': 'ea8_',
            'mysql_read': 'ea8_',
            'mysql_second': 'ddwx_',
            'mysql_log': 'ea8_',
            'mysql_cache': 'ea8_'
        };
        return prefixes[connection] || '';
    }

    // 更新连接状态
    function updateConnectionStatus(connection) {
        var $statusItem = $('#connection-status');

        $statusItem.removeClass('connected error')
                   .addClass('connecting')
                   .find('.status-text').text('连接中...');

        setTimeout(function() {
            $statusItem.removeClass('connecting')
                       .addClass('connected')
                       .find('.status-text').text('已连接 (' + connection + ')');
        }, 1000);
    }

    // 测试数据库连接
    function testConnection(connection) {
        var loadIndex = layer.load(2, {content: '测试连接中...'});

        $.post('/curdtest/tables', {
            connection: connection
        }, function(res) {
            layer.close(loadIndex);
            if (res.code === 1) {
                layer.msg('连接测试成功！找到 ' + res.data.length + ' 个表', {icon: 1, time: 3000});
                updateConnectionStatus(connection);
            } else {
                layer.msg('连接测试失败: ' + res.msg, {icon: 2, time: 3000});
                $('#connection-status').removeClass('connected connecting')
                                      .addClass('error')
                                      .find('.status-text').text('连接失败');
            }
        }).fail(function() {
            layer.close(loadIndex);
            layer.msg('网络请求失败', {icon: 2});
            $('#connection-status').removeClass('connected connecting')
                                  .addClass('error')
                                  .find('.status-text').text('网络错误');
        });
    }

    // 加载数据表列表
    function loadTables() {
        var connection = $('select[name="connection"]').val() || 'mysql';
        console.log('开始加载表列表，连接:', connection);

        $.post('/curdtest/tables', {
            connection: connection
        }, function(res) {
            console.log('表列表请求响应:', res);
            hideLoading();
            if (res.code === 1) {
                console.log('成功获取表列表，数量:', res.data.length);
                var html = '<option value="">请选择数据表</option>';
                $.each(res.data, function(i, table) {
                    html += '<option value="' + table.name + '">' + table.name + ' (' + table.comment + ')</option>';
                });
                $('select[name="table_name"]').html(html);
                form.render('select');

                $('#table-count-info').text('共找到 ' + res.data.length + ' 个表');

                var message = '数据表加载成功 (' + res.data.length + ' 个表)';
                if (res.msg.indexOf('演示数据') !== -1) {
                    message += ' [演示模式]';
                }
                layer.msg(message, {icon: 1, time: 2000});
            } else {
                console.error('表列表加载失败:', res.msg);
                layer.msg('加载失败: ' + res.msg, {icon: 2});
                $('select[name="table_name"]').html('<option value="">加载失败</option>');
                form.render('select');
                $('#table-count-info').text('');
            }
        }).fail(function(xhr, status, error) {
            console.error('表列表请求失败:', status, error);
            console.error('响应状态:', xhr.status);
            console.error('响应内容:', xhr.responseText);
            hideLoading();
            layer.msg('网络请求失败', {icon: 2});
            $('select[name="table_name"]').html('<option value="">网络错误</option>');
            form.render('select');
            $('#table-count-info').text('');
        });
    }

    // 分析表结构
    function analyzeTable() {
        var tableName = $('select[name="table_name"]').val();
        var tablePrefix = $('input[name="table_prefix"]').val();
        var connection = $('select[name="connection"]').val() || 'mysql';

        if (!tableName) {
            layer.msg('请选择数据表', {icon: 2});
            return;
        }

        showLoading('正在分析表结构...');

        $.post('/curdtest/analyze', {
            table_name: tableName,
            table_prefix: tablePrefix,
            connection: connection
        }, function(res) {
            hideLoading();
            if (res.code === 1) {
                tableInfo = res.data;
                displayTableInfo();

                // 显示表信息面板
                $('#table-info-panel').show();
                $('#analyze-status').removeClass('status-badge').addClass('status-badge success').text('已分析');

                // 启用下一步按钮
                $('#next-btn').removeClass('layui-btn-disabled');

                var message = '表结构分析完成';
                if (res.msg && res.msg.indexOf('演示数据') !== -1) {
                    message += ' [演示模式]';
                }
                layer.msg(message, {icon: 1, time: 2000});

                // 自动跳转提示
                setTimeout(function() {
                    if (currentStep === 1) {
                        layer.confirm('表结构分析完成，是否继续配置字段？', {
                            btn: ['继续', '稍后'],
                            icon: 3
                        }, function(index) {
                            layer.close(index);
                            nextStep();
                        });
                    }
                }, 1000);

            } else {
                layer.msg('分析失败: ' + res.msg, {icon: 2, time: 3000});
                $('#analyze-status').removeClass('status-badge success').addClass('status-badge error').text('分析失败');
            }
        }).fail(function() {
            hideLoading();
            layer.msg('网络请求失败', {icon: 2});
            $('#analyze-status').removeClass('status-badge success').addClass('status-badge error').text('网络错误');
        });
    }

    // 显示表信息
    function displayTableInfo() {
        if (!tableInfo) return;

        // 更新基本信息
        $('#info-table-name').text(tableInfo.name || '-');
        $('#info-table-comment').text(tableInfo.comment || '无注释');
        $('#info-field-count').text(tableInfo.fields ? tableInfo.fields.length : 0);
        $('#info-connection').text($('select[name="connection"]').val() || '-');

        // 显示主键
        var primaryKeys = [];
        if (tableInfo.fields) {
            $.each(tableInfo.fields, function(i, field) {
                if (field.primary) {
                    primaryKeys.push(field.name);
                }
            });
        }
        $('#info-primary-key').text(primaryKeys.length > 0 ? primaryKeys.join(', ') : '无主键');

        // 显示索引数
        $('#info-index-count').text(tableInfo.indexes ? tableInfo.indexes.length : 0);

        // 添加动画效果
        $('#table-info-panel .info-item').each(function(index) {
            $(this).css('opacity', '0').delay(index * 100).animate({opacity: 1}, 300);
        });

        // 更新字段配置
        if (tableInfo.fields) {
            fieldConfigs = [];
            $.each(tableInfo.fields, function(i, field) {
                fieldConfigs.push({
                    name: field.name,
                    type: field.type,
                    length: field.length,
                    nullable: field.nullable,
                    default: field.default,
                    comment: field.comment,
                    primary: field.primary,
                    auto_increment: field.auto_increment,
                    component: field.component || getDefaultComponent(field.type),
                    show_in_list: !field.primary && field.name !== 'created_at' && field.name !== 'updated_at',
                    show_in_form: !field.primary && !field.auto_increment,
                    show_in_search: field.name.indexOf('name') !== -1 || field.name.indexOf('title') !== -1,
                    required: !field.nullable && !field.auto_increment,
                    sortable: true
                });
            });
        }
    }

    // 获取默认组件类型
    function getDefaultComponent(fieldType) {
        var type = fieldType.toLowerCase();
        if (type.indexOf('int') !== -1) return 'number';
        if (type.indexOf('decimal') !== -1 || type.indexOf('float') !== -1) return 'number';
        if (type.indexOf('text') !== -1) return 'textarea';
        if (type.indexOf('date') !== -1) return 'date';
        if (type.indexOf('time') !== -1) return 'datetime';
        if (type.indexOf('enum') !== -1) return 'select';
        if (type.indexOf('json') !== -1) return 'textarea';
        return 'input';
    }

    // 加载字段配置
    function loadFieldConfigs() {
        if (!fieldConfigs || fieldConfigs.length === 0) {
            layer.msg('请先分析表结构', {icon: 2});
            return;
        }

        var tbody = $('#field-config-tbody');
        tbody.empty();

        $.each(fieldConfigs, function(index, field) {
            var row = createFieldConfigRow(field, index);
            tbody.append(row);
        });

        // 更新统计
        updateFieldStats();

        // 重新渲染表单
        form.render();
    }

    // 创建字段配置行
    function createFieldConfigRow(field, index) {
        var checked = {
            list: field.show_in_list ? 'checked' : '',
            form: field.show_in_form ? 'checked' : '',
            search: field.show_in_search ? 'checked' : '',
            required: field.required ? 'checked' : '',
            sortable: field.sortable ? 'checked' : ''
        };

        return '<tr data-field="' + field.name + '" data-index="' + index + '">' +
            '<td><i class="layui-icon layui-icon-slider drag-handle"></i></td>' +
            '<td><input type="checkbox" lay-skin="primary" class="field-checkbox"></td>' +
            '<td><span class="field-name" data-field="' + field.name + '">' + field.name + '</span></td>' +
            '<td><span class="field-type">' + field.type + '</span></td>' +
            '<td><input type="text" class="layui-input field-label" value="' + (field.comment || field.name) + '" data-field="' + field.name + '"></td>' +
            '<td><select class="layui-input field-component" data-field="' + field.name + '">' +
                '<option value="input"' + (field.component === 'input' ? ' selected' : '') + '>文本框</option>' +
                '<option value="textarea"' + (field.component === 'textarea' ? ' selected' : '') + '>文本域</option>' +
                '<option value="number"' + (field.component === 'number' ? ' selected' : '') + '>数字框</option>' +
                '<option value="select"' + (field.component === 'select' ? ' selected' : '') + '>下拉框</option>' +
                '<option value="radio"' + (field.component === 'radio' ? ' selected' : '') + '>单选框</option>' +
                '<option value="checkbox"' + (field.component === 'checkbox' ? ' selected' : '') + '>复选框</option>' +
                '<option value="switch"' + (field.component === 'switch' ? ' selected' : '') + '>开关</option>' +
                '<option value="date"' + (field.component === 'date' ? ' selected' : '') + '>日期</option>' +
                '<option value="datetime"' + (field.component === 'datetime' ? ' selected' : '') + '>日期时间</option>' +
                '<option value="image"' + (field.component === 'image' ? ' selected' : '') + '>图片上传</option>' +
                '<option value="file"' + (field.component === 'file' ? ' selected' : '') + '>文件上传</option>' +
                '<option value="editor"' + (field.component === 'editor' ? ' selected' : '') + '>富文本编辑器</option>' +
            '</select></td>' +
            '<td><input type="checkbox" lay-skin="primary" class="field-show-list" ' + checked.list + '></td>' +
            '<td><input type="checkbox" lay-skin="primary" class="field-show-form" ' + checked.form + '></td>' +
            '<td><input type="checkbox" lay-skin="primary" class="field-show-search" ' + checked.search + '></td>' +
            '<td><input type="checkbox" lay-skin="primary" class="field-required" ' + checked.required + '></td>' +
            '<td><input type="checkbox" lay-skin="primary" class="field-sortable" ' + checked.sortable + '></td>' +
            '<td>' +
                '<button type="button" class="layui-btn layui-btn-xs" onclick="editField(\'' + field.name + '\')">' +
                    '<i class="layui-icon layui-icon-edit"></i>' +
                '</button>' +
                '<button type="button" class="layui-btn layui-btn-xs layui-btn-danger" onclick="removeField(\'' + field.name + '\')">' +
                    '<i class="layui-icon layui-icon-delete"></i>' +
                '</button>' +
            '</td>' +
        '</tr>';
    }

    // 更新字段统计
    function updateFieldStats() {
        var total = fieldConfigs.length;
        var listCount = 0, formCount = 0, searchCount = 0;

        $.each(fieldConfigs, function(i, field) {
            if (field.show_in_list) listCount++;
            if (field.show_in_form) formCount++;
            if (field.show_in_search) searchCount++;
        });

        $('#total-fields-count').text(total);
        $('#list-fields-stat').text(listCount);
        $('#form-fields-stat').text(formCount);
        $('#search-fields-stat').text(searchCount);
    }

    // 生成代码
    function generateCode() {
        var data = collectFormData();
        if (!data.table_name) {
            layer.msg('请先选择数据表', {icon: 2});
            return;
        }

        showLoading('正在生成代码...');

        $.post('/curdtest/generate', data, function(res) {
            hideLoading();
            if (res.code === 1) {
                displayGenerateResult(res.data);
                layer.msg('代码生成成功', {icon: 1});
            } else {
                layer.msg('生成失败: ' + res.msg, {icon: 2});
            }
        }).fail(function() {
            hideLoading();
            layer.msg('网络请求失败', {icon: 2});
        });
    }

    // 收集表单数据
    function collectFormData() {
        return {
            table_name: $('select[name="table_name"]').val(),
            table_prefix: $('input[name="table_prefix"]').val(),
            connection: $('select[name="connection"]').val(),
            fields: fieldConfigs,
            options: {
                generate_controller: $('input[name="gen_controller"]').is(':checked'),
                generate_model: $('input[name="gen_model"]').is(':checked'),
                generate_view: $('input[name="gen_view"]').is(':checked'),
                generate_js: $('input[name="gen_js"]').is(':checked'),
                generate_route: $('input[name="gen_route"]').is(':checked'),
                output_format: $('input[name="output_format"]:checked').val(),
                force_overwrite: $('input[name="force_overwrite"]').is(':checked'),
                backup_existing: $('input[name="backup_existing"]').is(':checked')
            }
        };
    }

    // 显示生成结果
    function displayGenerateResult(data) {
        var html = '<div class="result-list">';

        if (data.files && data.files.length > 0) {
            $.each(data.files, function(i, file) {
                html += '<div class="result-item">';
                html += '<div class="result-icon success"><i class="layui-icon layui-icon-ok"></i></div>';
                html += '<div class="result-content">';
                html += '<div class="result-title">' + file.name + '</div>';
                html += '<div class="result-desc">' + file.path + '</div>';
                html += '</div>';
                html += '<div class="result-actions">';
                html += '<button type="button" class="layui-btn layui-btn-xs" onclick="previewFile(\'' + file.path + '\')">预览</button>';
                html += '<button type="button" class="layui-btn layui-btn-xs layui-btn-primary" onclick="downloadFile(\'' + file.path + '\')">下载</button>';
                html += '</div>';
                html += '</div>';
            });
        } else {
            html += '<div class="empty-result">';
            html += '<div class="empty-icon"><i class="layui-icon layui-icon-face-cry"></i></div>';
            html += '<div class="empty-text">';
            html += '<h4>生成失败</h4>';
            html += '<p>没有生成任何文件</p>';
            html += '</div>';
            html += '</div>';
        }

        html += '</div>';
        $('#generate-result').html(html);

        // 更新状态
        $('#generate-status').removeClass('waiting generating').addClass('success').text('生成完成');
    }

    // 暴露全局函数
    window.CurdGeneratorV2 = {
        goToStep: goToStep,
        nextStep: nextStep,
        prevStep: prevStep,
        analyzeTable: analyzeTable,
        loadFieldConfigs: loadFieldConfigs,
        generateCode: generateCode
    };

    // 全局函数
    window.editField = function(fieldName) {
        layer.open({
            type: 1,
            title: '配置字段: ' + fieldName,
            content: $('#field-config-modal'),
            area: ['600px', '500px'],
            btn: ['保存', '取消'],
            yes: function(index) {
                layer.close(index);
            }
        });
    };

    window.removeField = function(fieldName) {
        layer.confirm('确定要移除字段 "' + fieldName + '" 吗？', {icon: 3}, function(index) {
            // 从配置中移除字段
            fieldConfigs = fieldConfigs.filter(function(field) {
                return field.name !== fieldName;
            });

            // 重新加载字段配置
            loadFieldConfigs();

            layer.close(index);
            layer.msg('字段已移除', {icon: 1});
        });
    };

    window.previewFile = function(filePath) {
        layer.open({
            type: 2,
            title: '文件预览: ' + filePath,
            area: ['80%', '80%'],
            content: '/admin/system/curd_generate_v2/preview?file=' + encodeURIComponent(filePath)
        });
    };

    window.downloadFile = function(filePath) {
        window.open('/admin/system/curd_generate_v2/download?file=' + encodeURIComponent(filePath));
    };
});
