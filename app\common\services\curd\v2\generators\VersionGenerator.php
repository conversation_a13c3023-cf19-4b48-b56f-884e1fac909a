<?php

namespace app\common\services\curd\v2\generators;

/**
 * 版本管理生成器
 * 根据版本管理需求分析结果生成相应的配置文件和脚本
 */
class VersionGenerator
{
    /**
     * 生成版本管理配置
     */
    public function generateVersionConfig(array $versionAnalysis, array $tableInfo, array $options = []): array
    {
        $configs = [];

        // 生成 Git 配置
        $configs['git'] = $this->generateGitConfig($versionAnalysis, $tableInfo);

        // 生成 CI/CD 配置
        $configs['cicd'] = $this->generateCiCdConfig($versionAnalysis, $tableInfo);

        // 生成 Git Hooks
        $configs['hooks'] = $this->generateGitHooks($versionAnalysis, $tableInfo);

        // 生成工作流文档
        $configs['documentation'] = $this->generateWorkflowDocumentation($versionAnalysis, $tableInfo);

        // 生成自动化脚本
        $configs['scripts'] = $this->generateAutomationScripts($versionAnalysis, $tableInfo);

        return $configs;
    }

    /**
     * 生成 Git 配置
     */
    protected function generateGitConfig(array $versionAnalysis, array $tableInfo): array
    {
        $gitConfig = [];

        // .gitignore 文件
        $gitConfig['.gitignore'] = $this->generateGitignore();

        // .gitattributes 文件
        $gitConfig['.gitattributes'] = $this->generateGitattributes();

        // Git 配置文件
        $gitConfig['.gitmessage'] = $this->generateCommitTemplate($versionAnalysis);

        return $gitConfig;
    }

    /**
     * 生成 .gitignore 文件
     */
    protected function generateGitignore(): string
    {
        return <<<EOT
# Dependencies
/vendor/
/node_modules/
composer.lock
package-lock.json

# Environment files
.env
.env.local
.env.*.local

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS files
.DS_Store
Thumbs.db

# Log files
*.log
/storage/logs/
/runtime/logs/

# Cache files
/storage/cache/
/runtime/cache/
/storage/framework/cache/
/storage/framework/sessions/
/storage/framework/views/

# Upload files
/public/uploads/
/storage/uploads/

# Build files
/dist/
/build/
*.min.js
*.min.css

# Test files
/coverage/
.phpunit.result.cache

# Backup files
*.bak
*.backup
*.sql

# Temporary files
/tmp/
/temp/
EOT;
    }

    /**
     * 生成 .gitattributes 文件
     */
    protected function generateGitattributes(): string
    {
        return <<<EOT
# Auto detect text files and perform LF normalization
* text=auto

# PHP files
*.php text eol=lf

# JavaScript files
*.js text eol=lf
*.json text eol=lf

# CSS files
*.css text eol=lf
*.scss text eol=lf
*.sass text eol=lf

# HTML files
*.html text eol=lf
*.htm text eol=lf

# XML files
*.xml text eol=lf

# Markdown files
*.md text eol=lf

# Configuration files
*.yml text eol=lf
*.yaml text eol=lf
*.ini text eol=lf
*.conf text eol=lf

# Shell scripts
*.sh text eol=lf

# Binary files
*.png binary
*.jpg binary
*.jpeg binary
*.gif binary
*.ico binary
*.pdf binary
*.zip binary
*.tar.gz binary
EOT;
    }

    /**
     * 生成提交模板
     */
    protected function generateCommitTemplate(array $versionAnalysis): string
    {
        $scopes = implode('|', array_keys($versionAnalysis['requirements']['commits']['scopes'] ?? []));
        
        return <<<EOT
# <type>(<scope>): <subject>
#
# <body>
#
# <footer>

# Type should be one of the following:
# * feat: A new feature
# * fix: A bug fix
# * docs: Documentation only changes
# * style: Changes that do not affect the meaning of the code
# * refactor: A code change that neither fixes a bug nor adds a feature
# * perf: A code change that improves performance
# * test: Adding missing tests or correcting existing tests
# * chore: Changes to the build process or auxiliary tools

# Scope should be one of the following:
# * {$scopes}

# Subject line should:
# * use the imperative, present tense: "change" not "changed" nor "changes"
# * not capitalize the first letter
# * not end with a dot (.)
# * be no more than 50 characters

# Body should:
# * use the imperative, present tense: "change" not "changed" nor "changes"
# * include motivation for the change and contrasts with previous behavior
# * wrap at 72 characters

# Footer should contain any information about Breaking Changes
# and is also the place to reference GitHub issues that this commit Closes.
EOT;
    }

    /**
     * 生成 CI/CD 配置
     */
    protected function generateCiCdConfig(array $versionAnalysis, array $tableInfo): array
    {
        $cicdConfig = [];

        // GitHub Actions
        $cicdConfig['.github/workflows/ci.yml'] = $this->generateGitHubActions($versionAnalysis, $tableInfo);

        // GitLab CI
        $cicdConfig['.gitlab-ci.yml'] = $this->generateGitLabCI($versionAnalysis, $tableInfo);

        // Jenkins
        $cicdConfig['Jenkinsfile'] = $this->generateJenkinsfile($versionAnalysis, $tableInfo);

        return $cicdConfig;
    }

    /**
     * 生成 GitHub Actions 配置
     */
    protected function generateGitHubActions(array $versionAnalysis, array $tableInfo): string
    {
        $modelName = $this->getModelName($tableInfo['name']);
        
        return <<<EOT
name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]
  release:
    types: [ published ]

jobs:
  test:
    runs-on: ubuntu-latest
    
    services:
      mysql:
        image: mysql:8.0
        env:
          MYSQL_ROOT_PASSWORD: password
          MYSQL_DATABASE: test_db
        ports:
          - 3306:3306
        options: --health-cmd="mysqladmin ping" --health-interval=10s --health-timeout=5s --health-retries=3

    steps:
    - uses: actions/checkout@v3

    - name: Setup PHP
      uses: shivammathur/setup-php@v2
      with:
        php-version: '8.1'
        extensions: mbstring, xml, ctype, iconv, intl, pdo_mysql
        coverage: xdebug

    - name: Cache Composer packages
      id: composer-cache
      uses: actions/cache@v3
      with:
        path: vendor
        key: \${{ runner.os }}-php-\${{ hashFiles('**/composer.lock') }}
        restore-keys: |
          \${{ runner.os }}-php-

    - name: Install dependencies
      run: composer install --prefer-dist --no-progress

    - name: Copy environment file
      run: cp .env.example .env

    - name: Generate application key
      run: php artisan key:generate

    - name: Run database migrations
      run: php artisan migrate
      env:
        DB_CONNECTION: mysql
        DB_HOST: 127.0.0.1
        DB_PORT: 3306
        DB_DATABASE: test_db
        DB_USERNAME: root
        DB_PASSWORD: password

    - name: Run tests
      run: vendor/bin/phpunit --coverage-clover coverage.xml

    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.xml

  lint:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup PHP
      uses: shivammathur/setup-php@v2
      with:
        php-version: '8.1'
        tools: phpcs, phpstan
    
    - name: Install dependencies
      run: composer install --prefer-dist --no-progress
    
    - name: Run PHP CodeSniffer
      run: vendor/bin/phpcs --standard=PSR12 app/
    
    - name: Run PHPStan
      run: vendor/bin/phpstan analyse app/ --level=5

  security:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup PHP
      uses: shivammathur/setup-php@v2
      with:
        php-version: '8.1'
    
    - name: Install dependencies
      run: composer install --prefer-dist --no-progress
    
    - name: Run security check
      run: composer audit

  deploy:
    needs: [test, lint, security]
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Deploy to production
      run: |
        echo "Deploying {$modelName} module to production..."
        # Add your deployment commands here
EOT;
    }

    /**
     * 生成 GitLab CI 配置
     */
    protected function generateGitLabCI(array $versionAnalysis, array $tableInfo): string
    {
        return <<<EOT
stages:
  - test
  - lint
  - security
  - deploy

variables:
  MYSQL_ROOT_PASSWORD: password
  MYSQL_DATABASE: test_db

cache:
  paths:
    - vendor/

before_script:
  - apt-get update -qq && apt-get install -y -qq git curl libmcrypt-dev libjpeg-dev libpng-dev libfreetype6-dev libbz2-dev
  - curl -sS https://getcomposer.org/installer | php
  - php composer.phar install --prefer-dist --no-progress

test:
  stage: test
  image: php:8.1
  services:
    - mysql:8.0
  script:
    - cp .env.example .env
    - php artisan key:generate
    - php artisan migrate
    - vendor/bin/phpunit --coverage-text --colors=never
  coverage: '/^\s*Lines:\s*\d+.\d+\%/'

lint:
  stage: lint
  image: php:8.1
  script:
    - vendor/bin/phpcs --standard=PSR12 app/
    - vendor/bin/phpstan analyse app/ --level=5

security:
  stage: security
  image: php:8.1
  script:
    - composer audit

deploy_staging:
  stage: deploy
  image: php:8.1
  script:
    - echo "Deploying to staging..."
  only:
    - develop

deploy_production:
  stage: deploy
  image: php:8.1
  script:
    - echo "Deploying to production..."
  only:
    - main
  when: manual
EOT;
    }

    /**
     * 生成 Jenkinsfile
     */
    protected function generateJenkinsfile(array $versionAnalysis, array $tableInfo): string
    {
        return <<<EOT
pipeline {
    agent any
    
    environment {
        DB_CONNECTION = 'mysql'
        DB_HOST = 'localhost'
        DB_PORT = '3306'
        DB_DATABASE = 'test_db'
        DB_USERNAME = 'root'
        DB_PASSWORD = 'password'
    }
    
    stages {
        stage('Checkout') {
            steps {
                checkout scm
            }
        }
        
        stage('Install Dependencies') {
            steps {
                sh 'composer install --prefer-dist --no-progress'
            }
        }
        
        stage('Prepare Environment') {
            steps {
                sh 'cp .env.example .env'
                sh 'php artisan key:generate'
            }
        }
        
        stage('Run Tests') {
            steps {
                sh 'php artisan migrate'
                sh 'vendor/bin/phpunit --coverage-clover coverage.xml'
            }
            post {
                always {
                    publishHTML([
                        allowMissing: false,
                        alwaysLinkToLastBuild: true,
                        keepAll: true,
                        reportDir: 'coverage',
                        reportFiles: 'index.html',
                        reportName: 'Coverage Report'
                    ])
                }
            }
        }
        
        stage('Code Quality') {
            parallel {
                stage('Lint') {
                    steps {
                        sh 'vendor/bin/phpcs --standard=PSR12 app/'
                    }
                }
                stage('Static Analysis') {
                    steps {
                        sh 'vendor/bin/phpstan analyse app/ --level=5'
                    }
                }
            }
        }
        
        stage('Security Check') {
            steps {
                sh 'composer audit'
            }
        }
        
        stage('Deploy') {
            when {
                branch 'main'
            }
            steps {
                sh 'echo "Deploying to production..."'
                // Add deployment commands here
            }
        }
    }
    
    post {
        always {
            cleanWs()
        }
        success {
            echo 'Pipeline succeeded!'
        }
        failure {
            echo 'Pipeline failed!'
        }
    }
}
EOT;
    }

    /**
     * 生成 Git Hooks
     */
    protected function generateGitHooks(array $versionAnalysis, array $tableInfo): array
    {
        $hooks = [];

        // pre-commit hook
        $hooks['.git/hooks/pre-commit'] = $this->generatePreCommitHook($versionAnalysis);

        // commit-msg hook
        $hooks['.git/hooks/commit-msg'] = $this->generateCommitMsgHook($versionAnalysis);

        // pre-push hook
        $hooks['.git/hooks/pre-push'] = $this->generatePrePushHook($versionAnalysis);

        return $hooks;
    }

    /**
     * 生成 pre-commit hook
     */
    protected function generatePreCommitHook(array $versionAnalysis): string
    {
        return <<<EOT
#!/bin/sh
# Pre-commit hook for code quality checks

echo "Running pre-commit checks..."

# Check for PHP syntax errors
echo "Checking PHP syntax..."
for file in \$(git diff --cached --name-only --diff-filter=ACM | grep '\.php\$'); do
    php -l "\$file"
    if [ \$? -ne 0 ]; then
        echo "PHP syntax error in \$file"
        exit 1
    fi
done

# Run PHP CodeSniffer
echo "Running PHP CodeSniffer..."
vendor/bin/phpcs --standard=PSR12 --colors \$(git diff --cached --name-only --diff-filter=ACM | grep '\.php\$' | tr '\n' ' ')
if [ \$? -ne 0 ]; then
    echo "PHP CodeSniffer found issues"
    exit 1
fi

# Run PHPStan
echo "Running PHPStan..."
vendor/bin/phpstan analyse \$(git diff --cached --name-only --diff-filter=ACM | grep '\.php\$' | tr '\n' ' ') --level=5
if [ \$? -ne 0 ]; then
    echo "PHPStan found issues"
    exit 1
fi

echo "Pre-commit checks passed!"
exit 0
EOT;
    }

    /**
     * 生成 commit-msg hook
     */
    protected function generateCommitMsgHook(array $versionAnalysis): string
    {
        return <<<EOT
#!/bin/sh
# Commit message validation hook

commit_regex='^(feat|fix|docs|style|refactor|test|chore|perf|ci)(\(.+\))?: .{1,50}'

error_msg="Aborting commit. Your commit message is invalid. Please use the format:
<type>(<scope>): <subject>

Example: feat(user): add user registration feature

Types: feat, fix, docs, style, refactor, test, chore, perf, ci
Subject should be no more than 50 characters"

if ! grep -qE "\$commit_regex" "\$1"; then
    echo "\$error_msg" >&2
    exit 1
fi
EOT;
    }

    /**
     * 生成 pre-push hook
     */
    protected function generatePrePushHook(array $versionAnalysis): string
    {
        return <<<EOT
#!/bin/sh
# Pre-push hook for running tests

echo "Running tests before push..."

# Run unit tests
vendor/bin/phpunit
if [ \$? -ne 0 ]; then
    echo "Tests failed. Push aborted."
    exit 1
fi

echo "All tests passed. Proceeding with push."
exit 0
EOT;
    }

    /**
     * 生成工作流文档
     */
    protected function generateWorkflowDocumentation(array $versionAnalysis, array $tableInfo): array
    {
        $docs = [];

        // Git 工作流文档
        $docs['docs/git-workflow.md'] = $this->generateGitWorkflowDoc($versionAnalysis);

        // 发布流程文档
        $docs['docs/release-process.md'] = $this->generateReleaseProcessDoc($versionAnalysis);

        // 代码审查指南
        $docs['docs/code-review-guide.md'] = $this->generateCodeReviewGuide($versionAnalysis);

        return $docs;
    }

    /**
     * 生成 Git 工作流文档
     */
    protected function generateGitWorkflowDoc(array $versionAnalysis): string
    {
        $workflow = $versionAnalysis['workflow_recommendation']['workflow'] ?? 'gitflow';
        
        return <<<EOT
# Git 工作流指南

## 工作流类型

我们使用 **{$workflow}** 工作流进行版本管理。

## 分支说明

### 主要分支

- **main/master**: 生产分支，包含稳定的生产代码
- **develop**: 开发分支，包含最新的开发代码

### 辅助分支

- **feature/**: 功能分支，用于开发新功能
- **bugfix/**: 修复分支，用于修复开发分支的问题
- **hotfix/**: 热修复分支，用于修复生产环境的紧急问题
- **release/**: 发布分支，用于准备新版本发布

## 分支命名规范

- 功能分支: `feature/功能描述`
- 修复分支: `bugfix/问题描述`
- 热修复分支: `hotfix/紧急修复描述`
- 发布分支: `release/v版本号`

## 工作流程

### 开发新功能

1. 从 develop 分支创建功能分支
   ```bash
   git checkout develop
   git pull origin develop
   git checkout -b feature/新功能描述
   ```

2. 开发功能并提交
   ```bash
   git add .
   git commit -m "feat: 添加新功能"
   ```

3. 推送分支并创建 Pull Request
   ```bash
   git push origin feature/新功能描述
   ```

4. 代码审查通过后合并到 develop

### 修复问题

1. 从 develop 分支创建修复分支
   ```bash
   git checkout develop
   git pull origin develop
   git checkout -b bugfix/问题描述
   ```

2. 修复问题并提交
   ```bash
   git add .
   git commit -m "fix: 修复问题"
   ```

3. 推送分支并创建 Pull Request

### 发布版本

1. 从 develop 分支创建发布分支
   ```bash
   git checkout develop
   git pull origin develop
   git checkout -b release/v1.0.0
   ```

2. 更新版本号和变更日志

3. 测试发布分支

4. 合并到 main 和 develop 分支

5. 创建版本标签

## 提交规范

请遵循 [Conventional Commits](https://www.conventionalcommits.org/) 规范：

```
<type>(<scope>): <subject>

<body>

<footer>
```

### 提交类型

- **feat**: 新功能
- **fix**: 修复
- **docs**: 文档
- **style**: 格式
- **refactor**: 重构
- **test**: 测试
- **chore**: 构建

### 示例

```
feat(user): 添加用户注册功能

添加了用户注册的完整流程，包括：
- 注册表单验证
- 邮箱验证
- 密码加密

Closes #123
```
EOT;
    }

    /**
     * 生成发布流程文档
     */
    protected function generateReleaseProcessDoc(array $versionAnalysis): string
    {
        return <<<EOT
# 发布流程指南

## 版本号规范

我们使用 [语义化版本](https://semver.org/) 规范：

- **主版本号**: 不兼容的 API 修改
- **次版本号**: 向下兼容的功能性新增
- **修订号**: 向下兼容的问题修正

格式：`MAJOR.MINOR.PATCH`

## 发布流程

### 1. 准备发布

1. 确保所有功能已合并到 develop 分支
2. 运行完整的测试套件
3. 更新文档和变更日志

### 2. 创建发布分支

```bash
git checkout develop
git pull origin develop
git checkout -b release/v1.0.0
```

### 3. 更新版本信息

1. 更新 `composer.json` 中的版本号
2. 更新 `CHANGELOG.md`
3. 更新文档中的版本引用

### 4. 测试发布分支

1. 运行所有测试
2. 进行集成测试
3. 进行用户验收测试

### 5. 完成发布

1. 合并到 main 分支
   ```bash
   git checkout main
   git merge --no-ff release/v1.0.0
   ```

2. 创建版本标签
   ```bash
   git tag -a v1.0.0 -m "Release version 1.0.0"
   git push origin v1.0.0
   ```

3. 合并回 develop 分支
   ```bash
   git checkout develop
   git merge --no-ff release/v1.0.0
   ```

4. 删除发布分支
   ```bash
   git branch -d release/v1.0.0
   git push origin --delete release/v1.0.0
   ```

### 6. 部署到生产环境

1. 自动化部署流程会在标签创建后触发
2. 监控部署状态
3. 验证生产环境功能

## 热修复流程

### 紧急修复

1. 从 main 分支创建热修复分支
   ```bash
   git checkout main
   git pull origin main
   git checkout -b hotfix/v1.0.1
   ```

2. 修复问题并测试

3. 更新版本号（修订号 +1）

4. 合并到 main 和 develop 分支

5. 创建新的版本标签

## 变更日志

每次发布都需要更新 `CHANGELOG.md` 文件，包含：

- 新增功能
- 修复的问题
- 重大变更
- 废弃的功能

### 格式示例

```markdown
## [1.0.0] - 2024-01-01

### Added
- 用户注册功能
- 邮箱验证功能

### Fixed
- 修复登录页面样式问题

### Changed
- 优化数据库查询性能

### Deprecated
- 旧版本 API 将在下个版本移除
```
EOT;
    }

    /**
     * 生成代码审查指南
     */
    protected function generateCodeReviewGuide(array $versionAnalysis): string
    {
        return <<<EOT
# 代码审查指南

## 审查目标

代码审查的主要目标是：

1. **提高代码质量**: 发现潜在的问题和改进点
2. **知识共享**: 团队成员之间分享知识和经验
3. **保持一致性**: 确保代码风格和架构的一致性
4. **降低风险**: 减少生产环境中的问题

## 审查清单

### 功能性

- [ ] 代码是否实现了预期的功能？
- [ ] 是否有遗漏的边界情况？
- [ ] 错误处理是否充分？
- [ ] 是否有潜在的性能问题？

### 代码质量

- [ ] 代码是否易于理解？
- [ ] 变量和函数命名是否清晰？
- [ ] 是否遵循了编码规范？
- [ ] 是否有重复的代码？

### 安全性

- [ ] 是否有安全漏洞？
- [ ] 用户输入是否得到适当验证？
- [ ] 敏感信息是否得到保护？
- [ ] 是否使用了安全的编程实践？

### 测试

- [ ] 是否有足够的测试覆盖？
- [ ] 测试是否有意义？
- [ ] 是否测试了边界情况？
- [ ] 测试是否易于维护？

### 文档

- [ ] 代码注释是否充分？
- [ ] API 文档是否更新？
- [ ] README 是否需要更新？
- [ ] 变更日志是否更新？

## 审查流程

### 1. 准备审查

1. 理解变更的背景和目标
2. 检查相关的需求和设计文档
3. 了解变更的范围和影响

### 2. 进行审查

1. 从整体架构开始审查
2. 逐个文件进行详细审查
3. 关注关键路径和复杂逻辑
4. 检查测试的完整性

### 3. 提供反馈

1. 使用建设性的语言
2. 提供具体的改进建议
3. 区分必须修改和建议改进
4. 认可好的代码实践

### 4. 跟进修改

1. 验证修改是否解决了问题
2. 确保没有引入新的问题
3. 必要时进行二次审查

## 审查标准

### 必须修改 (Must Fix)

- 功能错误
- 安全漏洞
- 性能问题
- 违反编码规范

### 建议改进 (Should Fix)

- 代码可读性
- 设计模式应用
- 注释完善
- 测试改进

### 可选优化 (Could Fix)

- 性能优化
- 代码简化
- 重构建议

## 最佳实践

### 对于审查者

1. **及时审查**: 尽快进行审查，避免阻塞开发
2. **全面审查**: 不仅关注代码，也关注测试和文档
3. **建设性反馈**: 提供具体的改进建议
4. **学习态度**: 从他人的代码中学习

### 对于被审查者

1. **小批量提交**: 保持 PR 的大小合理
2. **清晰描述**: 提供清晰的变更描述
3. **自我审查**: 提交前先自己审查一遍
4. **积极响应**: 及时回应审查意见

## 工具支持

### 自动化检查

- **代码风格**: PHP CodeSniffer
- **静态分析**: PHPStan
- **安全扫描**: Security Checker
- **测试覆盖**: PHPUnit Coverage

### 审查平台

- **GitHub**: Pull Request 审查
- **GitLab**: Merge Request 审查
- **Bitbucket**: Pull Request 审查

## 审查模板

### Pull Request 模板

```markdown
## 变更描述

简要描述这次变更的内容和目标。

## 变更类型

- [ ] 新功能
- [ ] 修复问题
- [ ] 重构
- [ ] 文档更新
- [ ] 测试改进

## 测试

- [ ] 单元测试已通过
- [ ] 集成测试已通过
- [ ] 手动测试已完成

## 审查清单

- [ ] 代码符合编码规范
- [ ] 测试覆盖充分
- [ ] 文档已更新
- [ ] 无安全问题

## 相关链接

- 相关 Issue: #123
- 设计文档: [链接]
```
EOT;
    }

    /**
     * 生成自动化脚本
     */
    protected function generateAutomationScripts(array $versionAnalysis, array $tableInfo): array
    {
        $scripts = [];

        // 发布脚本
        $scripts['scripts/release.sh'] = $this->generateReleaseScript($versionAnalysis);

        // 版本更新脚本
        $scripts['scripts/bump-version.sh'] = $this->generateVersionBumpScript($versionAnalysis);

        // 变更日志生成脚本
        $scripts['scripts/generate-changelog.sh'] = $this->generateChangelogScript($versionAnalysis);

        return $scripts;
    }

    /**
     * 生成发布脚本
     */
    protected function generateReleaseScript(array $versionAnalysis): string
    {
        return <<<EOT
#!/bin/bash

# 自动化发布脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 函数定义
log_info() {
    echo -e "\${GREEN}[INFO]\${NC} \$1"
}

log_warn() {
    echo -e "\${YELLOW}[WARN]\${NC} \$1"
}

log_error() {
    echo -e "\${RED}[ERROR]\${NC} \$1"
}

# 检查参数
if [ \$# -ne 1 ]; then
    log_error "Usage: \$0 <version>"
    log_error "Example: \$0 1.0.0"
    exit 1
fi

VERSION=\$1

# 验证版本号格式
if ! [[ \$VERSION =~ ^[0-9]+\.[0-9]+\.[0-9]+\$ ]]; then
    log_error "Invalid version format. Use semantic versioning (e.g., 1.0.0)"
    exit 1
fi

log_info "Starting release process for version \$VERSION"

# 检查当前分支
CURRENT_BRANCH=\$(git rev-parse --abbrev-ref HEAD)
if [ "\$CURRENT_BRANCH" != "develop" ]; then
    log_error "Please switch to develop branch before releasing"
    exit 1
fi

# 检查工作目录是否干净
if [ -n "\$(git status --porcelain)" ]; then
    log_error "Working directory is not clean. Please commit or stash changes."
    exit 1
fi

# 拉取最新代码
log_info "Pulling latest changes..."
git pull origin develop

# 运行测试
log_info "Running tests..."
vendor/bin/phpunit
if [ \$? -ne 0 ]; then
    log_error "Tests failed. Aborting release."
    exit 1
fi

# 创建发布分支
log_info "Creating release branch..."
git checkout -b release/v\$VERSION

# 更新版本号
log_info "Updating version number..."
./scripts/bump-version.sh \$VERSION

# 生成变更日志
log_info "Generating changelog..."
./scripts/generate-changelog.sh \$VERSION

# 提交版本更新
git add .
git commit -m "chore: bump version to \$VERSION"

# 合并到 main 分支
log_info "Merging to main branch..."
git checkout main
git pull origin main
git merge --no-ff release/v\$VERSION

# 创建标签
log_info "Creating tag..."
git tag -a v\$VERSION -m "Release version \$VERSION"

# 推送到远程仓库
log_info "Pushing to remote repository..."
git push origin main
git push origin v\$VERSION

# 合并回 develop 分支
log_info "Merging back to develop..."
git checkout develop
git merge --no-ff release/v\$VERSION
git push origin develop

# 删除发布分支
log_info "Cleaning up..."
git branch -d release/v\$VERSION

log_info "Release \$VERSION completed successfully!"
log_info "Don't forget to:"
log_info "1. Update the release notes on GitHub/GitLab"
log_info "2. Deploy to production environment"
log_info "3. Announce the release to the team"
EOT;
    }

    /**
     * 生成版本更新脚本
     */
    protected function generateVersionBumpScript(array $versionAnalysis): string
    {
        return <<<EOT
#!/bin/bash

# 版本号更新脚本

set -e

if [ \$# -ne 1 ]; then
    echo "Usage: \$0 <version>"
    exit 1
fi

VERSION=\$1

echo "Updating version to \$VERSION..."

# 更新 composer.json
if [ -f "composer.json" ]; then
    sed -i.bak "s/\"version\": \".*\"/\"version\": \"\$VERSION\"/" composer.json
    rm composer.json.bak
    echo "Updated composer.json"
fi

# 更新 package.json
if [ -f "package.json" ]; then
    sed -i.bak "s/\"version\": \".*\"/\"version\": \"\$VERSION\"/" package.json
    rm package.json.bak
    echo "Updated package.json"
fi

# 更新版本常量文件
if [ -f "config/app.php" ]; then
    sed -i.bak "s/'version' => '.*'/'version' => '\$VERSION'/" config/app.php
    rm config/app.php.bak
    echo "Updated config/app.php"
fi

echo "Version updated to \$VERSION"
EOT;
    }

    /**
     * 生成变更日志脚本
     */
    protected function generateChangelogScript(array $versionAnalysis): string
    {
        return <<<EOT
#!/bin/bash

# 变更日志生成脚本

set -e

if [ \$# -ne 1 ]; then
    echo "Usage: \$0 <version>"
    exit 1
fi

VERSION=\$1
DATE=\$(date +%Y-%m-%d)

echo "Generating changelog for version \$VERSION..."

# 获取上一个版本标签
LAST_TAG=\$(git describe --tags --abbrev=0 2>/dev/null || echo "")

if [ -z "\$LAST_TAG" ]; then
    echo "No previous tag found. Generating changelog from first commit."
    COMMIT_RANGE=""
else
    echo "Last tag: \$LAST_TAG"
    COMMIT_RANGE="\$LAST_TAG..HEAD"
fi

# 创建临时文件
TEMP_FILE=\$(mktemp)

# 生成变更日志头部
cat > \$TEMP_FILE << EOF
## [\$VERSION] - \$DATE

EOF

# 获取新功能
FEATURES=\$(git log \$COMMIT_RANGE --pretty=format:"- %s" --grep="^feat" | head -20)
if [ -n "\$FEATURES" ]; then
    echo "### Added" >> \$TEMP_FILE
    echo "\$FEATURES" >> \$TEMP_FILE
    echo "" >> \$TEMP_FILE
fi

# 获取修复
FIXES=\$(git log \$COMMIT_RANGE --pretty=format:"- %s" --grep="^fix" | head -20)
if [ -n "\$FIXES" ]; then
    echo "### Fixed" >> \$TEMP_FILE
    echo "\$FIXES" >> \$TEMP_FILE
    echo "" >> \$TEMP_FILE
fi

# 获取变更
CHANGES=\$(git log \$COMMIT_RANGE --pretty=format:"- %s" --grep="^refactor\\|^perf\\|^style" | head -10)
if [ -n "\$CHANGES" ]; then
    echo "### Changed" >> \$TEMP_FILE
    echo "\$CHANGES" >> \$TEMP_FILE
    echo "" >> \$TEMP_FILE
fi

# 如果 CHANGELOG.md 存在，则插入到文件开头
if [ -f "CHANGELOG.md" ]; then
    # 保存现有内容
    tail -n +2 CHANGELOG.md > \${TEMP_FILE}.old
    
    # 创建新的 CHANGELOG.md
    echo "# Changelog" > CHANGELOG.md
    echo "" >> CHANGELOG.md
    cat \$TEMP_FILE >> CHANGELOG.md
    cat \${TEMP_FILE}.old >> CHANGELOG.md
    
    rm \${TEMP_FILE}.old
else
    # 创建新的 CHANGELOG.md
    echo "# Changelog" > CHANGELOG.md
    echo "" >> CHANGELOG.md
    cat \$TEMP_FILE >> CHANGELOG.md
fi

rm \$TEMP_FILE

echo "Changelog updated for version \$VERSION"
EOT;
    }

    /**
     * 获取模型名称
     */
    protected function getModelName(string $tableName): string
    {
        $name = preg_replace('/^[a-z]+_/', '', $tableName);
        return str_replace(' ', '', ucwords(str_replace('_', ' ', $name)));
    }
}
