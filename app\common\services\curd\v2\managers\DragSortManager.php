<?php

namespace app\common\services\curd\v2\managers;

use app\common\services\curd\v2\dto\FieldInfo;

/**
 * 拖拽排序管理器
 * 负责处理字段的拖拽排序功能
 * Source: 基于Atlassian Pragmatic Drag and Drop技术
 */
class DragSortManager
{
    protected array $fieldOrder = [];
    protected array $sortHistory = [];
    protected int $maxHistorySize = 50;

    /**
     * 设置字段排序
     */
    public function setFieldOrder(array $fields): void
    {
        // 保存历史记录
        $this->saveToHistory($this->fieldOrder);
        
        $this->fieldOrder = [];
        foreach ($fields as $index => $field) {
            if ($field instanceof FieldInfo) {
                $this->fieldOrder[$field->getName()] = $index;
            } elseif (is_array($field) && isset($field['name'])) {
                $this->fieldOrder[$field['name']] = $index;
            } elseif (is_string($field)) {
                $this->fieldOrder[$field] = $index;
            }
        }
    }

    /**
     * 移动字段位置
     */
    public function moveField(string $fieldName, int $fromIndex, int $toIndex): array
    {
        // 保存历史记录
        $this->saveToHistory($this->fieldOrder);
        
        // 获取所有字段名按当前顺序排列
        $orderedFields = $this->getOrderedFieldNames();
        
        // 验证索引
        if ($fromIndex < 0 || $fromIndex >= count($orderedFields) || 
            $toIndex < 0 || $toIndex >= count($orderedFields)) {
            throw new \InvalidArgumentException('索引超出范围');
        }
        
        // 移动字段
        $field = array_splice($orderedFields, $fromIndex, 1)[0];
        array_splice($orderedFields, $toIndex, 0, $field);
        
        // 更新排序
        $this->fieldOrder = array_flip($orderedFields);
        
        return [
            'success' => true,
            'from' => $fromIndex,
            'to' => $toIndex,
            'field' => $fieldName,
            'newOrder' => $this->getOrderedFieldNames()
        ];
    }

    /**
     * 批量移动字段
     */
    public function moveFields(array $moves): array
    {
        // 保存历史记录
        $this->saveToHistory($this->fieldOrder);
        
        $results = [];
        foreach ($moves as $move) {
            try {
                $result = $this->moveField(
                    $move['field'],
                    $move['from'],
                    $move['to']
                );
                $results[] = $result;
            } catch (\Exception $e) {
                $results[] = [
                    'success' => false,
                    'error' => $e->getMessage(),
                    'field' => $move['field']
                ];
            }
        }
        
        return $results;
    }

    /**
     * 获取按顺序排列的字段名
     */
    public function getOrderedFieldNames(): array
    {
        asort($this->fieldOrder);
        return array_keys($this->fieldOrder);
    }

    /**
     * 获取字段的排序位置
     */
    public function getFieldPosition(string $fieldName): int
    {
        return $this->fieldOrder[$fieldName] ?? -1;
    }

    /**
     * 按排序重新排列字段数组
     */
    public function sortFields(array $fields): array
    {
        if (empty($this->fieldOrder)) {
            return $fields;
        }
        
        $sortedFields = [];
        $unsortedFields = [];
        
        // 分离已排序和未排序的字段
        foreach ($fields as $field) {
            $fieldName = $this->getFieldName($field);
            if (isset($this->fieldOrder[$fieldName])) {
                $sortedFields[$this->fieldOrder[$fieldName]] = $field;
            } else {
                $unsortedFields[] = $field;
            }
        }
        
        // 按索引排序
        ksort($sortedFields);
        
        // 合并未排序的字段到末尾
        return array_merge(array_values($sortedFields), $unsortedFields);
    }

    /**
     * 撤销上一次操作
     */
    public function undo(): bool
    {
        if (empty($this->sortHistory)) {
            return false;
        }
        
        $this->fieldOrder = array_pop($this->sortHistory);
        return true;
    }

    /**
     * 清除排序历史
     */
    public function clearHistory(): void
    {
        $this->sortHistory = [];
    }

    /**
     * 获取排序历史数量
     */
    public function getHistoryCount(): int
    {
        return count($this->sortHistory);
    }

    /**
     * 重置排序为默认顺序
     */
    public function resetToDefault(array $defaultFields): void
    {
        // 保存历史记录
        $this->saveToHistory($this->fieldOrder);
        
        $this->fieldOrder = [];
        foreach ($defaultFields as $index => $field) {
            $fieldName = $this->getFieldName($field);
            $this->fieldOrder[$fieldName] = $index;
        }
    }

    /**
     * 导出排序配置
     */
    public function exportConfig(): array
    {
        return [
            'fieldOrder' => $this->fieldOrder,
            'timestamp' => time(),
            'version' => '1.0'
        ];
    }

    /**
     * 导入排序配置
     */
    public function importConfig(array $config): bool
    {
        if (!isset($config['fieldOrder']) || !is_array($config['fieldOrder'])) {
            return false;
        }
        
        // 保存历史记录
        $this->saveToHistory($this->fieldOrder);
        
        $this->fieldOrder = $config['fieldOrder'];
        return true;
    }

    /**
     * 验证排序配置
     */
    public function validateOrder(array $fields): array
    {
        $errors = [];
        $fieldNames = array_map([$this, 'getFieldName'], $fields);
        
        // 检查是否有重复的排序索引
        $positions = array_values($this->fieldOrder);
        if (count($positions) !== count(array_unique($positions))) {
            $errors[] = '存在重复的排序索引';
        }
        
        // 检查是否有未知字段
        foreach (array_keys($this->fieldOrder) as $fieldName) {
            if (!in_array($fieldName, $fieldNames)) {
                $errors[] = "未知字段: {$fieldName}";
            }
        }
        
        return $errors;
    }

    /**
     * 保存到历史记录
     */
    protected function saveToHistory(array $order): void
    {
        if (!empty($order)) {
            $this->sortHistory[] = $order;
            
            // 限制历史记录大小
            if (count($this->sortHistory) > $this->maxHistorySize) {
                array_shift($this->sortHistory);
            }
        }
    }

    /**
     * 获取字段名称
     */
    protected function getFieldName($field): string
    {
        if ($field instanceof FieldInfo) {
            return $field->getName();
        } elseif (is_array($field) && isset($field['name'])) {
            return $field['name'];
        } elseif (is_string($field)) {
            return $field;
        }
        
        throw new \InvalidArgumentException('无法获取字段名称');
    }

    /**
     * 生成拖拽排序的JavaScript配置
     */
    public function generateJavaScriptConfig(): array
    {
        return [
            'dragConfig' => [
                'animation' => 150,
                'ghostClass' => 'sortable-ghost',
                'chosenClass' => 'sortable-chosen',
                'dragClass' => 'sortable-drag',
                'forceFallback' => false,
                'fallbackClass' => 'sortable-fallback',
                'fallbackOnBody' => true,
                'swapThreshold' => 0.65,
                'invertSwap' => false,
                'invertedSwapThreshold' => 0.1,
                'direction' => 'vertical'
            ],
            'fieldOrder' => $this->fieldOrder,
            'callbacks' => [
                'onStart' => 'handleDragStart',
                'onEnd' => 'handleDragEnd',
                'onMove' => 'handleDragMove',
                'onChange' => 'handleOrderChange'
            ]
        ];
    }
}
