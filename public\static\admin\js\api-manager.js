/**
 * CURD API 接口管理组件
 * 支持 API 接口分析、设计和代码生成
 */
define(['jquery', 'layui'], function($, layui) {
    'use strict';
    
    var ApiManager = function(options) {
        this.options = $.extend({
            apiUrl: '/admin/system/curd_generate_v2',
            container: '#api-manager-container',
            enableAutoAnalyze: true
        }, options);
        
        this.container = $(this.options.container);
        this.currentTable = null;
        this.endpoints = [];
        this.apiOptions = {
            enable_batch: true,
            enable_export: true,
            enable_import: false,
            enable_search: true,
            enable_statistics: true
        };
        
        this.httpMethods = {
            'GET': { color: '#52c41a', name: 'GET' },
            'POST': { color: '#1890ff', name: 'POST' },
            'PUT': { color: '#fa8c16', name: 'PUT' },
            'DELETE': { color: '#ff4d4f', name: 'DELETE' }
        };
        
        this.init();
    };
    
    ApiManager.prototype = {
        
        /**
         * 初始化 API 管理器
         */
        init: function() {
            this.bindEvents();
            this.initUI();
        },
        
        /**
         * 绑定事件
         */
        bindEvents: function() {
            var self = this;
            
            // 分析 API 接口
            $(document).on('click', '.analyze-api-btn', function() {
                self.analyzeApiEndpoints();
            });
            
            // 生成 API 代码
            $(document).on('click', '.generate-api-code-btn', function() {
                self.generateApiCode();
            });
            
            // 查看接口详情
            $(document).on('click', '.view-endpoint-detail', function() {
                var endpointIndex = $(this).data('index');
                self.showEndpointDetail(endpointIndex);
            });
            
            // 切换接口启用状态
            $(document).on('change', '.endpoint-enabled', function() {
                var endpointIndex = $(this).data('index');
                var enabled = $(this).prop('checked');
                self.toggleEndpoint(endpointIndex, enabled);
            });
            
            // API 选项变更
            $(document).on('change', '.api-option', function() {
                var option = $(this).data('option');
                var enabled = $(this).prop('checked');
                self.updateApiOption(option, enabled);
            });
            
            // 预览 OpenAPI 文档
            $(document).on('click', '.preview-openapi-btn', function() {
                self.previewOpenApiDoc();
            });
        },
        
        /**
         * 初始化UI
         */
        initUI: function() {
            if (this.container.length === 0) {
                return;
            }
            
            var html = `
                <div class="api-manager">
                    <div class="api-header">
                        <h3>API 接口设计</h3>
                        <div class="api-actions">
                            <button type="button" class="layui-btn layui-btn-sm analyze-api-btn">
                                <i class="layui-icon layui-icon-search"></i> 分析接口
                            </button>
                            <button type="button" class="layui-btn layui-btn-sm layui-btn-normal generate-api-code-btn">
                                <i class="layui-icon layui-icon-code"></i> 生成代码
                            </button>
                            <button type="button" class="layui-btn layui-btn-sm layui-btn-warm preview-openapi-btn">
                                <i class="layui-icon layui-icon-file"></i> 预览文档
                            </button>
                        </div>
                    </div>
                    <div class="api-content">
                        <div class="api-options" id="api-options"></div>
                        <div class="api-stats" id="api-stats"></div>
                        <div class="api-endpoints" id="api-endpoints"></div>
                    </div>
                </div>
            `;
            
            this.container.html(html);
            this.renderApiOptions();
        },
        
        /**
         * 渲染 API 选项
         */
        renderApiOptions: function() {
            var container = $('#api-options');
            var self = this;
            
            var html = `
                <div class="options-panel">
                    <h4>API 功能选项</h4>
                    <div class="options-grid">
                        <label class="layui-form-checkbox">
                            <input type="checkbox" class="api-option" data-option="enable_batch" ${this.apiOptions.enable_batch ? 'checked' : ''}>
                            <div class="layui-form-checkbox-text">批量操作</div>
                        </label>
                        <label class="layui-form-checkbox">
                            <input type="checkbox" class="api-option" data-option="enable_export" ${this.apiOptions.enable_export ? 'checked' : ''}>
                            <div class="layui-form-checkbox-text">数据导出</div>
                        </label>
                        <label class="layui-form-checkbox">
                            <input type="checkbox" class="api-option" data-option="enable_import" ${this.apiOptions.enable_import ? 'checked' : ''}>
                            <div class="layui-form-checkbox-text">数据导入</div>
                        </label>
                        <label class="layui-form-checkbox">
                            <input type="checkbox" class="api-option" data-option="enable_search" ${this.apiOptions.enable_search ? 'checked' : ''}>
                            <div class="layui-form-checkbox-text">搜索功能</div>
                        </label>
                        <label class="layui-form-checkbox">
                            <input type="checkbox" class="api-option" data-option="enable_statistics" ${this.apiOptions.enable_statistics ? 'checked' : ''}>
                            <div class="layui-form-checkbox-text">统计功能</div>
                        </label>
                    </div>
                </div>
            `;
            
            container.html(html);
            layui.form.render('checkbox');
        },
        
        /**
         * 分析 API 接口
         */
        analyzeApiEndpoints: function() {
            var self = this;
            
            if (!this.currentTable) {
                layer.msg('请先选择数据表');
                return;
            }
            
            var loading = layer.load(2, { content: '正在分析 API 接口...' });
            
            $.ajax({
                url: this.options.apiUrl,
                type: 'POST',
                data: {
                    action: 'analyze_api_endpoints',
                    table_name: this.currentTable,
                    options: this.apiOptions
                },
                success: function(response) {
                    layer.close(loading);
                    
                    if (response.code === 0) {
                        self.endpoints = response.data;
                        self.renderEndpoints();
                        self.updateStats();
                        layer.msg('API 接口分析完成', { icon: 1 });
                    } else {
                        layer.msg(response.msg || '分析失败');
                    }
                },
                error: function() {
                    layer.close(loading);
                    layer.msg('网络错误');
                }
            });
        },
        
        /**
         * 渲染接口列表
         */
        renderEndpoints: function() {
            var self = this;
            var container = $('#api-endpoints');
            
            if (this.endpoints.length === 0) {
                container.html('<div class="no-endpoints">未发现 API 接口</div>');
                return;
            }
            
            var html = '<div class="endpoints-list">';
            
            this.endpoints.forEach(function(endpoint, index) {
                var methodInfo = self.httpMethods[endpoint.method];
                var isRelationship = !!endpoint.relationship;
                
                html += `
                    <div class="endpoint-card ${isRelationship ? 'relationship-endpoint' : ''}" data-index="${index}">
                        <div class="endpoint-header">
                            <div class="endpoint-method">
                                <span class="method-badge" style="background-color: ${methodInfo.color}">${endpoint.method}</span>
                                <span class="endpoint-path">${endpoint.path}</span>
                            </div>
                            <div class="endpoint-actions">
                                <label class="layui-form-switch layui-form-switch-sm">
                                    <input type="checkbox" class="endpoint-enabled" data-index="${index}" 
                                           ${endpoint.enabled !== false ? 'checked' : ''}>
                                    <div class="layui-form-switch-text">启用|禁用</div>
                                </label>
                                <button type="button" class="layui-btn layui-btn-xs view-endpoint-detail" data-index="${index}">
                                    详情
                                </button>
                            </div>
                        </div>
                        <div class="endpoint-body">
                            <div class="endpoint-description">${endpoint.description}</div>
                            <div class="endpoint-info">
                                <span class="info-item">
                                    <i class="layui-icon layui-icon-username"></i>
                                    ${endpoint.controller_method}
                                </span>
                                ${isRelationship ? `<span class="info-item relationship-tag">关联接口</span>` : ''}
                            </div>
                        </div>
                    </div>
                `;
            });
            
            html += '</div>';
            container.html(html);
            
            // 重新渲染表单组件
            layui.form.render();
        },
        
        /**
         * 更新统计信息
         */
        updateStats: function() {
            var container = $('#api-stats');
            var stats = this.calculateStats();
            
            var html = `
                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-value">${stats.total}</div>
                        <div class="stat-label">总接口数</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">${stats.enabled}</div>
                        <div class="stat-label">已启用</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">${stats.crud}</div>
                        <div class="stat-label">CRUD接口</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">${stats.relationship}</div>
                        <div class="stat-label">关联接口</div>
                    </div>
                </div>
            `;
            
            container.html(html);
        },
        
        /**
         * 计算统计信息
         */
        calculateStats: function() {
            var total = this.endpoints.length;
            var enabled = this.endpoints.filter(e => e.enabled !== false).length;
            var crud = this.endpoints.filter(e => !e.relationship).length;
            var relationship = this.endpoints.filter(e => e.relationship).length;
            
            return {
                total: total,
                enabled: enabled,
                crud: crud,
                relationship: relationship
            };
        },
        
        /**
         * 显示接口详情
         */
        showEndpointDetail: function(endpointIndex) {
            var endpoint = this.endpoints[endpointIndex];
            if (!endpoint) return;
            
            var methodInfo = this.httpMethods[endpoint.method];
            
            var html = `
                <div class="endpoint-detail">
                    <div class="detail-header">
                        <h4>
                            <span class="method-badge" style="background-color: ${methodInfo.color}">${endpoint.method}</span>
                            ${endpoint.path}
                        </h4>
                        <div class="endpoint-name">${endpoint.name}</div>
                    </div>
                    <div class="detail-content">
                        <div class="detail-section">
                            <h5>基本信息</h5>
                            <table class="layui-table">
                                <tr><td>描述</td><td>${endpoint.description}</td></tr>
                                <tr><td>控制器方法</td><td>${endpoint.controller_method}</td></tr>
                                <tr><td>中间件</td><td>${endpoint.middleware ? endpoint.middleware.join(', ') : '无'}</td></tr>
                                ${endpoint.relationship ? `<tr><td>关联类型</td><td>${endpoint.relationship.type}</td></tr>` : ''}
                            </table>
                        </div>
                        ${this.renderParametersSection(endpoint)}
                        ${this.renderResponsesSection(endpoint)}
                        ${this.renderValidationSection(endpoint)}
                    </div>
                </div>
            `;
            
            layer.open({
                type: 1,
                title: '接口详情',
                content: html,
                area: ['900px', '700px'],
                maxmin: true
            });
        },
        
        /**
         * 渲染参数部分
         */
        renderParametersSection: function(endpoint) {
            if (!endpoint.parameters || Object.keys(endpoint.parameters).length === 0) {
                return '';
            }
            
            var html = '<div class="detail-section"><h5>请求参数</h5><table class="layui-table">';
            html += '<thead><tr><th>参数名</th><th>类型</th><th>必填</th><th>描述</th></tr></thead><tbody>';
            
            for (var name in endpoint.parameters) {
                var param = endpoint.parameters[name];
                html += `
                    <tr>
                        <td>${name}</td>
                        <td>${param.type}</td>
                        <td>${param.required ? '是' : '否'}</td>
                        <td>${param.description || '-'}</td>
                    </tr>
                `;
            }
            
            html += '</tbody></table></div>';
            return html;
        },
        
        /**
         * 渲染响应部分
         */
        renderResponsesSection: function(endpoint) {
            if (!endpoint.responses || Object.keys(endpoint.responses).length === 0) {
                return '';
            }
            
            var html = '<div class="detail-section"><h5>响应状态</h5><table class="layui-table">';
            html += '<thead><tr><th>状态码</th><th>描述</th></tr></thead><tbody>';
            
            for (var code in endpoint.responses) {
                var response = endpoint.responses[code];
                html += `
                    <tr>
                        <td>${code}</td>
                        <td>${response.description || '-'}</td>
                    </tr>
                `;
            }
            
            html += '</tbody></table></div>';
            return html;
        },
        
        /**
         * 渲染验证部分
         */
        renderValidationSection: function(endpoint) {
            if (!endpoint.validation || Object.keys(endpoint.validation).length === 0) {
                return '';
            }
            
            var html = '<div class="detail-section"><h5>验证规则</h5><table class="layui-table">';
            html += '<thead><tr><th>字段</th><th>规则</th></tr></thead><tbody>';
            
            for (var field in endpoint.validation) {
                var rules = endpoint.validation[field];
                html += `
                    <tr>
                        <td>${field}</td>
                        <td>${rules}</td>
                    </tr>
                `;
            }
            
            html += '</tbody></table></div>';
            return html;
        },
        
        /**
         * 切换接口启用状态
         */
        toggleEndpoint: function(endpointIndex, enabled) {
            if (this.endpoints[endpointIndex]) {
                this.endpoints[endpointIndex].enabled = enabled;
                this.updateStats();
                
                // 触发接口变更事件
                if (window.onApiEndpointChange) {
                    window.onApiEndpointChange(this.endpoints);
                }
            }
        },
        
        /**
         * 更新 API 选项
         */
        updateApiOption: function(option, enabled) {
            this.apiOptions[option] = enabled;
            
            // 如果已经分析过接口，重新分析
            if (this.endpoints.length > 0) {
                this.analyzeApiEndpoints();
            }
        },
        
        /**
         * 生成 API 代码
         */
        generateApiCode: function() {
            var self = this;
            
            if (!this.currentTable) {
                layer.msg('请先选择数据表');
                return;
            }
            
            if (this.endpoints.length === 0) {
                layer.msg('请先分析 API 接口');
                return;
            }
            
            var loading = layer.load(2, { content: '正在生成 API 代码...' });
            
            $.ajax({
                url: this.options.apiUrl,
                type: 'POST',
                data: {
                    action: 'generate_api_code',
                    table_name: this.currentTable,
                    options: this.apiOptions
                },
                success: function(response) {
                    layer.close(loading);
                    
                    if (response.code === 0) {
                        self.showGeneratedApiCode(response.data);
                        layer.msg('API 代码生成成功', { icon: 1 });
                    } else {
                        layer.msg(response.msg || '生成失败');
                    }
                },
                error: function() {
                    layer.close(loading);
                    layer.msg('网络错误');
                }
            });
        },
        
        /**
         * 显示生成的 API 代码
         */
        showGeneratedApiCode: function(apiCode) {
            var html = `
                <div class="generated-api-code">
                    <div class="layui-tab layui-tab-brief">
                        <ul class="layui-tab-title">
                            <li class="layui-this">控制器代码</li>
                            <li>路由配置</li>
                            <li>OpenAPI 文档</li>
                        </ul>
                        <div class="layui-tab-content">
                            <div class="layui-tab-item layui-show">
                                <pre><code class="language-php">${this.escapeHtml(apiCode.controller)}</code></pre>
                            </div>
                            <div class="layui-tab-item">
                                <pre><code class="language-php">${this.escapeHtml(apiCode.routes)}</code></pre>
                            </div>
                            <div class="layui-tab-item">
                                <pre><code class="language-json">${JSON.stringify(apiCode.openapi_doc, null, 2)}</code></pre>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            
            layer.open({
                type: 1,
                title: '生成的 API 代码',
                content: html,
                area: ['1000px', '800px'],
                maxmin: true,
                success: function() {
                    layui.element.render('tab');
                }
            });
        },
        
        /**
         * 预览 OpenAPI 文档
         */
        previewOpenApiDoc: function() {
            var self = this;
            
            if (this.endpoints.length === 0) {
                layer.msg('请先分析 API 接口');
                return;
            }
            
            var loading = layer.load(2, { content: '正在生成文档...' });
            
            $.ajax({
                url: this.options.apiUrl,
                type: 'POST',
                data: {
                    action: 'generate_api_code',
                    table_name: this.currentTable,
                    options: this.apiOptions
                },
                success: function(response) {
                    layer.close(loading);
                    
                    if (response.code === 0) {
                        self.showOpenApiDoc(response.data.openapi_doc);
                    } else {
                        layer.msg(response.msg || '生成失败');
                    }
                },
                error: function() {
                    layer.close(loading);
                    layer.msg('网络错误');
                }
            });
        },
        
        /**
         * 显示 OpenAPI 文档
         */
        showOpenApiDoc: function(openApiDoc) {
            var html = `
                <div class="openapi-doc">
                    <div class="doc-header">
                        <h3>${openApiDoc.info.title}</h3>
                        <p>${openApiDoc.info.description}</p>
                        <div class="doc-info">
                            <span>版本: ${openApiDoc.info.version}</span>
                            <span>接口数量: ${Object.keys(openApiDoc.paths).length}</span>
                        </div>
                    </div>
                    <div class="doc-content">
                        <pre><code class="language-json">${JSON.stringify(openApiDoc, null, 2)}</code></pre>
                    </div>
                </div>
            `;
            
            layer.open({
                type: 1,
                title: 'OpenAPI 文档预览',
                content: html,
                area: ['1000px', '800px'],
                maxmin: true
            });
        },
        
        /**
         * 转义 HTML
         */
        escapeHtml: function(text) {
            var map = {
                '&': '&amp;',
                '<': '&lt;',
                '>': '&gt;',
                '"': '&quot;',
                "'": '&#039;'
            };
            
            return text.replace(/[&<>"']/g, function(m) { return map[m]; });
        },
        
        /**
         * 设置当前表
         */
        setCurrentTable: function(tableName) {
            this.currentTable = tableName;
            
            if (this.options.enableAutoAnalyze && tableName) {
                this.analyzeApiEndpoints();
            }
        },
        
        /**
         * 获取当前接口
         */
        getEndpoints: function() {
            return this.endpoints.filter(e => e.enabled !== false);
        },
        
        /**
         * 销毁组件
         */
        destroy: function() {
            this.container.empty();
            $(document).off('.api-manager');
        }
    };
    
    return ApiManager;
});
