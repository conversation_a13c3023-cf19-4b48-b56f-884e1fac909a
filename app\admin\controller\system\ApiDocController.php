<?php

namespace app\admin\controller\system;

use support\Request;
use support\Response;

/**
 * API文档控制器
 * 展示和管理生成的API文档
 */
class ApiDocController
{
    /**
     * 返回JSON响应
     */
    protected function json(array $data, int $status = 200): Response
    {
        return response(json_encode($data), $status, ['Content-Type' => 'application/json']);
    }

    /**
     * 返回错误响应
     */
    protected function error(string $message, int $code = 400): Response
    {
        return $this->json(['code' => $code, 'msg' => $message]);
    }

    /**
     * API文档首页
     */
    public function index(Request $request): Response
    {
        // 获取所有已生成的API文档
        $apiDocs = $this->getGeneratedApiDocs();

        // 获取API统计信息
        $apiStats = $this->getApiStatistics($apiDocs);

        return response(view('admin/system/apidoc/index', [
            'api_docs' => $apiDocs,
            'api_stats' => $apiStats,
            'page_title' => 'API文档管理',
            'breadcrumb' => [
                ['title' => '系统管理', 'url' => ''],
                ['title' => 'API文档管理', 'url' => '']
            ]
        ]));
    }

    /**
     * 查看API文档详情
     */
    public function view(Request $request): Response
    {
        $tableName = $request->get('table', '');

        if (empty($tableName)) {
            return $this->error('表名不能为空');
        }

        // 获取表信息
        $tableInfo = $this->getTableInfo($tableName);

        // 分析API需求
        $apiAnalysis = $this->apiAnalyzer->analyzeApiRequirements($tableInfo);

        // 生成API文档
        $apiDoc = $this->generateApiDocumentation($tableInfo, $apiAnalysis);

        return view('admin/system/apidoc/view', [
            'table_name' => $tableName,
            'table_info' => $tableInfo,
            'api_analysis' => $apiAnalysis,
            'api_doc' => $apiDoc,
            'page_title' => "API文档 - {$tableName}",
            'breadcrumb' => [
                ['title' => '系统管理', 'url' => ''],
                ['title' => 'API文档管理', 'url' => '/admin/system/apidoc'],
                ['title' => "API文档 - {$tableName}", 'url' => '']
            ]
        ]);
    }

    /**
     * 生成API文档
     */
    public function generate(Request $request): Response
    {
        $tableName = $request->post('table_name', '');
        $options = $request->post('options', []);

        if (empty($tableName)) {
            return $this->json(['code' => 400, 'msg' => '表名不能为空']);
        }

        try {
            // 获取表信息
            $tableInfo = $this->getTableInfo($tableName);

            // 分析API需求
            $apiAnalysis = $this->apiAnalyzer->analyzeApiRequirements($tableInfo, $options);

            // 生成API代码和文档
            $result = $this->curdGenerator->generateApiCode($apiAnalysis, $tableInfo, $options);

            // 保存API文档
            $this->saveApiDocumentation($tableName, $result);

            return $this->json([
                'code' => 200,
                'msg' => 'API文档生成成功',
                'data' => [
                    'table_name' => $tableName,
                    'api_count' => count($result['api_endpoints'] ?? []),
                    'doc_size' => strlen($result['api_documentation'] ?? ''),
                    'generated_at' => date('Y-m-d H:i:s')
                ]
            ]);

        } catch (\Exception $e) {
            return $this->json(['code' => 500, 'msg' => '生成失败：' . $e->getMessage()]);
        }
    }

    /**
     * 导出API文档
     */
    public function export(Request $request): Response
    {
        $tableName = $request->get('table', '');
        $format = $request->get('format', 'html'); // html, markdown, json, pdf

        if (empty($tableName)) {
            return $this->error('表名不能为空');
        }

        try {
            // 获取API文档
            $apiDoc = $this->getApiDocumentation($tableName);

            if (empty($apiDoc)) {
                return $this->error('API文档不存在，请先生成');
            }

            // 根据格式导出
            switch ($format) {
                case 'markdown':
                    return $this->exportMarkdown($tableName, $apiDoc);
                case 'json':
                    return $this->exportJson($tableName, $apiDoc);
                case 'pdf':
                    return $this->exportPdf($tableName, $apiDoc);
                default:
                    return $this->exportHtml($tableName, $apiDoc);
            }

        } catch (\Exception $e) {
            return $this->error('导出失败：' . $e->getMessage());
        }
    }

    /**
     * 测试API接口
     */
    public function test(Request $request): Response
    {
        $tableName = $request->post('table_name', '');
        $endpoint = $request->post('endpoint', '');
        $method = $request->post('method', 'GET');
        $params = $request->post('params', []);

        if (empty($tableName) || empty($endpoint)) {
            return $this->json(['code' => 400, 'msg' => '参数不完整']);
        }

        try {
            // 执行API测试
            $testResult = $this->executeApiTest($tableName, $endpoint, $method, $params);

            return $this->json([
                'code' => 200,
                'msg' => 'API测试完成',
                'data' => $testResult
            ]);

        } catch (\Exception $e) {
            return $this->json(['code' => 500, 'msg' => '测试失败：' . $e->getMessage()]);
        }
    }

    /**
     * 获取已生成的API文档列表
     */
    protected function getGeneratedApiDocs(): array
    {
        // 这里应该从数据库或文件系统获取已生成的API文档
        // 暂时返回示例数据
        return [
            [
                'table_name' => 'users',
                'display_name' => '用户管理',
                'api_count' => 5,
                'doc_size' => '15.2KB',
                'generated_at' => '2025-01-20 10:30:00',
                'status' => 'active'
            ],
            [
                'table_name' => 'articles',
                'display_name' => '文章管理',
                'api_count' => 7,
                'doc_size' => '22.8KB',
                'generated_at' => '2025-01-20 09:15:00',
                'status' => 'active'
            ],
            [
                'table_name' => 'categories',
                'display_name' => '分类管理',
                'api_count' => 4,
                'doc_size' => '8.5KB',
                'generated_at' => '2025-01-19 16:45:00',
                'status' => 'active'
            ]
        ];
    }

    /**
     * 获取API统计信息
     */
    protected function getApiStatistics(array $apiDocs): array
    {
        $totalApis = array_sum(array_column($apiDocs, 'api_count'));
        $totalTables = count($apiDocs);
        $totalSize = 0;

        foreach ($apiDocs as $doc) {
            $size = floatval(str_replace('KB', '', $doc['doc_size']));
            $totalSize += $size;
        }

        return [
            'total_tables' => $totalTables,
            'total_apis' => $totalApis,
            'total_size' => round($totalSize, 1) . 'KB',
            'avg_apis_per_table' => $totalTables > 0 ? round($totalApis / $totalTables, 1) : 0,
            'last_generated' => $apiDocs ? max(array_column($apiDocs, 'generated_at')) : '无'
        ];
    }

    /**
     * 获取表信息
     */
    protected function getTableInfo(string $tableName): array
    {
        // 这里应该从数据库获取真实的表信息
        // 暂时返回示例数据
        return [
            'name' => $tableName,
            'comment' => '示例表',
            'fields' => [
                ['name' => 'id', 'type' => 'int', 'comment' => '主键ID'],
                ['name' => 'name', 'type' => 'varchar', 'comment' => '名称'],
                ['name' => 'status', 'type' => 'tinyint', 'comment' => '状态'],
                ['name' => 'created_at', 'type' => 'timestamp', 'comment' => '创建时间'],
                ['name' => 'updated_at', 'type' => 'timestamp', 'comment' => '更新时间']
            ]
        ];
    }

    /**
     * 生成API文档
     */
    protected function generateApiDocumentation(array $tableInfo, array $apiAnalysis): array
    {
        $tableName = $tableInfo['name'];
        $modelName = $this->getModelName($tableName);

        return [
            'title' => "{$modelName} API 文档",
            'description' => "基于 {$tableName} 表自动生成的 RESTful API 接口文档",
            'version' => '1.0.0',
            'base_url' => '/api',
            'endpoints' => [
                [
                    'method' => 'GET',
                    'path' => "/{$tableName}",
                    'summary' => "获取{$modelName}列表",
                    'description' => "分页获取{$modelName}数据列表",
                    'parameters' => [
                        ['name' => 'page', 'type' => 'integer', 'description' => '页码', 'default' => 1],
                        ['name' => 'limit', 'type' => 'integer', 'description' => '每页数量', 'default' => 20],
                        ['name' => 'search', 'type' => 'string', 'description' => '搜索关键词', 'required' => false]
                    ],
                    'responses' => [
                        '200' => [
                            'description' => '成功',
                            'example' => [
                                'code' => 200,
                                'msg' => 'success',
                                'data' => [
                                    'list' => [],
                                    'total' => 0,
                                    'page' => 1,
                                    'limit' => 20
                                ]
                            ]
                        ]
                    ]
                ],
                [
                    'method' => 'GET',
                    'path' => "/{$tableName}/{id}",
                    'summary' => "获取{$modelName}详情",
                    'description' => "根据ID获取单个{$modelName}的详细信息",
                    'parameters' => [
                        ['name' => 'id', 'type' => 'integer', 'description' => '记录ID', 'required' => true]
                    ],
                    'responses' => [
                        '200' => [
                            'description' => '成功',
                            'example' => [
                                'code' => 200,
                                'msg' => 'success',
                                'data' => []
                            ]
                        ],
                        '404' => [
                            'description' => '记录不存在',
                            'example' => [
                                'code' => 404,
                                'msg' => '记录不存在'
                            ]
                        ]
                    ]
                ],
                [
                    'method' => 'POST',
                    'path' => "/{$tableName}",
                    'summary' => "创建{$modelName}",
                    'description' => "创建新的{$modelName}记录",
                    'parameters' => $this->generateCreateParameters($tableInfo['fields']),
                    'responses' => [
                        '201' => [
                            'description' => '创建成功',
                            'example' => [
                                'code' => 201,
                                'msg' => '创建成功',
                                'data' => ['id' => 1]
                            ]
                        ],
                        '400' => [
                            'description' => '参数错误',
                            'example' => [
                                'code' => 400,
                                'msg' => '参数验证失败'
                            ]
                        ]
                    ]
                ],
                [
                    'method' => 'PUT',
                    'path' => "/{$tableName}/{id}",
                    'summary' => "更新{$modelName}",
                    'description' => "根据ID更新{$modelName}记录",
                    'parameters' => array_merge(
                        [['name' => 'id', 'type' => 'integer', 'description' => '记录ID', 'required' => true]],
                        $this->generateUpdateParameters($tableInfo['fields'])
                    ),
                    'responses' => [
                        '200' => [
                            'description' => '更新成功',
                            'example' => [
                                'code' => 200,
                                'msg' => '更新成功'
                            ]
                        ]
                    ]
                ],
                [
                    'method' => 'DELETE',
                    'path' => "/{$tableName}/{id}",
                    'summary' => "删除{$modelName}",
                    'description' => "根据ID删除{$modelName}记录",
                    'parameters' => [
                        ['name' => 'id', 'type' => 'integer', 'description' => '记录ID', 'required' => true]
                    ],
                    'responses' => [
                        '200' => [
                            'description' => '删除成功',
                            'example' => [
                                'code' => 200,
                                'msg' => '删除成功'
                            ]
                        ]
                    ]
                ]
            ]
        ];
    }

    /**
     * 生成创建参数
     */
    protected function generateCreateParameters(array $fields): array
    {
        $parameters = [];

        foreach ($fields as $field) {
            if (in_array($field['name'], ['id', 'created_at', 'updated_at'])) {
                continue;
            }

            $parameters[] = [
                'name' => $field['name'],
                'type' => $this->mapFieldType($field['type']),
                'description' => $field['comment'] ?: $field['name'],
                'required' => !in_array($field['name'], ['status', 'sort', 'remark'])
            ];
        }

        return $parameters;
    }

    /**
     * 生成更新参数
     */
    protected function generateUpdateParameters(array $fields): array
    {
        $parameters = [];

        foreach ($fields as $field) {
            if (in_array($field['name'], ['id', 'created_at', 'updated_at'])) {
                continue;
            }

            $parameters[] = [
                'name' => $field['name'],
                'type' => $this->mapFieldType($field['type']),
                'description' => $field['comment'] ?: $field['name'],
                'required' => false
            ];
        }

        return $parameters;
    }

    /**
     * 映射字段类型
     */
    protected function mapFieldType(string $dbType): string
    {
        $typeMap = [
            'int' => 'integer',
            'bigint' => 'integer',
            'tinyint' => 'integer',
            'varchar' => 'string',
            'text' => 'string',
            'longtext' => 'string',
            'decimal' => 'number',
            'float' => 'number',
            'double' => 'number',
            'timestamp' => 'string',
            'datetime' => 'string',
            'date' => 'string',
            'json' => 'object'
        ];

        return $typeMap[$dbType] ?? 'string';
    }

    /**
     * 获取模型名称
     */
    protected function getModelName(string $tableName): string
    {
        $name = preg_replace('/^[a-z]+_/', '', $tableName);
        return str_replace(' ', '', ucwords(str_replace('_', ' ', $name)));
    }

    /**
     * 保存API文档
     */
    protected function saveApiDocumentation(string $tableName, array $result): void
    {
        // 这里应该保存到数据库或文件系统
        // 暂时省略实现
    }

    /**
     * 获取API文档
     */
    protected function getApiDocumentation(string $tableName): array
    {
        // 这里应该从数据库或文件系统获取
        // 暂时返回空数组
        return [];
    }

    /**
     * 导出HTML格式
     */
    protected function exportHtml(string $tableName, array $apiDoc): Response
    {
        $html = view('admin/system/apidoc/export_html', ['api_doc' => $apiDoc])->getContent();

        return response($html, 200, [
            'Content-Type' => 'text/html',
            'Content-Disposition' => "attachment; filename=\"{$tableName}_api_doc.html\""
        ]);
    }

    /**
     * 导出Markdown格式
     */
    protected function exportMarkdown(string $tableName, array $apiDoc): Response
    {
        $markdown = $this->generateMarkdownDoc($apiDoc);

        return response($markdown, 200, [
            'Content-Type' => 'text/markdown',
            'Content-Disposition' => "attachment; filename=\"{$tableName}_api_doc.md\""
        ]);
    }

    /**
     * 导出JSON格式
     */
    protected function exportJson(string $tableName, array $apiDoc): Response
    {
        return response(json_encode($apiDoc, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE), 200, [
            'Content-Type' => 'application/json',
            'Content-Disposition' => "attachment; filename=\"{$tableName}_api_doc.json\""
        ]);
    }

    /**
     * 导出PDF格式
     */
    protected function exportPdf(string $tableName, array $apiDoc): Response
    {
        // 这里需要集成PDF生成库，如TCPDF或DomPDF
        // 暂时返回错误
        throw new \Exception('PDF导出功能暂未实现');
    }

    /**
     * 生成Markdown文档
     */
    protected function generateMarkdownDoc(array $apiDoc): string
    {
        $markdown = "# {$apiDoc['title']}\n\n";
        $markdown .= "{$apiDoc['description']}\n\n";
        $markdown .= "**版本**: {$apiDoc['version']}\n";
        $markdown .= "**基础URL**: {$apiDoc['base_url']}\n\n";

        $markdown .= "## 接口列表\n\n";

        foreach ($apiDoc['endpoints'] as $endpoint) {
            $markdown .= "### {$endpoint['method']} {$endpoint['path']}\n\n";
            $markdown .= "**描述**: {$endpoint['description']}\n\n";

            if (!empty($endpoint['parameters'])) {
                $markdown .= "**参数**:\n\n";
                $markdown .= "| 参数名 | 类型 | 必填 | 描述 |\n";
                $markdown .= "|--------|------|------|------|\n";

                foreach ($endpoint['parameters'] as $param) {
                    $required = isset($param['required']) && $param['required'] ? '是' : '否';
                    $markdown .= "| {$param['name']} | {$param['type']} | {$required} | {$param['description']} |\n";
                }
                $markdown .= "\n";
            }

            $markdown .= "**响应示例**:\n\n";
            foreach ($endpoint['responses'] as $code => $response) {
                $markdown .= "**{$code}**: {$response['description']}\n\n";
                $markdown .= "```json\n";
                $markdown .= json_encode($response['example'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
                $markdown .= "\n```\n\n";
            }
        }

        return $markdown;
    }

    /**
     * 执行API测试
     */
    protected function executeApiTest(string $tableName, string $endpoint, string $method, array $params): array
    {
        // 这里应该实际执行API请求测试
        // 暂时返回模拟结果
        return [
            'status' => 'success',
            'response_time' => '125ms',
            'status_code' => 200,
            'response_body' => [
                'code' => 200,
                'msg' => 'success',
                'data' => []
            ],
            'headers' => [
                'Content-Type' => 'application/json',
                'X-Response-Time' => '125ms'
            ]
        ];
    }

    /**
     * 获取API文档列表 (AJAX)
     */
    public function getApiDocList(Request $request): Response
    {
        try {
            $apiDocs = $this->getGeneratedApiDocs();
            $statistics = $this->getApiStatistics($apiDocs);

            return $this->json([
                'code' => 200,
                'msg' => 'success',
                'data' => $apiDocs,
                'statistics' => $statistics
            ]);
        } catch (\Exception $e) {
            return $this->json(['code' => 500, 'msg' => '获取列表失败：' . $e->getMessage()]);
        }
    }

    /**
     * 获取API接口列表 (AJAX)
     */
    public function getApiEndpoints(Request $request): Response
    {
        $tableName = $request->get('table', '');

        if (empty($tableName)) {
            return $this->json(['code' => 400, 'msg' => '表名不能为空']);
        }

        try {
            // 获取表信息
            $tableInfo = $this->getTableInfo($tableName);

            // 分析API需求
            $apiAnalysis = $this->apiAnalyzer->analyzeApiRequirements($tableInfo);

            // 生成API文档
            $apiDoc = $this->generateApiDocumentation($tableInfo, $apiAnalysis);

            return $this->json([
                'code' => 200,
                'msg' => 'success',
                'data' => $apiDoc['endpoints']
            ]);
        } catch (\Exception $e) {
            return $this->json(['code' => 500, 'msg' => '获取接口列表失败：' . $e->getMessage()]);
        }
    }

    /**
     * 获取表列表 (AJAX)
     */
    public function getTableList(Request $request): Response
    {
        try {
            // 这里应该从数据库获取真实的表列表
            // 暂时返回示例数据
            $tables = [
                ['name' => 'users', 'comment' => '用户表'],
                ['name' => 'articles', 'comment' => '文章表'],
                ['name' => 'categories', 'comment' => '分类表'],
                ['name' => 'tags', 'comment' => '标签表'],
                ['name' => 'comments', 'comment' => '评论表'],
                ['name' => 'orders', 'comment' => '订单表'],
                ['name' => 'products', 'comment' => '产品表'],
                ['name' => 'settings', 'comment' => '设置表']
            ];

            return $this->json([
                'code' => 200,
                'msg' => 'success',
                'data' => $tables
            ]);
        } catch (\Exception $e) {
            return $this->json(['code' => 500, 'msg' => '获取表列表失败：' . $e->getMessage()]);
        }
    }

    /**
     * 删除API文档
     */
    public function delete(Request $request): Response
    {
        $tableName = $request->get('table', '');

        if (empty($tableName)) {
            return $this->json(['code' => 400, 'msg' => '表名不能为空']);
        }

        try {
            // 这里应该实际删除API文档
            // 暂时返回成功

            return $this->json([
                'code' => 200,
                'msg' => 'API文档删除成功'
            ]);
        } catch (\Exception $e) {
            return $this->json(['code' => 500, 'msg' => '删除失败：' . $e->getMessage()]);
        }
    }

    /**
     * 批量删除API文档
     */
    public function batchDelete(Request $request): Response
    {
        $tables = $request->post('tables', []);

        if (empty($tables)) {
            return $this->json(['code' => 400, 'msg' => '请选择要删除的表']);
        }

        try {
            // 这里应该实际批量删除API文档
            // 暂时返回成功

            return $this->json([
                'code' => 200,
                'msg' => '批量删除成功',
                'data' => ['deleted_count' => count($tables)]
            ]);
        } catch (\Exception $e) {
            return $this->json(['code' => 500, 'msg' => '批量删除失败：' . $e->getMessage()]);
        }
    }

    /**
     * 批量导出API文档
     */
    public function batchExport(Request $request): Response
    {
        $tables = $request->post('tables', []);
        $format = $request->post('format', 'html');

        if (empty($tables)) {
            return $this->json(['code' => 400, 'msg' => '请选择要导出的表']);
        }

        try {
            // 这里应该实际批量导出API文档
            // 暂时返回下载链接

            $downloadUrl = "/downloads/api-docs-batch-{$format}-" . date('YmdHis') . ".zip";

            return $this->json([
                'code' => 200,
                'msg' => '批量导出成功',
                'data' => [
                    'download_url' => $downloadUrl,
                    'exported_count' => count($tables)
                ]
            ]);
        } catch (\Exception $e) {
            return $this->json(['code' => 500, 'msg' => '批量导出失败：' . $e->getMessage()]);
        }
    }

    /**
     * 获取API文档统计信息
     */
    public function getStatistics(Request $request): Response
    {
        try {
            $apiDocs = $this->getGeneratedApiDocs();
            $statistics = $this->getApiStatistics($apiDocs);

            // 添加更多统计信息
            $statistics['recent_activity'] = [
                ['action' => '生成API文档', 'table' => 'users', 'time' => '2分钟前'],
                ['action' => '测试API接口', 'table' => 'articles', 'time' => '5分钟前'],
                ['action' => '导出文档', 'table' => 'categories', 'time' => '10分钟前']
            ];

            $statistics['popular_tables'] = [
                ['table' => 'users', 'views' => 156],
                ['table' => 'articles', 'views' => 89],
                ['table' => 'orders', 'views' => 67]
            ];

            return $this->json([
                'code' => 200,
                'msg' => 'success',
                'data' => $statistics
            ]);
        } catch (\Exception $e) {
            return $this->json(['code' => 500, 'msg' => '获取统计信息失败：' . $e->getMessage()]);
        }
    }

    /**
     * 搜索API文档
     */
    public function search(Request $request): Response
    {
        $keyword = $request->get('keyword', '');

        if (empty($keyword)) {
            return $this->getApiDocList($request);
        }

        try {
            $allDocs = $this->getGeneratedApiDocs();
            $filteredDocs = array_filter($allDocs, function($doc) use ($keyword) {
                return stripos($doc['table_name'], $keyword) !== false ||
                       stripos($doc['display_name'], $keyword) !== false;
            });

            return $this->json([
                'code' => 200,
                'msg' => 'success',
                'data' => array_values($filteredDocs)
            ]);
        } catch (\Exception $e) {
            return $this->json(['code' => 500, 'msg' => '搜索失败：' . $e->getMessage()]);
        }
    }
}
