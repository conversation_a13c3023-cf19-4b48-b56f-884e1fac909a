<?php
/**
 * 部署验证脚本 - 确认多数据库 CURD 生成器完全就绪
 */

echo "=== 多数据库 CURD 生成器部署验证 ===\n\n";

$checks = [];
$warnings = [];
$errors = [];

// 检查 1: 核心文件完整性
echo "🔍 检查 1: 核心文件完整性\n";
$coreFiles = [
    'app/admin/controller/system/CurdGenerateV2Controller.php' => 'CURD生成器控制器',
    'app/admin/controller/system/DatabaseTestController.php' => '数据库测试控制器',
    'app/admin/view/admin/system/curdgeneratev2/index.blade.php' => 'CURD生成器视图',
    'app/admin/view/admin/system/databasetest/index.blade.php' => '数据库测试视图',
    'app/common/services/curd/v2/CurdGenerator.php' => 'CURD生成器服务',
    'app/common/services/curd/v2/analyzers/TableAnalyzer.php' => '表分析器',
    'config/database.php' => '数据库配置',
    'config/route.php' => '路由配置',
];

foreach ($coreFiles as $file => $desc) {
    if (file_exists($file)) {
        echo "   ✅ {$desc}\n";
        $checks[] = "文件存在: {$desc}";
    } else {
        echo "   ❌ {$desc} - 文件缺失\n";
        $errors[] = "文件缺失: {$desc}";
    }
}

// 检查 2: 数据库配置验证
echo "\n🔍 检查 2: 数据库配置验证\n";
if (file_exists('config/database.php')) {
    $dbConfig = include 'config/database.php';
    $requiredConnections = [
        'mysql' => '默认连接',
        'mysql_read' => '读库连接',
        'mysql_second' => '第二数据库',
        'mysql_log' => '日志数据库',
        'mysql_cache' => '缓存数据库',
        'mysql_without_prefix' => '无前缀连接',
        'pgsql' => 'PostgreSQL',
        'sqlite' => 'SQLite',
        'sqlsrv' => 'SQL Server',
    ];
    
    foreach ($requiredConnections as $conn => $desc) {
        if (isset($dbConfig['connections'][$conn])) {
            echo "   ✅ {$desc} ({$conn})\n";
            $checks[] = "数据库配置: {$desc}";
        } else {
            echo "   ⚠️  {$desc} ({$conn}) - 配置缺失\n";
            $warnings[] = "数据库配置缺失: {$desc}";
        }
    }
} else {
    echo "   ❌ 数据库配置文件不存在\n";
    $errors[] = "数据库配置文件不存在";
}

// 检查 3: 路由配置验证
echo "\n🔍 检查 3: 路由配置验证\n";
if (file_exists('config/route.php')) {
    $routeContent = file_get_contents('config/route.php');
    $requiredRoutes = [
        '/admin/system/curdgeneratev2' => 'CURD生成器路由',
        '/admin/system/databasetest' => '数据库测试路由',
    ];
    
    foreach ($requiredRoutes as $route => $desc) {
        if (strpos($routeContent, $route) !== false) {
            echo "   ✅ {$desc}\n";
            $checks[] = "路由配置: {$desc}";
        } else {
            echo "   ❌ {$desc} - 路由缺失\n";
            $errors[] = "路由缺失: {$desc}";
        }
    }
} else {
    echo "   ❌ 路由配置文件不存在\n";
    $errors[] = "路由配置文件不存在";
}

// 检查 4: 服务类方法验证
echo "\n🔍 检查 4: 服务类方法验证\n";

// 检查 CurdGenerator
if (file_exists('app/common/services/curd/v2/CurdGenerator.php')) {
    $generatorContent = file_get_contents('app/common/services/curd/v2/CurdGenerator.php');
    $generatorMethods = [
        'getAllTables(string $connection = \'mysql\')' => 'getAllTables方法支持连接参数',
        'validateTable(string $tableName, string $tablePrefix = \'\', string $connection = \'mysql\')' => 'validateTable方法支持连接参数',
        'getTableInfo(string $tableName, string $tablePrefix = \'\', string $connection = \'mysql\')' => 'getTableInfo方法支持连接参数',
    ];
    
    foreach ($generatorMethods as $method => $desc) {
        if (strpos($generatorContent, $method) !== false) {
            echo "   ✅ {$desc}\n";
            $checks[] = "CurdGenerator: {$desc}";
        } else {
            echo "   ❌ {$desc}\n";
            $errors[] = "CurdGenerator: {$desc}";
        }
    }
}

// 检查 TableAnalyzer
if (file_exists('app/common/services/curd/v2/analyzers/TableAnalyzer.php')) {
    $analyzerContent = file_get_contents('app/common/services/curd/v2/analyzers/TableAnalyzer.php');
    $analyzerMethods = [
        'analyze(string $tableName, string $tablePrefix = \'\', string $connection = \'mysql\')' => 'analyze方法支持连接参数',
        'tableExists(string $tableName, string $tablePrefix = \'\', string $connection = \'mysql\')' => 'tableExists方法支持连接参数',
        'getAllTables(string $connection = \'mysql\')' => 'getAllTables方法支持连接参数',
    ];
    
    foreach ($analyzerMethods as $method => $desc) {
        if (strpos($analyzerContent, $method) !== false) {
            echo "   ✅ {$desc}\n";
            $checks[] = "TableAnalyzer: {$desc}";
        } else {
            echo "   ❌ {$desc}\n";
            $errors[] = "TableAnalyzer: {$desc}";
        }
    }
}

// 检查 5: 前端界面验证
echo "\n🔍 检查 5: 前端界面验证\n";
if (file_exists('app/admin/view/admin/system/curdgeneratev2/index.blade.php')) {
    $viewContent = file_get_contents('app/admin/view/admin/system/curdgeneratev2/index.blade.php');
    $uiElements = [
        'connectionSelect' => '数据库连接选择器',
        'loadTables' => '加载表列表功能',
        'analyzeTable' => '分析表结构功能',
        'connection:' => '连接参数传递',
    ];
    
    foreach ($uiElements as $element => $desc) {
        if (strpos($viewContent, $element) !== false) {
            echo "   ✅ {$desc}\n";
            $checks[] = "前端界面: {$desc}";
        } else {
            echo "   ❌ {$desc}\n";
            $errors[] = "前端界面: {$desc}";
        }
    }
}

// 检查 6: 服务运行状态
echo "\n🔍 检查 6: 服务运行状态\n";
$serviceUrl = 'http://localhost:8787';

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $serviceUrl);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 5);
curl_setopt($ch, CURLOPT_NOBODY, true);

$result = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

if ($error) {
    echo "   ❌ 服务连接失败: {$error}\n";
    $errors[] = "服务连接失败";
} elseif ($httpCode === 200) {
    echo "   ✅ webman 服务运行正常 (端口 8787)\n";
    $checks[] = "webman 服务运行正常";
} else {
    echo "   ⚠️  服务响应异常 (HTTP {$httpCode})\n";
    $warnings[] = "服务响应异常";
}

// 生成部署报告
echo "\n" . str_repeat("=", 60) . "\n";
echo "📊 部署验证报告\n";
echo str_repeat("=", 60) . "\n";

echo "✅ 通过检查: " . count($checks) . " 项\n";
echo "⚠️  警告: " . count($warnings) . " 项\n";
echo "❌ 错误: " . count($errors) . " 项\n\n";

if (count($errors) > 0) {
    echo "❌ 发现严重错误:\n";
    foreach ($errors as $error) {
        echo "   • {$error}\n";
    }
    echo "\n";
}

if (count($warnings) > 0) {
    echo "⚠️  警告信息:\n";
    foreach ($warnings as $warning) {
        echo "   • {$warning}\n";
    }
    echo "\n";
}

// 部署状态判断
$deploymentStatus = count($errors) === 0 ? 'SUCCESS' : 'FAILED';
$readinessLevel = count($errors) === 0 && count($warnings) === 0 ? 'FULLY_READY' : 
                 (count($errors) === 0 ? 'READY_WITH_WARNINGS' : 'NOT_READY');

echo "🎯 部署状态: {$deploymentStatus}\n";
echo "🚀 就绪级别: {$readinessLevel}\n\n";

if ($deploymentStatus === 'SUCCESS') {
    echo "🎉 多数据库 CURD 生成器部署成功！\n\n";
    
    echo "📋 使用指南:\n";
    echo "1. 访问后台登录: {$serviceUrl}/admin/login\n";
    echo "2. 访问 CURD 生成器: {$serviceUrl}/admin/system/curdgeneratev2\n";
    echo "3. 访问数据库测试工具: {$serviceUrl}/admin/system/databasetest\n\n";
    
    echo "🔧 功能特性:\n";
    echo "• 支持 9 种数据库连接配置\n";
    echo "• 动态数据库连接选择\n";
    echo "• 跨数据库表结构分析\n";
    echo "• 实时连接状态检测\n";
    echo "• 用户友好的错误处理\n";
    echo "• 完整的权限控制\n\n";
    
    echo "🧪 测试建议:\n";
    echo "• 测试不同数据库连接的切换\n";
    echo "• 验证表列表加载功能\n";
    echo "• 检查表结构分析准确性\n";
    echo "• 确认生成代码的正确性\n";
    echo "• 测试错误处理机制\n";
} else {
    echo "❌ 部署验证失败，请修复上述错误后重新验证\n";
}

echo "\n=== 部署验证完成 ===\n";
?>
