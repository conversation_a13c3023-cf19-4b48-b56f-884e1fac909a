<?php
/**
 * CURD生成器V2 数据库连接问题修复验证脚本
 * 一键运行所有必要的测试
 */

echo "🚀 CURD生成器V2 - 数据库连接修复验证\n";
echo str_repeat("=", 60) . "\n\n";

echo "🎯 本脚本将验证以下修复:\n";
echo "  1. 路由配置修复\n";
echo "  2. JavaScript事件监听器修复\n";
echo "  3. 模拟数据一致性修复\n";
echo "  4. mysql_second连接表列表显示\n\n";

// 检查PHP版本
if (version_compare(PHP_VERSION, '8.0.0', '<')) {
    echo "⚠️  警告: 建议使用PHP 8.0+，当前版本: " . PHP_VERSION . "\n\n";
}

// 步骤1: 编译检查
echo "📋 步骤1: 运行编译检查\n";
echo str_repeat("-", 40) . "\n";

if (file_exists('scripts/compile-and-test.php')) {
    echo "正在运行编译检查...\n";
    
    ob_start();
    $startTime = microtime(true);
    
    try {
        include 'scripts/compile-and-test.php';
        $duration = round((microtime(true) - $startTime) * 1000, 2);
        $output = ob_get_clean();
        
        // 检查是否有错误
        if (preg_match('/❌|错误|失败/', $output)) {
            echo "❌ 编译检查发现问题 ({$duration}ms)\n";
            echo "详细信息请查看上面的输出\n";
        } else {
            echo "✅ 编译检查通过 ({$duration}ms)\n";
        }
    } catch (Exception $e) {
        ob_get_clean();
        echo "💥 编译检查异常: " . $e->getMessage() . "\n";
    }
} else {
    echo "❌ 编译检查脚本不存在\n";
}

echo "\n";

// 步骤2: API接口测试
echo "📡 步骤2: 运行API接口测试\n";
echo str_repeat("-", 40) . "\n";

if (file_exists('test/simple-api-test.php')) {
    echo "正在测试API接口...\n";
    
    ob_start();
    $startTime = microtime(true);
    
    try {
        include 'test/simple-api-test.php';
        $duration = round((microtime(true) - $startTime) * 1000, 2);
        $output = ob_get_clean();
        
        // 检查mysql_second是否成功
        if (preg_match('/mysql_second 连接测试成功/', $output)) {
            echo "✅ API接口测试通过 ({$duration}ms)\n";
            echo "🎯 mysql_second连接正常，能够获取表列表\n";
        } else {
            echo "❌ API接口测试失败 ({$duration}ms)\n";
            echo "详细信息请查看上面的输出\n";
        }
    } catch (Exception $e) {
        ob_get_clean();
        echo "💥 API测试异常: " . $e->getMessage() . "\n";
    }
} else {
    echo "❌ API测试脚本不存在\n";
}

echo "\n";

// 步骤3: 生成测试报告
echo "📊 步骤3: 生成测试报告\n";
echo str_repeat("-", 40) . "\n";

$report = [
    'timestamp' => date('Y-m-d H:i:s'),
    'php_version' => PHP_VERSION,
    'tests_run' => [
        'compile_check' => file_exists('scripts/compile-and-test.php'),
        'api_test' => file_exists('test/simple-api-test.php')
    ],
    'fix_verification' => [
        'route_config' => file_exists('config/route.php') && 
                         strpos(file_get_contents('config/route.php'), 'CurdTestController') !== false,
        'js_events' => file_exists('public/static/admin/js/curd-generator-v2.js') &&
                      strpos(file_get_contents('public/static/admin/js/curd-generator-v2.js'), 'connectionSelect') !== false,
        'html_filters' => file_exists('app/admin/view/admin/system/curdgeneratev2/index.blade.php') &&
                         strpos(file_get_contents('app/admin/view/admin/system/curdgeneratev2/index.blade.php'), 'lay-filter="connectionSelect"') !== false
    ]
];

$reportFile = 'test/fix-verification-report.json';
file_put_contents($reportFile, json_encode($report, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));

echo "✅ 测试报告已生成: $reportFile\n";

// 显示修复验证结果
echo "\n🔍 修复验证结果:\n";
foreach ($report['fix_verification'] as $fix => $status) {
    $icon = $status ? '✅' : '❌';
    $fixName = [
        'route_config' => '路由配置修复',
        'js_events' => 'JavaScript事件修复',
        'html_filters' => 'HTML过滤器修复'
    ][$fix];
    echo "  $icon $fixName\n";
}

echo "\n";

// 步骤4: 提供下一步指导
echo "💡 下一步操作指导\n";
echo str_repeat("-", 40) . "\n";

$allFixed = array_reduce($report['fix_verification'], function($carry, $item) {
    return $carry && $item;
}, true);

if ($allFixed) {
    echo "🎉 所有修复都已正确应用！\n\n";
    
    echo "🚀 现在可以测试实际功能:\n";
    echo "  1. 启动Web服务器:\n";
    echo "     php start.php start\n\n";
    
    echo "  2. 访问测试页面验证前端功能:\n";
    echo "     http://localhost:8787/test/simple-connection-test.html\n\n";
    
    echo "  3. 访问实际的CURD生成器:\n";
    echo "     http://localhost:8787/admin/system/curdgeneratev2\n\n";
    
    echo "  4. 测试步骤:\n";
    echo "     a) 选择'第二数据库 (mysql_second)'\n";
    echo "     b) 检查表列表是否显示6个表\n";
    echo "     c) 验证表前缀是否自动设置为'ddwx_'\n";
    echo "     d) 选择一个表进行结构分析\n\n";
    
    echo "📋 期望看到的表:\n";
    $expectedTables = ['admin', 'member', 'shop_product', 'shop_order', 'article', 'business'];
    foreach ($expectedTables as $table) {
        echo "     - $table\n";
    }
    
} else {
    echo "⚠️  发现未修复的问题，需要手动检查:\n\n";
    
    foreach ($report['fix_verification'] as $fix => $status) {
        if (!$status) {
            $fixName = [
                'route_config' => '路由配置',
                'js_events' => 'JavaScript事件监听器',
                'html_filters' => 'HTML过滤器属性'
            ][$fix];
            echo "  ❌ $fixName 需要修复\n";
        }
    }
    
    echo "\n🔧 修复建议:\n";
    echo "  1. 检查所有修改文件是否正确保存\n";
    echo "  2. 重新运行修复脚本\n";
    echo "  3. 查看详细错误日志\n";
}

echo "\n";

// 额外的调试工具
echo "🛠️  额外调试工具\n";
echo str_repeat("-", 40) . "\n";
echo "如果仍有问题，可以使用以下工具进一步调试:\n\n";

echo "📡 API直接测试:\n";
echo "  php test/simple-api-test.php\n\n";

echo "🌐 CURL命令测试:\n";
if (PHP_OS_FAMILY === 'Windows') {
    echo "  test\\curl-test.bat\n\n";
} else {
    echo "  chmod +x test/curl-test.sh && ./test/curl-test.sh\n\n";
}

echo "🔍 浏览器调试:\n";
echo "  1. 打开浏览器开发者工具 (F12)\n";
echo "  2. 查看Console标签页的错误信息\n";
echo "  3. 查看Network标签页的请求状态\n";
echo "  4. 清空浏览器缓存 (Ctrl+F5)\n\n";

echo "📞 获取帮助:\n";
echo "  如果问题仍然存在，请提供以下信息:\n";
echo "  - 测试报告文件: $reportFile\n";
echo "  - 浏览器控制台错误截图\n";
echo "  - 网络请求失败的详细信息\n";

echo "\n" . str_repeat("=", 60) . "\n";
echo "修复验证完成！\n";

// 返回适当的退出码
exit($allFixed ? 0 : 1);
?>
