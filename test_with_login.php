<?php
/**
 * 带登录的 CURD 功能测试
 */

echo "=== 带登录的 CURD 功能测试 ===\n\n";

$baseUrl = 'http://localhost:8787';
$cookieJar = tempnam(sys_get_temp_dir(), 'cookies');

// 1. 尝试获取登录页面
echo "1. 获取登录页面\n";
echo str_repeat("-", 40) . "\n";

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $baseUrl . '/admin/login');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
curl_setopt($ch, CURLOPT_COOKIEJAR, $cookieJar);
curl_setopt($ch, CURLOPT_COOKIEFILE, $cookieJar);
curl_setopt($ch, CURLOPT_TIMEOUT, 10);

$loginPage = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

echo "HTTP 状态: {$httpCode}\n";

if ($httpCode === 200) {
    echo "✅ 登录页面获取成功\n";
    
    // 尝试提取 CSRF token 或其他必要信息
    if (preg_match('/name="__token__"\s+value="([^"]+)"/', $loginPage, $matches)) {
        $token = $matches[1];
        echo "🔑 找到 token: " . substr($token, 0, 20) . "...\n";
    } else {
        $token = '';
        echo "⚠️  未找到 token\n";
    }
} else {
    echo "❌ 登录页面获取失败\n";
    exit(1);
}

// 2. 尝试登录
echo "\n2. 尝试登录\n";
echo str_repeat("-", 40) . "\n";

$loginCredentials = [
    ['username' => 'admin', 'password' => 'admin123'],
    ['username' => 'admin', 'password' => '123456'],
    ['username' => 'admin', 'password' => 'admin'],
    ['username' => 'admin', 'password' => 'password'],
    ['username' => 'root', 'password' => 'root'],
];

$loginSuccess = false;

foreach ($loginCredentials as $cred) {
    echo "尝试登录: {$cred['username']} / {$cred['password']}\n";
    
    $postData = [
        'username' => $cred['username'],
        'password' => $cred['password'],
    ];
    
    if ($token) {
        $postData['__token__'] = $token;
    }
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $baseUrl . '/admin/login');
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($postData));
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_COOKIEJAR, $cookieJar);
    curl_setopt($ch, CURLOPT_COOKIEFILE, $cookieJar);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/x-www-form-urlencoded',
    ]);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $finalUrl = curl_getinfo($ch, CURLINFO_EFFECTIVE_URL);
    curl_close($ch);
    
    echo "   HTTP 状态: {$httpCode}\n";
    echo "   最终 URL: {$finalUrl}\n";
    
    // 检查是否登录成功
    if (strpos($finalUrl, '/admin/index') !== false || strpos($finalUrl, '/admin/main') !== false) {
        echo "   ✅ 登录成功!\n";
        $loginSuccess = true;
        break;
    } elseif (strpos($response, '登录成功') !== false || strpos($response, 'success') !== false) {
        echo "   ✅ 登录成功!\n";
        $loginSuccess = true;
        break;
    } else {
        echo "   ❌ 登录失败\n";
        
        // 检查响应中的错误信息
        if (preg_match('/msg["\']:\s*["\']([^"\']+)["\']/', $response, $matches)) {
            echo "   错误信息: {$matches[1]}\n";
        }
    }
    
    echo "\n";
}

if (!$loginSuccess) {
    echo "❌ 所有登录尝试都失败了\n";
    echo "💡 请手动登录后台，然后测试 CURD 功能\n";
    echo "   访问: {$baseUrl}/admin/login\n";
    echo "   然后访问: {$baseUrl}/admin/system/curdgeneratev2\n\n";
    
    // 即使登录失败，我们也继续测试 API（可能会返回需要登录的提示）
    echo "继续测试 API（未登录状态）...\n\n";
}

// 3. 测试 CURD API
echo "3. 测试 CURD API\n";
echo str_repeat("-", 40) . "\n";

function testAPIWithCookies($url, $postData, $cookieJar, $description) {
    echo "🧪 测试: {$description}\n";
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($postData));
    curl_setopt($ch, CURLOPT_COOKIEJAR, $cookieJar);
    curl_setopt($ch, CURLOPT_COOKIEFILE, $cookieJar);
    curl_setopt($ch, CURLOPT_TIMEOUT, 15);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/x-www-form-urlencoded',
        'X-Requested-With: XMLHttpRequest',
    ]);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($error) {
        echo "   ❌ 请求失败: {$error}\n";
        return false;
    }
    
    echo "   ✅ HTTP 状态: {$httpCode}\n";
    
    if ($httpCode !== 200) {
        echo "   ❌ HTTP 错误\n";
        return false;
    }
    
    $json = json_decode($response, true);
    if ($json === null) {
        echo "   ❌ 响应不是有效的 JSON\n";
        echo "   📄 原始响应: " . substr($response, 0, 300) . "...\n";
        return false;
    }
    
    echo "   📊 响应码: {$json['code']}\n";
    echo "   📝 消息: {$json['msg']}\n";
    
    if (isset($json['data']) && is_array($json['data'])) {
        echo "   📈 数据量: " . count($json['data']) . " 项\n";
        
        if (count($json['data']) > 0) {
            echo "   📋 数据示例:\n";
            for ($i = 0; $i < min(3, count($json['data'])); $i++) {
                $item = $json['data'][$i];
                if (is_array($item) && isset($item['name'])) {
                    echo "      - {$item['name']} ({$item['comment']})\n";
                } else {
                    $display = json_encode($item, JSON_UNESCAPED_UNICODE);
                    if (strlen($display) > 80) {
                        $display = substr($display, 0, 80) . '...';
                    }
                    echo "      - {$display}\n";
                }
            }
        }
    }
    
    echo "\n";
    return $json;
}

// 测试获取表列表
$connections = ['mysql_second', 'mysql', 'mysql_read'];

foreach ($connections as $conn) {
    $result = testAPIWithCookies(
        $baseUrl . '/admin/system/curdgeneratev2',
        ['action' => 'get_tables', 'connection' => $conn],
        $cookieJar,
        "获取 {$conn} 连接的表列表"
    );
    
    if ($result && $result['code'] === 1 && isset($result['data']) && count($result['data']) > 0) {
        echo "   🎉 成功获取到表列表！\n";
        
        // 测试分析第一个表
        $firstTable = $result['data'][0];
        if (isset($firstTable['name'])) {
            echo "   🔍 测试分析表: {$firstTable['name']}\n";
            
            $analyzeResult = testAPIWithCookies(
                $baseUrl . '/admin/system/curdgeneratev2',
                [
                    'action' => 'analyze_table',
                    'table_name' => $firstTable['name'],
                    'table_prefix' => $conn === 'mysql_second' ? 'ddwx_' : 'ea8_',
                    'connection' => $conn
                ],
                $cookieJar,
                "分析表 {$firstTable['name']}"
            );
            
            if ($analyzeResult && $analyzeResult['code'] === 1) {
                echo "   🎉 表分析成功！\n";
            }
        }
        
        break; // 找到一个可用的连接就停止
    }
    
    echo str_repeat("-", 30) . "\n";
}

// 清理临时文件
unlink($cookieJar);

echo "\n=== 测试总结 ===\n";

if ($loginSuccess) {
    echo "✅ 登录成功，可以正常使用 CURD 功能\n";
} else {
    echo "⚠️  自动登录失败，需要手动登录\n";
}

echo "\n💡 解决方案:\n";
echo "1. 手动登录后台: {$baseUrl}/admin/login\n";
echo "2. 访问 CURD 页面: {$baseUrl}/admin/system/curdgeneratev2\n";
echo "3. 选择数据库连接 (推荐 mysql_second)\n";
echo "4. 点击刷新表列表按钮\n";
echo "5. 如果仍然没有表显示，检查浏览器开发者工具的网络请求\n\n";

echo "🔧 如果问题仍然存在:\n";
echo "1. 检查浏览器控制台是否有 JavaScript 错误\n";
echo "2. 检查网络请求的响应内容\n";
echo "3. 确认数据库连接配置正确\n";
echo "4. 检查后台权限设置\n";

echo "\n=== 测试完成 ===\n";
?>
