<?php

namespace app\admin\controller\system;

use app\common\controller\AdminController;
use support\Request;
use support\Response;
use support\Db;

/**
 * 数据库连接测试控制器
 */
class DatabaseTestController extends AdminController
{
    /**
     * 测试数据库连接
     */
    public function index(Request $request): Response
    {
        if ($request->method() === 'POST') {
            return $this->testConnection($request);
        }

        return $this->fetch();
    }

    /**
     * 测试数据库连接
     */
    protected function testConnection(Request $request): Response
    {
        try {
            $connection = $request->input('connection', 'mysql');

            // 获取连接配置
            $config = config("database.connections.{$connection}");
            if (!$config) {
                return $this->error("数据库连接配置 '{$connection}' 不存在");
            }

            // 测试连接
            $startTime = microtime(true);
            $result = Db::connection($connection)->select('SELECT 1 as test, NOW() as current_time');
            $endTime = microtime(true);

            $responseTime = round(($endTime - $startTime) * 1000, 2);

            // 获取数据库信息
            $dbInfo = $this->getDatabaseInfo($connection);

            return $this->success('连接测试成功', [
                'connection' => $connection,
                'config' => [
                    'driver' => $config['driver'] ?? 'unknown',
                    'host' => $config['host'] ?? 'unknown',
                    'port' => $config['port'] ?? 'unknown',
                    'database' => $config['database'] ?? 'unknown',
                    'username' => $config['username'] ?? 'unknown',
                ],
                'test_result' => $result[0] ?? null,
                'response_time_ms' => $responseTime,
                'database_info' => $dbInfo,
                'timestamp' => date('Y-m-d H:i:s'),
            ]);

        } catch (\Exception $e) {
            return $this->error('连接测试失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取数据库信息
     */
    protected function getDatabaseInfo(string $connection): array
    {
        try {
            $info = [];

            // 获取数据库版本
            try {
                $version = Db::connection($connection)->select('SELECT VERSION() as version');
                $info['version'] = $version[0]->version ?? 'unknown';
            } catch (\Exception $e) {
                $info['version'] = 'unknown';
            }

            // 获取当前数据库
            try {
                $database = Db::connection($connection)->select('SELECT DATABASE() as current_db');
                $info['current_database'] = $database[0]->current_db ?? 'unknown';
            } catch (\Exception $e) {
                $info['current_database'] = 'unknown';
            }

            // 获取表数量
            try {
                $tables = Db::connection($connection)->select('SHOW TABLES');
                $info['table_count'] = count($tables);
            } catch (\Exception $e) {
                $info['table_count'] = 0;
            }

            // 获取字符集
            try {
                $charset = Db::connection($connection)->select('SELECT @@character_set_database as charset');
                $info['charset'] = $charset[0]->charset ?? 'unknown';
            } catch (\Exception $e) {
                $info['charset'] = 'unknown';
            }

            // 获取时区
            try {
                $timezone = Db::connection($connection)->select('SELECT @@time_zone as timezone');
                $info['timezone'] = $timezone[0]->timezone ?? 'unknown';
            } catch (\Exception $e) {
                $info['timezone'] = 'unknown';
            }

            return $info;

        } catch (\Exception $e) {
            return ['error' => $e->getMessage()];
        }
    }

    /**
     * 获取所有可用的数据库连接
     */
    public function connections(): Response
    {
        try {
            $connections = config('database.connections', []);
            $result = [];

            foreach ($connections as $name => $config) {
                $result[] = [
                    'name' => $name,
                    'driver' => $config['driver'] ?? 'unknown',
                    'host' => $config['host'] ?? 'unknown',
                    'port' => $config['port'] ?? 'unknown',
                    'database' => $config['database'] ?? 'unknown',
                    'description' => $this->getConnectionDescription($name),
                ];
            }

            return $this->success('获取成功', $result);

        } catch (\Exception $e) {
            return $this->error('获取失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取连接描述
     */
    protected function getConnectionDescription(string $connection): string
    {
        $descriptions = [
            'mysql' => '默认连接',
            'mysql_read' => '读库连接',
            'mysql_second' => '第二数据库',
            'mysql_log' => '日志数据库',
            'mysql_cache' => '缓存数据库',
            'mysql_without_prefix' => '无前缀连接',
            'pgsql' => 'PostgreSQL',
            'sqlite' => 'SQLite',
            'sqlsrv' => 'SQL Server',
        ];

        return $descriptions[$connection] ?? $connection;
    }
}