<?php

namespace app\common\services\curd\v2\dto;

/**
 * 生成请求数据传输对象
 */
class GenerateRequest
{
    protected string $tableName;
    protected string $tablePrefix;
    protected array $options;
    protected bool $force;

    public function __construct(
        string $tableName,
        string $tablePrefix = '',
        array $options = [],
        bool $force = false
    ) {
        $this->tableName = $tableName;
        $this->tablePrefix = $tablePrefix;
        $this->options = $options;
        $this->force = $force;
    }

    public function getTableName(): string
    {
        return $this->tableName;
    }

    public function getTablePrefix(): string
    {
        return $this->tablePrefix;
    }

    public function getOptions(): array
    {
        return $this->options;
    }

    public function getOption(string $key, $default = null)
    {
        return $this->options[$key] ?? $default;
    }

    public function isForce(): bool
    {
        return $this->force;
    }

    public function setOption(string $key, $value): self
    {
        $this->options[$key] = $value;
        return $this;
    }

    public function setForce(bool $force): self
    {
        $this->force = $force;
        return $this;
    }

    /**
     * 从数组创建请求对象
     */
    public static function fromArray(array $data): self
    {
        return new self(
            $data['table_name'] ?? '',
            $data['table_prefix'] ?? '',
            $data['options'] ?? [],
            $data['force'] ?? false
        );
    }

    /**
     * 转换为数组
     */
    public function toArray(): array
    {
        return [
            'table_name' => $this->tableName,
            'table_prefix' => $this->tablePrefix,
            'options' => $this->options,
            'force' => $this->force,
        ];
    }
}
