<?php

/**
 * 权限系统配置
 */
return [
    // 权限开关
    'auth_on' => function_exists('env') ? env('AUTH_ENABLED', true) : true,

    // 数据表配置
    'tables' => [
        'admin' => function_exists('env') ? env('AUTH_ADMIN_TABLE', 'system_admin') : 'system_admin',
        'auth' => function_exists('env') ? env('AUTH_AUTH_TABLE', 'system_auth') : 'system_auth',
        'node' => function_exists('env') ? env('AUTH_NODE_TABLE', 'system_node') : 'system_node',
        'auth_node' => function_exists('env') ? env('AUTH_AUTH_NODE_TABLE', 'system_auth_node') : 'system_auth_node',
    ],

    // 缓存配置
    'cache' => [
        'prefix' => 'auth_',
        'ttl' => function_exists('env') ? env('AUTH_CACHE_TTL', 3600) : 3600,
        'keys' => [
            'admin_info' => 'admin_info_',
            'admin_nodes' => 'admin_nodes_',
            'node_list' => 'node_list',
        ],
    ],

    // 超级管理员配置
    'super_admin' => [
        'id' => function_exists('env') ? env('SUPER_ADMIN_ID', 1) : 1,
        'bypass_auth' => function_exists('env') ? env('SUPER_ADMIN_BYPASS_AUTH', true) : true,
    ],

    // 权限验证配置
    'validation' => [
        'check_node_exists' => true,
        'strict_mode' => function_exists('env') ? env('AUTH_STRICT_MODE', false) : false,
    ],
];
