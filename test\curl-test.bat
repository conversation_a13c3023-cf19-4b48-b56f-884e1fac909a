@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

REM CURD生成器V2 - Windows CURL API测试脚本

echo 🔧 CURD生成器V2 - CURL API测试
echo ==================================================
echo.

REM 配置
set BASE_URL=http://localhost:8787
set API_URL=%BASE_URL%/curdtest/tables
set ANALYZE_URL=%BASE_URL%/curdtest/analyze

REM 检查curl是否可用
curl --version >nul 2>&1
if errorlevel 1 (
    echo ❌ curl命令不可用，请安装curl或使用Git Bash
    pause
    exit /b 1
)

REM 测试1: 检查服务器状态
echo 🌐 测试1: 检查服务器状态
echo ------------------------------

echo 测试URL: %BASE_URL%
curl -s -o nul -w "HTTP状态码: %%{http_code}" --connect-timeout 5 "%BASE_URL%"
echo.

if errorlevel 1 (
    echo ❌ 服务器连接失败
    echo 💡 请确保服务器已启动: php start.php start
    pause
    exit /b 1
) else (
    echo ✅ 服务器运行正常
)
echo.

REM 测试2: 测试API路由
echo 🛣️  测试2: 测试API路由
echo ------------------------------

echo 测试URL: %API_URL%
curl -s -X POST ^
    -H "Content-Type: application/x-www-form-urlencoded" ^
    -H "User-Agent: CURD-CURL-Test/1.0" ^
    -d "connection=mysql" ^
    "%API_URL%" > temp_response.json

if errorlevel 1 (
    echo ❌ API路由请求失败
) else (
    echo ✅ API路由请求成功
    echo 响应内容:
    type temp_response.json
)
echo.

REM 测试3: 重点测试mysql_second连接
echo 🎯 测试3: 重点测试mysql_second连接
echo ------------------------------

echo 发送请求到: %API_URL%
echo 参数: connection=mysql_second

curl -s -X POST ^
    -H "Content-Type: application/x-www-form-urlencoded" ^
    -H "X-Requested-With: XMLHttpRequest" ^
    -H "X-Debug-Mode: true" ^
    -d "connection=mysql_second&debug=true" ^
    "%API_URL%" > mysql_second_response.json

if errorlevel 1 (
    echo ❌ mysql_second 请求失败
) else (
    echo ✅ mysql_second 请求成功
    echo 响应内容:
    type mysql_second_response.json
    echo.
    
    REM 简单检查是否包含期望的表
    findstr /C:"admin" mysql_second_response.json >nul
    if not errorlevel 1 echo ✅ 找到 admin 表
    
    findstr /C:"member" mysql_second_response.json >nul
    if not errorlevel 1 echo ✅ 找到 member 表
    
    findstr /C:"shop_product" mysql_second_response.json >nul
    if not errorlevel 1 echo ✅ 找到 shop_product 表
    
    findstr /C:"shop_order" mysql_second_response.json >nul
    if not errorlevel 1 echo ✅ 找到 shop_order 表
    
    findstr /C:"article" mysql_second_response.json >nul
    if not errorlevel 1 echo ✅ 找到 article 表
    
    findstr /C:"business" mysql_second_response.json >nul
    if not errorlevel 1 echo ✅ 找到 business 表
    
    findstr /C:"演示数据" mysql_second_response.json >nul
    if not errorlevel 1 echo ⚠️  注意: 使用演示数据模式
)
echo.

REM 测试4: 测试所有连接
echo 🗄️  测试4: 测试所有数据库连接
echo ------------------------------

set connections=mysql mysql_read mysql_second mysql_log mysql_cache

for %%c in (%connections%) do (
    echo 测试 %%c:
    curl -s -X POST ^
        -H "Content-Type: application/x-www-form-urlencoded" ^
        -d "connection=%%c" ^
        --max-time 10 ^
        "%API_URL%" > temp_%%c.json
    
    if errorlevel 1 (
        echo   ❌ 请求失败
    ) else (
        findstr /C:"""code"":1" temp_%%c.json >nul
        if not errorlevel 1 (
            echo   ✅ 成功
        ) else (
            echo   ❌ API返回错误
        )
    )
    echo.
)

REM 测试5: 测试表分析功能
echo 🔍 测试5: 测试表分析功能
echo ------------------------------

echo 测试URL: %ANALYZE_URL%
echo 参数: table_name=admin, connection=mysql_second

curl -s -X POST ^
    -H "Content-Type: application/x-www-form-urlencoded" ^
    -d "table_name=admin&table_prefix=&connection=mysql_second" ^
    "%ANALYZE_URL%" > analyze_response.json

if errorlevel 1 (
    echo ❌ 表分析请求失败
) else (
    echo ✅ 表分析请求成功
    echo 响应内容:
    type analyze_response.json
    
    findstr /C:"""code"":1" analyze_response.json >nul
    if not errorlevel 1 (
        echo ✅ 表分析成功
    ) else (
        echo ❌ 表分析失败
    )
)
echo.

REM 总结
echo 📊 测试总结
echo ==================================================
echo ✅ CURL API测试完成
echo.
echo 🎯 关键检查点:
echo   1. 服务器是否运行正常
echo   2. API路由是否可访问
echo   3. mysql_second是否返回6个表
echo   4. 表分析功能是否正常
echo.
echo 💡 下一步:
echo   - 如果API测试全部通过，问题在前端JavaScript
echo   - 如果API测试失败，检查服务器配置和路由
echo   - 访问简化测试页面: http://localhost:8787/test/simple-connection-test.html
echo.

REM 清理临时文件
del temp_*.json >nul 2>&1
del mysql_second_response.json >nul 2>&1
del analyze_response.json >nul 2>&1

echo 按任意键退出...
pause >nul
