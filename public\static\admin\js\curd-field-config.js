/**
 * CURD 字段配置高级功能
 */

layui.use(['form', 'layer', 'transfer'], function() {
    var form = layui.form;
    var layer = layui.layer;
    var transfer = layui.transfer;

    // 全局变量
    var fieldTemplates = {};
    var dragSortable = null;

    // 初始化字段配置
    function initFieldConfig() {
        bindFieldEvents();
        initDragSort();
        loadFieldTemplates();
    }

    // 绑定字段相关事件
    function bindFieldEvents() {
        // 批量配置
        $(document).on('click', '#batch-config', function() {
            openBatchConfigModal();
        });

        // 重置配置
        $(document).on('click', '#reset-config', function() {
            resetFieldConfig();
        });

        // 保存模板
        $(document).on('click', '.save-template-btn', function() {
            saveFieldTemplate();
        });

        // 加载模板
        $(document).on('click', '.load-template-btn', function() {
            loadFieldTemplate();
        });

        // 智能推荐
        $(document).on('click', '.recommend-template-btn', function() {
            recommendFieldConfig();
        });

        // 字段配置变化监听
        $(document).on('change', '.field-checkbox, .field-show-list, .field-show-form, .field-show-search, .field-required, .field-sortable', function() {
            updateFieldConfig($(this));
        });

        // 字段标签编辑
        $(document).on('blur', '.field-label', function() {
            updateFieldLabel($(this));
        });

        // 组件类型变化
        $(document).on('change', '.field-component', function() {
            updateFieldComponent($(this));
        });

        // 全选/反选
        $(document).on('change', '#select-all-fields, #select-all-fields-header', function() {
            var checked = $(this).is(':checked');
            $('.field-checkbox').prop('checked', checked);
            form.render('checkbox');
        });
    }

    // 初始化拖拽排序
    function initDragSort() {
        if (typeof Sortable !== 'undefined') {
            var tbody = document.getElementById('field-config-tbody');
            if (tbody) {
                dragSortable = Sortable.create(tbody, {
                    handle: '.drag-handle',
                    animation: 150,
                    onEnd: function(evt) {
                        updateFieldOrder();
                    }
                });
            }
        }
    }

    // 打开批量配置弹窗
    function openBatchConfigModal() {
        var content = `
            <div class="batch-config-modal">
                <div class="layui-form" lay-filter="batch-form">
                    <div class="layui-form-item">
                        <label class="layui-form-label">批量操作</label>
                        <div class="layui-input-block">
                            <select name="batch_action" lay-filter="batch-action">
                                <option value="">请选择操作</option>
                                <option value="show_in_list">显示在列表</option>
                                <option value="hide_in_list">隐藏在列表</option>
                                <option value="show_in_form">显示在表单</option>
                                <option value="hide_in_form">隐藏在表单</option>
                                <option value="show_in_search">显示在搜索</option>
                                <option value="hide_in_search">隐藏在搜索</option>
                                <option value="set_required">设为必填</option>
                                <option value="set_optional">设为可选</option>
                                <option value="set_component">设置组件</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="layui-form-item" id="component-select" style="display: none;">
                        <label class="layui-form-label">组件类型</label>
                        <div class="layui-input-block">
                            <select name="component_type">
                                <option value="input">文本框</option>
                                <option value="textarea">文本域</option>
                                <option value="number">数字框</option>
                                <option value="select">下拉框</option>
                                <option value="radio">单选框</option>
                                <option value="checkbox">复选框</option>
                                <option value="switch">开关</option>
                                <option value="date">日期</option>
                                <option value="datetime">日期时间</option>
                                <option value="image">图片上传</option>
                                <option value="file">文件上传</option>
                                <option value="editor">富文本编辑器</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">应用范围</label>
                        <div class="layui-input-block">
                            <input type="radio" name="apply_scope" value="selected" title="选中字段" checked>
                            <input type="radio" name="apply_scope" value="all" title="全部字段">
                            <input type="radio" name="apply_scope" value="type" title="指定类型">
                        </div>
                    </div>
                    
                    <div class="layui-form-item" id="field-type-select" style="display: none;">
                        <label class="layui-form-label">字段类型</label>
                        <div class="layui-input-block">
                            <select name="field_type" multiple>
                                <option value="varchar">VARCHAR</option>
                                <option value="int">INT</option>
                                <option value="text">TEXT</option>
                                <option value="datetime">DATETIME</option>
                                <option value="decimal">DECIMAL</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
        `;

        layer.open({
            type: 1,
            title: '批量配置字段',
            content: content,
            area: ['500px', '400px'],
            btn: ['应用', '取消'],
            yes: function(index) {
                applyBatchConfig();
                layer.close(index);
            },
            success: function() {
                form.render();
                
                // 监听批量操作选择
                form.on('select(batch-action)', function(data) {
                    if (data.value === 'set_component') {
                        $('#component-select').show();
                    } else {
                        $('#component-select').hide();
                    }
                });
                
                // 监听应用范围选择
                form.on('radio(apply_scope)', function(data) {
                    if (data.value === 'type') {
                        $('#field-type-select').show();
                    } else {
                        $('#field-type-select').hide();
                    }
                });
            }
        });
    }

    // 应用批量配置
    function applyBatchConfig() {
        var formData = form.val('batch-form');
        var action = formData.batch_action;
        var scope = formData.apply_scope;
        
        if (!action) {
            layer.msg('请选择批量操作', {icon: 2});
            return;
        }

        var targetFields = getTargetFields(scope, formData.field_type);
        
        targetFields.forEach(function(fieldName) {
            applyActionToField(fieldName, action, formData.component_type);
        });
        
        // 重新渲染表单
        form.render();
        updateFieldStats();
        
        layer.msg('批量配置已应用', {icon: 1});
    }

    // 获取目标字段
    function getTargetFields(scope, fieldType) {
        var fields = [];
        
        switch (scope) {
            case 'selected':
                $('.field-checkbox:checked').each(function() {
                    var fieldName = $(this).closest('tr').data('field');
                    if (fieldName) fields.push(fieldName);
                });
                break;
            case 'all':
                $('#field-config-tbody tr').each(function() {
                    var fieldName = $(this).data('field');
                    if (fieldName) fields.push(fieldName);
                });
                break;
            case 'type':
                if (fieldType) {
                    $('#field-config-tbody tr').each(function() {
                        var $row = $(this);
                        var fieldName = $row.data('field');
                        var type = $row.find('.field-type').text().toLowerCase();
                        if (fieldName && fieldType.includes(type)) {
                            fields.push(fieldName);
                        }
                    });
                }
                break;
        }
        
        return fields;
    }

    // 对字段应用操作
    function applyActionToField(fieldName, action, componentType) {
        var $row = $('tr[data-field="' + fieldName + '"]');
        
        switch (action) {
            case 'show_in_list':
                $row.find('.field-show-list').prop('checked', true);
                break;
            case 'hide_in_list':
                $row.find('.field-show-list').prop('checked', false);
                break;
            case 'show_in_form':
                $row.find('.field-show-form').prop('checked', true);
                break;
            case 'hide_in_form':
                $row.find('.field-show-form').prop('checked', false);
                break;
            case 'show_in_search':
                $row.find('.field-show-search').prop('checked', true);
                break;
            case 'hide_in_search':
                $row.find('.field-show-search').prop('checked', false);
                break;
            case 'set_required':
                $row.find('.field-required').prop('checked', true);
                break;
            case 'set_optional':
                $row.find('.field-required').prop('checked', false);
                break;
            case 'set_component':
                if (componentType) {
                    $row.find('.field-component').val(componentType);
                }
                break;
        }
    }

    // 重置字段配置
    function resetFieldConfig() {
        layer.confirm('确定要重置所有字段配置吗？', {icon: 3}, function(index) {
            // 重新加载字段配置
            if (window.CurdGeneratorV2 && window.CurdGeneratorV2.loadFieldConfigs) {
                window.CurdGeneratorV2.loadFieldConfigs();
            }
            layer.close(index);
            layer.msg('字段配置已重置', {icon: 1});
        });
    }

    // 保存字段模板
    function saveFieldTemplate() {
        var templateName = '';
        layer.prompt({
            title: '保存字段模板',
            formType: 0
        }, function(value, index) {
            templateName = value;
            
            var config = collectCurrentFieldConfig();
            var template = {
                name: templateName,
                config: config,
                created_at: new Date().toISOString()
            };
            
            // 保存到本地存储
            var templates = JSON.parse(localStorage.getItem('curd_field_templates') || '{}');
            templates[templateName] = template;
            localStorage.setItem('curd_field_templates', JSON.stringify(templates));
            
            layer.close(index);
            layer.msg('模板保存成功', {icon: 1});
        });
    }

    // 加载字段模板
    function loadFieldTemplate() {
        var templates = JSON.parse(localStorage.getItem('curd_field_templates') || '{}');
        var templateNames = Object.keys(templates);
        
        if (templateNames.length === 0) {
            layer.msg('暂无保存的模板', {icon: 0});
            return;
        }
        
        var options = templateNames.map(function(name) {
            return '<option value="' + name + '">' + name + '</option>';
        }).join('');
        
        var content = `
            <div class="template-load-modal">
                <div class="layui-form">
                    <div class="layui-form-item">
                        <label class="layui-form-label">选择模板</label>
                        <div class="layui-input-block">
                            <select name="template_name" lay-filter="template-select">
                                <option value="">请选择模板</option>
                                ${options}
                            </select>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">应用方式</label>
                        <div class="layui-input-block">
                            <input type="radio" name="apply_mode" value="replace" title="替换当前配置" checked>
                            <input type="radio" name="apply_mode" value="merge" title="合并配置">
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        layer.open({
            type: 1,
            title: '加载字段模板',
            content: content,
            area: ['400px', '250px'],
            btn: ['加载', '取消'],
            yes: function(index) {
                var templateName = $('select[name="template_name"]').val();
                var applyMode = $('input[name="apply_mode"]:checked').val();
                
                if (!templateName) {
                    layer.msg('请选择模板', {icon: 2});
                    return;
                }
                
                applyFieldTemplate(templates[templateName], applyMode);
                layer.close(index);
            },
            success: function() {
                form.render();
            }
        });
    }

    // 应用字段模板
    function applyFieldTemplate(template, mode) {
        if (mode === 'replace') {
            // 替换模式：清空当前配置，应用模板
            $('#field-config-tbody').empty();
        }
        
        // 应用模板配置
        if (template.config && template.config.length > 0) {
            template.config.forEach(function(fieldConfig) {
                // 查找对应的字段行
                var $row = $('tr[data-field="' + fieldConfig.name + '"]');
                if ($row.length > 0) {
                    applyFieldConfigToRow($row, fieldConfig);
                }
            });
        }
        
        form.render();
        updateFieldStats();
        layer.msg('模板加载成功', {icon: 1});
    }

    // 智能推荐字段配置
    function recommendFieldConfig() {
        layer.msg('正在分析字段特征...', {icon: 16, time: 1000});
        
        setTimeout(function() {
            var recommendations = analyzeFieldsAndRecommend();
            applyRecommendations(recommendations);
            
            layer.msg('智能推荐已应用', {icon: 1});
        }, 1000);
    }

    // 分析字段并推荐配置
    function analyzeFieldsAndRecommend() {
        var recommendations = [];
        
        $('#field-config-tbody tr').each(function() {
            var $row = $(this);
            var fieldName = $row.data('field');
            var fieldType = $row.find('.field-type').text().toLowerCase();
            
            var recommendation = {
                name: fieldName,
                show_in_list: shouldShowInList(fieldName, fieldType),
                show_in_form: shouldShowInForm(fieldName, fieldType),
                show_in_search: shouldShowInSearch(fieldName, fieldType),
                required: shouldBeRequired(fieldName, fieldType),
                component: recommendComponent(fieldName, fieldType)
            };
            
            recommendations.push(recommendation);
        });
        
        return recommendations;
    }

    // 判断是否应该在列表中显示
    function shouldShowInList(fieldName, fieldType) {
        var hideFields = ['password', 'created_at', 'updated_at', 'deleted_at'];
        var showFields = ['id', 'name', 'title', 'status', 'sort'];
        
        if (hideFields.some(field => fieldName.includes(field))) return false;
        if (showFields.some(field => fieldName.includes(field))) return true;
        if (fieldType.includes('text')) return false;
        
        return true;
    }

    // 判断是否应该在表单中显示
    function shouldShowInForm(fieldName, fieldType) {
        var hideFields = ['id', 'created_at', 'updated_at', 'deleted_at'];
        return !hideFields.some(field => fieldName.includes(field));
    }

    // 判断是否应该在搜索中显示
    function shouldShowInSearch(fieldName, fieldType) {
        var searchFields = ['name', 'title', 'keyword', 'content'];
        return searchFields.some(field => fieldName.includes(field));
    }

    // 判断是否应该必填
    function shouldBeRequired(fieldName, fieldType) {
        var requiredFields = ['name', 'title'];
        return requiredFields.some(field => fieldName.includes(field));
    }

    // 推荐组件类型
    function recommendComponent(fieldName, fieldType) {
        if (fieldName.includes('password')) return 'password';
        if (fieldName.includes('email')) return 'email';
        if (fieldName.includes('phone')) return 'tel';
        if (fieldName.includes('url')) return 'url';
        if (fieldName.includes('image') || fieldName.includes('avatar')) return 'image';
        if (fieldName.includes('file')) return 'file';
        if (fieldName.includes('content') || fieldName.includes('description')) return 'editor';
        if (fieldName.includes('status') || fieldName.includes('is_')) return 'switch';
        if (fieldName.includes('type') || fieldName.includes('category')) return 'select';
        if (fieldName.includes('date')) return fieldName.includes('time') ? 'datetime' : 'date';
        if (fieldType.includes('text')) return 'textarea';
        if (fieldType.includes('int') || fieldType.includes('decimal')) return 'number';
        
        return 'input';
    }

    // 应用推荐配置
    function applyRecommendations(recommendations) {
        recommendations.forEach(function(rec) {
            var $row = $('tr[data-field="' + rec.name + '"]');
            if ($row.length > 0) {
                $row.find('.field-show-list').prop('checked', rec.show_in_list);
                $row.find('.field-show-form').prop('checked', rec.show_in_form);
                $row.find('.field-show-search').prop('checked', rec.show_in_search);
                $row.find('.field-required').prop('checked', rec.required);
                $row.find('.field-component').val(rec.component);
            }
        });
        
        form.render();
        updateFieldStats();
    }

    // 收集当前字段配置
    function collectCurrentFieldConfig() {
        var config = [];
        
        $('#field-config-tbody tr').each(function() {
            var $row = $(this);
            var fieldConfig = {
                name: $row.data('field'),
                label: $row.find('.field-label').val(),
                component: $row.find('.field-component').val(),
                show_in_list: $row.find('.field-show-list').is(':checked'),
                show_in_form: $row.find('.field-show-form').is(':checked'),
                show_in_search: $row.find('.field-show-search').is(':checked'),
                required: $row.find('.field-required').is(':checked'),
                sortable: $row.find('.field-sortable').is(':checked')
            };
            config.push(fieldConfig);
        });
        
        return config;
    }

    // 更新字段配置
    function updateFieldConfig($element) {
        var $row = $element.closest('tr');
        var fieldName = $row.data('field');
        
        // 更新全局字段配置
        if (window.fieldConfigs) {
            var fieldConfig = window.fieldConfigs.find(f => f.name === fieldName);
            if (fieldConfig) {
                fieldConfig.show_in_list = $row.find('.field-show-list').is(':checked');
                fieldConfig.show_in_form = $row.find('.field-show-form').is(':checked');
                fieldConfig.show_in_search = $row.find('.field-show-search').is(':checked');
                fieldConfig.required = $row.find('.field-required').is(':checked');
                fieldConfig.sortable = $row.find('.field-sortable').is(':checked');
            }
        }
        
        updateFieldStats();
    }

    // 更新字段标签
    function updateFieldLabel($element) {
        var $row = $element.closest('tr');
        var fieldName = $row.data('field');
        var newLabel = $element.val();
        
        // 更新全局字段配置
        if (window.fieldConfigs) {
            var fieldConfig = window.fieldConfigs.find(f => f.name === fieldName);
            if (fieldConfig) {
                fieldConfig.comment = newLabel;
            }
        }
    }

    // 更新字段组件
    function updateFieldComponent($element) {
        var $row = $element.closest('tr');
        var fieldName = $row.data('field');
        var newComponent = $element.val();
        
        // 更新全局字段配置
        if (window.fieldConfigs) {
            var fieldConfig = window.fieldConfigs.find(f => f.name === fieldName);
            if (fieldConfig) {
                fieldConfig.component = newComponent;
            }
        }
    }

    // 更新字段顺序
    function updateFieldOrder() {
        var newOrder = [];
        $('#field-config-tbody tr').each(function(index) {
            var fieldName = $(this).data('field');
            $(this).data('index', index);
            newOrder.push(fieldName);
        });
        
        // 更新全局字段配置顺序
        if (window.fieldConfigs) {
            var reorderedConfigs = [];
            newOrder.forEach(function(fieldName) {
                var config = window.fieldConfigs.find(f => f.name === fieldName);
                if (config) {
                    reorderedConfigs.push(config);
                }
            });
            window.fieldConfigs = reorderedConfigs;
        }
        
        layer.msg('字段顺序已更新', {icon: 1, time: 1000});
    }

    // 更新字段统计
    function updateFieldStats() {
        if (window.CurdGeneratorV2 && window.CurdGeneratorV2.updateFieldStats) {
            window.CurdGeneratorV2.updateFieldStats();
        }
    }

    // 暴露全局函数
    window.CurdFieldConfig = {
        init: initFieldConfig,
        batchConfig: openBatchConfigModal,
        resetConfig: resetFieldConfig,
        saveTemplate: saveFieldTemplate,
        loadTemplate: loadFieldTemplate,
        recommend: recommendFieldConfig
    };

    // 自动初始化
    $(document).ready(function() {
        if ($('#field-config-table').length > 0) {
            initFieldConfig();
        }
    });
});
