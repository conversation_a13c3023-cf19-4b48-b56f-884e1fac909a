# 技术栈和依赖

## 核心技术栈
- **PHP**: >= 8.1.0
- **webman**: ^2.1 (基于 Workerman 的高性能框架)
- **Layui**: v2.9.x (前端 UI 框架)
- **MySQL**: >= 5.7
- **Redis**: 缓存和会话存储

## 主要依赖包
### 框架核心
- `workerman/webman-framework`: ^2.1 - webman 核心框架
- `workerman/validation`: ^3.0 - 数据验证
- `webman/database`: ^2.1 - 数据库 ORM
- `webman/console`: ^2.1 - 命令行工具

### Laravel 组件
- `illuminate/pagination`: ^11.0 || ^9.52 - 分页功能
- `illuminate/events`: ^11.0 || ^9.52 - 事件系统
- `illuminate/view`: ^11.0 || ^9.52 - 视图引擎
- `illuminate/filesystem`: ^11.0 || ^9.52 - 文件系统

### 模板和视图
- `webman/blade`: ^1.5 - Blade 模板引擎

### 功能扩展
- `webman/captcha`: ^1.0 - 验证码
- `webman/cache`: ^2.1 - 缓存系统
- `webman/log`: ^2.1 - 日志系统
- `webman/domain`: ^2.1 - 域名绑定

### 文件处理
- `phpoffice/phpspreadsheet`: ^1.29 - Excel 处理
- `aliyuncs/oss-sdk-php`: ^2.6 - 阿里云 OSS
- `qcloud/cos-sdk-v5`: ^2.6 - 腾讯云 COS
- `qiniu/php-sdk`: ^7.11 - 七牛云存储

### 工具库
- `monolog/monolog`: ^2.0 - 日志记录
- `symfony/var-dumper`: ^7.0 || ^6.4 - 调试工具
- `symfony/finder`: ^7.0 || ^6.4 - 文件查找
- `vlucas/phpdotenv`: ^5.5 - 环境变量管理
- `doctrine/annotations`: ^2.0 - 注解支持

### 开发工具
- `wolf-leo/phplogviewer`: ^0.11.3 - 日志查看器
- `wolfcode/authenticator`: ^0.0.6 - 身份验证

## 前端技术
- **Layui**: 主要 UI 框架
- **jQuery**: JavaScript 库
- **RequireJS**: 模块加载器
- **CKEditor**: 富文本编辑器
- **Echarts**: 图表库
- **UEditorPlus**: 富文本编辑器增强版