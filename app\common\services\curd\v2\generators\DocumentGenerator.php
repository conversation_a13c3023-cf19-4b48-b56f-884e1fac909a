<?php

namespace app\common\services\curd\v2\generators;

/**
 * 文档生成器
 * 根据文档需求分析结果生成完整的项目文档
 */
class DocumentGenerator
{
    protected array $documentFormats = [
        'markdown' => 'Markdown',
        'html' => 'HTML',
        'pdf' => 'PDF',
        'docx' => 'Word文档',
    ];

    /**
     * 生成文档
     */
    public function generateDocuments(array $documentAnalysis, array $tableInfo, array $options = []): array
    {
        $format = $options['format'] ?? 'markdown';
        $documents = [];

        // 生成 API 文档
        if (isset($documentAnalysis['requirements']['api'])) {
            $documents['api'] = $this->generateApiDocuments($documentAnalysis, $tableInfo, $format);
        }

        // 生成用户手册
        if (isset($documentAnalysis['requirements']['user'])) {
            $documents['user'] = $this->generateUserManual($documentAnalysis, $tableInfo, $format);
        }

        // 生成开发者文档
        if (isset($documentAnalysis['requirements']['developer'])) {
            $documents['developer'] = $this->generateDeveloperDocuments($documentAnalysis, $tableInfo, $format);
        }

        // 生成部署文档
        if (isset($documentAnalysis['requirements']['deployment'])) {
            $documents['deployment'] = $this->generateDeploymentDocuments($documentAnalysis, $tableInfo, $format);
        }

        // 生成主 README
        $documents['readme'] = $this->generateMainReadme($documentAnalysis, $tableInfo, $format);

        return $documents;
    }

    /**
     * 生成 API 文档
     */
    protected function generateApiDocuments(array $documentAnalysis, array $tableInfo, string $format): array
    {
        $apiDocs = [];
        $modelName = $this->getModelName($tableInfo['name']);
        $resourceName = strtolower($modelName);

        // API 概述文档
        $apiDocs['docs/api/README.md'] = $this->generateApiOverview($modelName, $tableInfo);

        // API 端点文档
        $apiDocs['docs/api/endpoints.md'] = $this->generateApiEndpoints($documentAnalysis, $tableInfo);

        // 认证文档
        $apiDocs['docs/api/authentication.md'] = $this->generateAuthenticationDoc();

        // 示例文档
        $apiDocs['docs/api/examples.md'] = $this->generateApiExamples($documentAnalysis, $tableInfo);

        // OpenAPI 规范文件
        $apiDocs['docs/api/openapi.yaml'] = $this->generateOpenApiSpec($documentAnalysis, $tableInfo);

        return $apiDocs;
    }

    /**
     * 生成 API 概述
     */
    protected function generateApiOverview(string $modelName, array $tableInfo): string
    {
        $content = "# {$modelName} API 文档\n\n";
        $content .= "## 概述\n\n";
        $content .= "本文档描述了 {$modelName} 相关的 RESTful API 接口。\n\n";
        
        $content .= "## 基础信息\n\n";
        $content .= "- **基础URL**: `/api`\n";
        $content .= "- **版本**: v1\n";
        $content .= "- **认证方式**: Bearer Token\n";
        $content .= "- **数据格式**: JSON\n\n";
        
        $content .= "## 快速开始\n\n";
        $content .= "### 1. 获取访问令牌\n\n";
        $content .= "```bash\n";
        $content .= "curl -X POST /api/auth/login \\\n";
        $content .= "  -H \"Content-Type: application/json\" \\\n";
        $content .= "  -d '{\"email\":\"<EMAIL>\",\"password\":\"password\"}'\n";
        $content .= "```\n\n";
        
        $content .= "### 2. 使用令牌访问 API\n\n";
        $content .= "```bash\n";
        $content .= "curl -X GET /api/" . strtolower($modelName) . " \\\n";
        $content .= "  -H \"Authorization: Bearer YOUR_TOKEN\"\n";
        $content .= "```\n\n";
        
        $content .= "## 响应格式\n\n";
        $content .= "### 成功响应\n\n";
        $content .= "```json\n";
        $content .= "{\n";
        $content .= "  \"code\": 0,\n";
        $content .= "  \"message\": \"success\",\n";
        $content .= "  \"data\": {...}\n";
        $content .= "}\n";
        $content .= "```\n\n";
        
        $content .= "### 错误响应\n\n";
        $content .= "```json\n";
        $content .= "{\n";
        $content .= "  \"code\": 1,\n";
        $content .= "  \"message\": \"error message\",\n";
        $content .= "  \"errors\": {...}\n";
        $content .= "}\n";
        $content .= "```\n\n";

        return $content;
    }

    /**
     * 生成 API 端点文档
     */
    protected function generateApiEndpoints(array $documentAnalysis, array $tableInfo): string
    {
        $modelName = $this->getModelName($tableInfo['name']);
        $resourceName = strtolower($modelName);
        
        $content = "# API 端点\n\n";
        $content .= "## {$modelName} 资源\n\n";

        $endpoints = [
            ['GET', "/{$resourceName}", '获取列表', 'index'],
            ['GET', "/{$resourceName}/{id}", '获取详情', 'show'],
            ['POST', "/{$resourceName}", '创建资源', 'store'],
            ['PUT', "/{$resourceName}/{id}", '更新资源', 'update'],
            ['DELETE', "/{$resourceName}/{id}", '删除资源', 'destroy'],
        ];

        foreach ($endpoints as $endpoint) {
            $content .= "### {$endpoint[0]} {$endpoint[1]}\n\n";
            $content .= "**描述**: {$endpoint[2]}\n\n";
            
            // 请求参数
            $content .= "**请求参数**:\n\n";
            $parameters = $this->getEndpointParameters($endpoint[3], $tableInfo);
            
            if (!empty($parameters)) {
                $content .= "| 参数名 | 类型 | 必填 | 描述 |\n";
                $content .= "|--------|------|------|------|\n";
                
                foreach ($parameters as $name => $param) {
                    $required = isset($param['required']) && $param['required'] ? '是' : '否';
                    $content .= "| {$name} | {$param['type']} | {$required} | {$param['description']} |\n";
                }
            } else {
                $content .= "无\n";
            }
            
            $content .= "\n";
            
            // 响应示例
            $content .= "**响应示例**:\n\n";
            $content .= "```json\n";
            $content .= $this->generateResponseExample($endpoint[3], $tableInfo);
            $content .= "\n```\n\n";
            
            $content .= "---\n\n";
        }

        return $content;
    }

    /**
     * 生成认证文档
     */
    protected function generateAuthenticationDoc(): string
    {
        $content = "# 认证说明\n\n";
        $content .= "## 认证方式\n\n";
        $content .= "API 使用 Bearer Token 认证方式。\n\n";
        
        $content .= "## 获取令牌\n\n";
        $content .= "### 登录接口\n\n";
        $content .= "```\nPOST /api/auth/login\n```\n\n";
        
        $content .= "**请求参数**:\n\n";
        $content .= "```json\n";
        $content .= "{\n";
        $content .= "  \"email\": \"<EMAIL>\",\n";
        $content .= "  \"password\": \"password\"\n";
        $content .= "}\n";
        $content .= "```\n\n";
        
        $content .= "**响应示例**:\n\n";
        $content .= "```json\n";
        $content .= "{\n";
        $content .= "  \"code\": 0,\n";
        $content .= "  \"message\": \"登录成功\",\n";
        $content .= "  \"data\": {\n";
        $content .= "    \"token\": \"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...\",\n";
        $content .= "    \"expires_in\": 3600,\n";
        $content .= "    \"user\": {...}\n";
        $content .= "  }\n";
        $content .= "}\n";
        $content .= "```\n\n";
        
        $content .= "## 使用令牌\n\n";
        $content .= "在请求头中添加 Authorization 字段：\n\n";
        $content .= "```\nAuthorization: Bearer YOUR_TOKEN\n```\n\n";
        
        $content .= "## 令牌刷新\n\n";
        $content .= "```\nPOST /api/auth/refresh\n```\n\n";
        
        $content .= "## 退出登录\n\n";
        $content .= "```\nPOST /api/auth/logout\n```\n\n";

        return $content;
    }

    /**
     * 生成用户手册
     */
    protected function generateUserManual(array $documentAnalysis, array $tableInfo, string $format): array
    {
        $userDocs = [];
        $modelName = $this->getModelName($tableInfo['name']);

        // 用户手册概述
        $userDocs['docs/user/README.md'] = $this->generateUserManualOverview($modelName, $tableInfo);

        // 快速开始指南
        $userDocs['docs/user/getting-started.md'] = $this->generateGettingStarted($modelName, $tableInfo);

        // 详细使用指南
        $userDocs['docs/user/user-guide.md'] = $this->generateDetailedUserGuide($documentAnalysis, $tableInfo);

        // 常见问题
        $userDocs['docs/user/faq.md'] = $this->generateFAQ($modelName, $tableInfo);

        return $userDocs;
    }

    /**
     * 生成用户手册概述
     */
    protected function generateUserManualOverview(string $modelName, array $tableInfo): string
    {
        $content = "# {$modelName}管理 - 用户手册\n\n";
        $content .= "## 功能概述\n\n";
        $content .= "{$modelName}管理模块提供了完整的数据管理功能，包括：\n\n";
        $content .= "- 📋 数据列表查看和搜索\n";
        $content .= "- ➕ 新增{$modelName}记录\n";
        $content .= "- ✏️ 编辑现有记录\n";
        $content .= "- 🗑️ 删除不需要的记录\n";
        $content .= "- 📊 数据统计和分析\n\n";
        
        $content .= "## 界面说明\n\n";
        $content .= "### 主要界面\n\n";
        $content .= "1. **列表页面**: 显示所有{$modelName}记录，支持搜索、筛选和排序\n";
        $content .= "2. **详情页面**: 显示单个{$modelName}的详细信息\n";
        $content .= "3. **编辑页面**: 用于创建和修改{$modelName}记录\n\n";
        
        $content .= "### 操作按钮\n\n";
        $content .= "- **新增**: 创建新的{$modelName}记录\n";
        $content .= "- **编辑**: 修改现有记录\n";
        $content .= "- **删除**: 删除选中的记录\n";
        $content .= "- **查看**: 查看记录详情\n";
        $content .= "- **搜索**: 根据关键词搜索记录\n\n";
        
        $content .= "## 权限说明\n\n";
        $content .= "不同角色的用户具有不同的操作权限：\n\n";
        $content .= "- **管理员**: 拥有所有操作权限\n";
        $content .= "- **编辑者**: 可以查看、创建和编辑记录\n";
        $content .= "- **查看者**: 只能查看记录，不能修改\n\n";

        return $content;
    }

    /**
     * 生成开发者文档
     */
    protected function generateDeveloperDocuments(array $documentAnalysis, array $tableInfo, string $format): array
    {
        $devDocs = [];
        $modelName = $this->getModelName($tableInfo['name']);

        // 开发者文档概述
        $devDocs['docs/developer/README.md'] = $this->generateDeveloperOverview($modelName, $tableInfo);

        // 架构说明
        $devDocs['docs/developer/architecture.md'] = $this->generateArchitectureDoc($modelName, $tableInfo);

        // 代码结构
        $devDocs['docs/developer/code-structure.md'] = $this->generateCodeStructureDoc($modelName, $tableInfo);

        // 扩展指南
        $devDocs['docs/developer/extension-guide.md'] = $this->generateExtensionGuide($modelName, $tableInfo);

        return $devDocs;
    }

    /**
     * 生成部署文档
     */
    protected function generateDeploymentDocuments(array $documentAnalysis, array $tableInfo, string $format): array
    {
        $deployDocs = [];

        // 部署文档概述
        $deployDocs['docs/deployment/README.md'] = $this->generateDeploymentOverview();

        // 安装指南
        $deployDocs['docs/deployment/installation.md'] = $this->generateInstallationGuide();

        // 配置说明
        $deployDocs['docs/deployment/configuration.md'] = $this->generateConfigurationGuide();

        // 维护指南
        $deployDocs['docs/deployment/maintenance.md'] = $this->generateMaintenanceGuide();

        return $deployDocs;
    }

    /**
     * 生成主 README
     */
    protected function generateMainReadme(array $documentAnalysis, array $tableInfo, string $format): string
    {
        $modelName = $this->getModelName($tableInfo['name']);
        
        $content = "# {$modelName}管理系统\n\n";
        $content .= "## 项目简介\n\n";
        $content .= "这是一个基于 EasyAdmin8-webman 框架开发的{$modelName}管理系统，提供了完整的 CRUD 功能和 RESTful API 接口。\n\n";
        
        $content .= "## 主要特性\n\n";
        $content .= "- 🚀 **高性能**: 基于 Webman 框架，性能卓越\n";
        $content .= "- 🎨 **现代化界面**: 响应式设计，支持多设备访问\n";
        $content .= "- 🔐 **安全可靠**: 完善的权限控制和数据验证\n";
        $content .= "- 📱 **API 支持**: 完整的 RESTful API 接口\n";
        $content .= "- 🔧 **易于扩展**: 模块化设计，便于二次开发\n\n";
        
        $content .= "## 快速开始\n\n";
        $content .= "### 环境要求\n\n";
        $content .= "- PHP >= 8.0\n";
        $content .= "- MySQL >= 5.7\n";
        $content .= "- Composer\n\n";
        
        $content .= "### 安装步骤\n\n";
        $content .= "1. 克隆项目\n";
        $content .= "```bash\n";
        $content .= "git clone <repository-url>\n";
        $content .= "cd project-directory\n";
        $content .= "```\n\n";
        
        $content .= "2. 安装依赖\n";
        $content .= "```bash\n";
        $content .= "composer install\n";
        $content .= "```\n\n";
        
        $content .= "3. 配置环境\n";
        $content .= "```bash\n";
        $content .= "cp .env.example .env\n";
        $content .= "# 编辑 .env 文件，配置数据库等信息\n";
        $content .= "```\n\n";
        
        $content .= "4. 运行迁移\n";
        $content .= "```bash\n";
        $content .= "php webman migrate\n";
        $content .= "```\n\n";
        
        $content .= "5. 启动服务\n";
        $content .= "```bash\n";
        $content .= "php start.php start\n";
        $content .= "```\n\n";
        
        $content .= "## 文档\n\n";
        $content .= "- [API 文档](docs/api/README.md)\n";
        $content .= "- [用户手册](docs/user/README.md)\n";
        $content .= "- [开发者文档](docs/developer/README.md)\n";
        $content .= "- [部署指南](docs/deployment/README.md)\n\n";
        
        $content .= "## 许可证\n\n";
        $content .= "MIT License\n\n";

        return $content;
    }

    /**
     * 获取端点参数
     */
    protected function getEndpointParameters(string $operation, array $tableInfo): array
    {
        // 复用 DocumentAnalyzer 中的逻辑
        $analyzer = new \app\common\services\curd\v2\analyzers\DocumentAnalyzer();
        return $analyzer->getEndpointParameters($operation, $tableInfo);
    }

    /**
     * 生成响应示例
     */
    protected function generateResponseExample(string $operation, array $tableInfo): string
    {
        switch ($operation) {
            case 'index':
                return json_encode([
                    'code' => 0,
                    'message' => 'success',
                    'data' => [
                        'data' => [['id' => 1, '...']],
                        'meta' => [
                            'current_page' => 1,
                            'per_page' => 15,
                            'total' => 100,
                            'last_page' => 7
                        ]
                    ]
                ], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);

            case 'show':
                return json_encode([
                    'code' => 0,
                    'message' => 'success',
                    'data' => ['id' => 1, '...']
                ], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);

            case 'store':
                return json_encode([
                    'code' => 0,
                    'message' => '创建成功',
                    'data' => ['id' => 1, '...']
                ], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);

            case 'update':
                return json_encode([
                    'code' => 0,
                    'message' => '更新成功',
                    'data' => ['id' => 1, '...']
                ], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);

            case 'destroy':
                return json_encode([
                    'code' => 0,
                    'message' => '删除成功'
                ], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);

            default:
                return '{}';
        }
    }

    /**
     * 获取模型名称
     */
    protected function getModelName(string $tableName): string
    {
        $name = preg_replace('/^[a-z]+_/', '', $tableName);
        return str_replace(' ', '', ucwords(str_replace('_', ' ', $name)));
    }

    // 其他生成方法的简化实现
    protected function generateApiExamples(array $documentAnalysis, array $tableInfo): string { return "# API 示例\n\n待补充..."; }
    protected function generateOpenApiSpec(array $documentAnalysis, array $tableInfo): string { return "# OpenAPI 规范\n\n待补充..."; }
    protected function generateGettingStarted(string $modelName, array $tableInfo): string { return "# 快速开始\n\n待补充..."; }
    protected function generateDetailedUserGuide(array $documentAnalysis, array $tableInfo): string { return "# 详细使用指南\n\n待补充..."; }
    protected function generateFAQ(string $modelName, array $tableInfo): string { return "# 常见问题\n\n待补充..."; }
    protected function generateDeveloperOverview(string $modelName, array $tableInfo): string { return "# 开发者文档\n\n待补充..."; }
    protected function generateArchitectureDoc(string $modelName, array $tableInfo): string { return "# 架构说明\n\n待补充..."; }
    protected function generateCodeStructureDoc(string $modelName, array $tableInfo): string { return "# 代码结构\n\n待补充..."; }
    protected function generateExtensionGuide(string $modelName, array $tableInfo): string { return "# 扩展指南\n\n待补充..."; }
    protected function generateDeploymentOverview(): string { return "# 部署文档\n\n待补充..."; }
    protected function generateInstallationGuide(): string { return "# 安装指南\n\n待补充..."; }
    protected function generateConfigurationGuide(): string { return "# 配置说明\n\n待补充..."; }
    protected function generateMaintenanceGuide(): string { return "# 维护指南\n\n待补充..."; }
}
