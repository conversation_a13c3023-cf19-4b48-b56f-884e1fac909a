<?php
/**
 * 简化的Webman模拟类
 * 避免类冲突，提供基本功能
 */

// 简单的模拟类，不使用命名空间避免冲突
class WebmanRouteMock {
    private static $routes = [];

    public static function get($path, $handler) {
        self::$routes['GET'][$path] = $handler;
        return new self();
    }

    public static function post($path, $handler) {
        self::$routes['POST'][$path] = $handler;
        return new self();
    }

    public static function put($path, $handler) {
        self::$routes['PUT'][$path] = $handler;
        return new self();
    }

    public static function delete($path, $handler) {
        self::$routes['DELETE'][$path] = $handler;
        return new self();
    }

    public static function any($path, $handler) {
        foreach (['GET', 'POST', 'PUT', 'DELETE'] as $method) {
            self::$routes[$method][$path] = $handler;
        }
        return new self();
    }

    public static function group($prefix, $callback) {
        if (is_callable($callback)) {
            $callback();
        }
        return new self();
    }

    public static function getRoutes() {
        return self::$routes;
    }
}

class WebmanAppMock {
    public static function run() {
        echo "Webman App Mock Running...\n";
    }
}

class RequestMock {
    private $data = [];

    public function __construct($data = []) {
        $this->data = $data;
    }

    public function get($key = null, $default = null) {
        if ($key === null) {
            return $_GET ?? [];
        }
        return $this->data[$key] ?? $_GET[$key] ?? $default;
    }

    public function post($key = null, $default = null) {
        if ($key === null) {
            return $_POST ?? [];
        }
        return $this->data[$key] ?? $_POST[$key] ?? $default;
    }

    public function all() {
        return array_merge($_GET ?? [], $_POST ?? [], $this->data);
    }

    public function method() {
        return $_SERVER['REQUEST_METHOD'] ?? 'GET';
    }

    public function path() {
        return $_SERVER['REQUEST_URI'] ?? '/';
    }
}

class ResponseMock {
    private $content;
    private $status;
    private $headers;

    public function __construct($content = '', $status = 200, $headers = []) {
        $this->content = $content;
        $this->status = $status;
        $this->headers = $headers;
    }

    public function getContent() {
        return $this->content;
    }

    public function getStatusCode() {
        return $this->status;
    }

    public function getHeaders() {
        return $this->headers;
    }
}

// 创建别名，只有在不存在时才创建
if (!class_exists('Webman\\Route')) {
    class_alias('WebmanRouteMock', 'Webman\\Route');
}

if (!class_exists('Webman\\App')) {
    class_alias('WebmanAppMock', 'Webman\\App');
}

// 对于support命名空间，我们不创建别名，而是在需要时直接使用Mock类
// 这样避免与原生类冲突

// 创建全局函数
if (!function_exists('response')) {
    function response($content = '', $status = 200, $headers = []) {
        return new ResponseMock($content, $status, $headers);
    }
}

if (!function_exists('view')) {
    function view($template, $data = []) {
        return new ResponseMock("View: {$template}");
    }
}
