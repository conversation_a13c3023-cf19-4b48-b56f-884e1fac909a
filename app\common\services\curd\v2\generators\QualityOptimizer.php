<?php

namespace app\common\services\curd\v2\generators;

/**
 * 代码质量优化器
 * 根据质量分析结果自动优化代码
 */
class QualityOptimizer
{
    protected array $optimizationRules = [
        'naming_convention' => 'optimizeNamingConvention',
        'code_complexity' => 'optimizeCodeComplexity',
        'security_issues' => 'optimizeSecurityIssues',
        'performance_issues' => 'optimizePerformanceIssues',
        'best_practices' => 'optimizeBestPractices',
        'documentation' => 'optimizeDocumentation',
    ];

    /**
     * 自动优化代码
     */
    public function optimizeCode(array $generatedCode, array $qualityAnalysis): array
    {
        $optimizedCode = $generatedCode;
        $optimizationLog = [];

        foreach ($qualityAnalysis['issues'] as $issue) {
            $optimizationMethod = $this->optimizationRules[$issue['type']] ?? null;
            
            if ($optimizationMethod && method_exists($this, $optimizationMethod)) {
                $result = $this->$optimizationMethod($optimizedCode, $issue);
                
                if ($result['optimized']) {
                    $optimizedCode = $result['code'];
                    $optimizationLog[] = [
                        'issue' => $issue,
                        'optimization' => $result['description'],
                        'impact' => $result['impact'],
                    ];
                }
            }
        }

        return [
            'optimized_code' => $optimizedCode,
            'optimization_log' => $optimizationLog,
            'improvement_summary' => $this->generateImprovementSummary($optimizationLog),
        ];
    }

    /**
     * 优化命名规范
     */
    protected function optimizeNamingConvention(array $code, array $issue): array
    {
        $optimized = false;
        $description = '';
        $impact = 'low';

        foreach ($code as $type => $content) {
            if (!is_string($content)) continue;

            // 修复类名命名
            $originalContent = $content;
            $content = preg_replace_callback(
                '/class\s+([a-z][a-zA-Z0-9]*)/',
                function($matches) {
                    return 'class ' . ucfirst($matches[1]);
                },
                $content
            );

            // 修复方法名命名
            $content = preg_replace_callback(
                '/public\s+function\s+([A-Z][a-zA-Z0-9]*)/',
                function($matches) {
                    return 'public function ' . lcfirst($matches[1]);
                },
                $content
            );

            if ($content !== $originalContent) {
                $code[$type] = $content;
                $optimized = true;
                $description = '自动修复了命名规范问题';
                $impact = 'medium';
            }
        }

        return [
            'optimized' => $optimized,
            'code' => $code,
            'description' => $description,
            'impact' => $impact,
        ];
    }

    /**
     * 优化代码复杂度
     */
    protected function optimizeCodeComplexity(array $code, array $issue): array
    {
        $optimized = false;
        $description = '';
        $impact = 'high';

        foreach ($code as $type => $content) {
            if (!is_string($content)) continue;

            $originalContent = $content;

            // 提取长方法
            $content = $this->extractLongMethods($content);

            // 减少嵌套层级
            $content = $this->reduceNesting($content);

            if ($content !== $originalContent) {
                $code[$type] = $content;
                $optimized = true;
                $description = '自动重构了复杂的方法，提高了代码可读性';
            }
        }

        return [
            'optimized' => $optimized,
            'code' => $code,
            'description' => $description,
            'impact' => $impact,
        ];
    }

    /**
     * 优化安全问题
     */
    protected function optimizeSecurityIssues(array $code, array $issue): array
    {
        $optimized = false;
        $description = '';
        $impact = 'critical';

        foreach ($code as $type => $content) {
            if (!is_string($content)) continue;

            $originalContent = $content;

            // 修复 SQL 注入风险
            $content = $this->fixSqlInjection($content);

            // 修复 XSS 风险
            $content = $this->fixXssVulnerability($content);

            // 添加权限检查
            $content = $this->addPermissionChecks($content);

            if ($content !== $originalContent) {
                $code[$type] = $content;
                $optimized = true;
                $description = '自动修复了安全漏洞，增强了代码安全性';
            }
        }

        return [
            'optimized' => $optimized,
            'code' => $code,
            'description' => $description,
            'impact' => $impact,
        ];
    }

    /**
     * 优化性能问题
     */
    protected function optimizePerformanceIssues(array $code, array $issue): array
    {
        $optimized = false;
        $description = '';
        $impact = 'high';

        foreach ($code as $type => $content) {
            if (!is_string($content)) continue;

            $originalContent = $content;

            // 添加查询优化
            $content = $this->optimizeQueries($content);

            // 添加缓存机制
            $content = $this->addCaching($content);

            // 优化循环
            $content = $this->optimizeLoops($content);

            if ($content !== $originalContent) {
                $code[$type] = $content;
                $optimized = true;
                $description = '自动优化了性能瓶颈，提高了执行效率';
            }
        }

        return [
            'optimized' => $optimized,
            'code' => $code,
            'description' => $description,
            'impact' => $impact,
        ];
    }

    /**
     * 优化最佳实践
     */
    protected function optimizeBestPractices(array $code, array $issue): array
    {
        $optimized = false;
        $description = '';
        $impact = 'medium';

        foreach ($code as $type => $content) {
            if (!is_string($content)) continue;

            $originalContent = $content;

            // 添加异常处理
            $content = $this->addExceptionHandling($content);

            // 添加日志记录
            $content = $this->addLogging($content);

            // 添加验证规则
            $content = $this->addValidationRules($content);

            if ($content !== $originalContent) {
                $code[$type] = $content;
                $optimized = true;
                $description = '自动应用了最佳实践，提高了代码质量';
            }
        }

        return [
            'optimized' => $optimized,
            'code' => $code,
            'description' => $description,
            'impact' => $impact,
        ];
    }

    /**
     * 优化文档
     */
    protected function optimizeDocumentation(array $code, array $issue): array
    {
        $optimized = false;
        $description = '';
        $impact = 'low';

        foreach ($code as $type => $content) {
            if (!is_string($content)) continue;

            $originalContent = $content;

            // 添加类注释
            $content = $this->addClassDocumentation($content);

            // 添加方法注释
            $content = $this->addMethodDocumentation($content);

            // 添加属性注释
            $content = $this->addPropertyDocumentation($content);

            if ($content !== $originalContent) {
                $code[$type] = $content;
                $optimized = true;
                $description = '自动添加了代码注释，提高了代码可维护性';
            }
        }

        return [
            'optimized' => $optimized,
            'code' => $code,
            'description' => $description,
            'impact' => $impact,
        ];
    }

    /**
     * 提取长方法
     */
    protected function extractLongMethods(string $code): string
    {
        // 简化实现：在长方法中添加注释提示
        return preg_replace_callback(
            '/(public\s+function\s+\w+.*?\{)(.*?)(\n\s*\})/s',
            function($matches) {
                $methodBody = $matches[2];
                $lineCount = substr_count($methodBody, "\n");
                
                if ($lineCount > 50) {
                    $comment = "\n        // TODO: 考虑将此方法拆分为多个小方法以提高可读性\n";
                    return $matches[1] . $comment . $matches[2] . $matches[3];
                }
                
                return $matches[0];
            },
            $code
        );
    }

    /**
     * 减少嵌套层级
     */
    protected function reduceNesting(string $code): string
    {
        // 简化实现：添加早期返回的建议注释
        return preg_replace(
            '/(\s+if\s*\([^)]+\)\s*\{[^}]*if\s*\([^)]+\)\s*\{)/',
            '$1' . "\n            // 建议使用早期返回减少嵌套层级\n",
            $code
        );
    }

    /**
     * 修复 SQL 注入
     */
    protected function fixSqlInjection(string $code): string
    {
        // 将字符串拼接的查询替换为参数化查询的建议
        return preg_replace(
            '/(\$.*\s*\.\s*["\'].*["\'])/',
            '// 建议使用参数化查询: $query->where("field", $value)',
            $code
        );
    }

    /**
     * 修复 XSS 漏洞
     */
    protected function fixXssVulnerability(string $code): string
    {
        // 在直接输出的地方添加转义建议
        return preg_replace(
            '/(echo\s+\$[^;]+;)/',
            '$1 // 建议使用 htmlspecialchars() 转义输出',
            $code
        );
    }

    /**
     * 添加权限检查
     */
    protected function addPermissionChecks(string $code): string
    {
        // 在敏感方法开头添加权限检查注释
        return preg_replace(
            '/(public\s+function\s+(store|update|destroy)\s*\([^)]*\)\s*\{)/',
            '$1' . "\n        // 建议添加权限检查: \$this->authorize('action', \$model);\n",
            $code
        );
    }

    /**
     * 优化查询
     */
    protected function optimizeQueries(string $code): string
    {
        // 添加预加载建议
        return preg_replace(
            '/(\$.*->paginate\([^)]*\))/',
            '$1 // 建议使用 with() 预加载关联关系',
            $code
        );
    }

    /**
     * 添加缓存
     */
    protected function addCaching(string $code): string
    {
        // 在查询方法中添加缓存建议
        return preg_replace(
            '/(public\s+function\s+index\s*\([^)]*\)\s*\{)/',
            '$1' . "\n        // 建议添加缓存: Cache::remember('key', 3600, function() { ... });\n",
            $code
        );
    }

    /**
     * 优化循环
     */
    protected function optimizeLoops(string $code): string
    {
        // 在循环中添加优化建议
        return preg_replace(
            '/(foreach\s*\([^)]+\)\s*\{)/',
            '$1' . "\n            // 建议避免在循环中执行数据库查询\n",
            $code
        );
    }

    /**
     * 添加异常处理
     */
    protected function addExceptionHandling(string $code): string
    {
        // 在没有 try-catch 的方法中添加建议
        return preg_replace_callback(
            '/(public\s+function\s+\w+.*?\{)(.*?)(\n\s*\})/s',
            function($matches) {
                $methodBody = $matches[2];
                if (strpos($methodBody, 'try') === false) {
                    $suggestion = "\n        // 建议添加异常处理: try { ... } catch (\\Exception \$e) { ... }\n";
                    return $matches[1] . $suggestion . $matches[2] . $matches[3];
                }
                return $matches[0];
            },
            $code
        );
    }

    /**
     * 添加日志记录
     */
    protected function addLogging(string $code): string
    {
        // 在 catch 块中添加日志建议
        return preg_replace(
            '/(catch\s*\([^)]+\)\s*\{)/',
            '$1' . "\n            // 建议记录错误日志: Log::error(\$e->getMessage());\n",
            $code
        );
    }

    /**
     * 添加验证规则
     */
    protected function addValidationRules(string $code): string
    {
        // 在 store/update 方法中添加验证建议
        return preg_replace(
            '/(public\s+function\s+(store|update)\s*\([^)]*\)\s*\{)/',
            '$1' . "\n        // 建议添加数据验证: \$this->validate(\$request, [...]);\n",
            $code
        );
    }

    /**
     * 添加类注释
     */
    protected function addClassDocumentation(string $code): string
    {
        return preg_replace(
            '/(class\s+\w+[^{]*\{)/',
            "/**\n * 自动生成的类\n * TODO: 添加类的详细描述\n */\n$1",
            $code
        );
    }

    /**
     * 添加方法注释
     */
    protected function addMethodDocumentation(string $code): string
    {
        return preg_replace(
            '/(public\s+function\s+(\w+)\s*\([^)]*\))/',
            "    /**\n     * $2 方法\n     * TODO: 添加方法描述和参数说明\n     */\n    $1",
            $code
        );
    }

    /**
     * 添加属性注释
     */
    protected function addPropertyDocumentation(string $code): string
    {
        return preg_replace(
            '/(protected\s+\$\w+[^;]*;)/',
            "    /** TODO: 添加属性描述 */\n    $1",
            $code
        );
    }

    /**
     * 生成改进总结
     */
    protected function generateImprovementSummary(array $optimizationLog): array
    {
        $totalOptimizations = count($optimizationLog);
        $impactCounts = array_count_values(array_column($optimizationLog, 'impact'));
        
        $criticalImprovements = $impactCounts['critical'] ?? 0;
        $highImprovements = $impactCounts['high'] ?? 0;
        $mediumImprovements = $impactCounts['medium'] ?? 0;
        $lowImprovements = $impactCounts['low'] ?? 0;

        return [
            'total_optimizations' => $totalOptimizations,
            'critical_improvements' => $criticalImprovements,
            'high_improvements' => $highImprovements,
            'medium_improvements' => $mediumImprovements,
            'low_improvements' => $lowImprovements,
            'improvement_categories' => $this->getImprovementCategories($optimizationLog),
            'quality_boost_estimate' => $this->estimateQualityBoost($optimizationLog),
        ];
    }

    /**
     * 获取改进分类
     */
    protected function getImprovementCategories(array $optimizationLog): array
    {
        $categories = [];
        foreach ($optimizationLog as $log) {
            $type = $log['issue']['type'];
            if (!isset($categories[$type])) {
                $categories[$type] = 0;
            }
            $categories[$type]++;
        }
        return $categories;
    }

    /**
     * 估算质量提升
     */
    protected function estimateQualityBoost(array $optimizationLog): int
    {
        $boost = 0;
        foreach ($optimizationLog as $log) {
            switch ($log['impact']) {
                case 'critical':
                    $boost += 15;
                    break;
                case 'high':
                    $boost += 10;
                    break;
                case 'medium':
                    $boost += 5;
                    break;
                case 'low':
                    $boost += 2;
                    break;
            }
        }
        return min(50, $boost); // 最多提升50分
    }
}
