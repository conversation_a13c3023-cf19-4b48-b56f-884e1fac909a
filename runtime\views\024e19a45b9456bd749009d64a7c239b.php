<?php echo $__env->make('admin.layout.head', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
<link rel="stylesheet" href="/static/admin/css/login.css?v=<?php echo e($version); ?>" media="all">
<div class="container">
    <div class="main-body">
        <div class="login-main">
            <div class="login-top">
                <span><?php echo e(sysconfig('site','site_name')); ?></span>
                <span class="bg1"></span>
                <span class="bg2"></span>
            </div>
            <form class="layui-form login-bottom">
                <div class="demo <?php if(!$isDemo): ?>layui-hide;<?php endif; ?>">用户名:admin 密码:123456</div>
                <div class="center">

                    <div class="item">
                        <span class="icon icon-2"></span>
                        <input type="text" name="username" lay-verify="required" placeholder="请输入登录账号" maxlength="24"/>
                    </div>

                    <div class="item">
                        <span class="icon icon-3"></span>
                        <input type="password" name="password" lay-verify="required" placeholder="请输入密码" maxlength="20">
                        <span class="bind-password icon icon-4"></span>
                    </div>

                    <div class="item layui-hide" id="gaCode">
                        <span class="icon icon-3"></span>
                        <input type="text" name="ga_code" placeholder="谷歌验证码" maxlength="6">
                    </div>

                    <?php if($captcha == 1): ?>
                        <div id="validatePanel" class="item" style="width: 137px;">
                            <input type="text" name="captcha" placeholder="请输入验证码" maxlength="4">
                            <img id="refreshCaptcha" class="validateImg" src="<?php echo e(__url('login/captcha')); ?>" onclick="this.src='<?php echo e(__url('login/captcha')); ?>?seed='+Math.random()">
                        </div>
                    <?php endif; ?>
                </div>
                <div class="tip">
                    <span class="icon-nocheck"></span>
                    <span class="login-tip">保持登录</span>
                    <a href="javascript:" class="forget-password">忘记密码？</a>
                </div>
                <div class="layui-form-item" style="text-align:center; width:100%;height:100%;margin:0px;">
                    <button type="button" class="login-btn" lay-submit>立即登录</button>
                </div>
            </form>
        </div>
    </div>
    <div class="footer">
        <?php echo e(sysconfig('site','site_copyright')); ?>

        <span class="padding-5">|</span>
        <a target="_blank" href="https://beian.miit.gov.cn">
            <?php echo e(sysconfig('site','site_beian')); ?>

        </a>
    </div>
</div>
<script>
    let backgroundUrl = "<?php echo e(sysconfig('site','admin_background')); ?>"
</script>
<?php echo $__env->make('admin.layout.foot', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
<?php /**PATH D:\wwwroot\127.0.0.1\EasyAdmin8-webman\app\admin\view/admin\login\index.blade.php ENDPATH**/ ?>