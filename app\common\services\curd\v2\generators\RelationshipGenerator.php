<?php

namespace app\common\services\curd\v2\generators;

/**
 * 关联关系代码生成器
 * 根据分析结果生成关联关系代码
 */
class RelationshipGenerator
{
    /**
     * 生成模型关联方法代码
     */
    public function generateModelRelationships(array $relationships): string
    {
        if (empty($relationships)) {
            return '';
        }

        $code = "\n    /**\n     * 关联关系\n     */\n";

        foreach ($relationships as $relationship) {
            $code .= $this->generateRelationshipMethod($relationship);
        }

        return $code;
    }

    /**
     * 生成单个关联方法
     */
    protected function generateRelationshipMethod(array $relationship): string
    {
        $methodName = $relationship['method_name'];
        $description = $relationship['description'];
        $confidence = $relationship['confidence'];

        $code = "\n    /**\n";
        $code .= "     * {$description}\n";
        $code .= "     * 置信度: {$confidence}%\n";
        $code .= "     */\n";
        $code .= "    public function {$methodName}()\n";
        $code .= "    {\n";

        switch ($relationship['type']) {
            case 'belongsTo':
                $code .= $this->generateBelongsToMethod($relationship);
                break;
            case 'hasOne':
                $code .= $this->generateHasOneMethod($relationship);
                break;
            case 'hasMany':
                $code .= $this->generateHasManyMethod($relationship);
                break;
            case 'belongsToMany':
                $code .= $this->generateBelongsToManyMethod($relationship);
                break;
        }

        $code .= "    }\n";

        return $code;
    }

    /**
     * 生成 belongsTo 方法
     */
    protected function generateBelongsToMethod(array $relationship): string
    {
        $relatedModel = $relationship['related_model'];
        $foreignKey = $relationship['foreign_key'];
        $ownerKey = $relationship['owner_key'];

        $code = "        return \$this->belongsTo({$relatedModel}::class";

        // 如果外键不是默认的，添加参数
        if ($foreignKey !== $this->getDefaultForeignKey($relatedModel)) {
            $code .= ", '{$foreignKey}'";
        }

        // 如果主键不是默认的，添加参数
        if ($ownerKey !== 'id') {
            $code .= ", '{$ownerKey}'";
        }

        $code .= ");\n";

        return $code;
    }

    /**
     * 生成 hasOne 方法
     */
    protected function generateHasOneMethod(array $relationship): string
    {
        $relatedModel = $relationship['related_model'];
        $foreignKey = $relationship['foreign_key'];
        $localKey = $relationship['local_key'];

        $code = "        return \$this->hasOne({$relatedModel}::class";

        // 如果外键不是默认的，添加参数
        if ($foreignKey !== $this->getDefaultForeignKey($this->getCurrentModelName())) {
            $code .= ", '{$foreignKey}'";
        }

        // 如果本地键不是默认的，添加参数
        if ($localKey !== 'id') {
            $code .= ", '{$localKey}'";
        }

        $code .= ");\n";

        return $code;
    }

    /**
     * 生成 hasMany 方法
     */
    protected function generateHasManyMethod(array $relationship): string
    {
        $relatedModel = $relationship['related_model'];
        $foreignKey = $relationship['foreign_key'];
        $localKey = $relationship['local_key'];

        $code = "        return \$this->hasMany({$relatedModel}::class";

        // 如果外键不是默认的，添加参数
        if ($foreignKey !== $this->getDefaultForeignKey($this->getCurrentModelName())) {
            $code .= ", '{$foreignKey}'";
        }

        // 如果本地键不是默认的，添加参数
        if ($localKey !== 'id') {
            $code .= ", '{$localKey}'";
        }

        $code .= ");\n";

        return $code;
    }

    /**
     * 生成 belongsToMany 方法
     */
    protected function generateBelongsToManyMethod(array $relationship): string
    {
        $relatedModel = $relationship['related_model'];
        $pivotTable = $relationship['pivot_table'];
        $foreignPivotKey = $relationship['foreign_pivot_key'];
        $relatedPivotKey = $relationship['related_pivot_key'];
        $parentKey = $relationship['parent_key'];
        $relatedKey = $relationship['related_key'];

        $code = "        return \$this->belongsToMany({$relatedModel}::class";

        // 添加中间表
        $code .= ", '{$pivotTable}'";

        // 添加外键
        $code .= ", '{$foreignPivotKey}', '{$relatedPivotKey}'";

        // 如果主键不是默认的，添加参数
        if ($parentKey !== 'id' || $relatedKey !== 'id') {
            $code .= ", '{$parentKey}', '{$relatedKey}'";
        }

        $code .= ");\n";

        return $code;
    }

    /**
     * 生成控制器关联查询代码
     */
    public function generateControllerRelationships(array $relationships): array
    {
        $eagerLoading = [];
        $relationshipMethods = [];

        foreach ($relationships as $relationship) {
            $methodName = $relationship['method_name'];
            
            // 添加预加载
            if ($relationship['confidence'] >= 80) {
                $eagerLoading[] = $methodName;
            }

            // 生成关联查询方法
            $relationshipMethods[] = $this->generateControllerRelationshipMethod($relationship);
        }

        return [
            'eager_loading' => $eagerLoading,
            'methods' => $relationshipMethods,
        ];
    }

    /**
     * 生成控制器关联查询方法
     */
    protected function generateControllerRelationshipMethod(array $relationship): string
    {
        $methodName = $relationship['method_name'];
        $relatedTable = $relationship['related_table'];
        $description = $relationship['description'];

        $code = "\n    /**\n";
        $code .= "     * 获取关联的{$description}\n";
        $code .= "     */\n";
        $code .= "    public function get" . ucfirst($methodName) . "(Request \$request): Response\n";
        $code .= "    {\n";
        $code .= "        \$id = \$request->input('id');\n";
        $code .= "        \$model = \$this->model->find(\$id);\n";
        $code .= "        \n";
        $code .= "        if (!\$model) {\n";
        $code .= "            return \$this->error('数据不存在');\n";
        $code .= "        }\n";
        $code .= "        \n";

        switch ($relationship['type']) {
            case 'belongsTo':
            case 'hasOne':
                $code .= "        \$related = \$model->{$methodName};\n";
                $code .= "        return \$this->success('获取成功', \$related);\n";
                break;
            case 'hasMany':
            case 'belongsToMany':
                $code .= "        \$related = \$model->{$methodName}()->paginate(15);\n";
                $code .= "        return \$this->success('获取成功', \$related);\n";
                break;
        }

        $code .= "    }\n";

        return $code;
    }

    /**
     * 生成视图关联显示代码
     */
    public function generateViewRelationships(array $relationships): array
    {
        $listColumns = [];
        $formFields = [];
        $detailSections = [];

        foreach ($relationships as $relationship) {
            if ($relationship['confidence'] < 70) {
                continue; // 跳过低置信度的关系
            }

            switch ($relationship['type']) {
                case 'belongsTo':
                    $listColumns[] = $this->generateBelongsToListColumn($relationship);
                    $formFields[] = $this->generateBelongsToFormField($relationship);
                    break;
                case 'hasOne':
                    $detailSections[] = $this->generateHasOneDetailSection($relationship);
                    break;
                case 'hasMany':
                case 'belongsToMany':
                    $detailSections[] = $this->generateHasManyDetailSection($relationship);
                    break;
            }
        }

        return [
            'list_columns' => $listColumns,
            'form_fields' => $formFields,
            'detail_sections' => $detailSections,
        ];
    }

    /**
     * 生成 belongsTo 列表列
     */
    protected function generateBelongsToListColumn(array $relationship): array
    {
        $methodName = $relationship['method_name'];
        $foreignKey = $relationship['foreign_key'];
        $relatedTable = $relationship['related_table'];

        return [
            'field' => $methodName,
            'title' => $relationship['description'],
            'templet' => "function(d) { return d.{$methodName} ? d.{$methodName}.name || d.{$methodName}.title || d.{$methodName}.id : '-'; }",
            'width' => 120,
        ];
    }

    /**
     * 生成 belongsTo 表单字段
     */
    protected function generateBelongsToFormField(array $relationship): array
    {
        $methodName = $relationship['method_name'];
        $foreignKey = $relationship['foreign_key'];
        $relatedTable = $relationship['related_table'];

        return [
            'name' => $foreignKey,
            'label' => $relationship['description'],
            'type' => 'select',
            'options_url' => "/admin/api/{$relatedTable}/options",
            'required' => false,
        ];
    }

    /**
     * 生成 hasOne 详情区域
     */
    protected function generateHasOneDetailSection(array $relationship): array
    {
        $methodName = $relationship['method_name'];
        $relatedTable = $relationship['related_table'];

        return [
            'title' => $relationship['description'],
            'type' => 'hasOne',
            'method' => $methodName,
            'template' => 'detail_has_one',
        ];
    }

    /**
     * 生成 hasMany 详情区域
     */
    protected function generateHasManyDetailSection(array $relationship): array
    {
        $methodName = $relationship['method_name'];
        $relatedTable = $relationship['related_table'];

        return [
            'title' => $relationship['description'],
            'type' => 'hasMany',
            'method' => $methodName,
            'template' => 'detail_has_many',
            'ajax_url' => "/admin/api/{$relatedTable}/by_parent",
        ];
    }

    /**
     * 获取默认外键名
     */
    protected function getDefaultForeignKey(string $modelName): string
    {
        return strtolower($modelName) . '_id';
    }

    /**
     * 获取当前模型名 (需要在实际使用时设置)
     */
    protected function getCurrentModelName(): string
    {
        return 'Model'; // 这里应该从上下文获取
    }

    /**
     * 生成关联关系配置数组
     */
    public function generateRelationshipConfig(array $relationships): array
    {
        $config = [
            'relationships' => [],
            'eager_loading' => [],
            'form_relations' => [],
            'list_relations' => [],
        ];

        foreach ($relationships as $relationship) {
            // 基本关系配置
            $config['relationships'][] = [
                'method' => $relationship['method_name'],
                'type' => $relationship['type'],
                'model' => $relationship['related_model'],
                'confidence' => $relationship['confidence'],
            ];

            // 预加载配置
            if ($relationship['confidence'] >= 80) {
                $config['eager_loading'][] = $relationship['method_name'];
            }

            // 表单关联配置
            if ($relationship['type'] === 'belongsTo' && $relationship['confidence'] >= 70) {
                $config['form_relations'][] = [
                    'field' => $relationship['foreign_key'],
                    'method' => $relationship['method_name'],
                    'type' => 'select',
                ];
            }

            // 列表关联配置
            if (in_array($relationship['type'], ['belongsTo']) && $relationship['confidence'] >= 70) {
                $config['list_relations'][] = [
                    'field' => $relationship['method_name'],
                    'type' => $relationship['type'],
                ];
            }
        }

        return $config;
    }
}
