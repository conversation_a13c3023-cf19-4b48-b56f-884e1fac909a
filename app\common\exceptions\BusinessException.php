<?php

namespace app\common\exceptions;

use Exception;

/**
 * 业务异常基类
 */
class BusinessException extends Exception
{
    protected array $context = [];

    public function __construct(string $message = "", int $code = 0, array $context = [], Exception $previous = null)
    {
        parent::__construct($message, $code, $previous);
        $this->context = $context;
    }

    public function getContext(): array
    {
        return $this->context;
    }

    public function setContext(array $context): self
    {
        $this->context = $context;
        return $this;
    }
}

/**
 * 数据验证异常
 */
class ValidationException extends BusinessException
{
    protected array $errors = [];

    public function __construct(string $message = "数据验证失败", array $errors = [], int $code = 422)
    {
        parent::__construct($message, $code);
        $this->errors = $errors;
    }

    public function getErrors(): array
    {
        return $this->errors;
    }
}

/**
 * 模型操作异常
 */
class ModelException extends BusinessException
{
    public function __construct(string $message = "数据操作失败", int $code = 500, array $context = [])
    {
        parent::__construct($message, $code, $context);
    }
}

/**
 * 权限异常
 */
class AuthException extends BusinessException
{
    public function __construct(string $message = "权限不足", int $code = 403, array $context = [])
    {
        parent::__construct($message, $code, $context);
    }
}

/**
 * 文件操作异常
 */
class FileException extends BusinessException
{
    public function __construct(string $message = "文件操作失败", int $code = 500, array $context = [])
    {
        parent::__construct($message, $code, $context);
    }
}

/**
 * 配置异常
 */
class ConfigException extends BusinessException
{
    public function __construct(string $message = "配置错误", int $code = 500, array $context = [])
    {
        parent::__construct($message, $code, $context);
    }
}
