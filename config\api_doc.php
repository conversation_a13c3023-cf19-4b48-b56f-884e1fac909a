<?php
/**
 * API文档配置文件
 * 用于配置API文档生成和展示的各种参数
 */

return [
    // 基础配置
    'basic' => [
        // 是否启用API文档功能
        'enabled' => true,
        
        // API文档版本
        'version' => '1.0.0',
        
        // API基础URL
        'base_url' => '/api',
        
        // API文档标题
        'title' => 'EasyAdmin8-webman API文档',
        
        // API文档描述
        'description' => '基于EasyAdmin8-webman框架自动生成的RESTful API接口文档',
        
        // 联系信息
        'contact' => [
            'name' => 'API支持团队',
            'email' => '<EMAIL>',
            'url' => 'https://example.com/support'
        ],
        
        // 许可证信息
        'license' => [
            'name' => 'MIT License',
            'url' => 'https://opensource.org/licenses/MIT'
        ]
    ],

    // 生成配置
    'generation' => [
        // 默认生成选项
        'default_options' => [
            'include_validation' => true,      // 包含参数验证
            'include_pagination' => true,     // 包含分页功能
            'include_search' => true,         // 包含搜索功能
            'include_filter' => true,         // 包含筛选功能
            'include_sort' => true,           // 包含排序功能
            'include_export' => false,        // 包含导出功能
            'include_batch' => false,         // 包含批量操作
            'include_cache' => true,          // 包含缓存机制
            'include_rate_limit' => true,     // 包含频率限制
            'include_auth' => true,           // 包含认证机制
        ],
        
        // 支持的HTTP方法
        'http_methods' => ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'],
        
        // 默认响应格式
        'response_format' => 'json',
        
        // 默认认证方式
        'auth_type' => 'jwt',
        
        // 支持的认证方式
        'auth_types' => [
            'none' => '无认证',
            'jwt' => 'JWT Token',
            'oauth2' => 'OAuth2',
            'api_key' => 'API Key',
            'basic' => 'Basic Auth'
        ],
        
        // 自动生成的接口
        'auto_endpoints' => [
            'list' => true,        // 列表接口
            'detail' => true,      // 详情接口
            'create' => true,      // 创建接口
            'update' => true,      // 更新接口
            'delete' => true,      // 删除接口
            'batch_delete' => false, // 批量删除接口
            'export' => false,     // 导出接口
            'import' => false,     // 导入接口
        ]
    ],

    // 导出配置
    'export' => [
        // 支持的导出格式
        'formats' => [
            'html' => [
                'name' => 'HTML格式',
                'extension' => '.html',
                'mime_type' => 'text/html',
                'template' => 'api_doc_html'
            ],
            'markdown' => [
                'name' => 'Markdown格式',
                'extension' => '.md',
                'mime_type' => 'text/markdown',
                'template' => 'api_doc_markdown'
            ],
            'json' => [
                'name' => 'JSON格式',
                'extension' => '.json',
                'mime_type' => 'application/json',
                'template' => 'api_doc_json'
            ],
            'pdf' => [
                'name' => 'PDF格式',
                'extension' => '.pdf',
                'mime_type' => 'application/pdf',
                'template' => 'api_doc_pdf',
                'enabled' => false  // PDF导出需要额外配置
            ],
            'postman' => [
                'name' => 'Postman集合',
                'extension' => '.json',
                'mime_type' => 'application/json',
                'template' => 'postman_collection'
            ],
            'swagger' => [
                'name' => 'Swagger文档',
                'extension' => '.yaml',
                'mime_type' => 'application/x-yaml',
                'template' => 'swagger_yaml'
            ]
        ],
        
        // 导出文件存储路径
        'storage_path' => 'storage/api_docs',
        
        // 导出文件URL前缀
        'url_prefix' => '/downloads/api-docs',
        
        // 导出文件保留天数
        'retention_days' => 7
    ],

    // 测试配置
    'testing' => [
        // 是否启用在线测试
        'enabled' => true,
        
        // 测试环境配置
        'environments' => [
            'development' => [
                'name' => '开发环境',
                'base_url' => 'http://localhost:8787',
                'default' => true
            ],
            'staging' => [
                'name' => '测试环境',
                'base_url' => 'https://staging-api.example.com',
                'default' => false
            ],
            'production' => [
                'name' => '生产环境',
                'base_url' => 'https://api.example.com',
                'default' => false
            ]
        ],
        
        // 测试请求超时时间（秒）
        'timeout' => 30,
        
        // 测试请求重试次数
        'retry_times' => 3,
        
        // 是否记录测试历史
        'log_history' => true,
        
        // 测试历史保留天数
        'history_retention_days' => 30
    ],

    // 界面配置
    'ui' => [
        // 主题配置
        'theme' => [
            'primary_color' => '#007bff',
            'success_color' => '#28a745',
            'info_color' => '#17a2b8',
            'warning_color' => '#ffc107',
            'danger_color' => '#dc3545',
            'dark_mode' => false
        ],
        
        // 分页配置
        'pagination' => [
            'per_page' => 20,
            'max_per_page' => 100
        ],
        
        // 搜索配置
        'search' => [
            'min_length' => 2,
            'delay' => 300  // 毫秒
        ],
        
        // 代码高亮
        'code_highlight' => [
            'enabled' => true,
            'theme' => 'github',
            'languages' => ['json', 'xml', 'html', 'javascript', 'php']
        ]
    ],

    // 缓存配置
    'cache' => [
        // 是否启用缓存
        'enabled' => true,
        
        // 缓存驱动
        'driver' => 'redis',
        
        // 缓存前缀
        'prefix' => 'api_doc:',
        
        // 缓存时间（秒）
        'ttl' => 3600,
        
        // 需要缓存的内容
        'cache_items' => [
            'table_list' => true,      // 表列表
            'table_info' => true,      // 表信息
            'api_docs' => true,        // API文档
            'statistics' => true,      // 统计信息
        ]
    ],

    // 安全配置
    'security' => [
        // API文档访问权限
        'access_control' => [
            'require_auth' => true,           // 是否需要登录
            'allowed_roles' => ['admin', 'developer'], // 允许的角色
            'ip_whitelist' => [],             // IP白名单
        ],
        
        // 公开分享配置
        'public_share' => [
            'enabled' => true,               // 是否允许公开分享
            'token_length' => 32,            // 分享token长度
            'expire_days' => 30,             // 分享链接过期天数
            'password_protected' => false,    // 是否需要密码保护
        ],
        
        // 测试安全配置
        'test_security' => [
            'rate_limit' => 60,              // 每分钟最大测试次数
            'allowed_methods' => ['GET', 'POST'], // 允许测试的HTTP方法
            'blocked_endpoints' => [],        // 禁止测试的接口
        ]
    ],

    // 数据库配置
    'database' => [
        // 表名前缀过滤
        'table_prefix_filter' => true,
        
        // 排除的表
        'excluded_tables' => [
            'migrations',
            'password_resets',
            'failed_jobs',
            'sessions',
            'cache',
            'jobs'
        ],
        
        // 排除的字段
        'excluded_fields' => [
            'password',
            'remember_token',
            'email_verified_at'
        ],
        
        // 字段类型映射
        'field_type_mapping' => [
            'int' => 'integer',
            'bigint' => 'integer',
            'tinyint' => 'integer',
            'smallint' => 'integer',
            'mediumint' => 'integer',
            'varchar' => 'string',
            'char' => 'string',
            'text' => 'string',
            'longtext' => 'string',
            'mediumtext' => 'string',
            'tinytext' => 'string',
            'decimal' => 'number',
            'float' => 'number',
            'double' => 'number',
            'timestamp' => 'string',
            'datetime' => 'string',
            'date' => 'string',
            'time' => 'string',
            'year' => 'string',
            'json' => 'object',
            'enum' => 'string',
            'set' => 'array'
        ]
    ],

    // 模板配置
    'templates' => [
        // 模板路径
        'path' => 'resources/views/api_doc_templates',
        
        // 默认模板
        'default' => 'standard',
        
        // 可用模板
        'available' => [
            'standard' => '标准模板',
            'minimal' => '简洁模板',
            'detailed' => '详细模板',
            'custom' => '自定义模板'
        ]
    ],

    // 日志配置
    'logging' => [
        // 是否启用日志
        'enabled' => true,
        
        // 日志级别
        'level' => 'info',
        
        // 日志文件
        'file' => 'storage/logs/api_doc.log',
        
        // 日志保留天数
        'retention_days' => 30,
        
        // 记录的操作
        'log_operations' => [
            'generate' => true,    // 生成文档
            'view' => false,       // 查看文档
            'export' => true,      // 导出文档
            'test' => true,        // 测试接口
            'delete' => true,      // 删除文档
        ]
    ],

    // 性能配置
    'performance' => [
        // 是否启用性能监控
        'monitoring' => true,
        
        // 慢查询阈值（毫秒）
        'slow_query_threshold' => 1000,
        
        // 内存使用监控
        'memory_monitoring' => true,
        
        // 内存使用阈值（MB）
        'memory_threshold' => 128,
        
        // 是否启用查询优化
        'query_optimization' => true
    ],

    // 扩展配置
    'extensions' => [
        // 第三方集成
        'integrations' => [
            'swagger' => false,
            'postman' => false,
            'insomnia' => false,
            'apidog' => false
        ],
        
        // 插件配置
        'plugins' => [
            'auto_refresh' => true,     // 自动刷新
            'dark_mode' => true,        // 深色模式
            'full_screen' => true,      // 全屏模式
            'print_mode' => true,       // 打印模式
        ]
    ]
];
