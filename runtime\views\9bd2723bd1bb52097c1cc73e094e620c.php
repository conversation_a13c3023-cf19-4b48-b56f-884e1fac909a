<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title><?php echo e(sysconfig('site','site_name')); ?></title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="icon" href="<?php echo e(sysconfig('site','site_ico')); ?>" type="image/x-icon">
    <meta name="csrf-token" content="">
    <!--[if lt IE 9]>
    <script src="https://cdn.staticfile.org/html5shiv/r29/html5.min.js"></script>
    <script src="https://cdn.staticfile.org/respond.js/1.4.2/respond.min.js"></script>
    <![endif]-->
    <link rel="stylesheet" href="/static/admin/css/public.css?v=<?php echo e($version); ?>" media="all">
    <link rel="stylesheet" href="" id="layuicss-theme-dark" media="all">
    <script>
        window.CONFIG = {
            ADMIN: "<?php echo e($adminModuleName); ?>",
            CONTROLLER_JS_PATH: "<?php echo e($thisControllerJsPath); ?>",
            ACTION: "<?php echo e($thisAction); ?>",
            AUTOLOAD_JS: "<?php echo e($autoloadJs?1:0); ?>",
            IS_SUPER_ADMIN: "<?php echo e($isSuperAdmin); ?>",
            VERSION: "<?php echo e($version); ?>",
            CSRF_TOKEN: '',
            ADMIN_UPLOAD_URL: "<?php echo e($adminUploadUrl); ?>",
            EDITOR_TYPE: "<?php echo e($adminEditor); ?>",
        };
    </script>
    <script src="/static/plugs/xmSelect/xm-select.js" charset="utf-8"></script>
    <script src="/static/plugs/layui-v2.x/layui.js" charset="utf-8"></script>
    <script src="/static/plugs/require-2.3.6/require.js" charset="utf-8"></script>
    <script src="/static/config-admin.js?v=<?php echo e($version); ?>" charset="utf-8"></script>
    <script src="/static/common/js/admin.js?v=<?php echo e($version); ?>" charset="utf-8"></script>
    <?php echo $__env->make('admin.layout.editor', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
</head>
<body>

<?php /**PATH D:\wwwroot\127.0.0.1\EasyAdmin8-webman\app\admin\view/admin/layout/head.blade.php ENDPATH**/ ?>