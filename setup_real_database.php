<?php
/**
 * 真实数据库连接设置向导
 */

echo "=== 真实数据库连接设置向导 ===\n\n";

// 获取用户输入
function getUserInput($prompt, $default = '') {
    echo $prompt;
    if ($default) {
        echo " (默认: {$default})";
    }
    echo ": ";
    
    $input = trim(fgets(STDIN));
    return $input ?: $default;
}

// 测试数据库连接
function testConnection($host, $port, $username, $password, $database = null) {
    try {
        $dsn = "mysql:host={$host};port={$port}";
        if ($database) {
            $dsn .= ";dbname={$database}";
        }
        $dsn .= ";charset=utf8mb4";
        
        $pdo = new PDO($dsn, $username, $password, [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_TIMEOUT => 5
        ]);
        
        return ['success' => true, 'pdo' => $pdo];
    } catch (PDOException $e) {
        return ['success' => false, 'error' => $e->getMessage()];
    }
}

echo "请输入数据库连接信息:\n\n";

// 获取连接信息
$host = getUserInput("数据库主机", "127.0.0.1");
$port = getUserInput("数据库端口", "3306");
$username = getUserInput("用户名", "root");
$password = getUserInput("密码");

echo "\n🔍 测试数据库连接...\n";

// 测试基础连接
$result = testConnection($host, $port, $username, $password);

if (!$result['success']) {
    echo "❌ 连接失败: {$result['error']}\n";
    echo "\n💡 常见解决方案:\n";
    echo "1. 检查 MySQL 服务是否启动\n";
    echo "2. 确认用户名和密码正确\n";
    echo "3. 检查主机和端口设置\n";
    echo "4. 确认防火墙设置\n";
    exit(1);
}

echo "✅ 基础连接成功!\n\n";

$pdo = $result['pdo'];

// 获取可用数据库
echo "📋 获取可用数据库...\n";
$stmt = $pdo->query("SHOW DATABASES");
$databases = $stmt->fetchAll(PDO::FETCH_COLUMN);

$userDatabases = array_filter($databases, function($db) {
    return !in_array($db, ['information_schema', 'performance_schema', 'mysql', 'sys']);
});

if (empty($userDatabases)) {
    echo "⚠️  没有找到用户数据库\n";
    $createNew = getUserInput("是否创建新数据库? (y/n)", "y");
    
    if (strtolower($createNew) === 'y') {
        $newDbName = getUserInput("新数据库名称", "easyadmin8");
        
        try {
            $pdo->exec("CREATE DATABASE IF NOT EXISTS `{$newDbName}` CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci");
            echo "✅ 数据库 '{$newDbName}' 创建成功\n";
            $selectedDb = $newDbName;
        } catch (PDOException $e) {
            echo "❌ 创建数据库失败: " . $e->getMessage() . "\n";
            exit(1);
        }
    } else {
        echo "❌ 需要数据库才能继续\n";
        exit(1);
    }
} else {
    echo "可用数据库:\n";
    foreach ($userDatabases as $i => $db) {
        echo "  " . ($i + 1) . ". {$db}\n";
    }
    
    $choice = getUserInput("\n选择数据库 (输入序号)", "1");
    $index = intval($choice) - 1;
    
    if (isset($userDatabases[$index])) {
        $selectedDb = array_values($userDatabases)[$index];
    } else {
        echo "❌ 无效选择\n";
        exit(1);
    }
}

echo "\n选择的数据库: {$selectedDb}\n";

// 测试数据库连接
echo "🔍 测试数据库连接...\n";
$dbResult = testConnection($host, $port, $username, $password, $selectedDb);

if (!$dbResult['success']) {
    echo "❌ 数据库连接失败: {$dbResult['error']}\n";
    exit(1);
}

echo "✅ 数据库连接成功!\n\n";

// 检查表
$dbPdo = $dbResult['pdo'];
$stmt = $dbPdo->query("SHOW TABLES");
$tables = $stmt->fetchAll(PDO::FETCH_COLUMN);

echo "📊 数据库中的表数量: " . count($tables) . "\n";

if (count($tables) === 0) {
    echo "⚠️  数据库中没有表\n";
    $createTables = getUserInput("是否创建示例表? (y/n)", "y");
    
    if (strtolower($createTables) === 'y') {
        echo "🔧 创建示例表...\n";
        
        // 创建用户表
        $userTableSQL = "
        CREATE TABLE IF NOT EXISTS `ea8_users` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `username` varchar(50) NOT NULL COMMENT '用户名',
            `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
            `password` varchar(255) NOT NULL COMMENT '密码',
            `status` tinyint(1) DEFAULT '1' COMMENT '状态:1=正常,0=禁用',
            `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
            PRIMARY KEY (`id`),
            UNIQUE KEY `username` (`username`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表'";
        
        $dbPdo->exec($userTableSQL);
        echo "   ✅ ea8_users 表创建成功\n";
        
        // 创建文章表
        $articleTableSQL = "
        CREATE TABLE IF NOT EXISTS `ea8_articles` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `title` varchar(200) NOT NULL COMMENT '标题',
            `content` text COMMENT '内容',
            `author_id` int(11) DEFAULT NULL COMMENT '作者ID',
            `category_id` int(11) DEFAULT NULL COMMENT '分类ID',
            `status` tinyint(1) DEFAULT '1' COMMENT '状态:1=发布,0=草稿',
            `views` int(11) DEFAULT '0' COMMENT '浏览量',
            `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
            PRIMARY KEY (`id`),
            KEY `idx_author` (`author_id`),
            KEY `idx_category` (`category_id`),
            KEY `idx_status` (`status`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='文章表'";
        
        $dbPdo->exec($articleTableSQL);
        echo "   ✅ ea8_articles 表创建成功\n";
        
        // 创建分类表
        $categoryTableSQL = "
        CREATE TABLE IF NOT EXISTS `ea8_categories` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `name` varchar(100) NOT NULL COMMENT '分类名称',
            `description` text COMMENT '分类描述',
            `parent_id` int(11) DEFAULT '0' COMMENT '父分类ID',
            `sort_order` int(11) DEFAULT '0' COMMENT '排序',
            `status` tinyint(1) DEFAULT '1' COMMENT '状态:1=启用,0=禁用',
            `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
            PRIMARY KEY (`id`),
            KEY `idx_parent` (`parent_id`),
            KEY `idx_status` (`status`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分类表'";
        
        $dbPdo->exec($categoryTableSQL);
        echo "   ✅ ea8_categories 表创建成功\n";
        
        // 插入示例数据
        echo "📋 插入示例数据...\n";
        
        $dbPdo->exec("INSERT IGNORE INTO ea8_users (username, email, password) VALUES 
            ('admin', '<EMAIL>', '" . password_hash('admin123', PASSWORD_DEFAULT) . "'),
            ('user1', '<EMAIL>', '" . password_hash('123456', PASSWORD_DEFAULT) . "')");
        
        $dbPdo->exec("INSERT IGNORE INTO ea8_categories (id, name, description) VALUES 
            (1, '技术', '技术相关文章'),
            (2, '生活', '生活相关文章')");
        
        $dbPdo->exec("INSERT IGNORE INTO ea8_articles (title, content, author_id, category_id) VALUES 
            ('测试文章1', '这是第一篇测试文章的内容', 1, 1),
            ('测试文章2', '这是第二篇测试文章的内容', 1, 2)");
        
        echo "   ✅ 示例数据插入成功\n";
    }
}

// 获取表前缀
$prefix = getUserInput("\n表前缀", "ea8_");

// 更新配置文件
echo "\n🔧 更新数据库配置...\n";

$configContent = "<?php
return [
    'default' => 'mysql',
    'connections' => [
        'mysql' => [
            'driver' => 'mysql',
            'host' => '{$host}',
            'port' => {$port},
            'database' => '{$selectedDb}',
            'username' => '{$username}',
            'password' => '{$password}',
            'charset' => 'utf8mb4',
            'collation' => 'utf8mb4_general_ci',
            'prefix' => '{$prefix}',
            'strict' => false,
            'engine' => null,
            'options' => [
                PDO::ATTR_TIMEOUT => 5,
                PDO::ATTR_CASE => PDO::CASE_NATURAL,
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_ORACLE_NULLS => PDO::NULL_NATURAL,
                PDO::ATTR_STRINGIFY_FETCHES => false,
                PDO::ATTR_EMULATE_PREPARES => false,
            ],
        ],
        'mysql_read' => [
            'driver' => 'mysql',
            'host' => '{$host}',
            'port' => {$port},
            'database' => '{$selectedDb}',
            'username' => '{$username}',
            'password' => '{$password}',
            'charset' => 'utf8mb4',
            'collation' => 'utf8mb4_general_ci',
            'prefix' => '{$prefix}',
            'strict' => false,
            'engine' => null,
        ],
        'mysql_second' => [
            'driver' => 'mysql',
            'host' => '{$host}',
            'port' => {$port},
            'database' => '{$selectedDb}',
            'username' => '{$username}',
            'password' => '{$password}',
            'charset' => 'utf8mb4',
            'collation' => 'utf8mb4_general_ci',
            'prefix' => '{$prefix}',
            'strict' => false,
            'engine' => null,
        ],
        'mysql_log' => [
            'driver' => 'mysql',
            'host' => '{$host}',
            'port' => {$port},
            'database' => '{$selectedDb}',
            'username' => '{$username}',
            'password' => '{$password}',
            'charset' => 'utf8mb4',
            'collation' => 'utf8mb4_general_ci',
            'prefix' => '{$prefix}',
            'strict' => false,
            'engine' => null,
        ],
        'mysql_cache' => [
            'driver' => 'mysql',
            'host' => '{$host}',
            'port' => {$port},
            'database' => '{$selectedDb}',
            'username' => '{$username}',
            'password' => '{$password}',
            'charset' => 'utf8mb4',
            'collation' => 'utf8mb4_general_ci',
            'prefix' => '{$prefix}',
            'strict' => false,
            'engine' => null,
        ],
        'mysql_without_prefix' => [
            'driver' => 'mysql',
            'host' => '{$host}',
            'port' => {$port},
            'database' => '{$selectedDb}',
            'username' => '{$username}',
            'password' => '{$password}',
            'charset' => 'utf8mb4',
            'collation' => 'utf8mb4_general_ci',
            'prefix' => '',
            'strict' => false,
            'engine' => null,
        ],
        'pgsql' => [
            'driver' => 'pgsql',
            'host' => '127.0.0.1',
            'port' => 5432,
            'database' => 'easyadmin8',
            'username' => 'postgres',
            'password' => 'password',
            'charset' => 'utf8',
            'prefix' => '',
            'schema' => 'public',
            'sslmode' => 'prefer',
        ],
        'sqlite' => [
            'driver' => 'sqlite',
            'database' => database_path() . '/database.sqlite',
            'prefix' => '',
        ],
        'sqlsrv' => [
            'driver' => 'sqlsrv',
            'host' => '127.0.0.1',
            'port' => 1433,
            'database' => 'easyadmin8',
            'username' => 'sa',
            'password' => 'password',
            'charset' => 'utf8',
            'prefix' => '',
        ],
    ],
];
";

// 备份原配置
if (file_exists('config/database.php')) {
    copy('config/database.php', 'config/database.backup.' . date('Y-m-d-H-i-s') . '.php');
    echo "   ✅ 原配置已备份\n";
}

file_put_contents('config/database.php', $configContent);
echo "   ✅ 数据库配置已更新\n";

echo "\n🎉 真实数据库连接设置完成!\n\n";

echo "📋 连接信息:\n";
echo "   主机: {$host}:{$port}\n";
echo "   数据库: {$selectedDb}\n";
echo "   用户名: {$username}\n";
echo "   前缀: {$prefix}\n\n";

echo "🔄 请重启 webman 服务以应用新配置:\n";
echo "   1. 停止当前服务 (Ctrl+C)\n";
echo "   2. 运行: php windows.php\n\n";

echo "🧪 然后测试功能:\n";
echo "   1. 访问: http://localhost:8787/admin/system/curdgeneratev2\n";
echo "   2. 登录后台\n";
echo "   3. 选择数据库连接\n";
echo "   4. 点击刷新表列表 - 应该能看到真实的表数据\n";

echo "\n=== 设置完成 ===\n";
?>
