# 代码风格和约定

## PHP 代码风格

### 基本约定
- **PHP 版本**: >= 8.1.0，使用现代 PHP 特性
- **编码格式**: UTF-8
- **行结束符**: LF (Unix 风格)
- **缩进**: 4个空格，不使用 Tab

### 命名约定
- **类名**: PascalCase (如 `UserController`, `AdminModel`)
- **方法名**: camelCase (如 `getUserList`, `createUser`)
- **变量名**: camelCase (如 `$userName`, `$userList`)
- **常量名**: UPPER_SNAKE_CASE (如 `MAX_USER_COUNT`)
- **数据库表名**: snake_case 带前缀 (如 `ea8_users`, `ea8_admin_menu`)
- **数据库字段名**: snake_case (如 `user_name`, `created_at`)

### 文件结构约定
```php
<?php

namespace app\admin\controller\system;

use support\Request;
use support\Response;

/**
 * 控制器类描述
 * Class ControllerName
 * @package app\admin\controller\system
 */
class ControllerName
{
    /**
     * 方法描述
     * @param Request $request
     * @return Response
     */
    public function methodName(Request $request): Response
    {
        // 方法实现
    }
}
```

### 注释约定
- **类注释**: 使用 PHPDoc 格式，包含类描述、包名
- **方法注释**: 包含方法描述、参数类型、返回值类型
- **行内注释**: 使用 `//` 进行简短说明
- **块注释**: 使用 `/* */` 进行详细说明

## 前端代码风格

### JavaScript 约定
- **变量命名**: camelCase (如 `userName`, `userList`)
- **函数命名**: camelCase (如 `getUserData`, `renderTable`)
- **常量命名**: UPPER_SNAKE_CASE (如 `MAX_RETRY_COUNT`)
- **缩进**: 4个空格
- **字符串**: 优先使用单引号

### CSS/Less 约定
- **类名**: kebab-case (如 `.user-list`, `.admin-panel`)
- **ID名**: camelCase (如 `#userTable`, `#adminForm`)
- **缩进**: 4个空格
- **属性顺序**: 位置 → 盒模型 → 字体 → 颜色 → 其他

## 目录结构约定

### 后端目录结构
```
app/
├── admin/                  # 后台管理模块
│   ├── controller/         # 控制器
│   │   └── system/        # 系统管理控制器
│   ├── model/             # 数据模型
│   └── view/              # 视图模板
├── controller/            # 前台控制器
├── model/                 # 通用模型
├── middleware/            # 中间件
├── command/               # 命令行工具
└── functions.php          # 全局函数
```

### 前端资源结构
```
public/static/
├── admin/                 # 后台资源
│   ├── css/              # 样式文件
│   ├── js/               # JavaScript 文件
│   ├── images/           # 图片资源
│   └── plugins/          # 插件文件
└── common/               # 公共资源
    ├── layui/            # Layui 框架
    ├── jquery/           # jQuery 库
    └── images/           # 公共图片
```

## 数据库约定

### 表命名约定
- **前缀**: 统一使用 `ea8_` 前缀
- **命名**: snake_case 格式
- **示例**: `ea8_admin_user`, `ea8_system_menu`

### 字段约定
- **主键**: 统一使用 `id`，自增整型
- **时间字段**: `created_at`, `updated_at`, `deleted_at`
- **状态字段**: `status` (1=启用, 0=禁用)
- **排序字段**: `sort` (数字越小越靠前)

## 错误处理约定

### 异常处理
```php
try {
    // 业务逻辑
} catch (\Exception $e) {
    // 记录日志
    \support\Log::error('错误信息', [
        'message' => $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine()
    ]);
    
    // 返回错误响应
    return json(['code' => 1, 'msg' => '操作失败']);
}
```

### 返回格式约定
```php
// 成功响应
return json([
    'code' => 0,
    'msg' => '操作成功',
    'data' => $data
]);

// 错误响应
return json([
    'code' => 1,
    'msg' => '错误信息',
    'data' => null
]);
```

## 安全约定

### 输入验证
- 所有用户输入必须进行验证和过滤
- 使用 webman/validation 进行数据验证
- SQL 查询使用参数绑定防止注入

### 权限控制
- 所有后台操作需要权限验证
- 使用中间件进行统一权限检查
- 敏感操作需要二次确认

## 性能约定

### 数据库查询
- 避免 N+1 查询问题
- 合理使用索引
- 大数据量查询使用分页

### 缓存策略
- 频繁查询的数据使用缓存
- 缓存键名使用统一前缀
- 设置合理的缓存过期时间