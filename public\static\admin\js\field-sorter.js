/**
 * 字段拖拽排序组件
 * 支持可视化调整字段在表单中的显示顺序
 */
define(['jquery'], function($) {
    'use strict';
    
    var FieldSorter = function(options) {
        this.options = $.extend({
            container: '#field-config-table tbody',
            handle: '.drag-handle',
            placeholder: 'field-sort-placeholder',
            animation: 150,
            ghostClass: 'field-sort-ghost',
            chosenClass: 'field-sort-chosen',
            dragClass: 'field-sort-drag',
            onSort: null,
            onStart: null,
            onEnd: null
        }, options);
        
        this.container = $(this.options.container);
        this.sortable = null;
        this.originalOrder = [];
        this.currentOrder = [];
        
        this.init();
    };
    
    FieldSorter.prototype = {
        
        /**
         * 初始化拖拽排序
         */
        init: function() {
            this.loadSortableLibrary();
            this.saveOriginalOrder();
            this.createDragHandles();
            this.bindEvents();
        },
        
        /**
         * 加载 Sortable.js 库
         */
        loadSortableLibrary: function() {
            var self = this;
            
            // 检查是否已加载 Sortable.js
            if (window.Sortable) {
                this.initSortable();
                return;
            }
            
            // 动态加载 Sortable.js
            var script = document.createElement('script');
            script.src = '/static/admin/lib/sortable/sortable.min.js';
            script.onload = function() {
                self.initSortable();
            };
            script.onerror = function() {
                console.warn('Sortable.js 加载失败，使用备用方案');
                self.initFallbackSorting();
            };
            document.head.appendChild(script);
        },
        
        /**
         * 初始化 Sortable
         */
        initSortable: function() {
            var self = this;
            
            this.sortable = new Sortable(this.container[0], {
                handle: this.options.handle,
                animation: this.options.animation,
                ghostClass: this.options.ghostClass,
                chosenClass: this.options.chosenClass,
                dragClass: this.options.dragClass,
                
                onStart: function(evt) {
                    self.onSortStart(evt);
                },
                
                onEnd: function(evt) {
                    self.onSortEnd(evt);
                },
                
                onSort: function(evt) {
                    self.onSort(evt);
                }
            });
        },
        
        /**
         * 备用排序方案 (不依赖 Sortable.js)
         */
        initFallbackSorting: function() {
            var self = this;
            
            this.container.on('mousedown', this.options.handle, function(e) {
                self.startFallbackDrag(e, $(this).closest('tr'));
            });
        },
        
        /**
         * 保存原始顺序
         */
        saveOriginalOrder: function() {
            var self = this;
            this.originalOrder = [];
            
            this.container.find('tr').each(function(index) {
                var $row = $(this);
                var fieldName = $row.data('field');
                
                self.originalOrder.push({
                    index: index,
                    fieldName: fieldName,
                    element: $row
                });
            });
            
            this.currentOrder = [...this.originalOrder];
        },
        
        /**
         * 创建拖拽手柄
         */
        createDragHandles: function() {
            var self = this;
            
            this.container.find('tr').each(function() {
                var $row = $(this);
                
                // 检查是否已有拖拽手柄
                if ($row.find('.drag-handle').length > 0) {
                    return;
                }
                
                // 创建拖拽手柄
                var $handle = $('<td class="drag-handle" title="拖拽排序">' +
                    '<i class="layui-icon layui-icon-slider"></i>' +
                    '</td>');
                
                // 插入到第一列
                $row.prepend($handle);
            });
            
            // 更新表头
            this.updateTableHeader();
        },
        
        /**
         * 更新表头
         */
        updateTableHeader: function() {
            var $thead = this.container.closest('table').find('thead tr');
            
            if ($thead.find('.drag-handle-header').length === 0) {
                var $headerCell = $('<th class="drag-handle-header" width="50">' +
                    '<i class="layui-icon layui-icon-slider"></i>' +
                    '</th>');
                $thead.prepend($headerCell);
            }
        },
        
        /**
         * 绑定事件
         */
        bindEvents: function() {
            var self = this;
            
            // 重置排序按钮
            $(document).on('click', '.reset-field-order', function() {
                self.resetOrder();
            });
            
            // 保存排序按钮
            $(document).on('click', '.save-field-order', function() {
                self.saveOrder();
            });
        },
        
        /**
         * 排序开始事件
         */
        onSortStart: function(evt) {
            this.addSortingClass();
            
            if (this.options.onStart) {
                this.options.onStart(evt);
            }
        },
        
        /**
         * 排序结束事件
         */
        onSortEnd: function(evt) {
            this.removeSortingClass();
            this.updateFieldOrder();
            this.triggerPreviewUpdate();
            
            if (this.options.onEnd) {
                this.options.onEnd(evt);
            }
        },
        
        /**
         * 排序事件
         */
        onSort: function(evt) {
            if (this.options.onSort) {
                this.options.onSort(evt);
            }
        },
        
        /**
         * 添加排序样式
         */
        addSortingClass: function() {
            this.container.addClass('sorting-active');
            $('body').addClass('field-sorting');
        },
        
        /**
         * 移除排序样式
         */
        removeSortingClass: function() {
            this.container.removeClass('sorting-active');
            $('body').removeClass('field-sorting');
        },
        
        /**
         * 更新字段顺序
         */
        updateFieldOrder: function() {
            var self = this;
            var newOrder = [];
            
            this.container.find('tr').each(function(index) {
                var $row = $(this);
                var fieldName = $row.data('field');
                
                newOrder.push({
                    index: index,
                    fieldName: fieldName,
                    element: $row
                });
                
                // 更新字段配置中的顺序
                if (window.fieldConfigs) {
                    var fieldConfig = window.fieldConfigs.find(f => f.name === fieldName);
                    if (fieldConfig) {
                        fieldConfig.order = index;
                    }
                }
            });
            
            this.currentOrder = newOrder;
            
            // 重新排序全局字段配置
            if (window.fieldConfigs) {
                window.fieldConfigs.sort((a, b) => (a.order || 0) - (b.order || 0));
            }
        },
        
        /**
         * 触发预览更新
         */
        triggerPreviewUpdate: function() {
            // 延迟触发，确保 DOM 更新完成
            setTimeout(function() {
                if (window.triggerPreview) {
                    window.triggerPreview();
                }
            }, 100);
        },
        
        /**
         * 重置排序
         */
        resetOrder: function() {
            var self = this;
            
            // 恢复原始顺序
            this.originalOrder.forEach(function(item, index) {
                self.container.append(item.element);
                
                // 更新字段配置
                if (window.fieldConfigs) {
                    var fieldConfig = window.fieldConfigs.find(f => f.name === item.fieldName);
                    if (fieldConfig) {
                        fieldConfig.order = index;
                    }
                }
            });
            
            this.currentOrder = [...this.originalOrder];
            this.triggerPreviewUpdate();
            
            // 提示信息
            if (window.layer) {
                layer.msg('已重置为原始顺序');
            }
        },
        
        /**
         * 保存排序
         */
        saveOrder: function() {
            var orderData = this.currentOrder.map(function(item, index) {
                return {
                    field: item.fieldName,
                    order: index
                };
            });
            
            // 这里可以发送到服务器保存
            console.log('保存字段排序:', orderData);
            
            if (window.layer) {
                layer.msg('排序已保存');
            }
        },
        
        /**
         * 获取当前排序
         */
        getCurrentOrder: function() {
            return this.currentOrder.map(function(item) {
                return item.fieldName;
            });
        },
        
        /**
         * 设置字段顺序
         */
        setFieldOrder: function(fieldNames) {
            var self = this;
            
            fieldNames.forEach(function(fieldName, index) {
                var $row = self.container.find('tr[data-field="' + fieldName + '"]');
                if ($row.length > 0) {
                    self.container.append($row);
                }
            });
            
            this.updateFieldOrder();
            this.triggerPreviewUpdate();
        },
        
        /**
         * 启用排序
         */
        enable: function() {
            if (this.sortable) {
                this.sortable.option('disabled', false);
            }
            this.container.removeClass('sorting-disabled');
        },
        
        /**
         * 禁用排序
         */
        disable: function() {
            if (this.sortable) {
                this.sortable.option('disabled', true);
            }
            this.container.addClass('sorting-disabled');
        },
        
        /**
         * 销毁组件
         */
        destroy: function() {
            if (this.sortable) {
                this.sortable.destroy();
            }
            
            this.container.off();
            this.container.find('.drag-handle').remove();
            this.container.closest('table').find('.drag-handle-header').remove();
        }
    };
    
    return FieldSorter;
});
