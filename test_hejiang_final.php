<?php
/**
 * 最终测试 hejiang 数据库连接（使用正确前缀）
 */

echo "=== 最终测试 hejiang 数据库连接 ===\n\n";

$config = [
    'host' => '127.0.0.1',
    'port' => 3306,
    'database' => 'hejiang',
    'username' => 'root',
    'password' => '5GeNi1v7P7Xcur5W',
    'prefix' => 'ddwx_'  // 更新为正确的前缀
];

echo "📋 更新后的连接信息:\n";
echo "   主机: {$config['host']}:{$config['port']}\n";
echo "   数据库: {$config['database']}\n";
echo "   用户名: {$config['username']}\n";
echo "   前缀: {$config['prefix']} (已更正)\n\n";

try {
    $dsn = "mysql:host={$config['host']};port={$config['port']};dbname={$config['database']};charset=utf8mb4";
    $pdo = new PDO($dsn, $config['username'], $config['password'], [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_TIMEOUT => 5
    ]);
    
    echo "🔍 测试带前缀的表...\n";
    
    // 获取所有表
    $stmt = $pdo->query("SHOW TABLES");
    $allTables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    // 筛选带前缀的表
    $prefixTables = array_filter($allTables, function($table) use ($config) {
        return strpos($table, $config['prefix']) === 0;
    });
    
    echo "   ✅ 找到 " . count($prefixTables) . " 个带前缀 '{$config['prefix']}' 的表\n\n";
    
    // 显示前10个表
    echo "📋 前10个表（去除前缀后的名称）:\n";
    $displayTables = array_slice($prefixTables, 0, 10);
    
    foreach ($displayTables as $table) {
        $displayName = substr($table, strlen($config['prefix']));
        
        // 获取表注释
        $stmt = $pdo->prepare("
            SELECT table_comment 
            FROM information_schema.tables 
            WHERE table_schema = ? AND table_name = ?
        ");
        $stmt->execute([$config['database'], $table]);
        $tableInfo = $stmt->fetch(PDO::FETCH_ASSOC);
        $comment = $tableInfo['table_comment'] ?? '';
        
        echo "   - {$displayName} ({$table}) - " . ($comment ?: '无注释') . "\n";
    }
    
    // 分析一个具体表的结构
    if (!empty($prefixTables)) {
        $sampleTable = reset($prefixTables);
        $sampleDisplayName = substr($sampleTable, strlen($config['prefix']));
        
        echo "\n🔍 分析示例表: {$sampleDisplayName} ({$sampleTable})\n";
        
        // 获取字段信息
        $stmt = $pdo->query("SHOW FULL COLUMNS FROM {$sampleTable}");
        $fields = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "   字段数: " . count($fields) . "\n";
        echo "   前5个字段:\n";
        
        for ($i = 0; $i < min(5, count($fields)); $i++) {
            $field = $fields[$i];
            $nullable = $field['Null'] === 'YES' ? '可空' : '非空';
            $key = $field['Key'] ? " [{$field['Key']}]" : '';
            $comment = $field['Comment'] ? " - {$field['Comment']}" : '';
            
            echo "      - {$field['Field']} ({$field['Type']}) {$nullable}{$key}{$comment}\n";
        }
    }
    
} catch (PDOException $e) {
    echo "❌ 连接失败: " . $e->getMessage() . "\n";
    exit(1);
}

echo "\n=== 测试 webman API 集成 ===\n";

// 重启 webman 服务以应用新配置
echo "🔄 重启 webman 服务以应用新配置...\n";
echo "   请手动重启 webman 服务，然后继续测试\n\n";

// 测试 API
echo "🧪 测试 API 接口（需要登录）...\n";

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'http://localhost:8787/admin/system/curdgeneratev2');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 10);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query([
    'action' => 'get_tables',
    'connection' => 'mysql_second'
]));

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

if ($httpCode === 200) {
    $json = json_decode($response, true);
    if ($json && isset($json['code'])) {
        echo "   ✅ API 响应成功\n";
        echo "   📊 响应码: {$json['code']}\n";
        echo "   📝 消息: {$json['msg']}\n";
        
        if ($json['code'] === 0 && $json['msg'] === '请先登录后台') {
            echo "   ℹ️  需要登录后台才能获取真实数据\n";
        }
    }
} else {
    echo "   ❌ API 请求失败 (HTTP {$httpCode})\n";
}

echo "\n=== 配置完成总结 ===\n";

echo "🎉 hejiang 数据库连接配置完成!\n\n";

echo "📋 最终配置:\n";
echo "   连接名: mysql_second\n";
echo "   数据库: hejiang\n";
echo "   前缀: ddwx_ (已更正)\n";
echo "   表数量: " . count($prefixTables) . " 个\n";
echo "   状态: ✅ 完全可用\n\n";

echo "🧪 测试步骤:\n";
echo "   1. 重启 webman 服务: 停止当前服务，运行 php windows.php\n";
echo "   2. 访问: http://localhost:8787/admin/system/curdgeneratev2\n";
echo "   3. 登录后台系统\n";
echo "   4. 选择数据库连接: mysql_second (第二数据库)\n";
echo "   5. 点击刷新表列表\n";
echo "   6. 应该能看到 " . count($prefixTables) . " 个表\n";
echo "   7. 选择任意表进行分析和代码生成\n\n";

echo "📊 可用的表类型:\n";
$tableTypes = [];
foreach ($prefixTables as $table) {
    $displayName = substr($table, strlen($config['prefix']));
    $parts = explode('_', $displayName);
    $type = $parts[0] ?? 'other';
    $tableTypes[$type] = ($tableTypes[$type] ?? 0) + 1;
}

foreach ($tableTypes as $type => $count) {
    echo "   - {$type}: {$count} 个表\n";
}

echo "\n🔧 其他配置选项:\n";
echo "   • 如需添加更多数据库连接，复制 mysql_second 配置\n";
echo "   • 如需修改前缀，编辑 config/database.php\n";
echo "   • 如需无前缀连接，使用 mysql_without_prefix\n";

echo "\n=== 测试完成 ===\n";
?>
