<?php

namespace app\common\services\curd\v2\config;

use app\common\services\curd\v2\dto\TableInfo;
use app\common\services\curd\v2\dto\GenerateConfig;

/**
 * 配置管理器
 * 负责构建生成配置，管理组件和模板配置
 */
class ConfigManager
{
    protected array $componentConfig;
    protected array $fieldTypeConfig;
    protected array $templateConfig;

    public function __construct()
    {
        $this->loadConfig();
    }

    /**
     * 构建生成配置
     */
    public function buildConfig(TableInfo $tableInfo, array $options = []): GenerateConfig
    {
        $config = new GenerateConfig();
        
        // 基础信息
        $config->setTableInfo($tableInfo);
        $config->setOptions($options);
        
        // 生成文件配置
        $config->setGenerateController($options['generate_controller'] ?? true);
        $config->setGenerateModel($options['generate_model'] ?? true);
        $config->setGenerateView($options['generate_view'] ?? true);
        $config->setGenerateJs($options['generate_js'] ?? true);
        
        // 功能配置
        $config->setEnableSoftDelete($tableInfo->hasSoftDelete());
        $config->setEnableTimestamps($tableInfo->hasTimestamps());
        $config->setEnableExport($options['enable_export'] ?? true);
        $config->setEnableBatch($options['enable_batch'] ?? true);
        
        // 权限配置
        $config->setEnableAuth($options['enable_auth'] ?? true);
        
        return $config;
    }

    /**
     * 获取可用组件
     */
    public function getAvailableComponents(): array
    {
        return $this->componentConfig;
    }

    /**
     * 获取字段类型配置
     */
    public function getFieldTypeConfig(): array
    {
        return $this->fieldTypeConfig;
    }

    /**
     * 获取组件配置
     */
    public function getComponentConfig(string $component): array
    {
        return $this->componentConfig[$component] ?? [];
    }

    /**
     * 获取模板配置
     */
    public function getTemplateConfig(): array
    {
        return $this->templateConfig;
    }

    /**
     * 加载配置
     */
    protected function loadConfig(): void
    {
        $this->loadComponentConfig();
        $this->loadFieldTypeConfig();
        $this->loadTemplateConfig();
    }

    /**
     * 加载组件配置
     */
    protected function loadComponentConfig(): void
    {
        $this->componentConfig = [
            'input' => [
                'name' => '文本输入框',
                'template' => 'input.blade.php',
                'icon' => 'layui-icon-edit',
                'options' => [
                    'placeholder' => ['type' => 'string', 'default' => '请输入'],
                    'maxlength' => ['type' => 'integer', 'default' => null],
                    'readonly' => ['type' => 'boolean', 'default' => false],
                ],
            ],
            'textarea' => [
                'name' => '多行文本',
                'template' => 'textarea.blade.php',
                'icon' => 'layui-icon-edit',
                'options' => [
                    'placeholder' => ['type' => 'string', 'default' => '请输入'],
                    'rows' => ['type' => 'integer', 'default' => 4],
                ],
            ],
            'number' => [
                'name' => '数字输入',
                'template' => 'number.blade.php',
                'icon' => 'layui-icon-number',
                'options' => [
                    'min' => ['type' => 'number', 'default' => null],
                    'max' => ['type' => 'number', 'default' => null],
                    'step' => ['type' => 'number', 'default' => 1],
                ],
            ],
            'password' => [
                'name' => '密码输入',
                'template' => 'password.blade.php',
                'icon' => 'layui-icon-password',
                'options' => [],
            ],
            'email' => [
                'name' => '邮箱输入',
                'template' => 'email.blade.php',
                'icon' => 'layui-icon-email',
                'options' => [],
            ],
            'tel' => [
                'name' => '电话输入',
                'template' => 'tel.blade.php',
                'icon' => 'layui-icon-cellphone',
                'options' => [],
            ],
            'url' => [
                'name' => 'URL输入',
                'template' => 'url.blade.php',
                'icon' => 'layui-icon-link',
                'options' => [],
            ],
            'select' => [
                'name' => '下拉选择',
                'template' => 'select.blade.php',
                'icon' => 'layui-icon-down',
                'options' => [
                    'multiple' => ['type' => 'boolean', 'default' => false],
                    'searchable' => ['type' => 'boolean', 'default' => false],
                    'remote' => ['type' => 'boolean', 'default' => false],
                    'options' => ['type' => 'array', 'default' => []],
                ],
            ],
            'radio' => [
                'name' => '单选框',
                'template' => 'radio.blade.php',
                'icon' => 'layui-icon-radio',
                'options' => [
                    'options' => ['type' => 'array', 'default' => []],
                ],
            ],
            'checkbox' => [
                'name' => '复选框',
                'template' => 'checkbox.blade.php',
                'icon' => 'layui-icon-ok',
                'options' => [
                    'options' => ['type' => 'array', 'default' => []],
                ],
            ],
            'switch' => [
                'name' => '开关',
                'template' => 'switch.blade.php',
                'icon' => 'layui-icon-switch',
                'options' => [
                    'text' => ['type' => 'string', 'default' => 'ON|OFF'],
                ],
            ],
            'date' => [
                'name' => '日期选择',
                'template' => 'date.blade.php',
                'icon' => 'layui-icon-date',
                'options' => [
                    'format' => ['type' => 'string', 'default' => 'yyyy-MM-dd'],
                ],
            ],
            'datetime' => [
                'name' => '日期时间',
                'template' => 'datetime.blade.php',
                'icon' => 'layui-icon-date',
                'options' => [
                    'format' => ['type' => 'string', 'default' => 'yyyy-MM-dd HH:mm:ss'],
                ],
            ],
            'time' => [
                'name' => '时间选择',
                'template' => 'time.blade.php',
                'icon' => 'layui-icon-time',
                'options' => [
                    'format' => ['type' => 'string', 'default' => 'HH:mm:ss'],
                ],
            ],
            'image' => [
                'name' => '图片上传',
                'template' => 'image.blade.php',
                'icon' => 'layui-icon-picture',
                'options' => [
                    'multiple' => ['type' => 'boolean', 'default' => false],
                    'accept' => ['type' => 'string', 'default' => 'image/*'],
                ],
            ],
            'file' => [
                'name' => '文件上传',
                'template' => 'file.blade.php',
                'icon' => 'layui-icon-upload',
                'options' => [
                    'multiple' => ['type' => 'boolean', 'default' => false],
                    'accept' => ['type' => 'string', 'default' => '*'],
                ],
            ],
            'editor' => [
                'name' => '富文本编辑器',
                'template' => 'editor.blade.php',
                'icon' => 'layui-icon-edit',
                'options' => [
                    'height' => ['type' => 'integer', 'default' => 300],
                    'toolbar' => ['type' => 'array', 'default' => []],
                ],
            ],
            'color' => [
                'name' => '颜色选择',
                'template' => 'color.blade.php',
                'icon' => 'layui-icon-theme',
                'options' => [],
            ],
            'tags' => [
                'name' => '标签输入',
                'template' => 'tags.blade.php',
                'icon' => 'layui-icon-tabs',
                'options' => [
                    'separator' => ['type' => 'string', 'default' => ','],
                ],
            ],
            'json_editor' => [
                'name' => 'JSON编辑器',
                'template' => 'json_editor.blade.php',
                'icon' => 'layui-icon-code',
                'options' => [
                    'height' => ['type' => 'integer', 'default' => 200],
                ],
            ],
        ];
    }

    /**
     * 加载字段类型配置
     */
    protected function loadFieldTypeConfig(): void
    {
        $this->fieldTypeConfig = [
            'string' => [
                'default_component' => 'input',
                'available_components' => ['input', 'textarea', 'select', 'radio', 'password', 'email', 'tel', 'url'],
                'validation' => ['required', 'min', 'max', 'regex'],
            ],
            'integer' => [
                'default_component' => 'number',
                'available_components' => ['number', 'select', 'radio'],
                'validation' => ['required', 'min', 'max', 'integer'],
            ],
            'text' => [
                'default_component' => 'textarea',
                'available_components' => ['textarea', 'editor'],
                'validation' => ['required', 'min', 'max'],
            ],
            'datetime' => [
                'default_component' => 'datetime',
                'available_components' => ['datetime', 'date', 'time'],
                'validation' => ['required', 'date'],
            ],
            'boolean' => [
                'default_component' => 'switch',
                'available_components' => ['switch', 'radio', 'select'],
                'validation' => ['boolean'],
            ],
            'enum' => [
                'default_component' => 'select',
                'available_components' => ['select', 'radio'],
                'validation' => ['required', 'in'],
            ],
            'json' => [
                'default_component' => 'json_editor',
                'available_components' => ['json_editor', 'textarea'],
                'validation' => ['json'],
            ],
        ];
    }

    /**
     * 加载模板配置
     */
    protected function loadTemplateConfig(): void
    {
        $this->templateConfig = [
            'controller' => [
                'template' => 'controller/controller.blade.php',
                'output_path' => 'app/admin/controller',
            ],
            'model' => [
                'template' => 'model/model.blade.php',
                'output_path' => 'app/admin/model',
            ],
            'view_index' => [
                'template' => 'view/index.blade.php',
                'output_path' => 'app/admin/view/admin',
            ],
            'view_form' => [
                'template' => 'view/form.blade.php',
                'output_path' => 'app/admin/view/admin',
            ],
            'js' => [
                'template' => 'static/js.blade.php',
                'output_path' => 'public/static/admin/js',
            ],
        ];
    }
}
