<?php
/**
 * 多数据库 CURD 生成器功能测试脚本
 */

echo "=== 多数据库 CURD 生成器功能测试 ===\n\n";

// 测试 1: 检查关键文件是否存在
echo "1. 检查关键文件...\n";
$files = [
    'app/admin/controller/system/CurdGenerateV2Controller.php' => 'CURD生成器控制器',
    'app/admin/controller/system/DatabaseTestController.php' => '数据库测试控制器',
    'app/admin/view/admin/system/curdgeneratev2/index.blade.php' => 'CURD生成器界面',
    'app/admin/view/admin/system/databasetest/index.blade.php' => '数据库测试界面',
    'config/database.php' => '数据库配置文件',
    'config/route.php' => '路由配置文件',
];

foreach ($files as $file => $desc) {
    if (file_exists($file)) {
        echo "   ✅ {$desc}: {$file}\n";
    } else {
        echo "   ❌ {$desc}: {$file} - 文件不存在\n";
    }
}

// 测试 2: 检查数据库配置
echo "\n2. 检查数据库配置...\n";
if (file_exists('config/database.php')) {
    $config = include 'config/database.php';
    $connections = [
        'mysql' => '默认连接',
        'mysql_read' => '读库连接', 
        'mysql_second' => '第二数据库',
        'mysql_log' => '日志数据库',
        'mysql_cache' => '缓存数据库',
        'mysql_without_prefix' => '无前缀连接',
        'pgsql' => 'PostgreSQL',
        'sqlite' => 'SQLite',
        'sqlsrv' => 'SQL Server',
    ];
    
    foreach ($connections as $conn => $desc) {
        if (isset($config['connections'][$conn])) {
            echo "   ✅ {$desc} ({$conn}): 配置存在\n";
        } else {
            echo "   ❌ {$desc} ({$conn}): 配置缺失\n";
        }
    }
} else {
    echo "   ❌ 数据库配置文件不存在\n";
}

// 测试 3: 检查路由配置
echo "\n3. 检查路由配置...\n";
if (file_exists('config/route.php')) {
    $routeContent = file_get_contents('config/route.php');
    $routes = [
        '/admin/system/curdgeneratev2' => 'CURD生成器路由',
        '/admin/system/databasetest' => '数据库测试路由',
    ];
    
    foreach ($routes as $route => $desc) {
        if (strpos($routeContent, $route) !== false) {
            echo "   ✅ {$desc}: {$route}\n";
        } else {
            echo "   ❌ {$desc}: {$route} - 路由缺失\n";
        }
    }
} else {
    echo "   ❌ 路由配置文件不存在\n";
}

// 测试 4: 检查控制器类
echo "\n4. 检查控制器类...\n";

// 检查 DatabaseTestController
if (file_exists('app/admin/controller/system/DatabaseTestController.php')) {
    $content = file_get_contents('app/admin/controller/system/DatabaseTestController.php');
    
    // 检查基类引用
    if (strpos($content, 'use app\common\controller\AdminController;') !== false) {
        echo "   ✅ DatabaseTestController: 基类引用正确\n";
    } else {
        echo "   ❌ DatabaseTestController: 基类引用错误\n";
    }
    
    // 检查关键方法
    $methods = ['testConnection', 'getDatabaseInfo', 'connections'];
    foreach ($methods as $method) {
        if (strpos($content, "function {$method}") !== false) {
            echo "   ✅ DatabaseTestController: {$method}() 方法存在\n";
        } else {
            echo "   ❌ DatabaseTestController: {$method}() 方法缺失\n";
        }
    }
}

// 检查 CurdGenerateV2Controller
if (file_exists('app/admin/controller/system/CurdGenerateV2Controller.php')) {
    $content = file_get_contents('app/admin/controller/system/CurdGenerateV2Controller.php');
    
    // 检查关键方法
    $methods = ['getTables', 'analyzeTable'];
    foreach ($methods as $method) {
        if (strpos($content, "function {$method}") !== false) {
            echo "   ✅ CurdGenerateV2Controller: {$method}() 方法存在\n";
        } else {
            echo "   ❌ CurdGenerateV2Controller: {$method}() 方法缺失\n";
        }
    }
    
    // 检查连接参数支持
    if (strpos($content, '$connection = $request->input(\'connection\'') !== false) {
        echo "   ✅ CurdGenerateV2Controller: 支持数据库连接参数\n";
    } else {
        echo "   ❌ CurdGenerateV2Controller: 不支持数据库连接参数\n";
    }
}

// 测试 5: 检查服务类
echo "\n5. 检查服务类...\n";

// 检查 CurdGenerator
if (file_exists('app/common/services/curd/v2/CurdGenerator.php')) {
    $content = file_get_contents('app/common/services/curd/v2/CurdGenerator.php');
    
    $methods = [
        'getAllTables(string $connection = \'mysql\')' => 'getAllTables',
        'validateTable(string $tableName, string $tablePrefix = \'\', string $connection = \'mysql\')' => 'validateTable',
        'getTableInfo(string $tableName, string $tablePrefix = \'\', string $connection = \'mysql\')' => 'getTableInfo',
    ];
    
    foreach ($methods as $signature => $method) {
        if (strpos($content, $signature) !== false) {
            echo "   ✅ CurdGenerator: {$method}() 支持连接参数\n";
        } else {
            echo "   ❌ CurdGenerator: {$method}() 不支持连接参数\n";
        }
    }
}

// 检查 TableAnalyzer
if (file_exists('app/common/services/curd/v2/analyzers/TableAnalyzer.php')) {
    $content = file_get_contents('app/common/services/curd/v2/analyzers/TableAnalyzer.php');
    
    $methods = [
        'analyze(string $tableName, string $tablePrefix = \'\', string $connection = \'mysql\')' => 'analyze',
        'tableExists(string $tableName, string $tablePrefix = \'\', string $connection = \'mysql\')' => 'tableExists',
        'getAllTables(string $connection = \'mysql\')' => 'getAllTables',
    ];
    
    foreach ($methods as $signature => $method) {
        if (strpos($content, $signature) !== false) {
            echo "   ✅ TableAnalyzer: {$method}() 支持连接参数\n";
        } else {
            echo "   ❌ TableAnalyzer: {$method}() 不支持连接参数\n";
        }
    }
}

// 测试 6: 检查前端界面
echo "\n6. 检查前端界面...\n";

// 检查 CURD 生成器界面
if (file_exists('app/admin/view/admin/system/curdgeneratev2/index.blade.php')) {
    $content = file_get_contents('app/admin/view/admin/system/curdgeneratev2/index.blade.php');
    
    $elements = [
        'connectionSelect' => '数据库连接选择器',
        'loadTables' => '加载表列表功能',
        'analyzeTable' => '分析表结构功能',
        'connection:' => '连接参数传递',
    ];
    
    foreach ($elements as $element => $desc) {
        if (strpos($content, $element) !== false) {
            echo "   ✅ CURD生成器界面: {$desc}\n";
        } else {
            echo "   ❌ CURD生成器界面: {$desc} - 缺失\n";
        }
    }
}

// 检查数据库测试界面
if (file_exists('app/admin/view/admin/system/databasetest/index.blade.php')) {
    echo "   ✅ 数据库测试界面: 文件存在\n";
} else {
    echo "   ❌ 数据库测试界面: 文件不存在\n";
}

// 测试总结
echo "\n=== 测试总结 ===\n";
echo "✅ 多数据库 CURD 生成器功能测试完成\n";
echo "\n📋 下一步操作:\n";
echo "1. 启动 webman 服务: php start.php start\n";
echo "2. 访问 CURD 生成器: http://localhost:8787/admin/system/curdgeneratev2\n";
echo "3. 访问数据库测试工具: http://localhost:8787/admin/system/databasetest\n";
echo "4. 测试不同数据库连接的功能\n";

echo "\n🎯 测试要点:\n";
echo "• 在 CURD 生成器中选择不同的数据库连接\n";
echo "• 验证表列表是否正确加载\n";
echo "• 测试表结构分析功能\n";
echo "• 检查错误处理是否友好\n";

echo "\n🔧 如果遇到问题:\n";
echo "• 检查数据库服务是否启动\n";
echo "• 确认数据库连接配置是否正确\n";
echo "• 查看 webman 日志获取详细错误信息\n";

echo "\n=== 测试脚本执行完成 ===\n";
?>