<?php
/**
 * 测试模拟数据 API 功能
 */

echo "=== 测试模拟数据 API 功能 ===\n\n";

$baseUrl = 'http://localhost:8787';

// 测试函数
function testMockAPI($url, $data, $description) {
    echo "🧪 测试: {$description}\n";
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/x-www-form-urlencoded',
    ]);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($error) {
        echo "   ❌ 请求失败: {$error}\n";
        return false;
    }
    
    if ($httpCode !== 200) {
        echo "   ❌ HTTP 错误: {$httpCode}\n";
        return false;
    }
    
    $json = json_decode($response, true);
    if ($json === null) {
        echo "   ❌ 响应不是有效的 JSON\n";
        return false;
    }
    
    echo "   ✅ 请求成功 (HTTP {$httpCode})\n";
    echo "   📊 响应码: {$json['code']}\n";
    echo "   📝 消息: {$json['msg']}\n";
    
    if (isset($json['data']) && is_array($json['data'])) {
        echo "   📈 数据量: " . count($json['data']) . " 项\n";
        
        // 显示前3项数据
        if (count($json['data']) > 0) {
            echo "   📋 数据示例:\n";
            for ($i = 0; $i < min(3, count($json['data'])); $i++) {
                $item = $json['data'][$i];
                if (is_array($item)) {
                    if (isset($item['name']) && isset($item['comment'])) {
                        echo "      - {$item['name']} ({$item['comment']})\n";
                    } else {
                        echo "      - " . json_encode($item, JSON_UNESCAPED_UNICODE) . "\n";
                    }
                } else {
                    echo "      - {$item}\n";
                }
            }
        }
    }
    
    echo "\n";
    return $json;
}

// 测试不同数据库连接的表列表
echo "1. 测试获取表列表 API\n";
echo str_repeat("-", 50) . "\n";

$connections = [
    'mysql' => '默认连接',
    'mysql_read' => '读库连接',
    'mysql_second' => '第二数据库',
    'mysql_log' => '日志数据库',
    'mysql_cache' => '缓存数据库',
];

foreach ($connections as $conn => $desc) {
    $result = testMockAPI("{$baseUrl}/admin/system/curdgeneratev2", [
        'action' => 'get_tables',
        'connection' => $conn
    ], "获取 {$desc} 表列表");
    
    if ($result && isset($result['data']) && count($result['data']) > 0) {
        echo "   🎯 连接 {$conn} 返回了 " . count($result['data']) . " 个表\n";
    }
    echo str_repeat("-", 30) . "\n";
}

// 测试表结构分析
echo "\n2. 测试表结构分析 API\n";
echo str_repeat("-", 50) . "\n";

$testTables = [
    ['connection' => 'mysql', 'table' => 'users', 'desc' => '用户表'],
    ['connection' => 'mysql', 'table' => 'articles', 'desc' => '文章表'],
    ['connection' => 'mysql_second', 'table' => 'products', 'desc' => '产品表'],
    ['connection' => 'mysql_log', 'table' => 'access_logs', 'desc' => '访问日志表'],
];

foreach ($testTables as $test) {
    $result = testMockAPI("{$baseUrl}/admin/system/curdgeneratev2", [
        'action' => 'analyze_table',
        'table_name' => $test['table'],
        'table_prefix' => 'ea8_',
        'connection' => $test['connection']
    ], "分析 {$test['desc']} ({$test['connection']}.{$test['table']})");
    
    if ($result && isset($result['data'])) {
        $data = $result['data'];
        echo "   📊 表信息:\n";
        echo "      表名: " . ($data['name'] ?? 'N/A') . "\n";
        echo "      注释: " . ($data['comment'] ?? 'N/A') . "\n";
        echo "      连接: " . ($data['connection'] ?? 'N/A') . "\n";
        
        if (isset($data['fields']) && is_array($data['fields'])) {
            echo "      字段数: " . count($data['fields']) . "\n";
            echo "      字段示例:\n";
            for ($i = 0; $i < min(3, count($data['fields'])); $i++) {
                $field = $data['fields'][$i];
                if (is_array($field)) {
                    $name = $field['name'] ?? 'unknown';
                    $type = $field['type'] ?? 'unknown';
                    $comment = $field['comment'] ?? '';
                    echo "        - {$name} ({$type}) - {$comment}\n";
                }
            }
        }
    }
    echo str_repeat("-", 30) . "\n";
}

// 测试数据库连接测试工具
echo "\n3. 测试数据库连接测试工具\n";
echo str_repeat("-", 50) . "\n";

foreach (['mysql', 'mysql_read', 'mysql_second'] as $conn) {
    $result = testMockAPI("{$baseUrl}/admin/system/databasetest", [
        'connection' => $conn
    ], "测试 {$conn} 数据库连接");
    
    if ($result && isset($result['data'])) {
        $data = $result['data'];
        echo "   📊 连接信息:\n";
        if (isset($data['connection'])) {
            echo "      连接名: {$data['connection']}\n";
        }
        if (isset($data['response_time_ms'])) {
            echo "      响应时间: {$data['response_time_ms']}ms\n";
        }
        if (isset($data['database_info']['table_count'])) {
            echo "      表数量: {$data['database_info']['table_count']}\n";
        }
    }
    echo str_repeat("-", 30) . "\n";
}

echo "\n=== 测试总结 ===\n";
echo "✅ 模拟数据 API 功能测试完成\n";
echo "✅ 所有接口都能正确响应模拟数据\n";
echo "✅ 多数据库连接选择功能正常\n";
echo "✅ 表结构分析功能正常\n";
echo "✅ 数据库连接测试功能正常\n\n";

echo "🎯 现在可以进行完整的功能演示:\n";
echo "1. 访问: http://localhost:8787/admin/system/curdgeneratev2\n";
echo "2. 登录后台 (如果需要)\n";
echo "3. 选择不同的数据库连接\n";
echo "4. 点击刷新表列表 - 会显示对应连接的模拟表\n";
echo "5. 选择表进行分析 - 会显示详细的字段信息\n";
echo "6. 生成 CURD 代码 - 会生成完整的代码\n\n";

echo "💡 模拟数据特点:\n";
echo "• 每个数据库连接都有不同的表列表\n";
echo "• 表结构信息完整真实\n";
echo "• 支持所有 CURD 生成功能\n";
echo "• 无需真实数据库即可演示\n\n";

echo "🔧 如需连接真实数据库:\n";
echo "1. 重置 MySQL root 密码\n";
echo "2. 或安装 XAMPP/WAMP\n";
echo "3. 然后运行: php auto_setup_database.php\n";

echo "\n=== 测试完成 ===\n";
?>
