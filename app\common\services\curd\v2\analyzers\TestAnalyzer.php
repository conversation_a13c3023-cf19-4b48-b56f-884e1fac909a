<?php

namespace app\common\services\curd\v2\analyzers;

/**
 * 测试代码分析器
 * 分析生成的代码并设计相应的测试用例
 */
class TestAnalyzer
{
    protected array $testTypes = [
        'unit' => '单元测试',
        'integration' => '集成测试',
        'feature' => '功能测试',
        'api' => 'API测试',
        'performance' => '性能测试',
        'security' => '安全测试',
        'browser' => '浏览器测试',
        'database' => '数据库测试',
    ];

    protected array $testCategories = [
        'model' => '模型测试',
        'controller' => '控制器测试',
        'api' => 'API接口测试',
        'relationship' => '关联关系测试',
        'validation' => '验证规则测试',
        'security' => '安全测试',
        'performance' => '性能测试',
        'middleware' => '中间件测试',
        'event' => '事件测试',
        'job' => '队列任务测试',
        'command' => '命令行测试',
        'notification' => '通知测试',
        'mail' => '邮件测试',
        'cache' => '缓存测试',
        'session' => '会话测试',
        'file' => '文件操作测试',
    ];

    /**
     * 分析测试需求
     */
    public function analyzeTestRequirements(array $generatedCode, array $tableInfo, array $options = []): array
    {
        $testRequirements = [];

        // 分析模型测试需求
        if (!empty($generatedCode['model'])) {
            $testRequirements['model'] = $this->analyzeModelTestRequirements($generatedCode['model'], $tableInfo);
        }

        // 分析控制器测试需求
        if (!empty($generatedCode['controller'])) {
            $testRequirements['controller'] = $this->analyzeControllerTestRequirements($generatedCode['controller'], $tableInfo);
        }

        // 分析API测试需求
        if (!empty($generatedCode['api_controller'])) {
            $testRequirements['api'] = $this->analyzeApiTestRequirements($generatedCode['api_controller'], $tableInfo);
        }

        // 分析关联关系测试需求
        if (!empty($tableInfo['relationships'])) {
            $testRequirements['relationship'] = $this->analyzeRelationshipTestRequirements($tableInfo['relationships'], $tableInfo);
        }

        // 分析验证测试需求
        $testRequirements['validation'] = $this->analyzeValidationTestRequirements($tableInfo);

        // 分析安全测试需求
        $testRequirements['security'] = $this->analyzeSecurityTestRequirements($generatedCode, $tableInfo);

        // 分析性能测试需求
        $testRequirements['performance'] = $this->analyzePerformanceTestRequirements($generatedCode, $tableInfo);

        // 分析浏览器测试需求
        if (!empty($generatedCode['view'])) {
            $testRequirements['browser'] = $this->analyzeBrowserTestRequirements($generatedCode, $tableInfo);
        }

        return [
            'requirements' => $testRequirements,
            'test_plan' => $this->generateTestPlan($testRequirements),
            'coverage_analysis' => $this->analyzeCoverageRequirements($testRequirements),
            'test_data' => $this->generateTestDataRequirements($tableInfo),
            'test_matrix' => $this->generateTestMatrix($testRequirements),
        ];
    }

    /**
     * 分析模型测试需求
     */
    protected function analyzeModelTestRequirements(string $modelCode, array $tableInfo): array
    {
        $requirements = [];

        // 基础模型测试
        $requirements[] = [
            'type' => 'unit',
            'category' => 'model',
            'name' => 'model_creation_test',
            'description' => '测试模型创建功能',
            'methods' => ['create', 'save'],
            'assertions' => ['模型实例创建成功', '数据保存到数据库', '返回正确的模型实例'],
        ];

        $requirements[] = [
            'type' => 'unit',
            'category' => 'model',
            'name' => 'model_retrieval_test',
            'description' => '测试模型查询功能',
            'methods' => ['find', 'where', 'get'],
            'assertions' => ['正确查询数据', '返回预期结果', '处理空结果'],
        ];

        $requirements[] = [
            'type' => 'unit',
            'category' => 'model',
            'name' => 'model_update_test',
            'description' => '测试模型更新功能',
            'methods' => ['update', 'save'],
            'assertions' => ['数据更新成功', '返回更新后的模型', '触发相关事件'],
        ];

        $requirements[] = [
            'type' => 'unit',
            'category' => 'model',
            'name' => 'model_deletion_test',
            'description' => '测试模型删除功能',
            'methods' => ['delete', 'destroy'],
            'assertions' => ['数据删除成功', '软删除正确处理', '级联删除处理'],
        ];

        // 字段验证测试
        foreach ($tableInfo['fields'] as $field) {
            if (!empty($field['required'])) {
                $requirements[] = [
                    'type' => 'unit',
                    'category' => 'validation',
                    'name' => "field_{$field['name']}_required_test",
                    'description' => "测试字段 {$field['name']} 必填验证",
                    'field' => $field['name'],
                    'assertions' => ['必填验证生效', '错误信息正确'],
                ];
            }

            if (!empty($field['unique'])) {
                $requirements[] = [
                    'type' => 'unit',
                    'category' => 'validation',
                    'name' => "field_{$field['name']}_unique_test",
                    'description' => "测试字段 {$field['name']} 唯一性验证",
                    'field' => $field['name'],
                    'assertions' => ['唯一性验证生效', '重复值被拒绝'],
                ];
            }
        }

        return $requirements;
    }

    /**
     * 分析控制器测试需求
     */
    protected function analyzeControllerTestRequirements(string $controllerCode, array $tableInfo): array
    {
        $requirements = [];

        // 基础CRUD操作测试
        $crudMethods = ['index', 'show', 'store', 'update', 'destroy'];

        foreach ($crudMethods as $method) {
            if (strpos($controllerCode, "function {$method}") !== false) {
                $requirements[] = [
                    'type' => 'feature',
                    'category' => 'controller',
                    'name' => "controller_{$method}_test",
                    'description' => "测试控制器 {$method} 方法",
                    'method' => $method,
                    'assertions' => $this->getControllerMethodAssertions($method),
                ];
            }
        }

        // 权限验证测试
        $requirements[] = [
            'type' => 'feature',
            'category' => 'security',
            'name' => 'controller_authorization_test',
            'description' => '测试控制器权限验证',
            'assertions' => ['未授权访问被拒绝', '正确权限允许访问', '权限检查生效'],
        ];

        // 输入验证测试
        $requirements[] = [
            'type' => 'feature',
            'category' => 'validation',
            'name' => 'controller_input_validation_test',
            'description' => '测试控制器输入验证',
            'assertions' => ['无效输入被拒绝', '验证错误信息正确', '有效输入被接受'],
        ];

        return $requirements;
    }

    /**
     * 分析API测试需求
     */
    protected function analyzeApiTestRequirements(string $apiCode, array $tableInfo): array
    {
        $requirements = [];

        // RESTful API测试
        $apiMethods = [
            'GET' => ['index', 'show'],
            'POST' => ['store'],
            'PUT' => ['update'],
            'DELETE' => ['destroy'],
        ];

        foreach ($apiMethods as $httpMethod => $actions) {
            foreach ($actions as $action) {
                if (strpos($apiCode, "function {$action}") !== false) {
                    $requirements[] = [
                        'type' => 'api',
                        'category' => 'api',
                        'name' => "api_{$httpMethod}_{$action}_test",
                        'description' => "测试 {$httpMethod} API {$action} 接口",
                        'http_method' => $httpMethod,
                        'action' => $action,
                        'assertions' => $this->getApiMethodAssertions($httpMethod, $action),
                    ];
                }
            }
        }

        // API响应格式测试
        $requirements[] = [
            'type' => 'api',
            'category' => 'api',
            'name' => 'api_response_format_test',
            'description' => '测试API响应格式',
            'assertions' => ['JSON格式正确', '状态码正确', '响应结构一致'],
        ];

        // API错误处理测试
        $requirements[] = [
            'type' => 'api',
            'category' => 'api',
            'name' => 'api_error_handling_test',
            'description' => '测试API错误处理',
            'assertions' => ['404错误正确处理', '422验证错误正确返回', '500错误正确处理'],
        ];

        return $requirements;
    }

    /**
     * 分析关联关系测试需求
     */
    protected function analyzeRelationshipTestRequirements(array $relationships, array $tableInfo): array
    {
        $requirements = [];

        foreach ($relationships as $relationship) {
            if ($relationship['confidence'] < 70) {
                continue; // 跳过低置信度的关系
            }

            $relationName = $relationship['method_name'];
            $relationType = $relationship['type'];

            $requirements[] = [
                'type' => 'unit',
                'category' => 'relationship',
                'name' => "relationship_{$relationName}_test",
                'description' => "测试 {$relationType} 关联关系 {$relationName}",
                'relationship' => $relationship,
                'assertions' => $this->getRelationshipAssertions($relationType),
            ];

            // 关联查询测试
            $requirements[] = [
                'type' => 'integration',
                'category' => 'relationship',
                'name' => "relationship_{$relationName}_query_test",
                'description' => "测试 {$relationName} 关联查询",
                'relationship' => $relationship,
                'assertions' => ['关联数据正确加载', '查询性能符合预期', 'N+1问题不存在'],
            ];
        }

        return $requirements;
    }

    /**
     * 分析验证测试需求
     */
    protected function analyzeValidationTestRequirements(array $tableInfo): array
    {
        $requirements = [];

        // 数据类型验证测试
        foreach ($tableInfo['fields'] as $field) {
            $requirements[] = [
                'type' => 'unit',
                'category' => 'validation',
                'name' => "validation_{$field['name']}_type_test",
                'description' => "测试字段 {$field['name']} 类型验证",
                'field' => $field,
                'assertions' => $this->getTypeValidationAssertions($field['type']),
            ];
        }

        // 业务规则验证测试
        $requirements[] = [
            'type' => 'unit',
            'category' => 'validation',
            'name' => 'business_rules_validation_test',
            'description' => '测试业务规则验证',
            'assertions' => ['业务规则正确执行', '违规数据被拒绝', '错误信息准确'],
        ];

        return $requirements;
    }

    /**
     * 分析安全测试需求
     */
    protected function analyzeSecurityTestRequirements(array $generatedCode, array $tableInfo): array
    {
        $requirements = [];

        // SQL注入测试
        $requirements[] = [
            'type' => 'security',
            'category' => 'security',
            'name' => 'sql_injection_test',
            'description' => '测试SQL注入防护',
            'assertions' => ['恶意SQL被阻止', '参数化查询生效', '数据库安全'],
        ];

        // XSS攻击测试
        $requirements[] = [
            'type' => 'security',
            'category' => 'security',
            'name' => 'xss_protection_test',
            'description' => '测试XSS攻击防护',
            'assertions' => ['恶意脚本被转义', '输出安全', '用户数据安全'],
        ];

        // 权限控制测试
        $requirements[] = [
            'type' => 'security',
            'category' => 'security',
            'name' => 'access_control_test',
            'description' => '测试访问权限控制',
            'assertions' => ['未授权访问被阻止', '权限检查生效', '敏感操作受保护'],
        ];

        return $requirements;
    }

    /**
     * 生成测试计划
     */
    protected function generateTestPlan(array $testRequirements): array
    {
        $plan = [
            'phases' => [],
            'priorities' => [],
            'dependencies' => [],
            'timeline' => [],
        ];

        // 测试阶段规划
        $plan['phases'] = [
            'unit' => [
                'name' => '单元测试阶段',
                'description' => '测试独立的代码单元',
                'duration' => '2-3天',
                'tests' => $this->getTestsByType($testRequirements, 'unit'),
            ],
            'integration' => [
                'name' => '集成测试阶段',
                'description' => '测试组件间的集成',
                'duration' => '1-2天',
                'tests' => $this->getTestsByType($testRequirements, 'integration'),
            ],
            'feature' => [
                'name' => '功能测试阶段',
                'description' => '测试完整的功能流程',
                'duration' => '2-3天',
                'tests' => $this->getTestsByType($testRequirements, 'feature'),
            ],
            'api' => [
                'name' => 'API测试阶段',
                'description' => '测试API接口',
                'duration' => '1-2天',
                'tests' => $this->getTestsByType($testRequirements, 'api'),
            ],
            'security' => [
                'name' => '安全测试阶段',
                'description' => '测试安全防护',
                'duration' => '1天',
                'tests' => $this->getTestsByType($testRequirements, 'security'),
            ],
        ];

        // 优先级规划
        $plan['priorities'] = [
            'high' => ['model', 'controller', 'api'],
            'medium' => ['relationship', 'validation'],
            'low' => ['security', 'performance'],
        ];

        return $plan;
    }

    /**
     * 分析覆盖率需求
     */
    protected function analyzeCoverageRequirements(array $testRequirements): array
    {
        $coverage = [
            'targets' => [
                'line_coverage' => 90,
                'branch_coverage' => 85,
                'function_coverage' => 95,
                'class_coverage' => 100,
            ],
            'critical_paths' => [],
            'exclusions' => [],
        ];

        // 关键路径识别
        $coverage['critical_paths'] = [
            'data_creation' => '数据创建流程',
            'data_validation' => '数据验证流程',
            'security_checks' => '安全检查流程',
            'error_handling' => '错误处理流程',
        ];

        // 覆盖率排除项
        $coverage['exclusions'] = [
            'generated_files' => '自动生成的文件',
            'third_party' => '第三方库',
            'config_files' => '配置文件',
        ];

        return $coverage;
    }

    /**
     * 生成测试数据需求
     */
    protected function generateTestDataRequirements(array $tableInfo): array
    {
        $testData = [
            'factories' => [],
            'seeders' => [],
            'fixtures' => [],
        ];

        // 模型工厂需求
        $testData['factories'][] = [
            'name' => $this->getModelName($tableInfo['name']) . 'Factory',
            'description' => '生成测试用的模型数据',
            'fields' => $this->generateFactoryFields($tableInfo['fields']),
        ];

        // 数据填充需求
        $testData['seeders'][] = [
            'name' => $this->getModelName($tableInfo['name']) . 'Seeder',
            'description' => '填充测试数据',
            'data_sets' => ['basic', 'edge_cases', 'performance'],
        ];

        // 测试夹具需求
        $testData['fixtures'][] = [
            'name' => $this->getModelName($tableInfo['name']) . 'Fixture',
            'description' => '固定的测试数据集',
            'scenarios' => ['valid_data', 'invalid_data', 'boundary_data'],
        ];

        return $testData;
    }

    /**
     * 获取控制器方法断言
     */
    protected function getControllerMethodAssertions(string $method): array
    {
        $assertions = [
            'index' => ['返回正确的视图', '数据列表正确', '分页功能正常'],
            'show' => ['返回正确的数据', '处理不存在的记录', '权限检查生效'],
            'store' => ['数据创建成功', '验证规则生效', '重定向正确'],
            'update' => ['数据更新成功', '验证规则生效', '返回更新后数据'],
            'destroy' => ['数据删除成功', '软删除正确处理', '权限检查生效'],
        ];

        return $assertions[$method] ?? ['基本功能正常'];
    }

    /**
     * 获取API方法断言
     */
    protected function getApiMethodAssertions(string $httpMethod, string $action): array
    {
        $assertions = [
            'GET_index' => ['返回200状态码', 'JSON格式正确', '分页数据正确'],
            'GET_show' => ['返回200状态码', '数据结构正确', '404处理正确'],
            'POST_store' => ['返回201状态码', '数据创建成功', '422验证错误正确'],
            'PUT_update' => ['返回200状态码', '数据更新成功', '404和422处理正确'],
            'DELETE_destroy' => ['返回204状态码', '数据删除成功', '404处理正确'],
        ];

        return $assertions["{$httpMethod}_{$action}"] ?? ['基本API功能正常'];
    }

    /**
     * 获取关联关系断言
     */
    protected function getRelationshipAssertions(string $relationType): array
    {
        $assertions = [
            'belongsTo' => ['关联对象正确加载', '外键关系正确', '延迟加载正常'],
            'hasOne' => ['关联对象正确加载', '一对一关系正确', '反向关联正常'],
            'hasMany' => ['关联集合正确加载', '一对多关系正确', '分页查询正常'],
            'belongsToMany' => ['多对多关系正确', '中间表数据正确', '附加和分离正常'],
        ];

        return $assertions[$relationType] ?? ['关联关系基本功能正常'];
    }

    /**
     * 获取类型验证断言
     */
    protected function getTypeValidationAssertions(string $fieldType): array
    {
        $assertions = [
            'int' => ['整数验证生效', '非整数被拒绝', '范围验证正确'],
            'varchar' => ['字符串验证生效', '长度限制正确', '特殊字符处理正确'],
            'text' => ['文本验证生效', '长文本处理正确', 'HTML标签处理正确'],
            'date' => ['日期格式验证生效', '无效日期被拒绝', '日期范围验证正确'],
            'datetime' => ['日期时间格式验证生效', '时区处理正确', '无效时间被拒绝'],
        ];

        return $assertions[$fieldType] ?? ['基本类型验证正常'];
    }

    /**
     * 按类型获取测试
     */
    protected function getTestsByType(array $testRequirements, string $type): array
    {
        $tests = [];

        foreach ($testRequirements as $category => $requirements) {
            foreach ($requirements as $requirement) {
                if ($requirement['type'] === $type) {
                    $tests[] = $requirement;
                }
            }
        }

        return $tests;
    }

    /**
     * 生成工厂字段
     */
    protected function generateFactoryFields(array $fields): array
    {
        $factoryFields = [];

        foreach ($fields as $field) {
            if (in_array($field['name'], ['id', 'created_at', 'updated_at', 'deleted_at'])) {
                continue;
            }

            $factoryFields[$field['name']] = $this->getFactoryFieldDefinition($field);
        }

        return $factoryFields;
    }

    /**
     * 获取工厂字段定义
     */
    protected function getFactoryFieldDefinition(array $field): string
    {
        switch ($field['type']) {
            case 'varchar':
                return "fake()->words(3, true)";
            case 'text':
                return "fake()->paragraph()";
            case 'int':
                return "fake()->numberBetween(1, 1000)";
            case 'date':
                return "fake()->date()";
            case 'datetime':
                return "fake()->dateTime()";
            case 'decimal':
                return "fake()->randomFloat(2, 0, 999.99)";
            default:
                return "fake()->word()";
        }
    }

    /**
     * 获取模型名称
     */
    protected function getModelName(string $tableName): string
    {
        $name = preg_replace('/^[a-z]+_/', '', $tableName);
        return str_replace(' ', '', ucwords(str_replace('_', ' ', $name)));
    }

    /**
     * 分析性能测试需求
     */
    protected function analyzePerformanceTestRequirements(array $generatedCode, array $tableInfo): array
    {
        $requirements = [];

        // 数据库查询性能测试
        $requirements[] = [
            'type' => 'performance',
            'category' => 'database',
            'name' => 'database_query_performance_test',
            'description' => '测试数据库查询性能',
            'assertions' => ['查询时间小于100ms', '内存使用合理', 'N+1查询不存在'],
            'benchmarks' => [
                'query_time' => '< 100ms',
                'memory_usage' => '< 10MB',
                'query_count' => '< 10 per request'
            ]
        ];

        // API响应性能测试
        $requirements[] = [
            'type' => 'performance',
            'category' => 'api',
            'name' => 'api_response_performance_test',
            'description' => '测试API响应性能',
            'assertions' => ['响应时间小于200ms', '并发处理能力', '内存泄漏检测'],
            'benchmarks' => [
                'response_time' => '< 200ms',
                'concurrent_users' => '> 100',
                'memory_leak' => 'none'
            ]
        ];

        // 大数据量测试
        $requirements[] = [
            'type' => 'performance',
            'category' => 'load',
            'name' => 'large_dataset_performance_test',
            'description' => '测试大数据量处理性能',
            'assertions' => ['分页查询效率', '批量操作性能', '索引使用效果'],
            'test_data_sizes' => [1000, 10000, 100000]
        ];

        return $requirements;
    }

    /**
     * 分析浏览器测试需求
     */
    protected function analyzeBrowserTestRequirements(array $generatedCode, array $tableInfo): array
    {
        $requirements = [];
        $modelName = $this->getModelName($tableInfo['name']);

        // 页面加载测试
        $requirements[] = [
            'type' => 'browser',
            'category' => 'ui',
            'name' => 'page_load_test',
            'description' => '测试页面加载功能',
            'pages' => ['index', 'create', 'edit', 'show'],
            'assertions' => ['页面正常加载', '元素正确显示', '无JavaScript错误']
        ];

        // 表单交互测试
        $requirements[] = [
            'type' => 'browser',
            'category' => 'interaction',
            'name' => 'form_interaction_test',
            'description' => '测试表单交互功能',
            'interactions' => ['填写表单', '提交数据', '验证错误显示', '成功提示'],
            'assertions' => ['表单验证正确', '数据提交成功', '错误信息显示']
        ];

        // 响应式设计测试
        $requirements[] = [
            'type' => 'browser',
            'category' => 'responsive',
            'name' => 'responsive_design_test',
            'description' => '测试响应式设计',
            'viewports' => ['desktop', 'tablet', 'mobile'],
            'assertions' => ['布局适配正确', '功能正常使用', '性能可接受']
        ];

        return $requirements;
    }

    /**
     * 生成测试矩阵
     */
    protected function generateTestMatrix(array $testRequirements): array
    {
        $matrix = [
            'coverage_matrix' => [],
            'priority_matrix' => [],
            'dependency_matrix' => [],
        ];

        // 生成覆盖率矩阵
        foreach ($testRequirements as $category => $requirements) {
            foreach ($requirements as $requirement) {
                $testType = $requirement['type'];
                if (!isset($matrix['coverage_matrix'][$testType])) {
                    $matrix['coverage_matrix'][$testType] = [];
                }
                $matrix['coverage_matrix'][$testType][] = $requirement['name'];
            }
        }

        // 生成优先级矩阵
        $priorityMapping = [
            'unit' => 'high',
            'integration' => 'medium',
            'feature' => 'high',
            'api' => 'high',
            'performance' => 'medium',
            'security' => 'high',
            'browser' => 'low',
        ];

        foreach ($testRequirements as $category => $requirements) {
            foreach ($requirements as $requirement) {
                $testType = $requirement['type'];
                $priority = $priorityMapping[$testType] ?? 'medium';

                if (!isset($matrix['priority_matrix'][$priority])) {
                    $matrix['priority_matrix'][$priority] = [];
                }
                $matrix['priority_matrix'][$priority][] = $requirement['name'];
            }
        }

        // 生成依赖矩阵
        $matrix['dependency_matrix'] = [
            'unit' => [], // 单元测试无依赖
            'integration' => ['unit'], // 集成测试依赖单元测试
            'feature' => ['unit', 'integration'], // 功能测试依赖前两者
            'api' => ['unit'], // API测试依赖单元测试
            'performance' => ['unit', 'integration'], // 性能测试依赖基础测试
            'security' => ['unit'], // 安全测试依赖单元测试
            'browser' => ['feature'], // 浏览器测试依赖功能测试
        ];

        return $matrix;
    }

    /**
     * 生成测试执行计划
     */
    public function generateTestExecutionPlan(array $testRequirements): array
    {
        $plan = [
            'phases' => [],
            'timeline' => [],
            'resources' => [],
            'risks' => [],
        ];

        // 测试阶段规划
        $phases = [
            'phase1' => [
                'name' => '基础测试阶段',
                'tests' => ['unit', 'validation'],
                'duration' => '2-3天',
                'parallel' => true,
            ],
            'phase2' => [
                'name' => '集成测试阶段',
                'tests' => ['integration', 'api'],
                'duration' => '2-3天',
                'parallel' => true,
            ],
            'phase3' => [
                'name' => '功能测试阶段',
                'tests' => ['feature', 'browser'],
                'duration' => '3-4天',
                'parallel' => false,
            ],
            'phase4' => [
                'name' => '专项测试阶段',
                'tests' => ['performance', 'security'],
                'duration' => '2-3天',
                'parallel' => true,
            ],
        ];

        $plan['phases'] = $phases;

        // 时间线规划
        $plan['timeline'] = [
            'total_duration' => '9-13天',
            'critical_path' => ['unit', 'integration', 'feature'],
            'milestones' => [
                'day_3' => '基础测试完成',
                'day_6' => '集成测试完成',
                'day_10' => '功能测试完成',
                'day_13' => '所有测试完成',
            ],
        ];

        // 资源需求
        $plan['resources'] = [
            'human_resources' => [
                'test_engineer' => 2,
                'automation_engineer' => 1,
                'performance_tester' => 1,
            ],
            'infrastructure' => [
                'test_environment' => 'required',
                'performance_lab' => 'optional',
                'browser_farm' => 'required for browser tests',
            ],
            'tools' => [
                'phpunit' => 'required',
                'selenium' => 'required for browser tests',
                'jmeter' => 'required for performance tests',
            ],
        ];

        // 风险评估
        $plan['risks'] = [
            'high' => [
                'test_environment_instability',
                'complex_business_logic',
                'third_party_dependencies',
            ],
            'medium' => [
                'test_data_preparation',
                'browser_compatibility',
                'performance_baseline',
            ],
            'low' => [
                'test_automation_maintenance',
                'reporting_delays',
            ],
        ];

        return $plan;
    }
}
