/**
 * CURD 代码模板管理器
 * 用于管理和自定义代码生成模板
 */

layui.use(['layer', 'form'], function() {
    var layer = layui.layer;
    var form = layui.form;

    // 模板管理器类
    var TemplateManager = {
        // 模板存储
        templates: {},
        
        // 当前编辑的模板
        currentTemplate: null,

        // 初始化
        init: function() {
            this.loadDefaultTemplates();
            this.bindEvents();
        },

        // 绑定事件
        bindEvents: function() {
            var self = this;

            // 模板管理按钮
            $(document).on('click', '#template-manager-btn', function() {
                self.openTemplateManager();
            });

            // 新建模板
            $(document).on('click', '#new-template-btn', function() {
                self.createNewTemplate();
            });

            // 编辑模板
            $(document).on('click', '.edit-template-btn', function() {
                var templateId = $(this).data('template-id');
                self.editTemplate(templateId);
            });

            // 删除模板
            $(document).on('click', '.delete-template-btn', function() {
                var templateId = $(this).data('template-id');
                self.deleteTemplate(templateId);
            });

            // 保存模板
            $(document).on('click', '#save-template-btn', function() {
                self.saveTemplate();
            });

            // 导入模板
            $(document).on('click', '#import-template-btn', function() {
                self.importTemplate();
            });

            // 导出模板
            $(document).on('click', '#export-template-btn', function() {
                self.exportTemplate();
            });

            // 模板预览
            $(document).on('click', '.preview-template-btn', function() {
                var templateId = $(this).data('template-id');
                self.previewTemplate(templateId);
            });
        },

        // 加载默认模板
        loadDefaultTemplates: function() {
            this.templates = {
                'basic_controller': {
                    name: '基础控制器',
                    type: 'controller',
                    content: this.getBasicControllerTemplate(),
                    variables: ['{{className}}', '{{tableName}}', '{{fields}}', '{{moduleNamespace}}'],
                    description: '标准的CRUD控制器模板'
                },
                'api_controller': {
                    name: 'API控制器',
                    type: 'controller',
                    content: this.getApiControllerTemplate(),
                    variables: ['{{className}}', '{{tableName}}', '{{fields}}'],
                    description: 'RESTful API控制器模板'
                },
                'basic_model': {
                    name: '基础模型',
                    type: 'model',
                    content: this.getBasicModelTemplate(),
                    variables: ['{{className}}', '{{tableName}}', '{{fields}}', '{{fillable}}'],
                    description: '标准的Eloquent模型模板'
                },
                'layui_view': {
                    name: 'Layui视图',
                    type: 'view',
                    content: this.getLayuiViewTemplate(),
                    variables: ['{{title}}', '{{tableName}}', '{{fields}}', '{{searchForm}}'],
                    description: 'Layui风格的视图模板'
                },
                'vue_component': {
                    name: 'Vue组件',
                    type: 'js',
                    content: this.getVueComponentTemplate(),
                    variables: ['{{componentName}}', '{{apiUrl}}', '{{fields}}'],
                    description: 'Vue.js组件模板'
                }
            };

            // 从本地存储加载自定义模板
            this.loadCustomTemplates();
        },

        // 从本地存储加载自定义模板
        loadCustomTemplates: function() {
            var customTemplates = localStorage.getItem('curd_custom_templates');
            if (customTemplates) {
                try {
                    var templates = JSON.parse(customTemplates);
                    Object.assign(this.templates, templates);
                } catch (e) {
                    console.error('加载自定义模板失败:', e);
                }
            }
        },

        // 保存自定义模板到本地存储
        saveCustomTemplates: function() {
            var customTemplates = {};
            Object.keys(this.templates).forEach(function(key) {
                if (!key.startsWith('basic_') && !key.startsWith('api_') && !key.startsWith('layui_') && !key.startsWith('vue_')) {
                    customTemplates[key] = this.templates[key];
                }
            }.bind(this));

            localStorage.setItem('curd_custom_templates', JSON.stringify(customTemplates));
        },

        // 打开模板管理器
        openTemplateManager: function() {
            var self = this;
            var content = this.buildTemplateManagerHTML();

            layer.open({
                type: 1,
                title: '代码模板管理器',
                content: content,
                area: ['1000px', '700px'],
                btn: ['关闭'],
                success: function() {
                    self.renderTemplateList();
                    form.render();
                }
            });
        },

        // 构建模板管理器HTML
        buildTemplateManagerHTML: function() {
            return `
                <div class="template-manager">
                    <div class="template-toolbar">
                        <button class="layui-btn layui-btn-sm" id="new-template-btn">
                            <i class="layui-icon layui-icon-add-1"></i> 新建模板
                        </button>
                        <button class="layui-btn layui-btn-sm layui-btn-primary" id="import-template-btn">
                            <i class="layui-icon layui-icon-upload"></i> 导入模板
                        </button>
                        <button class="layui-btn layui-btn-sm layui-btn-normal" id="export-template-btn">
                            <i class="layui-icon layui-icon-download"></i> 导出模板
                        </button>
                    </div>
                    
                    <div class="template-content">
                        <div class="template-list">
                            <div class="template-filter">
                                <select name="template_type" lay-filter="templateType">
                                    <option value="">全部类型</option>
                                    <option value="controller">控制器</option>
                                    <option value="model">模型</option>
                                    <option value="view">视图</option>
                                    <option value="js">JavaScript</option>
                                </select>
                            </div>
                            <div id="template-list-container"></div>
                        </div>
                        
                        <div class="template-editor" id="template-editor" style="display: none;">
                            <div class="editor-header">
                                <h4 id="editor-title">编辑模板</h4>
                                <div class="editor-actions">
                                    <button class="layui-btn layui-btn-sm" id="save-template-btn">保存</button>
                                    <button class="layui-btn layui-btn-sm layui-btn-primary" id="cancel-edit-btn">取消</button>
                                </div>
                            </div>
                            
                            <div class="editor-form">
                                <div class="layui-form">
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">模板名称</label>
                                        <div class="layui-input-block">
                                            <input type="text" name="template_name" class="layui-input" placeholder="请输入模板名称">
                                        </div>
                                    </div>
                                    
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">模板类型</label>
                                        <div class="layui-input-block">
                                            <select name="template_type_edit">
                                                <option value="controller">控制器</option>
                                                <option value="model">模型</option>
                                                <option value="view">视图</option>
                                                <option value="js">JavaScript</option>
                                            </select>
                                        </div>
                                    </div>
                                    
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">描述</label>
                                        <div class="layui-input-block">
                                            <input type="text" name="template_description" class="layui-input" placeholder="请输入模板描述">
                                        </div>
                                    </div>
                                    
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">模板变量</label>
                                        <div class="layui-input-block">
                                            <input type="text" name="template_variables" class="layui-input" placeholder="用逗号分隔，如: {{className}}, {{tableName}}">
                                        </div>
                                    </div>
                                    
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">模板内容</label>
                                        <div class="layui-input-block">
                                            <textarea name="template_content" class="layui-textarea" style="height: 300px;" placeholder="请输入模板内容"></textarea>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        },

        // 渲染模板列表
        renderTemplateList: function() {
            var self = this;
            var container = $('#template-list-container');
            var html = '';

            Object.keys(this.templates).forEach(function(key) {
                var template = self.templates[key];
                var isCustom = !key.startsWith('basic_') && !key.startsWith('api_') && !key.startsWith('layui_') && !key.startsWith('vue_');
                
                html += `
                    <div class="template-item" data-template-id="${key}">
                        <div class="template-info">
                            <h5>${template.name}</h5>
                            <p>${template.description}</p>
                            <div class="template-meta">
                                <span class="template-type">${template.type}</span>
                                ${isCustom ? '<span class="template-custom">自定义</span>' : '<span class="template-builtin">内置</span>'}
                            </div>
                        </div>
                        <div class="template-actions">
                            <button class="layui-btn layui-btn-xs preview-template-btn" data-template-id="${key}">预览</button>
                            <button class="layui-btn layui-btn-xs layui-btn-primary edit-template-btn" data-template-id="${key}">编辑</button>
                            ${isCustom ? `<button class="layui-btn layui-btn-xs layui-btn-danger delete-template-btn" data-template-id="${key}">删除</button>` : ''}
                        </div>
                    </div>
                `;
            });

            container.html(html);
        },

        // 创建新模板
        createNewTemplate: function() {
            this.currentTemplate = null;
            this.showTemplateEditor();
            $('#editor-title').text('新建模板');
            
            // 清空表单
            $('input[name="template_name"]').val('');
            $('select[name="template_type_edit"]').val('controller');
            $('input[name="template_description"]').val('');
            $('input[name="template_variables"]').val('');
            $('textarea[name="template_content"]').val('');
            
            form.render();
        },

        // 编辑模板
        editTemplate: function(templateId) {
            var template = this.templates[templateId];
            if (!template) return;

            this.currentTemplate = templateId;
            this.showTemplateEditor();
            $('#editor-title').text('编辑模板 - ' + template.name);

            // 填充表单
            $('input[name="template_name"]').val(template.name);
            $('select[name="template_type_edit"]').val(template.type);
            $('input[name="template_description"]').val(template.description);
            $('input[name="template_variables"]').val(template.variables ? template.variables.join(', ') : '');
            $('textarea[name="template_content"]').val(template.content);

            form.render();
        },

        // 显示模板编辑器
        showTemplateEditor: function() {
            $('.template-list').hide();
            $('#template-editor').show();
        },

        // 隐藏模板编辑器
        hideTemplateEditor: function() {
            $('#template-editor').hide();
            $('.template-list').show();
        },

        // 保存模板
        saveTemplate: function() {
            var name = $('input[name="template_name"]').val();
            var type = $('select[name="template_type_edit"]').val();
            var description = $('input[name="template_description"]').val();
            var variables = $('input[name="template_variables"]').val();
            var content = $('textarea[name="template_content"]').val();

            if (!name || !content) {
                layer.msg('请填写模板名称和内容', {icon: 2});
                return;
            }

            var templateId = this.currentTemplate || 'custom_' + Date.now();
            var variableArray = variables ? variables.split(',').map(v => v.trim()) : [];

            this.templates[templateId] = {
                name: name,
                type: type,
                description: description,
                variables: variableArray,
                content: content
            };

            this.saveCustomTemplates();
            this.renderTemplateList();
            this.hideTemplateEditor();

            layer.msg('模板保存成功', {icon: 1});
        },

        // 删除模板
        deleteTemplate: function(templateId) {
            var self = this;
            var template = this.templates[templateId];
            
            layer.confirm('确定删除模板 "' + template.name + '" 吗？', function(index) {
                delete self.templates[templateId];
                self.saveCustomTemplates();
                self.renderTemplateList();
                layer.close(index);
                layer.msg('模板删除成功', {icon: 1});
            });
        },

        // 预览模板
        previewTemplate: function(templateId) {
            var template = this.templates[templateId];
            if (!template) return;

            var content = `
                <div class="template-preview">
                    <h4>${template.name}</h4>
                    <p><strong>类型:</strong> ${template.type}</p>
                    <p><strong>描述:</strong> ${template.description}</p>
                    <p><strong>变量:</strong> ${template.variables ? template.variables.join(', ') : '无'}</p>
                    <div class="template-content-preview">
                        <h5>模板内容:</h5>
                        <pre><code>${template.content}</code></pre>
                    </div>
                </div>
            `;

            layer.open({
                type: 1,
                title: '模板预览',
                content: content,
                area: ['800px', '600px'],
                btn: ['关闭']
            });
        },

        // 导入模板
        importTemplate: function() {
            var self = this;
            
            layer.open({
                type: 1,
                title: '导入模板',
                content: `
                    <div class="import-template">
                        <div class="layui-form">
                            <div class="layui-form-item">
                                <label class="layui-form-label">选择文件</label>
                                <div class="layui-input-block">
                                    <input type="file" id="template-file" accept=".json">
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">或粘贴JSON</label>
                                <div class="layui-input-block">
                                    <textarea id="template-json" class="layui-textarea" placeholder="粘贴模板JSON数据"></textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                `,
                area: ['500px', '400px'],
                btn: ['导入', '取消'],
                yes: function(index) {
                    var file = document.getElementById('template-file').files[0];
                    var jsonText = $('#template-json').val();
                    
                    if (file) {
                        var reader = new FileReader();
                        reader.onload = function(e) {
                            self.processImportData(e.target.result);
                        };
                        reader.readAsText(file);
                    } else if (jsonText) {
                        self.processImportData(jsonText);
                    } else {
                        layer.msg('请选择文件或粘贴JSON数据', {icon: 2});
                        return;
                    }
                    
                    layer.close(index);
                }
            });
        },

        // 处理导入数据
        processImportData: function(jsonText) {
            try {
                var importedTemplates = JSON.parse(jsonText);
                var count = 0;
                
                Object.keys(importedTemplates).forEach(function(key) {
                    this.templates[key] = importedTemplates[key];
                    count++;
                }.bind(this));
                
                this.saveCustomTemplates();
                this.renderTemplateList();
                
                layer.msg('成功导入 ' + count + ' 个模板', {icon: 1});
            } catch (e) {
                layer.msg('JSON格式错误: ' + e.message, {icon: 2});
            }
        },

        // 导出模板
        exportTemplate: function() {
            var customTemplates = {};
            Object.keys(this.templates).forEach(function(key) {
                if (!key.startsWith('basic_') && !key.startsWith('api_') && !key.startsWith('layui_') && !key.startsWith('vue_')) {
                    customTemplates[key] = this.templates[key];
                }
            }.bind(this));

            if (Object.keys(customTemplates).length === 0) {
                layer.msg('没有自定义模板可导出', {icon: 2});
                return;
            }

            var jsonData = JSON.stringify(customTemplates, null, 2);
            var blob = new Blob([jsonData], { type: 'application/json' });
            var url = URL.createObjectURL(blob);
            
            var a = document.createElement('a');
            a.href = url;
            a.download = 'curd_templates_' + new Date().toISOString().slice(0, 10) + '.json';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);

            layer.msg('模板导出成功', {icon: 1});
        },

        // 获取模板
        getTemplate: function(templateId) {
            return this.templates[templateId];
        },

        // 获取基础控制器模板
        getBasicControllerTemplate: function() {
            return `<?php

namespace {{moduleNamespace}};

use support\\Request;
use support\\Response;
use app\\common\\model\\{{className}};
use app\\admin\\controller\\AdminController;

/**
 * {{className}} 控制器
 */
class {{className}}Controller extends AdminController
{
    public function index(Request $request): Response
    {
        if ($request->method() === 'POST') {
            return $this->selectList($request);
        }
        
        return view('admin/{{tableName}}/index');
    }
    
    // 其他方法...
}`;
        },

        // 获取API控制器模板
        getApiControllerTemplate: function() {
            return `<?php

namespace App\\Http\\Controllers\\Api;

use App\\Http\\Controllers\\Controller;
use App\\Models\\{{className}};
use Illuminate\\Http\\Request;

/**
 * {{className}} API控制器
 */
class {{className}}Controller extends Controller
{
    public function index()
    {
        $data = {{className}}::paginate(15);
        return response()->json($data);
    }
    
    // 其他API方法...
}`;
        },

        // 获取基础模型模板
        getBasicModelTemplate: function() {
            return `<?php

namespace App\\Models;

use Illuminate\\Database\\Eloquent\\Model;

/**
 * {{className}} 模型
 */
class {{className}} extends Model
{
    protected $table = '{{tableName}}';
    
    protected $fillable = [
        {{fillable}}
    ];
    
    // 其他模型方法...
}`;
        },

        // 获取Layui视图模板
        getLayuiViewTemplate: function() {
            return `@extends('admin.layouts.app')

@section('content')
<div class="layui-card">
    <div class="layui-card-header">
        <h3>{{title}}</h3>
    </div>
    <div class="layui-card-body">
        {{searchForm}}
        
        <table class="layui-hide" id="data-table"></table>
    </div>
</div>
@endsection`;
        },

        // 获取Vue组件模板
        getVueComponentTemplate: function() {
            return `<template>
  <div class="{{componentName}}-container">
    <el-table :data="tableData" style="width: 100%">
      {{fields}}
    </el-table>
  </div>
</template>

<script>
export default {
  name: '{{componentName}}',
  data() {
    return {
      tableData: []
    }
  },
  mounted() {
    this.loadData()
  },
  methods: {
    loadData() {
      // 加载数据
    }
  }
}
</script>`;
        }
    };

    // 暴露到全局
    window.CurdTemplateManager = TemplateManager;

    // 自动初始化
    $(document).ready(function() {
        TemplateManager.init();
    });
});
