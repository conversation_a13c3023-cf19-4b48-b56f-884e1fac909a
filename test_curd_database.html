<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>多数据库 CURD 生成器测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        
        .content {
            padding: 30px;
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background: #fafafa;
        }
        
        .test-title {
            font-size: 1.3em;
            font-weight: 600;
            margin-bottom: 15px;
            color: #333;
            display: flex;
            align-items: center;
        }
        
        .test-title::before {
            content: '🧪';
            margin-right: 10px;
            font-size: 1.2em;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #555;
        }
        
        .form-group select,
        .form-group input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .btn-success {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }
        
        .btn-danger {
            background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
        }
        
        .result-box {
            padding: 15px;
            margin: 10px 0;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            line-height: 1.5;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
            display: none;
        }
        
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 多数据库 CURD 生成器测试</h1>
            <p>测试数据库连接选择和表列表加载功能</p>
        </div>
        
        <div class="content">
            <div class="grid">
                <!-- 数据库连接测试 -->
                <div class="test-section">
                    <div class="test-title">数据库连接测试</div>
                    
                    <div class="form-group">
                        <label>选择数据库连接</label>
                        <select id="connectionSelect">
                            <option value="mysql">默认连接 (mysql)</option>
                            <option value="mysql_read">读库连接 (mysql_read)</option>
                            <option value="mysql_second">第二数据库 (mysql_second)</option>
                            <option value="mysql_log">日志数据库 (mysql_log)</option>
                            <option value="mysql_cache">缓存数据库 (mysql_cache)</option>
                            <option value="mysql_without_prefix">无前缀连接 (mysql_without_prefix)</option>
                            <option value="pgsql">PostgreSQL (pgsql)</option>
                            <option value="sqlite">SQLite (sqlite)</option>
                            <option value="sqlsrv">SQL Server (sqlsrv)</option>
                        </select>
                    </div>
                    
                    <button class="btn" onclick="testConnection()">
                        <span id="testConnBtn">测试连接</span>
                    </button>
                    
                    <div id="connectionResult" class="result-box"></div>
                </div>

                <!-- 数据表列表测试 -->
                <div class="test-section">
                    <div class="test-title">数据表列表测试</div>
                    
                    <button class="btn btn-success" onclick="loadTables()">
                        <span id="loadTablesBtn">加载数据表</span>
                    </button>
                    
                    <div id="tablesResult" class="result-box"></div>
                    
                    <div class="form-group" id="tableSelectGroup" style="display: none;">
                        <label>选择数据表</label>
                        <select id="tableSelect">
                            <option value="">请选择数据表</option>
                        </select>
                    </div>
                </div>
            </div>

            <div class="test-section">
                <div class="test-title">表结构分析测试</div>
                
                <div class="form-group">
                    <label>表前缀（可选）</label>
                    <input type="text" id="tablePrefix" placeholder="如: ea8_">
                </div>
                
                <button class="btn btn-danger" onclick="analyzeTable()">
                    <span id="analyzeBtn">分析表结构</span>
                </button>
                
                <div id="analyzeResult" class="result-box"></div>
            </div>

            <div class="test-section">
                <div class="test-title">使用说明</div>
                <div style="color: #666; line-height: 1.6;">
                    <p><strong>测试步骤：</strong></p>
                    <ol>
                        <li>选择不同的数据库连接进行连接测试</li>
                        <li>点击"加载数据表"按钮获取对应数据库的表列表</li>
                        <li>从下拉列表中选择要分析的数据表</li>
                        <li>可选择设置表前缀，然后点击"分析表结构"</li>
                        <li>查看返回的结果，验证功能是否正常</li>
                    </ol>
                    
                    <p><strong>注意事项：</strong></p>
                    <ul>
                        <li>确保对应的数据库连接配置正确</li>
                        <li>确保数据库服务正在运行</li>
                        <li>某些连接可能需要先创建对应的数据库</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentConnection = 'mysql';
        let availableTables = [];

        // 测试数据库连接
        function testConnection() {
            const connection = document.getElementById('connectionSelect').value;
            const btn = document.getElementById('testConnBtn');
            const result = document.getElementById('connectionResult');
            
            btn.innerHTML = '<div class="loading"></div> 测试中...';
            
            fetch('/admin/system/databasetest', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `connection=${connection}`
            })
            .then(response => response.json())
            .then(data => {
                btn.textContent = '测试连接';
                result.style.display = 'block';
                
                if (data.code === 0) {
                    result.className = 'result-box success';
                    result.textContent = `连接测试成功！\n连接: ${connection}\n${JSON.stringify(data.data, null, 2)}`;
                } else {
                    result.className = 'result-box error';
                    result.textContent = `连接测试失败！\n错误: ${data.msg}`;
                }
            })
            .catch(error => {
                btn.textContent = '测试连接';
                result.style.display = 'block';
                result.className = 'result-box error';
                result.textContent = `请求失败: ${error.message}`;
            });
        }

        // 加载数据表列表
        function loadTables() {
            const connection = document.getElementById('connectionSelect').value;
            const btn = document.getElementById('loadTablesBtn');
            const result = document.getElementById('tablesResult');
            const tableSelect = document.getElementById('tableSelect');
            const tableSelectGroup = document.getElementById('tableSelectGroup');
            
            currentConnection = connection;
            btn.innerHTML = '<div class="loading"></div> 加载中...';
            
            fetch('/admin/system/curdgeneratev2', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `action=get_tables&connection=${connection}`
            })
            .then(response => response.json())
            .then(data => {
                btn.textContent = '加载数据表';
                result.style.display = 'block';
                
                if (data.code === 0) {
                    availableTables = data.data;
                    result.className = 'result-box success';
                    result.textContent = `数据表加载成功！\n连接: ${connection}\n表数量: ${data.data.length}\n\n表列表:\n${data.data.map(table => `- ${table.name} (${table.comment})`).join('\n')}`;
                    
                    // 更新表选择下拉列表
                    tableSelect.innerHTML = '<option value="">请选择数据表</option>';
                    data.data.forEach(table => {
                        const option = document.createElement('option');
                        option.value = table.name;
                        option.textContent = `${table.name} (${table.comment})`;
                        tableSelect.appendChild(option);
                    });
                    
                    tableSelectGroup.style.display = 'block';
                } else {
                    result.className = 'result-box error';
                    result.textContent = `数据表加载失败！\n连接: ${connection}\n错误: ${data.msg}`;
                    tableSelectGroup.style.display = 'none';
                }
            })
            .catch(error => {
                btn.textContent = '加载数据表';
                result.style.display = 'block';
                result.className = 'result-box error';
                result.textContent = `请求失败: ${error.message}`;
                tableSelectGroup.style.display = 'none';
            });
        }

        // 分析表结构
        function analyzeTable() {
            const tableName = document.getElementById('tableSelect').value;
            const tablePrefix = document.getElementById('tablePrefix').value;
            const btn = document.getElementById('analyzeBtn');
            const result = document.getElementById('analyzeResult');
            
            if (!tableName) {
                alert('请先选择数据表');
                return;
            }
            
            btn.innerHTML = '<div class="loading"></div> 分析中...';
            
            fetch('/admin/system/curdgeneratev2', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `action=analyze_table&table_name=${tableName}&table_prefix=${tablePrefix}&connection=${currentConnection}`
            })
            .then(response => response.json())
            .then(data => {
                btn.textContent = '分析表结构';
                result.style.display = 'block';
                
                if (data.code === 0) {
                    result.className = 'result-box success';
                    result.textContent = `表结构分析成功！\n连接: ${currentConnection}\n表名: ${tableName}\n前缀: ${tablePrefix || '无'}\n\n分析结果:\n${JSON.stringify(data.data, null, 2)}`;
                } else {
                    result.className = 'result-box error';
                    result.textContent = `表结构分析失败！\n连接: ${currentConnection}\n表名: ${tableName}\n错误: ${data.msg}`;
                }
            })
            .catch(error => {
                btn.textContent = '分析表结构';
                result.style.display = 'block';
                result.className = 'result-box error';
                result.textContent = `请求失败: ${error.message}`;
            });
        }

        // 连接选择变化时自动加载表列表
        document.getElementById('connectionSelect').addEventListener('change', function() {
            document.getElementById('tableSelectGroup').style.display = 'none';
            document.getElementById('tablesResult').style.display = 'none';
            document.getElementById('analyzeResult').style.display = 'none';
        });
    </script>
</body>
</html>