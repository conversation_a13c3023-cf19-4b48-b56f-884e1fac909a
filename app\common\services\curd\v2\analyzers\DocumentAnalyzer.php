<?php

namespace app\common\services\curd\v2\analyzers;

/**
 * 文档分析器
 * 分析生成的代码并设计相应的文档结构
 */
class DocumentAnalyzer
{
    protected array $documentTypes = [
        'api' => 'API 文档',
        'user' => '用户手册',
        'developer' => '开发者文档',
        'deployment' => '部署文档',
        'changelog' => '更新日志',
    ];

    protected array $documentSections = [
        'overview' => '概述',
        'installation' => '安装指南',
        'configuration' => '配置说明',
        'usage' => '使用方法',
        'api_reference' => 'API 参考',
        'examples' => '示例代码',
        'troubleshooting' => '故障排除',
        'faq' => '常见问题',
    ];

    /**
     * 分析文档需求
     */
    public function analyzeDocumentRequirements(array $generatedCode, array $tableInfo, array $options = []): array
    {
        $documentRequirements = [];

        // 分析 API 文档需求
        if (!empty($generatedCode['api_controller'])) {
            $documentRequirements['api'] = $this->analyzeApiDocumentRequirements($generatedCode, $tableInfo);
        }

        // 分析用户手册需求
        $documentRequirements['user'] = $this->analyzeUserManualRequirements($generatedCode, $tableInfo);

        // 分析开发者文档需求
        $documentRequirements['developer'] = $this->analyzeDeveloperDocumentRequirements($generatedCode, $tableInfo);

        // 分析部署文档需求
        $documentRequirements['deployment'] = $this->analyzeDeploymentDocumentRequirements($generatedCode, $tableInfo);

        return [
            'requirements' => $documentRequirements,
            'document_structure' => $this->generateDocumentStructure($documentRequirements),
            'content_outline' => $this->generateContentOutline($documentRequirements),
            'assets_needed' => $this->analyzeAssetsNeeded($documentRequirements),
        ];
    }

    /**
     * 分析 API 文档需求
     */
    protected function analyzeApiDocumentRequirements(array $generatedCode, array $tableInfo): array
    {
        $requirements = [];
        $modelName = $this->getModelName($tableInfo['name']);

        // 基础 API 文档
        $requirements[] = [
            'type' => 'api_overview',
            'title' => "{$modelName} API 概述",
            'description' => "介绍 {$modelName} 相关的 API 接口",
            'sections' => ['认证', '请求格式', '响应格式', '错误处理'],
        ];

        // CRUD 接口文档
        $crudOperations = [
            'index' => ['GET', '获取列表', '支持分页、搜索、排序'],
            'show' => ['GET', '获取详情', '根据ID获取单个资源'],
            'store' => ['POST', '创建资源', '创建新的资源实例'],
            'update' => ['PUT', '更新资源', '更新现有资源'],
            'destroy' => ['DELETE', '删除资源', '删除指定资源'],
        ];

        foreach ($crudOperations as $operation => $details) {
            $requirements[] = [
                'type' => 'api_endpoint',
                'operation' => $operation,
                'method' => $details[0],
                'title' => $details[1],
                'description' => $details[2],
                'parameters' => $this->getEndpointParameters($operation, $tableInfo),
                'responses' => $this->getEndpointResponses($operation),
                'examples' => $this->getEndpointExamples($operation, $tableInfo),
            ];
        }

        // 关联接口文档
        if (!empty($tableInfo['relationships'])) {
            foreach ($tableInfo['relationships'] as $relationship) {
                if ($relationship['confidence'] >= 70) {
                    $requirements[] = [
                        'type' => 'api_relationship',
                        'relationship' => $relationship,
                        'title' => "获取{$relationship['method_name']}关联",
                        'description' => "获取与当前资源关联的{$relationship['method_name']}数据",
                    ];
                }
            }
        }

        return $requirements;
    }

    /**
     * 分析用户手册需求
     */
    protected function analyzeUserManualRequirements(array $generatedCode, array $tableInfo): array
    {
        $requirements = [];
        $modelName = $this->getModelName($tableInfo['name']);

        // 功能概述
        $requirements[] = [
            'type' => 'user_overview',
            'title' => "{$modelName}管理功能概述",
            'description' => "介绍{$modelName}管理模块的主要功能和使用方法",
            'sections' => ['功能介绍', '界面说明', '操作流程'],
        ];

        // 操作指南
        $operations = [
            'list' => ['查看列表', '如何查看和搜索{$modelName}列表'],
            'create' => ['新增{$modelName}', '如何创建新的{$modelName}记录'],
            'edit' => ['编辑{$modelName}', '如何修改现有{$modelName}信息'],
            'delete' => ['删除{$modelName}', '如何删除{$modelName}记录'],
        ];

        foreach ($operations as $operation => $details) {
            $requirements[] = [
                'type' => 'user_operation',
                'operation' => $operation,
                'title' => $details[0],
                'description' => str_replace('{$modelName}', $modelName, $details[1]),
                'steps' => $this->getOperationSteps($operation),
                'screenshots' => $this->getRequiredScreenshots($operation),
            ];
        }

        // 字段说明
        $requirements[] = [
            'type' => 'field_reference',
            'title' => '字段说明',
            'description' => '详细说明各个字段的含义和填写要求',
            'fields' => $this->getFieldDocumentation($tableInfo['fields']),
        ];

        return $requirements;
    }

    /**
     * 分析开发者文档需求
     */
    protected function analyzeDeveloperDocumentRequirements(array $generatedCode, array $tableInfo): array
    {
        $requirements = [];
        $modelName = $this->getModelName($tableInfo['name']);

        // 代码结构说明
        $requirements[] = [
            'type' => 'code_structure',
            'title' => '代码结构说明',
            'description' => '介绍生成的代码文件结构和组织方式',
            'sections' => ['文件结构', '类关系', '数据流'],
        ];

        // 模型文档
        if (!empty($generatedCode['model'])) {
            $requirements[] = [
                'type' => 'model_documentation',
                'title' => "{$modelName} 模型文档",
                'description' => '详细说明模型的属性、方法和关联关系',
                'sections' => ['属性说明', '方法列表', '关联关系', '事件处理'],
            ];
        }

        // 控制器文档
        if (!empty($generatedCode['controller'])) {
            $requirements[] = [
                'type' => 'controller_documentation',
                'title' => "{$modelName} 控制器文档",
                'description' => '详细说明控制器的方法和业务逻辑',
                'sections' => ['方法列表', '参数说明', '返回值', '异常处理'],
            ];
        }

        // 扩展指南
        $requirements[] = [
            'type' => 'extension_guide',
            'title' => '扩展开发指南',
            'description' => '如何基于生成的代码进行二次开发和扩展',
            'sections' => ['自定义验证', '添加业务逻辑', '扩展关联关系', '性能优化'],
        ];

        return $requirements;
    }

    /**
     * 分析部署文档需求
     */
    protected function analyzeDeploymentDocumentRequirements(array $generatedCode, array $tableInfo): array
    {
        $requirements = [];

        // 环境要求
        $requirements[] = [
            'type' => 'environment_requirements',
            'title' => '环境要求',
            'description' => '部署所需的系统环境和依赖',
            'sections' => ['系统要求', '软件依赖', '扩展要求'],
        ];

        // 安装步骤
        $requirements[] = [
            'type' => 'installation_steps',
            'title' => '安装步骤',
            'description' => '详细的安装和配置步骤',
            'sections' => ['下载代码', '安装依赖', '配置数据库', '运行迁移'],
        ];

        // 配置说明
        $requirements[] = [
            'type' => 'configuration_guide',
            'title' => '配置说明',
            'description' => '各种配置选项的说明和建议',
            'sections' => ['数据库配置', '缓存配置', '队列配置', '日志配置'],
        ];

        // 维护指南
        $requirements[] = [
            'type' => 'maintenance_guide',
            'title' => '维护指南',
            'description' => '日常维护和故障排除指南',
            'sections' => ['备份策略', '性能监控', '日志分析', '故障排除'],
        ];

        return $requirements;
    }

    /**
     * 生成文档结构
     */
    protected function generateDocumentStructure(array $documentRequirements): array
    {
        $structure = [
            'root' => [
                'README.md' => '项目概述和快速开始',
                'docs/' => [
                    'api/' => [
                        'README.md' => 'API 文档概述',
                        'endpoints.md' => 'API 端点详细说明',
                        'authentication.md' => '认证说明',
                        'examples.md' => '示例代码',
                    ],
                    'user/' => [
                        'README.md' => '用户手册概述',
                        'getting-started.md' => '快速开始',
                        'user-guide.md' => '详细使用指南',
                        'faq.md' => '常见问题',
                    ],
                    'developer/' => [
                        'README.md' => '开发者文档概述',
                        'architecture.md' => '架构说明',
                        'code-structure.md' => '代码结构',
                        'extension-guide.md' => '扩展指南',
                    ],
                    'deployment/' => [
                        'README.md' => '部署文档概述',
                        'installation.md' => '安装指南',
                        'configuration.md' => '配置说明',
                        'maintenance.md' => '维护指南',
                    ],
                ],
                'examples/' => [
                    'api-examples/' => 'API 调用示例',
                    'code-examples/' => '代码示例',
                ],
                'assets/' => [
                    'images/' => '文档图片',
                    'diagrams/' => '架构图和流程图',
                ],
            ],
        ];

        return $structure;
    }

    /**
     * 生成内容大纲
     */
    protected function generateContentOutline(array $documentRequirements): array
    {
        $outline = [];

        foreach ($documentRequirements as $type => $requirements) {
            $outline[$type] = [
                'title' => $this->documentTypes[$type] ?? $type,
                'sections' => [],
            ];

            foreach ($requirements as $requirement) {
                $outline[$type]['sections'][] = [
                    'title' => $requirement['title'],
                    'description' => $requirement['description'],
                    'subsections' => $requirement['sections'] ?? [],
                ];
            }
        }

        return $outline;
    }

    /**
     * 分析所需资源
     */
    protected function analyzeAssetsNeeded(array $documentRequirements): array
    {
        $assets = [
            'images' => [],
            'diagrams' => [],
            'examples' => [],
            'templates' => [],
        ];

        // 用户手册需要截图
        if (isset($documentRequirements['user'])) {
            $assets['images'] = [
                'list-page.png' => '列表页面截图',
                'create-form.png' => '创建表单截图',
                'edit-form.png' => '编辑表单截图',
                'detail-page.png' => '详情页面截图',
            ];
        }

        // 开发者文档需要架构图
        if (isset($documentRequirements['developer'])) {
            $assets['diagrams'] = [
                'architecture.svg' => '系统架构图',
                'data-flow.svg' => '数据流程图',
                'class-diagram.svg' => '类关系图',
            ];
        }

        // API 文档需要示例
        if (isset($documentRequirements['api'])) {
            $assets['examples'] = [
                'api-requests.json' => 'API 请求示例',
                'api-responses.json' => 'API 响应示例',
                'postman-collection.json' => 'Postman 集合',
            ];
        }

        return $assets;
    }

    /**
     * 获取端点参数
     */
    protected function getEndpointParameters(string $operation, array $tableInfo): array
    {
        $parameters = [];

        switch ($operation) {
            case 'index':
                $parameters = [
                    'page' => ['type' => 'integer', 'description' => '页码', 'default' => 1],
                    'per_page' => ['type' => 'integer', 'description' => '每页数量', 'default' => 15],
                    'sort' => ['type' => 'string', 'description' => '排序字段', 'default' => 'id'],
                    'order' => ['type' => 'string', 'description' => '排序方向', 'enum' => ['asc', 'desc']],
                ];
                break;

            case 'show':
                $parameters = [
                    'id' => ['type' => 'integer', 'description' => '资源ID', 'required' => true],
                ];
                break;

            case 'store':
            case 'update':
                foreach ($tableInfo['fields'] as $field) {
                    if (!in_array($field['name'], ['id', 'created_at', 'updated_at', 'deleted_at'])) {
                        $parameters[$field['name']] = [
                            'type' => $this->mapFieldTypeToApiType($field['type']),
                            'description' => $field['comment'] ?: $field['name'],
                            'required' => !empty($field['required']),
                        ];
                    }
                }
                break;

            case 'destroy':
                $parameters = [
                    'id' => ['type' => 'integer', 'description' => '资源ID', 'required' => true],
                ];
                break;
        }

        return $parameters;
    }

    /**
     * 获取端点响应
     */
    protected function getEndpointResponses(string $operation): array
    {
        $responses = [
            'index' => [
                '200' => ['description' => '成功返回列表', 'schema' => 'PaginatedResponse'],
                '400' => ['description' => '请求参数错误'],
                '500' => ['description' => '服务器内部错误'],
            ],
            'show' => [
                '200' => ['description' => '成功返回资源详情', 'schema' => 'ResourceResponse'],
                '404' => ['description' => '资源不存在'],
                '500' => ['description' => '服务器内部错误'],
            ],
            'store' => [
                '201' => ['description' => '成功创建资源', 'schema' => 'ResourceResponse'],
                '422' => ['description' => '验证失败'],
                '500' => ['description' => '服务器内部错误'],
            ],
            'update' => [
                '200' => ['description' => '成功更新资源', 'schema' => 'ResourceResponse'],
                '404' => ['description' => '资源不存在'],
                '422' => ['description' => '验证失败'],
                '500' => ['description' => '服务器内部错误'],
            ],
            'destroy' => [
                '204' => ['description' => '成功删除资源'],
                '404' => ['description' => '资源不存在'],
                '500' => ['description' => '服务器内部错误'],
            ],
        ];

        return $responses[$operation] ?? [];
    }

    /**
     * 获取端点示例
     */
    protected function getEndpointExamples(string $operation, array $tableInfo): array
    {
        $examples = [];
        $modelName = strtolower($this->getModelName($tableInfo['name']));

        switch ($operation) {
            case 'index':
                $examples['request'] = "GET /api/{$modelName}?page=1&per_page=10";
                $examples['response'] = [
                    'data' => ['...'],
                    'meta' => ['current_page' => 1, 'per_page' => 10, 'total' => 100],
                ];
                break;

            case 'show':
                $examples['request'] = "GET /api/{$modelName}/1";
                $examples['response'] = ['data' => ['id' => 1, '...']];
                break;

            case 'store':
                $examples['request'] = "POST /api/{$modelName}";
                $examples['body'] = $this->generateExampleData($tableInfo['fields']);
                $examples['response'] = ['data' => ['id' => 1, '...']];
                break;
        }

        return $examples;
    }

    /**
     * 获取操作步骤
     */
    protected function getOperationSteps(string $operation): array
    {
        $steps = [
            'list' => [
                '1. 进入管理后台',
                '2. 点击左侧菜单中的对应模块',
                '3. 查看列表页面',
                '4. 使用搜索和筛选功能',
            ],
            'create' => [
                '1. 在列表页面点击"新增"按钮',
                '2. 填写必填字段',
                '3. 选择可选字段',
                '4. 点击"保存"按钮',
            ],
            'edit' => [
                '1. 在列表页面找到要编辑的记录',
                '2. 点击"编辑"按钮',
                '3. 修改相应字段',
                '4. 点击"保存"按钮',
            ],
            'delete' => [
                '1. 在列表页面找到要删除的记录',
                '2. 点击"删除"按钮',
                '3. 确认删除操作',
            ],
        ];

        return $steps[$operation] ?? [];
    }

    /**
     * 获取所需截图
     */
    protected function getRequiredScreenshots(string $operation): array
    {
        $screenshots = [
            'list' => ['list-page.png', 'search-filter.png'],
            'create' => ['create-button.png', 'create-form.png', 'save-success.png'],
            'edit' => ['edit-button.png', 'edit-form.png', 'update-success.png'],
            'delete' => ['delete-button.png', 'confirm-dialog.png', 'delete-success.png'],
        ];

        return $screenshots[$operation] ?? [];
    }

    /**
     * 获取字段文档
     */
    protected function getFieldDocumentation(array $fields): array
    {
        $documentation = [];

        foreach ($fields as $field) {
            if (!in_array($field['name'], ['id', 'created_at', 'updated_at', 'deleted_at'])) {
                $documentation[] = [
                    'name' => $field['name'],
                    'type' => $field['type'],
                    'required' => !empty($field['required']),
                    'description' => $field['comment'] ?: '待补充说明',
                    'validation' => $this->getFieldValidationRules($field),
                    'example' => $this->getFieldExample($field),
                ];
            }
        }

        return $documentation;
    }

    /**
     * 映射字段类型到API类型
     */
    protected function mapFieldTypeToApiType(string $fieldType): string
    {
        $mapping = [
            'int' => 'integer',
            'bigint' => 'integer',
            'varchar' => 'string',
            'text' => 'string',
            'date' => 'string',
            'datetime' => 'string',
            'timestamp' => 'string',
            'decimal' => 'number',
            'float' => 'number',
            'boolean' => 'boolean',
        ];

        return $mapping[$fieldType] ?? 'string';
    }

    /**
     * 生成示例数据
     */
    protected function generateExampleData(array $fields): array
    {
        $data = [];

        foreach ($fields as $field) {
            if (!in_array($field['name'], ['id', 'created_at', 'updated_at', 'deleted_at'])) {
                $data[$field['name']] = $this->getFieldExample($field);
            }
        }

        return $data;
    }

    /**
     * 获取字段验证规则
     */
    protected function getFieldValidationRules(array $field): array
    {
        $rules = [];

        if (!empty($field['required'])) {
            $rules[] = '必填';
        }

        if (!empty($field['length'])) {
            $rules[] = "最大长度: {$field['length']}";
        }

        if (!empty($field['unique'])) {
            $rules[] = '唯一值';
        }

        return $rules;
    }

    /**
     * 获取字段示例
     */
    protected function getFieldExample(array $field): string
    {
        switch ($field['type']) {
            case 'varchar':
                return '示例文本';
            case 'text':
                return '这是一段示例文本内容';
            case 'int':
            case 'bigint':
                return '123';
            case 'date':
                return '2024-01-01';
            case 'datetime':
            case 'timestamp':
                return '2024-01-01 12:00:00';
            case 'decimal':
            case 'float':
                return '99.99';
            case 'boolean':
                return 'true';
            default:
                return '示例值';
        }
    }

    /**
     * 获取模型名称
     */
    protected function getModelName(string $tableName): string
    {
        $name = preg_replace('/^[a-z]+_/', '', $tableName);
        return str_replace(' ', '', ucwords(str_replace('_', ' ', $name)));
    }
}
