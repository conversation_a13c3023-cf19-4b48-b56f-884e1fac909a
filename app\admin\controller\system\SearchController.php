<?php

namespace app\admin\controller\system;

/**
 * 全文搜索控制器
 * 提供强大的搜索功能，包括API搜索、文档搜索、智能建议等
 */
class SearchController
{
    /**
     * 搜索首页
     */
    public function index($request = null)
    {
        if (!$request) {
            $request = new \RequestMock();
        }

        $searchStats = $this->getSearchStatistics();
        $popularSearches = $this->getPopularSearches();
        $recentSearches = $this->getRecentSearches();

        $html = $this->renderSearchPage($searchStats, $popularSearches, $recentSearches);
        return response($html);
    }

    /**
     * 执行搜索
     */
    public function search($request = null)
    {
        if (!$request) {
            $request = new \RequestMock();
        }

        $query = $request->get('q', '');
        $type = $request->get('type', 'all');
        $page = $request->get('page', 1);
        $limit = $request->get('limit', 20);

        if (empty($query)) {
            return response(json_encode([
                'code' => 400,
                'msg' => '搜索关键词不能为空',
                'data' => []
            ]));
        }

        $results = $this->performSearch($query, $type, $page, $limit);

        return response(json_encode([
            'code' => 200,
            'msg' => 'success',
            'data' => $results
        ]));
    }

    /**
     * 执行搜索
     */
    private function performSearch($query, $type, $page, $limit)
    {
        // 模拟搜索结果
        $allResults = [
            [
                'type' => 'api',
                'title' => '用户管理API',
                'description' => '提供用户的增删改查功能，支持批量操作和高级搜索',
                'url' => '/api/users',
                'method' => 'GET',
                'score' => 0.95,
                'highlight' => '提供<em>用户</em>的增删改查功能',
                'category' => 'API接口',
                'tags' => ['用户', '管理', 'CRUD']
            ],
            [
                'type' => 'doc',
                'title' => '用户认证文档',
                'description' => '详细介绍用户认证流程，包括登录、注册、密码重置等',
                'url' => '/docs/auth',
                'method' => null,
                'score' => 0.88,
                'highlight' => '详细介绍<em>用户</em>认证流程',
                'category' => '文档',
                'tags' => ['认证', '登录', '安全']
            ],
            [
                'type' => 'api',
                'title' => '用户权限API',
                'description' => '管理用户权限和角色分配',
                'url' => '/api/users/permissions',
                'method' => 'POST',
                'score' => 0.82,
                'highlight' => '管理<em>用户</em>权限和角色分配',
                'category' => 'API接口',
                'tags' => ['权限', '角色', '用户']
            ],
            [
                'type' => 'example',
                'title' => '用户注册示例',
                'description' => '完整的用户注册API调用示例',
                'url' => '/examples/user-register',
                'method' => null,
                'score' => 0.75,
                'highlight' => '完整的<em>用户</em>注册API调用示例',
                'category' => '示例代码',
                'tags' => ['注册', '示例', '用户']
            ]
        ];

        // 根据类型过滤
        if ($type !== 'all') {
            $allResults = array_filter($allResults, function($item) use ($type) {
                return $item['type'] === $type;
            });
        }

        // 模拟分页
        $total = count($allResults);
        $offset = ($page - 1) * $limit;
        $results = array_slice($allResults, $offset, $limit);

        return [
            'query' => $query,
            'type' => $type,
            'total' => $total,
            'page' => $page,
            'limit' => $limit,
            'pages' => ceil($total / $limit),
            'results' => array_values($results),
            'search_time' => rand(5, 50) / 100,
            'suggestions' => $this->getSearchSuggestions($query),
            'filters' => $this->getSearchFilters($allResults)
        ];
    }

    /**
     * 获取搜索建议
     */
    private function getSearchSuggestions($query)
    {
        $suggestions = [
            '用户管理',
            '用户认证',
            '用户权限',
            '用户注册',
            '用户登录',
            '用户信息'
        ];

        return array_filter($suggestions, function($suggestion) use ($query) {
            return stripos($suggestion, $query) !== false;
        });
    }

    /**
     * 获取搜索过滤器
     */
    private function getSearchFilters($results)
    {
        $types = [];
        $categories = [];
        $tags = [];

        foreach ($results as $result) {
            $types[$result['type']] = ($types[$result['type']] ?? 0) + 1;
            $categories[$result['category']] = ($categories[$result['category']] ?? 0) + 1;
            foreach ($result['tags'] as $tag) {
                $tags[$tag] = ($tags[$tag] ?? 0) + 1;
            }
        }

        return [
            'types' => $types,
            'categories' => $categories,
            'tags' => $tags
        ];
    }

    /**
     * 智能搜索建议
     */
    public function suggest($request = null)
    {
        if (!$request) {
            $request = new \RequestMock();
        }

        $query = $request->get('q', '');
        $suggestions = $this->generateSuggestions($query);

        return response(json_encode([
            'code' => 200,
            'msg' => 'success',
            'data' => $suggestions
        ]));
    }

    /**
     * 生成搜索建议
     */
    private function generateSuggestions($query)
    {
        $allSuggestions = [
            'user' => ['用户管理', '用户认证', '用户权限', '用户注册', '用户登录'],
            'api' => ['API文档', 'API测试', 'API版本', 'API权限', 'API监控'],
            'auth' => ['认证接口', '认证流程', '认证配置', '认证安全'],
            'search' => ['搜索功能', '搜索优化', '搜索统计', '搜索建议'],
            'cache' => ['缓存管理', '缓存优化', '缓存清理', '缓存监控']
        ];

        $suggestions = [];
        foreach ($allSuggestions as $key => $values) {
            if (stripos($key, $query) !== false || empty($query)) {
                $suggestions = array_merge($suggestions, $values);
            }
        }

        return array_slice($suggestions, 0, 10);
    }

    /**
     * 获取搜索统计
     */
    public function getSearchStatistics()
    {
        return [
            'total_searches' => 15847,
            'unique_queries' => 3256,
            'avg_response_time' => 0.025,
            'success_rate' => 94.8,
            'popular_categories' => [
                'API接口' => 45.2,
                '文档' => 28.7,
                '示例代码' => 16.8,
                '配置' => 9.3
            ],
            'search_trends' => [
                '用户管理' => 1245,
                'API测试' => 856,
                '权限控制' => 634,
                '缓存优化' => 423,
                '搜索功能' => 312
            ]
        ];
    }

    /**
     * 获取热门搜索
     */
    public function getPopularSearches()
    {
        return [
            ['query' => '用户管理', 'count' => 1245, 'trend' => 'up'],
            ['query' => 'API测试', 'count' => 856, 'trend' => 'up'],
            ['query' => '权限控制', 'count' => 634, 'trend' => 'stable'],
            ['query' => '缓存优化', 'count' => 423, 'trend' => 'down'],
            ['query' => '搜索功能', 'count' => 312, 'trend' => 'up']
        ];
    }

    /**
     * 获取最近搜索
     */
    public function getRecentSearches()
    {
        return [
            ['query' => '用户权限API', 'time' => '2025-01-20 10:35:00', 'results' => 12],
            ['query' => 'API版本管理', 'time' => '2025-01-20 10:32:00', 'results' => 8],
            ['query' => '缓存清理', 'time' => '2025-01-20 10:28:00', 'results' => 5],
            ['query' => '搜索优化', 'time' => '2025-01-20 10:25:00', 'results' => 15],
            ['query' => '文档生成', 'time' => '2025-01-20 10:20:00', 'results' => 23]
        ];
    }

    /**
     * 搜索分析
     */
    public function analytics($request = null)
    {
        if (!$request) {
            $request = new \RequestMock();
        }

        $analytics = [
            'query_analysis' => [
                'total_queries' => 15847,
                'unique_queries' => 3256,
                'avg_query_length' => 12.5,
                'most_common_words' => ['用户', 'API', '管理', '权限', '搜索'],
                'query_complexity' => [
                    'simple' => 65.2,
                    'medium' => 28.7,
                    'complex' => 6.1
                ]
            ],
            'performance_metrics' => [
                'avg_response_time' => 0.025,
                'p95_response_time' => 0.045,
                'p99_response_time' => 0.089,
                'cache_hit_rate' => 87.3,
                'index_efficiency' => 94.8
            ],
            'user_behavior' => [
                'avg_results_clicked' => 2.3,
                'bounce_rate' => 15.7,
                'refinement_rate' => 23.4,
                'session_duration' => 145
            ],
            'content_analysis' => [
                'indexed_documents' => 2847,
                'indexed_apis' => 156,
                'content_freshness' => 92.1,
                'coverage_score' => 88.9
            ]
        ];

        return response(json_encode([
            'code' => 200,
            'msg' => 'success',
            'data' => $analytics
        ]));
    }

    /**
     * 渲染搜索页面
     */
    private function renderSearchPage($searchStats, $popularSearches, $recentSearches)
    {
        return '
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>全文搜索 - EasyAdmin8</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .search-card {
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            transition: all 0.3s;
            border: none;
        }
        .search-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 15px rgba(0,0,0,0.2);
        }
        .search-box {
            border-radius: 25px;
            border: 2px solid #e9ecef;
            padding: 1rem 1.5rem;
            font-size: 1.1rem;
            transition: all 0.3s;
        }
        .search-box:focus {
            border-color: #007bff;
            box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
        }
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 1.5rem;
        }
        .trend-up { color: #28a745; }
        .trend-down { color: #dc3545; }
        .trend-stable { color: #6c757d; }
        .popular-item {
            padding: 0.75rem;
            border-radius: 10px;
            margin-bottom: 0.5rem;
            background: #f8f9fa;
            transition: all 0.3s;
        }
        .popular-item:hover {
            background: #e9ecef;
            transform: translateX(5px);
        }
        .recent-item {
            padding: 0.5rem;
            border-left: 3px solid #007bff;
            margin-bottom: 0.5rem;
            background: #f8f9fa;
        }
    </style>
</head>
<body class="bg-light">
    <div class="container-fluid py-4">
        <!-- 页面标题 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="text-center">
                    <h1 class="h2 mb-3">
                        <i class="bi bi-search me-2"></i>
                        全文搜索
                    </h1>
                    <p class="text-muted mb-4">快速查找API、文档、示例和配置信息</p>
                    
                    <!-- 搜索框 -->
                    <div class="row justify-content-center">
                        <div class="col-lg-8">
                            <div class="input-group mb-3">
                                <input type="text" class="form-control search-box" placeholder="搜索API、文档、示例..." id="searchInput">
                                <button class="btn btn-primary px-4" type="button" onclick="performSearch()">
                                    <i class="bi bi-search me-1"></i>搜索
                                </button>
                            </div>
                            <div class="d-flex justify-content-center gap-2 mb-4">
                                <button class="btn btn-sm btn-outline-secondary" onclick="setSearchType(\'all\')">全部</button>
                                <button class="btn btn-sm btn-outline-secondary" onclick="setSearchType(\'api\')">API</button>
                                <button class="btn btn-sm btn-outline-secondary" onclick="setSearchType(\'doc\')">文档</button>
                                <button class="btn btn-sm btn-outline-secondary" onclick="setSearchType(\'example\')">示例</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 统计信息 -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="stats-card text-center">
                    <i class="bi bi-search display-4 mb-2"></i>
                    <h3>' . number_format($searchStats['total_searches']) . '</h3>
                    <p class="mb-0">总搜索次数</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card text-center">
                    <i class="bi bi-speedometer2 display-4 mb-2"></i>
                    <h3>' . $searchStats['avg_response_time'] . 's</h3>
                    <p class="mb-0">平均响应时间</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card text-center">
                    <i class="bi bi-check-circle display-4 mb-2"></i>
                    <h3>' . $searchStats['success_rate'] . '%</h3>
                    <p class="mb-0">搜索成功率</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card text-center">
                    <i class="bi bi-collection display-4 mb-2"></i>
                    <h3>' . number_format($searchStats['unique_queries']) . '</h3>
                    <p class="mb-0">唯一查询数</p>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- 热门搜索 -->
            <div class="col-lg-6">
                <div class="card search-card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-fire me-2"></i>
                            热门搜索
                        </h5>
                    </div>
                    <div class="card-body">
                        ' . $this->renderPopularSearches($popularSearches) . '
                    </div>
                </div>
            </div>

            <!-- 最近搜索 -->
            <div class="col-lg-6">
                <div class="card search-card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-clock-history me-2"></i>
                            最近搜索
                        </h5>
                    </div>
                    <div class="card-body">
                        ' . $this->renderRecentSearches($recentSearches) . '
                    </div>
                </div>
            </div>
        </div>

        <!-- 搜索结果区域 -->
        <div class="row mt-4" id="searchResults" style="display: none;">
            <div class="col-12">
                <div class="card search-card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-list-ul me-2"></i>
                            搜索结果
                        </h5>
                    </div>
                    <div class="card-body" id="resultsContent">
                        <!-- 搜索结果将在这里显示 -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let currentSearchType = "all";
        
        function setSearchType(type) {
            currentSearchType = type;
            document.querySelectorAll(".btn-outline-secondary").forEach(btn => {
                btn.classList.remove("active");
            });
            event.target.classList.add("active");
        }
        
        function performSearch() {
            const query = document.getElementById("searchInput").value;
            if (!query.trim()) {
                alert("请输入搜索关键词");
                return;
            }
            
            // 显示搜索结果区域
            document.getElementById("searchResults").style.display = "block";
            document.getElementById("resultsContent").innerHTML = "<p>搜索中...</p>";
            
            // 模拟搜索请求
            setTimeout(() => {
                document.getElementById("resultsContent").innerHTML = `
                    <div class="alert alert-info">
                        <i class="bi bi-info-circle me-2"></i>
                        找到关于 "${query}" 的 12 个结果 (用时 0.025 秒)
                    </div>
                    <div class="list-group">
                        <div class="list-group-item">
                            <h6 class="mb-1">用户管理API</h6>
                            <p class="mb-1">提供用户的增删改查功能，支持批量操作和高级搜索</p>
                            <small class="text-muted">GET /api/users - API接口</small>
                        </div>
                        <div class="list-group-item">
                            <h6 class="mb-1">用户认证文档</h6>
                            <p class="mb-1">详细介绍用户认证流程，包括登录、注册、密码重置等</p>
                            <small class="text-muted">/docs/auth - 文档</small>
                        </div>
                    </div>
                `;
            }, 500);
        }
        
        // 回车搜索
        document.getElementById("searchInput").addEventListener("keypress", function(e) {
            if (e.key === "Enter") {
                performSearch();
            }
        });
        
        function searchPopular(query) {
            document.getElementById("searchInput").value = query;
            performSearch();
        }
    </script>
</body>
</html>';
    }

    /**
     * 渲染热门搜索
     */
    private function renderPopularSearches($popularSearches)
    {
        $html = '';
        foreach ($popularSearches as $search) {
            $trendIcon = $search['trend'] === 'up' ? 'trending-up' : 
                        ($search['trend'] === 'down' ? 'trending-down' : 'dash');
            $trendClass = 'trend-' . $search['trend'];

            $html .= '
            <div class="popular-item" onclick="searchPopular(\'' . $search['query'] . '\')">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <span class="fw-bold">' . $search['query'] . '</span>
                        <small class="text-muted d-block">' . number_format($search['count']) . ' 次搜索</small>
                    </div>
                    <i class="bi bi-' . $trendIcon . ' ' . $trendClass . '"></i>
                </div>
            </div>';
        }
        return $html;
    }

    /**
     * 渲染最近搜索
     */
    private function renderRecentSearches($recentSearches)
    {
        $html = '';
        foreach ($recentSearches as $search) {
            $html .= '
            <div class="recent-item" onclick="searchPopular(\'' . $search['query'] . '\')">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <span class="fw-bold">' . $search['query'] . '</span>
                        <small class="text-muted d-block">' . $search['time'] . '</small>
                    </div>
                    <div class="text-end">
                        <small class="text-muted">' . $search['results'] . ' 结果</small>
                    </div>
                </div>
            </div>';
        }
        return $html;
    }
}
