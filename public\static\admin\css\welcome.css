.layui-card {
    border: 1px solid #f2f2f2;
    border-radius: 5px;
}

.dark .layui-card {
    border: 1px solid #363636;
    border-radius: 5px;
}

.icon {
    margin-right: 10px;
    color: #1aa094;
}

.icon-cray {
    color: #ffb800 !important;
}

.icon-blue {
    color: #1e9fff !important;
}

.icon-tip {
    color: #ff5722 !important;
}

.layuimini-qiuck-module {
    text-align: center;
    margin-top: 10px
}

.layuimini-qiuck-module a i {
    display: inline-block;
    width: 80%;
    height: 60px;
    line-height: 60px;
    text-align: center;
    border-radius: 2px;
    font-size: 30px;
    background-color: #F8F8F8;
    color: #333;
    transition: all .3s;
    -webkit-transition: all .3s;
}

.layuimini-qiuck-module a cite {
    position: relative;
    top: 2px;
    display: block;
    color: #666;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    font-size: 14px;
}

.welcome-module {
    width: 100%;
    height: 210px;
    max-height: 210px;
    overflow: hidden;
}

.panel {
    border: 1px solid transparent;
    border-radius: 3px;
    -webkit-box-shadow: 0 1px 1px rgba(0, 0, 0, .05);
    box-shadow: 0 1px 1px rgba(0, 0, 0, .05)
}

.panel-body {
    padding: 10px
}

.panel-title {
    margin-top: 0;
    margin-bottom: 0;
    font-size: 12px;
    color: inherit
}

.label {
    display: inline;
    padding: .2em .6em .3em;
    font-size: 75%;
    font-weight: 700;
    line-height: 1;
    color: #fff;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: .25em;
    margin-top: .3em;
}

.layui-red {
    color: red
}

.main_btn > p {
    height: 40px;
}

/*.layui-bg-number {*/
/*    background-color: #F8F8F8;*/
/*}*/

.layuimini-notice:hover {
    background: #f6f6f6;
}

.layuimini-notice {
    padding: 7px 16px;
    clear: both;
    font-size: 12px !important;
    cursor: pointer;
    position: relative;
    transition: background 0.2s ease-in-out;
}

.layuimini-notice-title, .layuimini-notice-label {
    padding-right: 70px !important;
    text-overflow: ellipsis !important;
    overflow: hidden !important;
    white-space: nowrap !important;
}

.layuimini-notice-title {
    line-height: 28px;
    font-size: 14px;
}

.layuimini-notice-extra {
    position: absolute;
    top: 50%;
    margin-top: -8px;
    right: 16px;
    display: inline-block;
    height: 16px;
    color: #999;
}