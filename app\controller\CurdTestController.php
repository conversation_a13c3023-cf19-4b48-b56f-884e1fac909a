<?php

namespace app\controller;

use app\common\services\curd\v2\CurdGenerator;
use support\Request;
use support\Response;

/**
 * CURD 测试控制器 - 无需登录验证
 */
class CurdTestController
{
    protected CurdGenerator $generator;

    public function __construct()
    {
        $this->generator = new CurdGenerator();
    }

    /**
     * 获取表列表 - 无需登录
     */
    public function getTables(Request $request): Response
    {
        try {
            $connection = $request->input('connection', 'mysql_second');
            $tables = $this->generator->getAllTables($connection);

            return $this->success('获取成功', $tables);
        } catch (\Exception $e) {
            // 如果数据库连接失败，返回模拟数据
            $mockTables = $this->getMockTablesForConnection($request->input('connection', 'mysql_second'));
            return $this->success('获取成功 (演示数据)', $mockTables);
        }
    }

    /**
     * 分析表结构 - 无需登录
     */
    public function analyzeTable(Request $request): Response
    {
        try {
            $tableName = $request->input('table_name');
            $tablePrefix = $request->input('table_prefix', '');
            $connection = $request->input('connection', 'mysql_second');

            if (empty($tableName)) {
                return $this->error('表名不能为空');
            }

            if (!$this->generator->validateTable($tableName, $tablePrefix, $connection)) {
                return $this->error('表不存在');
            }

            $tableInfo = $this->generator->getTableInfo($tableName, $tablePrefix, $connection);

            return $this->success('分析成功', $tableInfo);
        } catch (\Exception $e) {
            // 如果数据库连接失败，返回模拟表信息
            try {
                $mockTableInfo = $this->getMockTableInfo($request->input('table_name'), $request->input('connection', 'mysql_second'));
                return $this->success('分析成功 (演示数据)', $mockTableInfo);
            } catch (\Exception $mockError) {
                return $this->error('分析失败: ' . $e->getMessage());
            }
        }
    }

    /**
     * 获取模拟表数据
     */
    protected function getMockTablesForConnection(string $connection): array
    {
        $mockTables = [
            'mysql' => [
                ['name' => 'users', 'comment' => '用户表'],
                ['name' => 'articles', 'comment' => '文章表'],
                ['name' => 'categories', 'comment' => '分类表'],
                ['name' => 'config', 'comment' => '配置表'],
                ['name' => 'admin_users', 'comment' => '管理员表'],
                ['name' => 'permissions', 'comment' => '权限表'],
            ],
            'mysql_read' => [
                ['name' => 'users', 'comment' => '用户表(读库)'],
                ['name' => 'articles', 'comment' => '文章表(读库)'],
                ['name' => 'statistics', 'comment' => '统计表(读库)'],
            ],
            'mysql_second' => [
                ['name' => 'admin', 'comment' => '管理员表'],
                ['name' => 'member', 'comment' => '会员表'],
                ['name' => 'shop_product', 'comment' => '商品表'],
                ['name' => 'shop_order', 'comment' => '订单表'],
                ['name' => 'article', 'comment' => '文章表'],
                ['name' => 'business', 'comment' => '商家表'],
                ['name' => 'restaurant_shop', 'comment' => '餐厅店铺表'],
                ['name' => 'restaurant_food', 'comment' => '餐厅菜品表'],
                ['name' => 'wxpay_log', 'comment' => '微信支付日志'],
                ['name' => 'alipay_log', 'comment' => '支付宝支付日志'],
            ],
            'mysql_log' => [
                ['name' => 'access_logs', 'comment' => '访问日志'],
                ['name' => 'error_logs', 'comment' => '错误日志'],
                ['name' => 'operation_logs', 'comment' => '操作日志'],
            ],
            'mysql_cache' => [
                ['name' => 'cache_data', 'comment' => '缓存数据'],
                ['name' => 'sessions', 'comment' => '会话数据'],
            ],
        ];

        return $mockTables[$connection] ?? [
            ['name' => 'demo_table', 'comment' => '演示表'],
        ];
    }

    /**
     * 获取模拟表信息
     */
    protected function getMockTableInfo(string $tableName, string $connection): array
    {
        $mockFields = $this->getMockFields($tableName);

        return [
            'name' => $tableName,
            'comment' => $this->getMockTableComment($tableName),
            'connection' => $connection,
            'fields' => $mockFields,
            'indexes' => [
                [
                    'name' => 'PRIMARY',
                    'unique' => true,
                    'primary' => true,
                    'columns' => ['id'],
                ]
            ],
            'foreign_keys' => [],
        ];
    }

    /**
     * 获取模拟字段
     */
    protected function getMockFields(string $tableName): array
    {
        $commonFields = [
            [
                'name' => 'id',
                'type' => 'int',
                'length' => 11,
                'nullable' => false,
                'default' => null,
                'comment' => '主键ID',
                'primary' => true,
                'auto_increment' => true,
                'component' => 'hidden',
            ],
            [
                'name' => 'created_at',
                'type' => 'timestamp',
                'length' => null,
                'nullable' => true,
                'default' => 'CURRENT_TIMESTAMP',
                'comment' => '创建时间',
                'primary' => false,
                'auto_increment' => false,
                'component' => 'datetime',
            ],
            [
                'name' => 'updated_at',
                'type' => 'timestamp',
                'length' => null,
                'nullable' => true,
                'default' => 'CURRENT_TIMESTAMP',
                'comment' => '更新时间',
                'primary' => false,
                'auto_increment' => false,
                'component' => 'datetime',
            ]
        ];

        $specificFields = [];

        switch ($tableName) {
            case 'admin':
                $specificFields = [
                    [
                        'name' => 'username',
                        'type' => 'varchar',
                        'length' => 50,
                        'nullable' => false,
                        'default' => null,
                        'comment' => '用户名',
                        'primary' => false,
                        'auto_increment' => false,
                        'component' => 'input',
                    ],
                    [
                        'name' => 'password',
                        'type' => 'varchar',
                        'length' => 255,
                        'nullable' => false,
                        'default' => null,
                        'comment' => '密码',
                        'primary' => false,
                        'auto_increment' => false,
                        'component' => 'password',
                    ],
                    [
                        'name' => 'status',
                        'type' => 'tinyint',
                        'length' => 1,
                        'nullable' => true,
                        'default' => '1',
                        'comment' => '状态',
                        'primary' => false,
                        'auto_increment' => false,
                        'component' => 'switch',
                    ]
                ];
                break;

            case 'member':
                $specificFields = [
                    [
                        'name' => 'nickname',
                        'type' => 'varchar',
                        'length' => 100,
                        'nullable' => true,
                        'default' => null,
                        'comment' => '昵称',
                        'primary' => false,
                        'auto_increment' => false,
                        'component' => 'input',
                    ],
                    [
                        'name' => 'mobile',
                        'type' => 'varchar',
                        'length' => 20,
                        'nullable' => true,
                        'default' => null,
                        'comment' => '手机号',
                        'primary' => false,
                        'auto_increment' => false,
                        'component' => 'input',
                    ],
                    [
                        'name' => 'avatar',
                        'type' => 'varchar',
                        'length' => 255,
                        'nullable' => true,
                        'default' => null,
                        'comment' => '头像',
                        'primary' => false,
                        'auto_increment' => false,
                        'component' => 'image',
                    ]
                ];
                break;

            case 'shop_product':
                $specificFields = [
                    [
                        'name' => 'title',
                        'type' => 'varchar',
                        'length' => 200,
                        'nullable' => false,
                        'default' => null,
                        'comment' => '商品标题',
                        'primary' => false,
                        'auto_increment' => false,
                        'component' => 'input',
                    ],
                    [
                        'name' => 'price',
                        'type' => 'decimal',
                        'length' => '10,2',
                        'nullable' => false,
                        'default' => '0.00',
                        'comment' => '价格',
                        'primary' => false,
                        'auto_increment' => false,
                        'component' => 'number',
                    ],
                    [
                        'name' => 'stock',
                        'type' => 'int',
                        'length' => 11,
                        'nullable' => false,
                        'default' => '0',
                        'comment' => '库存',
                        'primary' => false,
                        'auto_increment' => false,
                        'component' => 'number',
                    ]
                ];
                break;

            default:
                $specificFields = [
                    [
                        'name' => 'name',
                        'type' => 'varchar',
                        'length' => 100,
                        'nullable' => false,
                        'default' => null,
                        'comment' => '名称',
                        'primary' => false,
                        'auto_increment' => false,
                        'component' => 'input',
                    ]
                ];
        }

        return array_merge([$commonFields[0]], $specificFields, array_slice($commonFields, 1));
    }

    /**
     * 获取模拟表注释
     */
    protected function getMockTableComment(string $tableName): string
    {
        $comments = [
            'admin' => '管理员表',
            'member' => '会员表',
            'shop_product' => '商品表',
            'shop_order' => '订单表',
            'article' => '文章表',
            'business' => '商家表',
            'restaurant_shop' => '餐厅店铺表',
            'restaurant_food' => '餐厅菜品表',
            'wxpay_log' => '微信支付日志',
            'alipay_log' => '支付宝支付日志',
            'users' => '用户表',
            'categories' => '分类表',
            'config' => '配置表',
        ];

        return $comments[$tableName] ?? '演示表';
    }

    /**
     * 成功响应
     */
    protected function success(string $message, $data = []): Response
    {
        return new Response(200, [
            'Content-Type' => 'application/json'
        ], json_encode([
            'code' => 1,
            'msg' => $message,
            'data' => $data,
        ], JSON_UNESCAPED_UNICODE));
    }

    /**
     * 错误响应
     */
    protected function error(string $message): Response
    {
        return new Response(200, [
            'Content-Type' => 'application/json'
        ], json_encode([
            'code' => 0,
            'msg' => $message,
            'data' => [],
        ], JSON_UNESCAPED_UNICODE));
    }
}
