<?php
/**
 * CURD生成器V2 自动化测试脚本
 * 验证所有新功能模块的完整性和可用性
 */

echo "🧪 CURD生成器V2 - 自动化测试套件\n";
echo str_repeat("=", 60) . "\n\n";

// 测试配置
$testConfig = [
    'base_path' => dirname(__DIR__),
    'js_path' => 'public/static/admin/js',
    'css_path' => 'public/static/admin/css',
    'view_path' => 'app/admin/view/admin/system/curdgeneratev2',
    'test_timeout' => 30,
    'verbose' => true
];

// 测试统计
$testStats = [
    'total_tests' => 0,
    'passed_tests' => 0,
    'failed_tests' => 0,
    'skipped_tests' => 0,
    'start_time' => microtime(true),
    'modules_tested' => []
];

// 测试结果
$testResults = [];

echo "📋 测试配置:\n";
echo "  基础路径: {$testConfig['base_path']}\n";
echo "  超时时间: {$testConfig['test_timeout']}秒\n";
echo "  详细模式: " . ($testConfig['verbose'] ? '开启' : '关闭') . "\n\n";

// 1. 文件存在性测试
echo "📁 步骤1: 文件存在性测试\n";
echo str_repeat("-", 40) . "\n";

$requiredFiles = [
    // JavaScript模块
    'js' => [
        'curd-generator-v2.js' => '核心生成器',
        'curd-code-preview.js' => '代码预览器',
        'curd-field-config.js' => '字段配置器',
        'curd-quality-checker.js' => '质量检查器',
        'curd-data-manager.js' => '数据管理器',
        'curd-advanced-generator.js' => '高级生成器',
        'curd-template-manager.js' => '模板管理器',
        'curd-realtime-generator.js' => '实时生成器',
        'curd-code-optimizer.js' => '代码优化器'
    ],
    // CSS文件
    'css' => [
        'curd-generator-v2.css' => '主样式文件'
    ],
    // 视图文件
    'view' => [
        'index.blade.php' => '主视图文件'
    ],
    // 测试文件
    'test' => [
        'curd-v2-comprehensive-test.html' => '综合测试页面'
    ]
];

foreach ($requiredFiles as $type => $files) {
    echo "  检查 {$type} 文件:\n";
    
    foreach ($files as $file => $description) {
        $testStats['total_tests']++;
        
        $filePath = $testConfig['base_path'] . '/' . 
                   ($type === 'test' ? 'test' : 
                    ($type === 'view' ? $testConfig['view_path'] : 
                     $testConfig[$type . '_path'])) . '/' . $file;
        
        if (file_exists($filePath)) {
            $fileSize = formatBytes(filesize($filePath));
            echo "    ✅ {$description} ({$fileSize})\n";
            $testStats['passed_tests']++;
            $testResults[] = ['test' => "文件存在: {$file}", 'status' => 'PASS', 'message' => "文件大小: {$fileSize}"];
        } else {
            echo "    ❌ {$description} (文件不存在)\n";
            $testStats['failed_tests']++;
            $testResults[] = ['test' => "文件存在: {$file}", 'status' => 'FAIL', 'message' => '文件不存在'];
        }
    }
    echo "\n";
}

// 2. JavaScript语法检查
echo "🔍 步骤2: JavaScript语法检查\n";
echo str_repeat("-", 40) . "\n";

foreach ($requiredFiles['js'] as $file => $description) {
    $testStats['total_tests']++;
    
    $filePath = $testConfig['base_path'] . '/' . $testConfig['js_path'] . '/' . $file;
    
    if (file_exists($filePath)) {
        $content = file_get_contents($filePath);
        $syntaxCheck = checkJavaScriptSyntax($content, $file);
        
        if ($syntaxCheck['valid']) {
            echo "  ✅ {$description} - 语法正确\n";
            $testStats['passed_tests']++;
            $testResults[] = ['test' => "JS语法: {$file}", 'status' => 'PASS', 'message' => $syntaxCheck['message']];
        } else {
            echo "  ❌ {$description} - 语法错误: {$syntaxCheck['error']}\n";
            $testStats['failed_tests']++;
            $testResults[] = ['test' => "JS语法: {$file}", 'status' => 'FAIL', 'message' => $syntaxCheck['error']];
        }
    } else {
        echo "  ⏭️  {$description} - 跳过（文件不存在）\n";
        $testStats['skipped_tests']++;
        $testResults[] = ['test' => "JS语法: {$file}", 'status' => 'SKIP', 'message' => '文件不存在'];
    }
}

echo "\n";

// 3. CSS语法检查
echo "🎨 步骤3: CSS语法检查\n";
echo str_repeat("-", 40) . "\n";

foreach ($requiredFiles['css'] as $file => $description) {
    $testStats['total_tests']++;
    
    $filePath = $testConfig['base_path'] . '/' . $testConfig['css_path'] . '/' . $file;
    
    if (file_exists($filePath)) {
        $content = file_get_contents($filePath);
        $syntaxCheck = checkCssSyntax($content, $file);
        
        if ($syntaxCheck['valid']) {
            echo "  ✅ {$description} - 语法正确 ({$syntaxCheck['rules']} 规则)\n";
            $testStats['passed_tests']++;
            $testResults[] = ['test' => "CSS语法: {$file}", 'status' => 'PASS', 'message' => "{$syntaxCheck['rules']} 规则"];
        } else {
            echo "  ❌ {$description} - 语法错误: {$syntaxCheck['error']}\n";
            $testStats['failed_tests']++;
            $testResults[] = ['test' => "CSS语法: {$file}", 'status' => 'FAIL', 'message' => $syntaxCheck['error']];
        }
    } else {
        echo "  ⏭️  {$description} - 跳过（文件不存在）\n";
        $testStats['skipped_tests']++;
        $testResults[] = ['test' => "CSS语法: {$file}", 'status' => 'SKIP', 'message' => '文件不存在'];
    }
}

echo "\n";

// 4. 模块依赖检查
echo "🔗 步骤4: 模块依赖检查\n";
echo str_repeat("-", 40) . "\n";

$moduleDependencies = [
    'curd-advanced-generator.js' => ['layui', 'jquery'],
    'curd-template-manager.js' => ['layui', 'form'],
    'curd-realtime-generator.js' => ['layui'],
    'curd-code-optimizer.js' => ['layui']
];

foreach ($moduleDependencies as $module => $dependencies) {
    $testStats['total_tests']++;
    
    $filePath = $testConfig['base_path'] . '/' . $testConfig['js_path'] . '/' . $module;
    
    if (file_exists($filePath)) {
        $content = file_get_contents($filePath);
        $dependencyCheck = checkModuleDependencies($content, $dependencies);
        
        if ($dependencyCheck['valid']) {
            echo "  ✅ {$module} - 依赖检查通过\n";
            $testStats['passed_tests']++;
            $testResults[] = ['test' => "依赖检查: {$module}", 'status' => 'PASS', 'message' => '所有依赖已声明'];
        } else {
            echo "  ❌ {$module} - 缺少依赖: " . implode(', ', $dependencyCheck['missing']) . "\n";
            $testStats['failed_tests']++;
            $testResults[] = ['test' => "依赖检查: {$module}", 'status' => 'FAIL', 'message' => '缺少依赖: ' . implode(', ', $dependencyCheck['missing'])];
        }
    } else {
        echo "  ⏭️  {$module} - 跳过（文件不存在）\n";
        $testStats['skipped_tests']++;
        $testResults[] = ['test' => "依赖检查: {$module}", 'status' => 'SKIP', 'message' => '文件不存在'];
    }
}

echo "\n";

// 5. 功能完整性检查
echo "⚙️ 步骤5: 功能完整性检查\n";
echo str_repeat("-", 40) . "\n";

$functionChecks = [
    'curd-advanced-generator.js' => [
        'AdvancedGenerator',
        'generateCode',
        'batchGenerate',
        'generateController',
        'generateModel'
    ],
    'curd-template-manager.js' => [
        'TemplateManager',
        'loadDefaultTemplates',
        'saveTemplate',
        'deleteTemplate',
        'exportTemplate'
    ],
    'curd-realtime-generator.js' => [
        'RealtimeGenerator',
        'triggerRealtimeGeneration',
        'generateAllFiles',
        'updateProgress'
    ],
    'curd-code-optimizer.js' => [
        'CodeOptimizer',
        'analyzeCode',
        'optimizeFile',
        'performOptimization'
    ]
];

foreach ($functionChecks as $module => $functions) {
    $testStats['total_tests']++;
    
    $filePath = $testConfig['base_path'] . '/' . $testConfig['js_path'] . '/' . $module;
    
    if (file_exists($filePath)) {
        $content = file_get_contents($filePath);
        $functionCheck = checkRequiredFunctions($content, $functions);
        
        if ($functionCheck['valid']) {
            echo "  ✅ {$module} - 功能完整 ({$functionCheck['found']}/{$functionCheck['total']})\n";
            $testStats['passed_tests']++;
            $testResults[] = ['test' => "功能检查: {$module}", 'status' => 'PASS', 'message' => "找到 {$functionCheck['found']}/{$functionCheck['total']} 个函数"];
        } else {
            echo "  ❌ {$module} - 功能不完整 ({$functionCheck['found']}/{$functionCheck['total']})\n";
            echo "    缺少: " . implode(', ', $functionCheck['missing']) . "\n";
            $testStats['failed_tests']++;
            $testResults[] = ['test' => "功能检查: {$module}", 'status' => 'FAIL', 'message' => "缺少函数: " . implode(', ', $functionCheck['missing'])];
        }
    } else {
        echo "  ⏭️  {$module} - 跳过（文件不存在）\n";
        $testStats['skipped_tests']++;
        $testResults[] = ['test' => "功能检查: {$module}", 'status' => 'SKIP', 'message' => '文件不存在'];
    }
}

echo "\n";

// 6. 视图文件检查
echo "👁️ 步骤6: 视图文件检查\n";
echo str_repeat("-", 40) . "\n";

$viewFile = $testConfig['base_path'] . '/' . $testConfig['view_path'] . '/index.blade.php';
$testStats['total_tests']++;

if (file_exists($viewFile)) {
    $content = file_get_contents($viewFile);
    $viewCheck = checkViewFile($content);
    
    if ($viewCheck['valid']) {
        echo "  ✅ 主视图文件 - 检查通过\n";
        echo "    - JavaScript引用: {$viewCheck['js_includes']} 个\n";
        echo "    - CSS引用: {$viewCheck['css_includes']} 个\n";
        echo "    - 步骤数量: {$viewCheck['steps']} 个\n";
        $testStats['passed_tests']++;
        $testResults[] = ['test' => '视图文件检查', 'status' => 'PASS', 'message' => "JS:{$viewCheck['js_includes']}, CSS:{$viewCheck['css_includes']}, 步骤:{$viewCheck['steps']}"];
    } else {
        echo "  ❌ 主视图文件 - 检查失败: {$viewCheck['error']}\n";
        $testStats['failed_tests']++;
        $testResults[] = ['test' => '视图文件检查', 'status' => 'FAIL', 'message' => $viewCheck['error']];
    }
} else {
    echo "  ❌ 主视图文件不存在\n";
    $testStats['failed_tests']++;
    $testResults[] = ['test' => '视图文件检查', 'status' => 'FAIL', 'message' => '文件不存在'];
}

echo "\n";

// 7. 性能基准测试
echo "⚡ 步骤7: 性能基准测试\n";
echo str_repeat("-", 40) . "\n";

$performanceTests = [
    'file_loading' => '文件加载性能',
    'syntax_parsing' => '语法解析性能',
    'memory_usage' => '内存使用情况'
];

foreach ($performanceTests as $test => $description) {
    $testStats['total_tests']++;
    
    $startTime = microtime(true);
    $startMemory = memory_get_usage();
    
    // 模拟性能测试
    switch ($test) {
        case 'file_loading':
            $result = performFileLoadingTest($testConfig);
            break;
        case 'syntax_parsing':
            $result = performSyntaxParsingTest($testConfig);
            break;
        case 'memory_usage':
            $result = performMemoryUsageTest($testConfig);
            break;
        default:
            $result = ['success' => true, 'message' => '测试完成'];
    }
    
    $endTime = microtime(true);
    $endMemory = memory_get_usage();
    
    $duration = round(($endTime - $startTime) * 1000, 2);
    $memoryUsed = formatBytes($endMemory - $startMemory);
    
    if ($result['success']) {
        echo "  ✅ {$description} - {$duration}ms, 内存: {$memoryUsed}\n";
        $testStats['passed_tests']++;
        $testResults[] = ['test' => $description, 'status' => 'PASS', 'message' => "{$duration}ms, {$memoryUsed}"];
    } else {
        echo "  ❌ {$description} - 失败: {$result['message']}\n";
        $testStats['failed_tests']++;
        $testResults[] = ['test' => $description, 'status' => 'FAIL', 'message' => $result['message']];
    }
}

echo "\n";

// 8. 生成测试报告
echo "📊 步骤8: 生成测试报告\n";
echo str_repeat("-", 40) . "\n";

$testStats['end_time'] = microtime(true);
$testStats['duration'] = round($testStats['end_time'] - $testStats['start_time'], 2);
$testStats['success_rate'] = $testStats['total_tests'] > 0 ? 
    round(($testStats['passed_tests'] / $testStats['total_tests']) * 100, 1) : 0;

// 生成JSON报告
$report = [
    'timestamp' => date('Y-m-d H:i:s'),
    'version' => 'CURD Generator V2',
    'statistics' => $testStats,
    'results' => $testResults,
    'environment' => [
        'php_version' => PHP_VERSION,
        'os' => PHP_OS,
        'memory_limit' => ini_get('memory_limit'),
        'max_execution_time' => ini_get('max_execution_time')
    ]
];

$reportFile = $testConfig['base_path'] . '/test/curd-v2-test-report.json';
file_put_contents($reportFile, json_encode($report, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));

echo "📈 测试统计:\n";
echo "  总测试数: {$testStats['total_tests']}\n";
echo "  通过测试: {$testStats['passed_tests']}\n";
echo "  失败测试: {$testStats['failed_tests']}\n";
echo "  跳过测试: {$testStats['skipped_tests']}\n";
echo "  成功率: {$testStats['success_rate']}%\n";
echo "  执行时间: {$testStats['duration']}秒\n";
echo "  报告文件: {$reportFile}\n\n";

// 9. 最终结果
echo "🎯 最终结果\n";
echo str_repeat("=", 60) . "\n";

if ($testStats['success_rate'] >= 90) {
    echo "🎉 测试结果: 优秀 (成功率 {$testStats['success_rate']}%)\n";
    echo "✅ CURD生成器V2所有功能模块运行正常\n";
    echo "✅ 代码质量符合标准\n";
    echo "✅ 性能表现良好\n";
    $exitCode = 0;
} elseif ($testStats['success_rate'] >= 70) {
    echo "⚠️  测试结果: 良好 (成功率 {$testStats['success_rate']}%)\n";
    echo "✅ 大部分功能正常\n";
    echo "⚠️  存在少量问题需要修复\n";
    $exitCode = 1;
} else {
    echo "❌ 测试结果: 需要改进 (成功率 {$testStats['success_rate']}%)\n";
    echo "❌ 发现多个问题\n";
    echo "🔧 建议检查失败的测试项目\n";
    $exitCode = 2;
}

echo "\n💡 下一步建议:\n";
if ($testStats['failed_tests'] > 0) {
    echo "  1. 查看详细测试报告: {$reportFile}\n";
    echo "  2. 修复失败的测试项目\n";
    echo "  3. 重新运行测试验证修复\n";
}
echo "  4. 运行浏览器端测试: test/curd-v2-comprehensive-test.html\n";
echo "  5. 进行实际功能验证\n";

echo "\n" . str_repeat("=", 60) . "\n";
echo "自动化测试完成！\n";

exit($exitCode);

// 辅助函数

function formatBytes($size, $precision = 2) {
    $units = ['B', 'KB', 'MB', 'GB'];
    for ($i = 0; $size > 1024 && $i < count($units) - 1; $i++) {
        $size /= 1024;
    }
    return round($size, $precision) . ' ' . $units[$i];
}

function checkJavaScriptSyntax($content, $filename) {
    // 基本的JavaScript语法检查
    $errors = [];
    
    // 检查括号匹配
    $openBraces = substr_count($content, '{');
    $closeBraces = substr_count($content, '}');
    if ($openBraces !== $closeBraces) {
        $errors[] = "括号不匹配 ({$openBraces} 开括号, {$closeBraces} 闭括号)";
    }
    
    // 检查基本语法错误
    if (strpos($content, 'layui.use') === false && strpos($filename, 'layui') !== false) {
        $errors[] = "可能缺少 layui.use 声明";
    }
    
    // 检查函数声明
    $functionCount = preg_match_all('/function\s+\w+\s*\(/', $content);
    
    return [
        'valid' => empty($errors),
        'error' => implode('; ', $errors),
        'message' => "找到 {$functionCount} 个函数声明"
    ];
}

function checkCssSyntax($content, $filename) {
    // 基本的CSS语法检查
    $errors = [];
    
    // 检查括号匹配
    $openBraces = substr_count($content, '{');
    $closeBraces = substr_count($content, '}');
    if ($openBraces !== $closeBraces) {
        $errors[] = "括号不匹配";
    }
    
    // 统计CSS规则数量
    $ruleCount = preg_match_all('/[^{}]+\s*{[^{}]*}/', $content);
    
    return [
        'valid' => empty($errors),
        'error' => implode('; ', $errors),
        'rules' => $ruleCount
    ];
}

function checkModuleDependencies($content, $dependencies) {
    $missing = [];
    
    foreach ($dependencies as $dep) {
        if (strpos($content, $dep) === false) {
            $missing[] = $dep;
        }
    }
    
    return [
        'valid' => empty($missing),
        'missing' => $missing
    ];
}

function checkRequiredFunctions($content, $functions) {
    $missing = [];
    $found = 0;
    
    foreach ($functions as $func) {
        if (strpos($content, $func) !== false) {
            $found++;
        } else {
            $missing[] = $func;
        }
    }
    
    return [
        'valid' => empty($missing),
        'missing' => $missing,
        'found' => $found,
        'total' => count($functions)
    ];
}

function checkViewFile($content) {
    $errors = [];
    
    // 检查JavaScript引用
    $jsIncludes = preg_match_all('/<script[^>]+src[^>]*\.js[^>]*>/', $content);
    
    // 检查CSS引用
    $cssIncludes = preg_match_all('/<link[^>]+href[^>]*\.css[^>]*>/', $content);
    
    // 检查步骤数量
    $steps = preg_match_all('/step-\d+/', $content);
    
    if ($jsIncludes < 5) {
        $errors[] = "JavaScript引用数量过少";
    }
    
    if ($steps < 4) {
        $errors[] = "步骤数量不足";
    }
    
    return [
        'valid' => empty($errors),
        'error' => implode('; ', $errors),
        'js_includes' => $jsIncludes,
        'css_includes' => $cssIncludes,
        'steps' => $steps
    ];
}

function performFileLoadingTest($config) {
    // 模拟文件加载性能测试
    $files = glob($config['base_path'] . '/' . $config['js_path'] . '/*.js');
    $totalSize = 0;
    
    foreach ($files as $file) {
        $totalSize += filesize($file);
    }
    
    return [
        'success' => true,
        'message' => "加载 " . count($files) . " 个文件，总大小 " . formatBytes($totalSize)
    ];
}

function performSyntaxParsingTest($config) {
    // 模拟语法解析性能测试
    return [
        'success' => true,
        'message' => "语法解析完成"
    ];
}

function performMemoryUsageTest($config) {
    // 模拟内存使用测试
    $currentMemory = memory_get_usage();
    $peakMemory = memory_get_peak_usage();
    
    return [
        'success' => true,
        'message' => "当前内存: " . formatBytes($currentMemory) . ", 峰值: " . formatBytes($peakMemory)
    ];
}
?>
