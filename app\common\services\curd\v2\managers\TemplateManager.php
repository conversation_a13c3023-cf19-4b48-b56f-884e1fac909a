<?php

namespace app\common\services\curd\v2\managers;

use support\Db;

/**
 * 模板管理器
 * 负责 CURD 配置模板的保存、加载、管理
 */
class TemplateManager
{
    protected string $tableName = 'ea8_curd_templates';
    protected string $usageTableName = 'ea8_curd_template_usage';

    /**
     * 保存模板
     */
    public function saveTemplate(array $data): int
    {
        $template = [
            'name' => $data['name'],
            'description' => $data['description'] ?? '',
            'table_pattern' => $data['table_pattern'] ?? null,
            'config' => json_encode($data['config'], JSON_UNESCAPED_UNICODE),
            'tags' => $data['tags'] ?? '',
            'is_public' => $data['is_public'] ?? 0,
            'created_by' => $data['created_by'] ?? session('admin.id'),
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s'),
        ];

        return Db::table($this->tableName)->insertGetId($template);
    }

    /**
     * 更新模板
     */
    public function updateTemplate(int $id, array $data): bool
    {
        $updateData = [
            'updated_at' => date('Y-m-d H:i:s'),
        ];

        if (isset($data['name'])) {
            $updateData['name'] = $data['name'];
        }
        if (isset($data['description'])) {
            $updateData['description'] = $data['description'];
        }
        if (isset($data['table_pattern'])) {
            $updateData['table_pattern'] = $data['table_pattern'];
        }
        if (isset($data['config'])) {
            $updateData['config'] = json_encode($data['config'], JSON_UNESCAPED_UNICODE);
        }
        if (isset($data['tags'])) {
            $updateData['tags'] = $data['tags'];
        }
        if (isset($data['is_public'])) {
            $updateData['is_public'] = $data['is_public'];
        }

        return Db::table($this->tableName)->where('id', $id)->update($updateData) > 0;
    }

    /**
     * 删除模板
     */
    public function deleteTemplate(int $id): bool
    {
        // 检查是否有权限删除
        $template = $this->getTemplate($id);
        if (!$template) {
            return false;
        }

        $currentUserId = session('admin.id');
        if ($template['created_by'] != $currentUserId && !$this->isAdmin()) {
            throw new \Exception('无权限删除此模板');
        }

        return Db::table($this->tableName)->where('id', $id)->delete() > 0;
    }

    /**
     * 获取模板
     */
    public function getTemplate(int $id): ?array
    {
        $template = Db::table($this->tableName)->where('id', $id)->first();
        
        if ($template) {
            $template = (array)$template;
            $template['config'] = json_decode($template['config'], true);
        }

        return $template;
    }

    /**
     * 获取模板列表
     */
    public function getTemplates(array $filters = []): array
    {
        $query = Db::table($this->tableName);

        // 筛选条件
        if (!empty($filters['keyword'])) {
            $keyword = '%' . $filters['keyword'] . '%';
            $query->where(function($q) use ($keyword) {
                $q->where('name', 'like', $keyword)
                  ->orWhere('description', 'like', $keyword)
                  ->orWhere('tags', 'like', $keyword);
            });
        }

        if (isset($filters['is_public'])) {
            $query->where('is_public', $filters['is_public']);
        }

        if (!empty($filters['created_by'])) {
            $query->where('created_by', $filters['created_by']);
        }

        if (!empty($filters['tags'])) {
            $tags = is_array($filters['tags']) ? $filters['tags'] : [$filters['tags']];
            foreach ($tags as $tag) {
                $query->where('tags', 'like', '%' . $tag . '%');
            }
        }

        // 排序
        $orderBy = $filters['order_by'] ?? 'created_at';
        $orderDir = $filters['order_dir'] ?? 'desc';
        $query->orderBy($orderBy, $orderDir);

        // 分页
        if (!empty($filters['limit'])) {
            $offset = $filters['offset'] ?? 0;
            $query->offset($offset)->limit($filters['limit']);
        }

        $templates = $query->get()->toArray();

        // 解析配置
        foreach ($templates as &$template) {
            $template = (array)$template;
            $template['config'] = json_decode($template['config'], true);
        }

        return $templates;
    }

    /**
     * 根据表名推荐模板
     */
    public function recommendTemplates(string $tableName): array
    {
        $recommendations = [];

        // 获取所有公开模板
        $templates = $this->getTemplates(['is_public' => 1]);

        foreach ($templates as $template) {
            $score = $this->calculateMatchScore($tableName, $template);
            if ($score > 0) {
                $template['match_score'] = $score;
                $recommendations[] = $template;
            }
        }

        // 按匹配度排序
        usort($recommendations, function($a, $b) {
            return $b['match_score'] <=> $a['match_score'];
        });

        return array_slice($recommendations, 0, 5); // 返回前5个推荐
    }

    /**
     * 计算模板匹配度
     */
    protected function calculateMatchScore(string $tableName, array $template): int
    {
        $score = 0;

        // 检查表名模式匹配
        if (!empty($template['table_pattern'])) {
            $patterns = explode(',', $template['table_pattern']);
            foreach ($patterns as $pattern) {
                $pattern = trim($pattern);
                if (fnmatch($pattern, $tableName)) {
                    $score += 50; // 模式匹配得分
                    break;
                }
            }
        }

        // 检查标签匹配
        if (!empty($template['tags'])) {
            $tags = explode(',', $template['tags']);
            foreach ($tags as $tag) {
                $tag = trim($tag);
                if (stripos($tableName, $tag) !== false) {
                    $score += 20; // 标签匹配得分
                }
            }
        }

        // 使用次数加分
        $score += min($template['use_count'] ?? 0, 30); // 最多30分

        return $score;
    }

    /**
     * 应用模板
     */
    public function applyTemplate(int $templateId, string $tableName): array
    {
        $template = $this->getTemplate($templateId);
        if (!$template) {
            throw new \Exception('模板不存在');
        }

        // 记录使用
        $this->recordUsage($templateId, $tableName);

        // 增加使用次数
        Db::table($this->tableName)
            ->where('id', $templateId)
            ->increment('use_count');

        return $template['config'];
    }

    /**
     * 记录模板使用
     */
    protected function recordUsage(int $templateId, string $tableName): void
    {
        $usage = [
            'template_id' => $templateId,
            'table_name' => $tableName,
            'user_id' => session('admin.id'),
            'used_at' => date('Y-m-d H:i:s'),
        ];

        Db::table($this->usageTableName)->insert($usage);
    }

    /**
     * 获取使用统计
     */
    public function getUsageStats(int $templateId): array
    {
        $stats = Db::table($this->usageTableName)
            ->where('template_id', $templateId)
            ->selectRaw('
                COUNT(*) as total_uses,
                COUNT(DISTINCT user_id) as unique_users,
                COUNT(DISTINCT table_name) as unique_tables,
                MAX(used_at) as last_used_at
            ')
            ->first();

        return (array)$stats;
    }

    /**
     * 获取热门模板
     */
    public function getPopularTemplates(int $limit = 10): array
    {
        return $this->getTemplates([
            'is_public' => 1,
            'order_by' => 'use_count',
            'order_dir' => 'desc',
            'limit' => $limit
        ]);
    }

    /**
     * 获取我的模板
     */
    public function getMyTemplates(): array
    {
        $userId = session('admin.id');
        return $this->getTemplates([
            'created_by' => $userId,
            'order_by' => 'created_at',
            'order_dir' => 'desc'
        ]);
    }

    /**
     * 复制模板
     */
    public function copyTemplate(int $templateId, string $newName): int
    {
        $template = $this->getTemplate($templateId);
        if (!$template) {
            throw new \Exception('模板不存在');
        }

        $newTemplate = [
            'name' => $newName,
            'description' => $template['description'] . ' (复制)',
            'table_pattern' => $template['table_pattern'],
            'config' => $template['config'],
            'tags' => $template['tags'],
            'is_public' => 0, // 复制的模板默认为私有
            'created_by' => session('admin.id'),
        ];

        return $this->saveTemplate($newTemplate);
    }

    /**
     * 检查是否为管理员
     */
    protected function isAdmin(): bool
    {
        // 这里可以根据实际的权限系统来判断
        return session('admin.role') === 'admin' || session('admin.id') == 1;
    }

    /**
     * 导出模板
     */
    public function exportTemplate(int $templateId): array
    {
        $template = $this->getTemplate($templateId);
        if (!$template) {
            throw new \Exception('模板不存在');
        }

        // 移除敏感信息
        unset($template['id'], $template['created_by'], $template['use_count']);

        return $template;
    }

    /**
     * 导入模板
     */
    public function importTemplate(array $templateData): int
    {
        // 验证必要字段
        if (empty($templateData['name']) || empty($templateData['config'])) {
            throw new \Exception('模板数据不完整');
        }

        // 检查名称是否重复
        $existing = Db::table($this->tableName)
            ->where('name', $templateData['name'])
            ->where('created_by', session('admin.id'))
            ->first();

        if ($existing) {
            $templateData['name'] .= ' (导入)';
        }

        return $this->saveTemplate($templateData);
    }
}
