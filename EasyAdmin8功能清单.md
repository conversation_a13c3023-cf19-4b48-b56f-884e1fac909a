# EasyAdmin8-webman 功能清单

## 项目概述
EasyAdmin8-webman 是基于 Webman 框架和 Layui 前端框架开发的快速后台管理系统，支持快速 CURD 生成和权限管理。

## 核心技术栈
- **后端框架**: Webman (基于 Workerman)
- **前端框架**: Layui v2.9.x
- **数据库**: MySQL 5.7+
- **PHP版本**: 8.1+
- **ORM**: Illuminate Database (Laravel Eloquent)

## 目录结构分析

### 应用目录 (app/)
```
app/
├── admin/              # 后台管理模块
│   ├── controller/     # 后台控制器
│   ├── model/         # 后台模型
│   └── view/          # 后台视图
├── common/            # 公共模块
│   ├── controller/    # 公共控制器
│   ├── services/      # 业务服务
│   └── traits/        # 公共特性
├── controller/        # 前台控制器
├── middleware/        # 中间件
├── model/            # 前台模型
├── process/          # 进程处理
└── view/             # 前台视图
```

## 功能模块详细清单

### 1. 系统管理模块 (system/)

#### 1.1 管理员管理 (AdminController)
- **功能**: 管理员账户的增删改查
- **文件**: `app/admin/controller/system/AdminController.php`
- **模型**: `app/admin/model/SystemAdmin.php`
- **主要功能**:
  - 添加管理员
  - 编辑管理员信息
  - 删除管理员
  - 权限分配
  - 密码管理
  - 谷歌验证码登录支持

#### 1.2 权限管理 (AuthController)
- **功能**: 角色权限管理
- **文件**: `app/admin/controller/system/AuthController.php`
- **模型**: `app/admin/model/SystemAuth.php`
- **主要功能**:
  - 角色创建和管理
  - 权限节点分配
  - 权限继承

#### 1.3 菜单管理 (MenuController)
- **功能**: 后台菜单结构管理
- **文件**: `app/admin/controller/system/MenuController.php`
- **模型**: `app/admin/model/SystemMenu.php`
- **主要功能**:
  - 菜单树形结构管理
  - 菜单图标设置
  - 菜单排序
  - 菜单权限控制

#### 1.4 节点管理 (NodeController)
- **功能**: 权限节点管理
- **文件**: `app/admin/controller/system/NodeController.php`
- **模型**: `app/admin/model/SystemNode.php`
- **主要功能**:
  - 自动扫描控制器方法
  - 权限节点生成
  - 节点权限配置

#### 1.5 系统配置 (ConfigController)
- **功能**: 系统参数配置
- **文件**: `app/admin/controller/system/ConfigController.php`
- **模型**: `app/admin/model/SystemConfig.php`
- **主要功能**:
  - 系统基础配置
  - 上传配置
  - 邮件配置
  - 其他系统参数

#### 1.6 系统日志 (LogController)
- **功能**: 操作日志记录和查看
- **文件**: `app/admin/controller/system/LogController.php`
- **模型**: `app/admin/model/SystemLog.php`
- **主要功能**:
  - 操作日志记录
  - 日志查询和筛选
  - 日志详情查看

#### 1.7 快捷菜单 (QuickController)
- **功能**: 个人快捷菜单管理
- **文件**: `app/admin/controller/system/QuickController.php`
- **模型**: `app/admin/model/SystemQuick.php`
- **主要功能**:
  - 个人快捷菜单设置
  - 常用功能快速访问

#### 1.8 文件管理 (UploadfileController)
- **功能**: 上传文件管理
- **文件**: `app/admin/controller/system/UploadfileController.php`
- **模型**: `app/admin/model/SystemUploadfile.php`
- **主要功能**:
  - 文件上传记录
  - 文件管理和删除
  - 多种存储支持 (本地、阿里云OSS、腾讯云COS、七牛云)

#### 1.9 CURD生成器 (CurdGenerateController)
- **功能**: 可视化CURD代码生成
- **文件**: `app/admin/controller/system/CurdGenerateController.php`
- **主要功能**:
  - 数据表扫描
  - 自动生成控制器
  - 自动生成模型
  - 自动生成视图
  - 自动生成JS文件

### 2. 商城模块 (mall/)

#### 2.1 商品分类管理 (CateController)
- **功能**: 商品分类管理
- **文件**: `app/admin/controller/mall/CateController.php`
- **模型**: `app/admin/model/MallCate.php`
- **主要功能**:
  - 分类增删改查
  - 分类排序
  - 分类图片管理

#### 2.2 商品管理 (GoodsController)
- **功能**: 商品信息管理
- **文件**: `app/admin/controller/mall/GoodsController.php`
- **模型**: `app/admin/model/MallGoods.php`
- **主要功能**:
  - 商品基本信息管理
  - 商品库存管理
  - 商品分类关联
  - 商品状态控制

### 3. 公共服务模块 (common/services/)

#### 3.1 认证服务 (AuthService)
- **功能**: 用户认证和权限验证
- **文件**: `app/common/services/AuthService.php`

#### 3.2 菜单服务 (MenuService)
- **功能**: 菜单数据处理
- **文件**: `app/common/services/MenuService.php`

#### 3.3 节点服务 (NodeService)
- **功能**: 权限节点处理
- **文件**: `app/common/services/NodeService.php`

#### 3.4 系统日志服务 (SystemLogService)
- **功能**: 系统日志记录
- **文件**: `app/common/services/SystemLogService.php`

#### 3.5 上传服务 (UploadService)
- **功能**: 文件上传处理
- **文件**: `app/common/services/UploadService.php`
- **支持存储**:
  - 本地存储
  - 阿里云 OSS
  - 腾讯云 COS
  - 七牛云存储

#### 3.6 CURD构建服务 (BuildCurd)
- **功能**: 自动化CURD代码生成
- **文件**: `app/common/services/curd/BuildCurd.php`
- **模板目录**: `app/common/services/curd/templates/`

### 4. 注解系统 (annotation/)

#### 4.1 控制器注解 (ControllerAnnotation)
- **功能**: 控制器元数据标注
- **文件**: `app/common/services/annotation/ControllerAnnotation.php`

#### 4.2 节点注解 (NodeAnnotation)
- **功能**: 权限节点标注
- **文件**: `app/common/services/annotation/NodeAnnotation.php`

#### 4.3 中间件注解 (MiddlewareAnnotation)
- **功能**: 中间件标注
- **文件**: `app/common/services/annotation/MiddlewareAnnotation.php`

### 5. 中间件系统 (middleware/)

#### 5.1 权限检查 (CheckAuth)
- **功能**: 权限验证中间件
- **文件**: `app/middleware/CheckAuth.php`

#### 5.2 登录检查 (CheckLogin)
- **功能**: 登录状态验证
- **文件**: `app/middleware/CheckLogin.php`

#### 5.3 安装检查 (CheckInstall)
- **功能**: 系统安装状态检查
- **文件**: `app/middleware/CheckInstall.php`

#### 5.4 系统日志 (SystemLog)
- **功能**: 操作日志记录
- **文件**: `app/middleware/SystemLog.php`

#### 5.5 静态文件 (StaticFile)
- **功能**: 静态文件处理
- **文件**: `app/middleware/StaticFile.php`

### 6. 命令行工具 (command/)

#### 6.1 CURD命令 (Curd)
- **功能**: 命令行CURD生成
- **文件**: `app/command/Curd.php`
- **使用方法**: `php webman curd -t table_name`

### 7. 前端资源 (public/static/)

#### 7.1 管理后台资源 (admin/)
- CSS样式文件
- JavaScript脚本
- 图片资源
- 字体文件

#### 7.2 前端插件 (plugs/)
- **Layui**: 前端UI框架
- **jQuery**: JavaScript库
- **ECharts**: 图表库
- **富文本编辑器**: CKEditor、UEditor、wangEditor
- **其他插件**: 各种前端组件

### 8. 配置系统 (config/)

#### 8.1 应用配置 (app.php)
- 基础应用配置
- 时区设置
- 错误报告级别

#### 8.2 数据库配置 (database.php)
- 数据库连接配置
- 多数据库支持

#### 8.3 管理后台配置 (admin.php)
- 后台路径配置
- 权限控制配置
- 上传类型配置

#### 8.4 进程配置 (process.php)
- Webman进程配置
- 监控进程配置

## 开发特性

### 1. 快速CURD生成
- 支持命令行和可视化两种方式
- 自动生成完整的增删改查功能
- 支持自定义模板

### 2. 权限管理
- 基于RBAC的权限控制
- 支持注解式权限配置
- 细粒度权限控制

### 3. 多存储支持
- 本地文件存储
- 阿里云OSS
- 腾讯云COS
- 七牛云存储

### 4. 系统监控
- 操作日志记录
- 系统状态监控
- 文件变更监控

### 5. 扩展性
- 插件化架构
- 中间件支持
- 事件系统

## 数据库表结构

### 系统核心表
- `ea8_system_admin`: 管理员表
- `ea8_system_auth`: 权限角色表
- `ea8_system_menu`: 菜单表
- `ea8_system_node`: 权限节点表
- `ea8_system_config`: 系统配置表
- `ea8_system_log`: 系统日志表
- `ea8_system_uploadfile`: 上传文件表

### 商城模块表
- `ea8_mall_cate`: 商品分类表
- `ea8_mall_goods`: 商品表

## 安装和部署

### 环境要求
- PHP >= 8.1
- MySQL >= 5.7
- Composer
- 必要的PHP扩展

### 安装步骤
1. 克隆项目代码
2. 安装Composer依赖
3. 配置.env文件
4. 启动Webman服务
5. 访问安装页面完成初始化

### 启动命令
```bash
# Windows
php windows.php
# 或
windows.bat

# Linux/Unix
php start.php start
php start.php start -d  # 守护进程
```

这个功能清单为您提供了EasyAdmin8-webman项目的完整功能概览，方便您进行后续的开发工作。
