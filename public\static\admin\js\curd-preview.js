/**
 * CURD 代码实时预览组件
 * 支持实时预览生成的代码，语法高亮，分屏显示
 */
define(['jquery', 'layui'], function($, layui) {
    'use strict';
    
    var CodePreview = function(options) {
        this.options = $.extend({
            container: '#code-preview-container',
            previewUrl: '/admin/system/curd_generate_v2',
            debounceDelay: 300,
            defaultTab: 'controller',
            enableHighlight: true,
            enableSplit: true
        }, options);
        
        this.container = $(this.options.container);
        this.currentConfig = {};
        this.debounceTimer = null;
        this.isVisible = false;
        this.currentTab = this.options.defaultTab;
        
        this.init();
    };
    
    CodePreview.prototype = {
        
        /**
         * 初始化预览组件
         */
        init: function() {
            this.createPreviewContainer();
            this.bindEvents();
            this.initHighlight();
        },
        
        /**
         * 创建预览容器
         */
        createPreviewContainer: function() {
            var html = `
                <div class="code-preview-wrapper">
                    <div class="preview-header">
                        <div class="preview-title">
                            <i class="layui-icon layui-icon-code"></i>
                            代码预览
                        </div>
                        <div class="preview-controls">
                            <button class="layui-btn layui-btn-xs" id="refresh-preview">
                                <i class="layui-icon layui-icon-refresh"></i> 刷新
                            </button>
                            <button class="layui-btn layui-btn-xs layui-btn-primary" id="toggle-split">
                                <i class="layui-icon layui-icon-cols"></i> 分屏
                            </button>
                            <button class="layui-btn layui-btn-xs layui-btn-danger" id="close-preview">
                                <i class="layui-icon layui-icon-close"></i>
                            </button>
                        </div>
                    </div>
                    <div class="preview-tabs">
                        <div class="tab-item active" data-tab="controller">
                            <i class="layui-icon layui-icon-component"></i> 控制器
                        </div>
                        <div class="tab-item" data-tab="model">
                            <i class="layui-icon layui-icon-template"></i> 模型
                        </div>
                        <div class="tab-item" data-tab="view">
                            <i class="layui-icon layui-icon-face-smile"></i> 视图
                        </div>
                        <div class="tab-item" data-tab="js">
                            <i class="layui-icon layui-icon-file"></i> JavaScript
                        </div>
                    </div>
                    <div class="preview-content">
                        <div class="loading-mask">
                            <i class="layui-icon layui-icon-loading layui-anim layui-anim-rotate"></i>
                            正在生成预览...
                        </div>
                        <div class="code-container">
                            <pre class="code-block" id="code-display">
                                <code class="language-php">// 请先配置字段以查看预览</code>
                            </pre>
                        </div>
                        <div class="preview-info">
                            <div class="info-item">
                                <span class="info-label">文件大小:</span>
                                <span class="info-value" id="file-size">0 B</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">代码行数:</span>
                                <span class="info-value" id="code-lines">0</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">更新时间:</span>
                                <span class="info-value" id="update-time">--</span>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            
            this.container.html(html);
        },
        
        /**
         * 绑定事件
         */
        bindEvents: function() {
            var self = this;
            
            // 标签切换
            this.container.on('click', '.tab-item', function() {
                var tab = $(this).data('tab');
                self.switchTab(tab);
            });
            
            // 刷新预览
            this.container.on('click', '#refresh-preview', function() {
                self.updatePreview(self.currentConfig, true);
            });
            
            // 分屏切换
            this.container.on('click', '#toggle-split', function() {
                self.toggleSplitView();
            });
            
            // 关闭预览
            this.container.on('click', '#close-preview', function() {
                self.hide();
            });
        },
        
        /**
         * 初始化代码高亮
         */
        initHighlight: function() {
            if (this.options.enableHighlight && window.Prism) {
                // 确保 Prism 已加载
                this.highlightCode();
            }
        },
        
        /**
         * 配置变更时触发预览更新
         */
        onConfigChange: function(config) {
            var self = this;
            
            // 防抖处理
            clearTimeout(this.debounceTimer);
            this.debounceTimer = setTimeout(function() {
                self.updatePreview(config);
            }, this.options.debounceDelay);
        },
        
        /**
         * 更新预览内容
         */
        updatePreview: function(config, force) {
            var self = this;
            
            // 检查配置是否有变化
            if (!force && JSON.stringify(config) === JSON.stringify(this.currentConfig)) {
                return;
            }
            
            this.currentConfig = config;
            this.showLoading();
            
            $.ajax({
                url: this.options.previewUrl,
                type: 'POST',
                data: {
                    action: 'preview_code',
                    config: config
                },
                success: function(response) {
                    self.hideLoading();
                    if (response.code === 0) {
                        self.renderPreview(response.data);
                        self.show();
                    } else {
                        self.showError(response.msg || '预览失败');
                    }
                },
                error: function(xhr, status, error) {
                    self.hideLoading();
                    self.showError('网络错误: ' + error);
                }
            });
        },
        
        /**
         * 渲染预览内容
         */
        renderPreview: function(files) {
            this.files = files;
            this.displayCurrentTab();
            this.updateInfo();
        },
        
        /**
         * 显示当前标签的代码
         */
        displayCurrentTab: function() {
            var content = this.files[this.currentTab] || '// 暂无内容';
            var language = this.getLanguage(this.currentTab);
            
            var codeBlock = this.container.find('#code-display code');
            codeBlock.removeClass().addClass('language-' + language);
            codeBlock.text(content);
            
            this.highlightCode();
        },
        
        /**
         * 获取语言类型
         */
        getLanguage: function(tab) {
            var languages = {
                'controller': 'php',
                'model': 'php',
                'view': 'html',
                'js': 'javascript'
            };
            return languages[tab] || 'text';
        },
        
        /**
         * 代码高亮
         */
        highlightCode: function() {
            if (window.Prism) {
                Prism.highlightAll();
            }
        },
        
        /**
         * 切换标签
         */
        switchTab: function(tab) {
            this.currentTab = tab;
            
            // 更新标签状态
            this.container.find('.tab-item').removeClass('active');
            this.container.find('.tab-item[data-tab="' + tab + '"]').addClass('active');
            
            // 显示对应内容
            this.displayCurrentTab();
        },
        
        /**
         * 更新信息显示
         */
        updateInfo: function() {
            var currentFile = this.files[this.currentTab] || '';
            var fileSize = this.formatFileSize(currentFile.length);
            var codeLines = currentFile.split('\n').length;
            var updateTime = new Date().toLocaleTimeString();
            
            this.container.find('#file-size').text(fileSize);
            this.container.find('#code-lines').text(codeLines);
            this.container.find('#update-time').text(updateTime);
        },
        
        /**
         * 格式化文件大小
         */
        formatFileSize: function(bytes) {
            if (bytes === 0) return '0 B';
            var k = 1024;
            var sizes = ['B', 'KB', 'MB'];
            var i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
        },
        
        /**
         * 显示加载状态
         */
        showLoading: function() {
            this.container.find('.loading-mask').show();
        },
        
        /**
         * 隐藏加载状态
         */
        hideLoading: function() {
            this.container.find('.loading-mask').hide();
        },
        
        /**
         * 显示错误信息
         */
        showError: function(message) {
            var codeBlock = this.container.find('#code-display code');
            codeBlock.removeClass().addClass('language-text');
            codeBlock.text('// 预览失败: ' + message);
        },
        
        /**
         * 显示预览面板
         */
        show: function() {
            this.container.show();
            this.isVisible = true;
        },
        
        /**
         * 隐藏预览面板
         */
        hide: function() {
            this.container.hide();
            this.isVisible = false;
        },
        
        /**
         * 切换分屏模式
         */
        toggleSplitView: function() {
            var wrapper = this.container.find('.code-preview-wrapper');
            wrapper.toggleClass('split-view');
            
            var button = this.container.find('#toggle-split');
            if (wrapper.hasClass('split-view')) {
                button.html('<i class="layui-icon layui-icon-shrink-right"></i> 合并');
            } else {
                button.html('<i class="layui-icon layui-icon-cols"></i> 分屏');
            }
        },
        
        /**
         * 销毁组件
         */
        destroy: function() {
            clearTimeout(this.debounceTimer);
            this.container.empty();
        }
    };
    
    return CodePreview;
});
