<?php

namespace app\common\services\curd\v2\generators;

/**
 * 性能监控生成器
 * 根据性能监控需求分析结果生成监控配置和仪表板
 */
class PerformanceMonitoringGenerator
{
    /**
     * 生成性能监控配置
     */
    public function generatePerformanceMonitoringConfig(array $monitoringAnalysis, array $tableInfo, array $options = []): array
    {
        $configs = [];

        // 生成Prometheus配置
        $configs['prometheus'] = $this->generatePrometheusConfig($monitoringAnalysis, $tableInfo);

        // 生成Grafana配置
        $configs['grafana'] = $this->generateGrafanaConfig($monitoringAnalysis, $tableInfo);

        // 生成ELK Stack配置
        $configs['elk'] = $this->generateELKConfig($monitoringAnalysis, $tableInfo);

        // 生成Jaeger配置
        $configs['jaeger'] = $this->generateJaegerConfig($monitoringAnalysis, $tableInfo);

        // 生成告警配置
        $configs['alerting'] = $this->generateAlertingConfig($monitoringAnalysis, $tableInfo);

        // 生成监控脚本
        $configs['scripts'] = $this->generateMonitoringScripts($monitoringAnalysis, $tableInfo);

        // 生成Docker Compose
        $configs['docker'] = $this->generateMonitoringDockerCompose($monitoringAnalysis, $tableInfo);

        return $configs;
    }

    /**
     * 生成Prometheus配置
     */
    protected function generatePrometheusConfig(array $monitoringAnalysis, array $tableInfo): array
    {
        return [
            'prometheus.yml' => $this->generatePrometheusYml($monitoringAnalysis, $tableInfo),
            'alert.rules.yml' => $this->generatePrometheusAlertRules($monitoringAnalysis, $tableInfo),
            'recording.rules.yml' => $this->generatePrometheusRecordingRules($monitoringAnalysis, $tableInfo),
        ];
    }

    /**
     * 生成Prometheus主配置
     */
    protected function generatePrometheusYml(array $monitoringAnalysis, array $tableInfo): string
    {
        $appName = $tableInfo['name'] ?? 'app';
        
        return <<<EOT
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "alert.rules.yml"
  - "recording.rules.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

scrape_configs:
  # Prometheus自身监控
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  # 应用监控
  - job_name: '{$appName}-app'
    static_configs:
      - targets: ['app:8080']
    metrics_path: '/metrics'
    scrape_interval: 30s
    scrape_timeout: 10s

  # 系统监控
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']
    scrape_interval: 30s

  # 数据库监控
  - job_name: 'mysql-exporter'
    static_configs:
      - targets: ['mysql-exporter:9104']
    scrape_interval: 30s

  # Redis监控
  - job_name: 'redis-exporter'
    static_configs:
      - targets: ['redis-exporter:9121']
    scrape_interval: 30s

  # Nginx监控
  - job_name: 'nginx-exporter'
    static_configs:
      - targets: ['nginx-exporter:9113']
    scrape_interval: 30s

  # 黑盒监控
  - job_name: 'blackbox'
    metrics_path: /probe
    params:
      module: [http_2xx]
    static_configs:
      - targets:
        - http://app:8080/health
        - http://app:8080/api/status
    relabel_configs:
      - source_labels: [__address__]
        target_label: __param_target
      - source_labels: [__param_target]
        target_label: instance
      - target_label: __address__
        replacement: blackbox-exporter:9115

  # Kubernetes监控 (如果使用K8s)
  - job_name: 'kubernetes-apiservers'
    kubernetes_sd_configs:
    - role: endpoints
    scheme: https
    tls_config:
      ca_file: /var/run/secrets/kubernetes.io/serviceaccount/ca.crt
    bearer_token_file: /var/run/secrets/kubernetes.io/serviceaccount/token
    relabel_configs:
    - source_labels: [__meta_kubernetes_namespace, __meta_kubernetes_service_name, __meta_kubernetes_endpoint_port_name]
      action: keep
      regex: default;kubernetes;https

  # 容器监控
  - job_name: 'cadvisor'
    static_configs:
      - targets: ['cadvisor:8080']
    scrape_interval: 30s
EOT;
    }

    /**
     * 生成Grafana配置
     */
    protected function generateGrafanaConfig(array $monitoringAnalysis, array $tableInfo): array
    {
        return [
            'grafana.ini' => $this->generateGrafanaIni($monitoringAnalysis),
            'datasources.yml' => $this->generateGrafanaDatasources($monitoringAnalysis),
            'dashboards.yml' => $this->generateGrafanaDashboardsConfig($monitoringAnalysis),
            'dashboards/overview.json' => $this->generateOverviewDashboard($monitoringAnalysis, $tableInfo),
            'dashboards/application.json' => $this->generateApplicationDashboard($monitoringAnalysis, $tableInfo),
            'dashboards/infrastructure.json' => $this->generateInfrastructureDashboard($monitoringAnalysis, $tableInfo),
            'dashboards/business.json' => $this->generateBusinessDashboard($monitoringAnalysis, $tableInfo),
        ];
    }

    /**
     * 生成Grafana主配置
     */
    protected function generateGrafanaIni(array $monitoringAnalysis): string
    {
        return <<<EOT
[server]
http_port = 3000
domain = localhost
root_url = http://localhost:3000

[database]
type = sqlite3
path = grafana.db

[security]
admin_user = admin
admin_password = admin
secret_key = SW2YcwTIb9zpOOhoPsMm

[users]
allow_sign_up = false
allow_org_create = false
auto_assign_org = true
auto_assign_org_role = Viewer

[auth.anonymous]
enabled = false

[alerting]
enabled = true
execute_alerts = true

[smtp]
enabled = false
host = localhost:587
user = 
password = 
from_address = <EMAIL>
from_name = Grafana

[log]
mode = console
level = info

[metrics]
enabled = true
interval_seconds = 10

[tracing.jaeger]
address = jaeger:14268
always_included_tag = tag1:value1
sampler_type = const
sampler_param = 1

[feature_toggles]
enable = ngalert
EOT;
    }

    /**
     * 生成概览仪表板
     */
    protected function generateOverviewDashboard(array $monitoringAnalysis, array $tableInfo): string
    {
        $appName = $tableInfo['name'] ?? 'app';
        
        return <<<EOT
{
  "dashboard": {
    "id": null,
    "title": "{$appName} - 系统概览",
    "tags": ["overview", "{$appName}"],
    "timezone": "browser",
    "panels": [
      {
        "id": 1,
        "title": "系统健康状态",
        "type": "stat",
        "targets": [
          {
            "expr": "up{job=\"{$appName}-app\"}",
            "legendFormat": "应用状态"
          }
        ],
        "fieldConfig": {
          "defaults": {
            "color": {
              "mode": "thresholds"
            },
            "thresholds": {
              "steps": [
                {"color": "red", "value": 0},
                {"color": "green", "value": 1}
              ]
            }
          }
        },
        "gridPos": {"h": 8, "w": 6, "x": 0, "y": 0}
      },
      {
        "id": 2,
        "title": "响应时间",
        "type": "graph",
        "targets": [
          {
            "expr": "histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m]))",
            "legendFormat": "95th percentile"
          },
          {
            "expr": "histogram_quantile(0.50, rate(http_request_duration_seconds_bucket[5m]))",
            "legendFormat": "50th percentile"
          }
        ],
        "yAxes": [
          {
            "label": "时间 (秒)",
            "min": 0
          }
        ],
        "gridPos": {"h": 8, "w": 12, "x": 6, "y": 0}
      },
      {
        "id": 3,
        "title": "错误率",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(http_requests_total{status=~\"5..\"}[5m]) / rate(http_requests_total[5m]) * 100",
            "legendFormat": "5xx错误率"
          },
          {
            "expr": "rate(http_requests_total{status=~\"4..\"}[5m]) / rate(http_requests_total[5m]) * 100",
            "legendFormat": "4xx错误率"
          }
        ],
        "yAxes": [
          {
            "label": "错误率 (%)",
            "min": 0,
            "max": 100
          }
        ],
        "gridPos": {"h": 8, "w": 6, "x": 18, "y": 0}
      },
      {
        "id": 4,
        "title": "请求吞吐量",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(http_requests_total[5m])",
            "legendFormat": "RPS"
          }
        ],
        "yAxes": [
          {
            "label": "请求/秒",
            "min": 0
          }
        ],
        "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}
      },
      {
        "id": 5,
        "title": "资源使用率",
        "type": "graph",
        "targets": [
          {
            "expr": "100 - (avg by (instance) (rate(node_cpu_seconds_total{mode=\"idle\"}[5m])) * 100)",
            "legendFormat": "CPU使用率"
          },
          {
            "expr": "(1 - (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes)) * 100",
            "legendFormat": "内存使用率"
          }
        ],
        "yAxes": [
          {
            "label": "使用率 (%)",
            "min": 0,
            "max": 100
          }
        ],
        "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}
      }
    ],
    "time": {
      "from": "now-1h",
      "to": "now"
    },
    "refresh": "30s"
  }
}
EOT;
    }

    /**
     * 生成ELK Stack配置
     */
    protected function generateELKConfig(array $monitoringAnalysis, array $tableInfo): array
    {
        return [
            'elasticsearch.yml' => $this->generateElasticsearchConfig($monitoringAnalysis),
            'logstash.conf' => $this->generateLogstashConfig($monitoringAnalysis, $tableInfo),
            'kibana.yml' => $this->generateKibanaConfig($monitoringAnalysis),
            'filebeat.yml' => $this->generateFilebeatConfig($monitoringAnalysis, $tableInfo),
        ];
    }

    /**
     * 生成Logstash配置
     */
    protected function generateLogstashConfig(array $monitoringAnalysis, array $tableInfo): string
    {
        $appName = $tableInfo['name'] ?? 'app';
        
        return <<<EOT
input {
  beats {
    port => 5044
  }
  
  tcp {
    port => 5000
    codec => json_lines
  }
}

filter {
  if [fields][app] == "{$appName}" {
    # 解析应用日志
    if [fields][log_type] == "application" {
      grok {
        match => { "message" => "%{TIMESTAMP_ISO8601:timestamp} %{LOGLEVEL:level} %{GREEDYDATA:message}" }
      }
      
      date {
        match => [ "timestamp", "ISO8601" ]
      }
      
      # 提取错误信息
      if [level] == "ERROR" {
        grok {
          match => { "message" => "(?<error_type>\\w+Exception): (?<error_message>.*)" }
          tag_on_failure => ["_grokparsefailure_error"]
        }
      }
    }
    
    # 解析访问日志
    if [fields][log_type] == "access" {
      grok {
        match => { "message" => "%{COMBINEDAPACHELOG}" }
      }
      
      mutate {
        convert => { "response" => "integer" }
        convert => { "bytes" => "integer" }
      }
      
      # 计算响应时间
      if [response_time] {
        mutate {
          convert => { "response_time" => "float" }
        }
      }
    }
    
    # 解析数据库日志
    if [fields][log_type] == "database" {
      grok {
        match => { "message" => "%{TIMESTAMP_ISO8601:timestamp}.*Query_time: %{NUMBER:query_time:float}.*Lock_time: %{NUMBER:lock_time:float}.*Rows_sent: %{NUMBER:rows_sent:int}.*Rows_examined: %{NUMBER:rows_examined:int}.*%{GREEDYDATA:query}" }
      }
      
      # 标记慢查询
      if [query_time] and [query_time] > 1 {
        mutate {
          add_tag => ["slow_query"]
        }
      }
    }
  }
  
  # 添加地理位置信息
  if [clientip] {
    geoip {
      source => "clientip"
      target => "geoip"
    }
  }
  
  # 添加用户代理解析
  if [agent] {
    useragent {
      source => "agent"
      target => "useragent"
    }
  }
}

output {
  elasticsearch {
    hosts => ["elasticsearch:9200"]
    index => "{$appName}-logs-%{+YYYY.MM.dd}"
  }
  
  # 输出到控制台用于调试
  stdout {
    codec => rubydebug
  }
}
EOT;
    }

    /**
     * 生成监控Docker Compose
     */
    protected function generateMonitoringDockerCompose(array $monitoringAnalysis, array $tableInfo): string
    {
        $appName = $tableInfo['name'] ?? 'app';
        
        return <<<EOT
version: '3.8'

services:
  prometheus:
    image: prom/prometheus:latest
    container_name: {$appName}_prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus:/etc/prometheus
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - monitoring

  grafana:
    image: grafana/grafana:latest
    container_name: {$appName}_grafana
    restart: unless-stopped
    ports:
      - "3000:3000"
    volumes:
      - ./monitoring/grafana:/etc/grafana
      - ./monitoring/grafana/dashboards:/var/lib/grafana/dashboards
      - grafana_data:/var/lib/grafana
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
      - GF_USERS_ALLOW_SIGN_UP=false
    networks:
      - monitoring

  alertmanager:
    image: prom/alertmanager:latest
    container_name: {$appName}_alertmanager
    restart: unless-stopped
    ports:
      - "9093:9093"
    volumes:
      - ./monitoring/alertmanager:/etc/alertmanager
    command:
      - '--config.file=/etc/alertmanager/config.yml'
      - '--storage.path=/alertmanager'
      - '--web.external-url=http://localhost:9093'
    networks:
      - monitoring

  node-exporter:
    image: prom/node-exporter:latest
    container_name: {$appName}_node_exporter
    restart: unless-stopped
    ports:
      - "9100:9100"
    volumes:
      - /proc:/host/proc:ro
      - /sys:/host/sys:ro
      - /:/rootfs:ro
    command:
      - '--path.procfs=/host/proc'
      - '--path.rootfs=/rootfs'
      - '--path.sysfs=/host/sys'
      - '--collector.filesystem.ignored-mount-points=^/(sys|proc|dev|host|etc)($$|/)'
    networks:
      - monitoring

  cadvisor:
    image: gcr.io/cadvisor/cadvisor:latest
    container_name: {$appName}_cadvisor
    restart: unless-stopped
    ports:
      - "8080:8080"
    volumes:
      - /:/rootfs:ro
      - /var/run:/var/run:rw
      - /sys:/sys:ro
      - /var/lib/docker:/var/lib/docker:ro
    networks:
      - monitoring

  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:7.15.0
    container_name: {$appName}_elasticsearch
    restart: unless-stopped
    environment:
      - discovery.type=single-node
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    ports:
      - "9200:9200"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    networks:
      - monitoring

  logstash:
    image: docker.elastic.co/logstash/logstash:7.15.0
    container_name: {$appName}_logstash
    restart: unless-stopped
    ports:
      - "5044:5044"
      - "5000:5000/tcp"
    volumes:
      - ./monitoring/logstash:/usr/share/logstash/pipeline
    environment:
      - "LS_JAVA_OPTS=-Xmx256m -Xms256m"
    networks:
      - monitoring
    depends_on:
      - elasticsearch

  kibana:
    image: docker.elastic.co/kibana/kibana:7.15.0
    container_name: {$appName}_kibana
    restart: unless-stopped
    ports:
      - "5601:5601"
    volumes:
      - ./monitoring/kibana:/usr/share/kibana/config
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
    networks:
      - monitoring
    depends_on:
      - elasticsearch

  jaeger:
    image: jaegertracing/all-in-one:latest
    container_name: {$appName}_jaeger
    restart: unless-stopped
    ports:
      - "16686:16686"
      - "14268:14268"
    environment:
      - COLLECTOR_ZIPKIN_HTTP_PORT=9411
    networks:
      - monitoring

volumes:
  prometheus_data:
  grafana_data:
  elasticsearch_data:

networks:
  monitoring:
    driver: bridge
EOT;
    }

    // 其他生成方法的占位实现
    protected function generatePrometheusAlertRules(array $monitoringAnalysis, array $tableInfo): string { return "# Prometheus Alert Rules"; }
    protected function generatePrometheusRecordingRules(array $monitoringAnalysis, array $tableInfo): string { return "# Prometheus Recording Rules"; }
    protected function generateGrafanaDatasources(array $monitoringAnalysis): string { return "# Grafana Datasources"; }
    protected function generateGrafanaDashboardsConfig(array $monitoringAnalysis): string { return "# Grafana Dashboards Config"; }
    protected function generateApplicationDashboard(array $monitoringAnalysis, array $tableInfo): string { return "// Application Dashboard"; }
    protected function generateInfrastructureDashboard(array $monitoringAnalysis, array $tableInfo): string { return "// Infrastructure Dashboard"; }
    protected function generateBusinessDashboard(array $monitoringAnalysis, array $tableInfo): string { return "// Business Dashboard"; }
    protected function generateElasticsearchConfig(array $monitoringAnalysis): string { return "# Elasticsearch Config"; }
    protected function generateKibanaConfig(array $monitoringAnalysis): string { return "# Kibana Config"; }
    protected function generateFilebeatConfig(array $monitoringAnalysis, array $tableInfo): string { return "# Filebeat Config"; }
    protected function generateJaegerConfig(array $monitoringAnalysis, array $tableInfo): array { return []; }
    protected function generateAlertingConfig(array $monitoringAnalysis, array $tableInfo): array { return []; }
    protected function generateMonitoringScripts(array $monitoringAnalysis, array $tableInfo): array { return []; }
}
