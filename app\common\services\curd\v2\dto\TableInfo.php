<?php

namespace app\common\services\curd\v2\dto;

/**
 * 表信息数据传输对象
 */
class TableInfo
{
    protected string $name;
    protected string $prefix;
    protected string $comment;
    protected array $fields;
    protected array $indexes;
    protected array $foreignKeys;

    public function __construct(
        string $name,
        string $prefix,
        string $comment,
        array $fields = [],
        array $indexes = [],
        array $foreignKeys = []
    ) {
        $this->name = $name;
        $this->prefix = $prefix;
        $this->comment = $comment;
        $this->fields = $fields;
        $this->indexes = $indexes;
        $this->foreignKeys = $foreignKeys;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function getPrefix(): string
    {
        return $this->prefix;
    }

    public function getFullName(): string
    {
        return $this->prefix . $this->name;
    }

    public function getComment(): string
    {
        return $this->comment;
    }

    public function getFields(): array
    {
        return $this->fields;
    }

    public function getField(string $name): ?FieldInfo
    {
        foreach ($this->fields as $field) {
            if ($field->getName() === $name) {
                return $field;
            }
        }
        return null;
    }

    public function getIndexes(): array
    {
        return $this->indexes;
    }

    public function getForeignKeys(): array
    {
        return $this->foreignKeys;
    }

    public function getPrimaryKey(): ?FieldInfo
    {
        foreach ($this->fields as $field) {
            if ($field->isPrimary()) {
                return $field;
            }
        }
        return null;
    }

    public function getListFields(): array
    {
        return array_filter($this->fields, function (FieldInfo $field) {
            return $field->isShowInList();
        });
    }

    public function getFormFields(): array
    {
        return array_filter($this->fields, function (FieldInfo $field) {
            return $field->isShowInForm();
        });
    }

    public function getSearchableFields(): array
    {
        return array_filter($this->fields, function (FieldInfo $field) {
            return $field->isSearchable();
        });
    }

    public function getSortableFields(): array
    {
        return array_filter($this->fields, function (FieldInfo $field) {
            return $field->isSortable();
        });
    }

    public function hasTimestamps(): bool
    {
        $timestampFields = ['created_at', 'updated_at'];
        foreach ($timestampFields as $field) {
            if ($this->getField($field)) {
                return true;
            }
        }
        return false;
    }

    public function hasSoftDelete(): bool
    {
        return $this->getField('deleted_at') !== null || $this->getField('delete_time') !== null;
    }

    public function toArray(): array
    {
        return [
            'name' => $this->name,
            'prefix' => $this->prefix,
            'full_name' => $this->getFullName(),
            'comment' => $this->comment,
            'fields' => array_map(function (FieldInfo $field) {
                return $field->toArray();
            }, $this->fields),
            'indexes' => $this->indexes,
            'foreign_keys' => $this->foreignKeys,
            'has_timestamps' => $this->hasTimestamps(),
            'has_soft_delete' => $this->hasSoftDelete(),
        ];
    }
}
