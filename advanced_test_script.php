<?php
/**
 * 高级测试脚本 - 深入测试多数据库 CURD 生成器功能
 */

echo "=== 多数据库 CURD 生成器深度测试 ===\n\n";

$baseUrl = 'http://localhost:8787';
$testResults = [];

// 测试配置
$connections = [
    'mysql' => '默认连接',
    'mysql_read' => '读库连接',
    'mysql_second' => '第二数据库',
    'mysql_log' => '日志数据库',
    'mysql_cache' => '缓存数据库',
];

// 高级测试函数
function advancedTest($url, $data = null, $description = '', $expectedCode = 200) {
    global $testResults;
    
    echo "🧪 测试: {$description}\n";
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 15);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    
    if ($data) {
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/x-www-form-urlencoded',
            'User-Agent: MultiDB-CURD-Tester/1.0'
        ]);
    }
    
    $startTime = microtime(true);
    $response = curl_exec($ch);
    $endTime = microtime(true);
    $responseTime = round(($endTime - $startTime) * 1000, 2);
    
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    $result = [
        'description' => $description,
        'success' => false,
        'http_code' => $httpCode,
        'response_time' => $responseTime,
        'error' => $error,
        'data' => null
    ];
    
    if ($error) {
        echo "   ❌ 网络错误: {$error}\n";
        $result['error'] = $error;
    } elseif ($httpCode !== $expectedCode) {
        echo "   ❌ HTTP 状态码错误: 期望 {$expectedCode}, 实际 {$httpCode}\n";
    } else {
        $json = json_decode($response, true);
        if ($json !== null) {
            $result['success'] = true;
            $result['data'] = $json;
            echo "   ✅ 成功 (响应时间: {$responseTime}ms)\n";
            
            // 分析响应内容
            if (isset($json['code'])) {
                echo "   📊 响应码: {$json['code']}\n";
                echo "   📝 消息: {$json['msg']}\n";
                
                if (isset($json['data']) && is_array($json['data'])) {
                    $dataCount = count($json['data']);
                    echo "   📈 数据量: {$dataCount} 项\n";
                }
            }
        } else {
            $result['success'] = strpos($response, '<!DOCTYPE html>') !== false;
            echo $result['success'] ? "   ✅ HTML 页面正常\n" : "   ❌ 响应格式异常\n";
        }
    }
    
    $testResults[] = $result;
    echo "   ⏱️  响应时间: {$responseTime}ms\n\n";
    
    return $result;
}

// 测试 1: 基础连通性测试
echo "📡 第一阶段: 基础连通性测试\n";
echo str_repeat("-", 60) . "\n";

$basicTests = [
    ['url' => "{$baseUrl}/admin/system/curdgeneratev2", 'desc' => 'CURD生成器页面访问'],
    ['url' => "{$baseUrl}/admin/system/databasetest", 'desc' => '数据库测试页面访问'],
    ['url' => "{$baseUrl}/admin", 'desc' => '后台首页访问'],
];

foreach ($basicTests as $test) {
    advancedTest($test['url'], null, $test['desc']);
}

// 测试 2: API 接口功能测试
echo "🔌 第二阶段: API 接口功能测试\n";
echo str_repeat("-", 60) . "\n";

foreach ($connections as $conn => $desc) {
    // 测试获取表列表
    advancedTest("{$baseUrl}/admin/system/curdgeneratev2", [
        'action' => 'get_tables',
        'connection' => $conn
    ], "获取 {$desc} 表列表");
    
    // 测试数据库连接
    advancedTest("{$baseUrl}/admin/system/databasetest", [
        'connection' => $conn
    ], "测试 {$desc} 连接状态");
}

// 测试 3: 错误处理测试
echo "⚠️  第三阶段: 错误处理测试\n";
echo str_repeat("-", 60) . "\n";

$errorTests = [
    [
        'data' => ['action' => 'get_tables', 'connection' => 'invalid_connection'],
        'desc' => '无效数据库连接测试'
    ],
    [
        'data' => ['action' => 'analyze_table', 'table_name' => 'non_existent_table', 'connection' => 'mysql'],
        'desc' => '不存在的表分析测试'
    ],
    [
        'data' => ['action' => 'invalid_action'],
        'desc' => '无效操作测试'
    ],
];

foreach ($errorTests as $test) {
    advancedTest("{$baseUrl}/admin/system/curdgeneratev2", $test['data'], $test['desc']);
}

// 测试 4: 性能压力测试
echo "🚀 第四阶段: 性能压力测试\n";
echo str_repeat("-", 60) . "\n";

$performanceTests = [];
for ($i = 1; $i <= 5; $i++) {
    $result = advancedTest("{$baseUrl}/admin/system/curdgeneratev2", [
        'action' => 'get_tables',
        'connection' => 'mysql'
    ], "并发测试 #{$i}");
    
    $performanceTests[] = $result['response_time'];
}

$avgResponseTime = array_sum($performanceTests) / count($performanceTests);
$maxResponseTime = max($performanceTests);
$minResponseTime = min($performanceTests);

echo "📊 性能统计:\n";
echo "   平均响应时间: {$avgResponseTime}ms\n";
echo "   最大响应时间: {$maxResponseTime}ms\n";
echo "   最小响应时间: {$minResponseTime}ms\n\n";

// 测试结果汇总
echo "📋 测试结果汇总\n";
echo str_repeat("=", 60) . "\n";

$totalTests = count($testResults);
$successfulTests = array_filter($testResults, function($result) {
    return $result['success'];
});
$successCount = count($successfulTests);
$failureCount = $totalTests - $successCount;

echo "总测试数: {$totalTests}\n";
echo "成功: {$successCount} ✅\n";
echo "失败: {$failureCount} ❌\n";
echo "成功率: " . round(($successCount / $totalTests) * 100, 2) . "%\n\n";

// 失败测试详情
if ($failureCount > 0) {
    echo "❌ 失败测试详情:\n";
    foreach ($testResults as $result) {
        if (!$result['success']) {
            echo "   • {$result['description']}: ";
            echo $result['error'] ?: "HTTP {$result['http_code']}";
            echo "\n";
        }
    }
    echo "\n";
}

// 性能分析
$responseTimes = array_column($testResults, 'response_time');
$avgTime = array_sum($responseTimes) / count($responseTimes);
$slowTests = array_filter($testResults, function($result) {
    return $result['response_time'] > 1000; // 超过1秒的测试
});

echo "⚡ 性能分析:\n";
echo "平均响应时间: " . round($avgTime, 2) . "ms\n";
echo "慢响应测试: " . count($slowTests) . " 个\n";

if (count($slowTests) > 0) {
    echo "慢响应详情:\n";
    foreach ($slowTests as $test) {
        echo "   • {$test['description']}: {$test['response_time']}ms\n";
    }
}

echo "\n";

// 建议和下一步
echo "💡 测试建议:\n";
if ($successCount === $totalTests) {
    echo "🎉 所有测试通过！系统运行正常。\n";
    echo "建议进行手动功能测试以验证用户体验。\n";
} else {
    echo "⚠️  发现 {$failureCount} 个问题，建议检查:\n";
    echo "   1. 数据库连接配置\n";
    echo "   2. 服务器权限设置\n";
    echo "   3. 网络连接状态\n";
}

echo "\n下一步操作:\n";
echo "1. 登录后台: {$baseUrl}/admin/login\n";
echo "2. 测试完整功能流程\n";
echo "3. 验证代码生成质量\n";

echo "\n=== 深度测试完成 ===\n";
?>
