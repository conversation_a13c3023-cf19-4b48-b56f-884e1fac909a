<?php
/**
 * EasyAdmin8-webman 服务器问题诊断和修复脚本
 * 自动检测和修复常见的服务器配置问题
 */

echo "=== EasyAdmin8-webman 服务器问题诊断和修复 ===\n\n";

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

/**
 * 输出诊断结果
 */
function outputDiagnosis($test, $result, $details = '', $solution = '') {
    $status = $result ? '✅' : '❌';
    echo "   {$status} {$test}";
    if ($details) {
        echo " - {$details}";
    }
    echo "\n";
    if (!$result && $solution) {
        echo "      🔧 解决方案: {$solution}\n";
    }
    return $result;
}

/**
 * 检查PHP环境
 */
function checkPhpEnvironment() {
    echo "1. PHP环境检查\n";
    
    $passed = 0;
    $total = 5;
    
    // PHP版本检查
    $phpVersion = PHP_VERSION;
    $versionOk = version_compare($phpVersion, '7.4.0', '>=');
    outputDiagnosis('PHP版本', $versionOk, "当前版本: {$phpVersion}", 
        $versionOk ? '' : '请升级到PHP 7.4或更高版本');
    if ($versionOk) $passed++;
    
    // 必需扩展检查
    $extensions = ['curl', 'json', 'mbstring', 'openssl'];
    foreach ($extensions as $ext) {
        $loaded = extension_loaded($ext);
        outputDiagnosis("PHP扩展 {$ext}", $loaded, '', 
            $loaded ? '' : "请安装PHP {$ext}扩展");
        if ($loaded) $passed++;
    }
    
    echo "   📊 PHP环境: {$passed}/{$total} (" . round(($passed/$total)*100, 1) . "%)\n\n";
    return $passed >= 4; // 至少4项通过
}

/**
 * 检查文件权限
 */
function checkFilePermissions() {
    echo "2. 文件权限检查\n";
    
    $directories = [
        'runtime' => '运行时目录',
        'public' => '公共目录',
        'config' => '配置目录',
        'app' => '应用目录'
    ];
    
    $passed = 0;
    $total = count($directories);
    
    foreach ($directories as $dir => $desc) {
        if (is_dir($dir)) {
            $readable = is_readable($dir);
            $writable = is_writable($dir);
            $permission = $readable && $writable;
            
            outputDiagnosis($desc, $permission, 
                $permission ? '读写权限正常' : '权限不足',
                $permission ? '' : "请设置 {$dir} 目录为可读写");
            
            if ($permission) $passed++;
        } else {
            outputDiagnosis($desc, false, '目录不存在', "请创建 {$dir} 目录");
        }
    }
    
    echo "   📊 文件权限: {$passed}/{$total} (" . round(($passed/$total)*100, 1) . "%)\n\n";
    return $passed >= 3; // 至少3项通过
}

/**
 * 检查端口占用
 */
function checkPortUsage() {
    echo "3. 端口占用检查\n";
    
    $ports = [8787, 8080, 80, 3000];
    $availablePorts = [];
    
    foreach ($ports as $port) {
        $connection = @fsockopen('localhost', $port, $errno, $errstr, 1);
        if ($connection) {
            fclose($connection);
            outputDiagnosis("端口 {$port}", false, '端口被占用', '');
        } else {
            outputDiagnosis("端口 {$port}", true, '端口可用', '');
            $availablePorts[] = $port;
        }
    }
    
    echo "   📊 可用端口: " . implode(', ', $availablePorts) . "\n\n";
    return !empty($availablePorts);
}

/**
 * 检查Webman配置
 */
function checkWebmanConfig() {
    echo "4. Webman配置检查\n";
    
    $configFiles = [
        'config/app.php' => '应用配置',
        'config/server.php' => '服务器配置',
        'config/route.php' => '路由配置'
    ];
    
    $passed = 0;
    $total = count($configFiles);
    
    foreach ($configFiles as $file => $desc) {
        if (file_exists($file)) {
            try {
                $config = include $file;
                $valid = is_array($config) || is_callable($config);
                outputDiagnosis($desc, $valid, 
                    $valid ? '配置格式正确' : '配置格式错误',
                    $valid ? '' : "请检查 {$file} 文件格式");
                if ($valid) $passed++;
            } catch (Exception $e) {
                outputDiagnosis($desc, false, '配置加载失败', "请检查 {$file} 语法错误");
            }
        } else {
            outputDiagnosis($desc, false, '配置文件不存在', "请创建 {$file} 文件");
        }
    }
    
    echo "   📊 Webman配置: {$passed}/{$total} (" . round(($passed/$total)*100, 1) . "%)\n\n";
    return $passed >= 2; // 至少2项通过
}

/**
 * 检查进程状态
 */
function checkProcessStatus() {
    echo "5. 进程状态检查\n";
    
    // 检查PHP进程
    $output = [];
    if (PHP_OS_FAMILY === 'Windows') {
        exec('tasklist | findstr php.exe', $output);
    } else {
        exec('ps aux | grep php', $output);
    }
    
    $phpProcesses = count($output);
    outputDiagnosis('PHP进程', $phpProcesses > 0, "发现 {$phpProcesses} 个PHP进程");
    
    // 检查端口监听
    $output = [];
    if (PHP_OS_FAMILY === 'Windows') {
        exec('netstat -an | findstr :8787', $output);
    } else {
        exec('netstat -an | grep :8787', $output);
    }
    
    $listening = !empty($output);
    outputDiagnosis('端口监听', $listening, 
        $listening ? '端口8787正在监听' : '端口8787未监听',
        $listening ? '' : '请启动Webman服务器');
    
    echo "   📊 进程状态: " . ($phpProcesses > 0 && $listening ? '正常' : '异常') . "\n\n";
    return $phpProcesses > 0;
}

/**
 * 自动修复常见问题
 */
function autoFixCommonIssues() {
    echo "6. 自动修复常见问题\n";
    
    $fixed = 0;
    
    // 创建必要目录
    $directories = ['runtime', 'runtime/logs', 'runtime/cache', 'runtime/sessions'];
    foreach ($directories as $dir) {
        if (!is_dir($dir)) {
            if (mkdir($dir, 0755, true)) {
                outputDiagnosis("创建目录 {$dir}", true, '目录创建成功');
                $fixed++;
            } else {
                outputDiagnosis("创建目录 {$dir}", false, '目录创建失败');
            }
        }
    }
    
    // 设置目录权限
    $writableDirs = ['runtime', 'public'];
    foreach ($writableDirs as $dir) {
        if (is_dir($dir)) {
            if (chmod($dir, 0755)) {
                outputDiagnosis("设置 {$dir} 权限", true, '权限设置成功');
                $fixed++;
            } else {
                outputDiagnosis("设置 {$dir} 权限", false, '权限设置失败');
            }
        }
    }
    
    // 清理临时文件
    $tempFiles = glob('runtime/cache/*');
    if (!empty($tempFiles)) {
        foreach ($tempFiles as $file) {
            if (is_file($file)) {
                unlink($file);
            }
        }
        outputDiagnosis('清理缓存文件', true, '缓存清理完成');
        $fixed++;
    }
    
    echo "   📊 修复项目: {$fixed} 个问题已修复\n\n";
    return $fixed > 0;
}

/**
 * 生成启动脚本
 */
function generateStartupScript() {
    echo "7. 生成启动脚本\n";
    
    $isWindows = PHP_OS_FAMILY === 'Windows';
    
    if ($isWindows) {
        $script = "@echo off\n";
        $script .= "echo Starting EasyAdmin8-webman server...\n";
        $script .= "cd /d \"%~dp0\"\n";
        $script .= "php windows.php\n";
        $script .= "pause\n";
        
        $filename = 'start_server.bat';
    } else {
        $script = "#!/bin/bash\n";
        $script .= "echo \"Starting EasyAdmin8-webman server...\"\n";
        $script .= "cd \"$(dirname \"$0\")\"\n";
        $script .= "php start.php start\n";
        
        $filename = 'start_server.sh';
    }
    
    if (file_put_contents($filename, $script)) {
        if (!$isWindows) {
            chmod($filename, 0755);
        }
        outputDiagnosis('生成启动脚本', true, "脚本保存为 {$filename}");
        return true;
    } else {
        outputDiagnosis('生成启动脚本', false, '脚本生成失败');
        return false;
    }
}

/**
 * 测试API文档功能
 */
function testApiDocFunction() {
    echo "8. API文档功能测试\n";
    
    try {
        // 模拟必要的类
        if (!function_exists('response')) {
            function response($content, $status = 200, $headers = []) {
                return new MockResponse($content, $status, $headers);
            }
        }
        
        if (!class_exists('MockResponse')) {
            class MockResponse {
                public $content, $status, $headers;
                public function __construct($content, $status = 200, $headers = []) {
                    $this->content = $content;
                    $this->status = $status;
                    $this->headers = $headers;
                }
            }
        }
        
        if (!class_exists('MockRequest')) {
            class MockRequest {
                public function get($key, $default = null) { return $default; }
                public function post($key, $default = null) { return $default; }
            }
        }
        
        if (!class_exists('support\Request')) {
            class_alias('MockRequest', 'support\Request');
        }
        if (!class_exists('support\Response')) {
            class_alias('MockResponse', 'support\Response');
        }
        
        // 测试简化版控制器
        require_once 'app/admin/controller/system/ApiDocControllerSimple.php';
        $controller = new \app\admin\controller\system\ApiDocControllerSimple();
        $request = new MockRequest();
        
        $response = $controller->getApiDocList($request);
        outputDiagnosis('简化版控制器', $response instanceof MockResponse, '功能正常');
        
        // 测试增强版控制器
        require_once 'app/admin/controller/system/ApiDocControllerEnhanced.php';
        $controller = new \app\admin\controller\system\ApiDocControllerEnhanced();
        
        $response = $controller->dashboard($request);
        outputDiagnosis('增强版控制器', $response instanceof MockResponse, '功能正常');
        
        return true;
        
    } catch (Exception $e) {
        outputDiagnosis('API文档功能', false, $e->getMessage());
        return false;
    }
}

/**
 * 生成诊断报告
 */
function generateDiagnosisReport($results) {
    echo "=== 诊断报告 ===\n";
    
    $testNames = [
        'php_env' => 'PHP环境',
        'file_permissions' => '文件权限',
        'port_usage' => '端口检查',
        'webman_config' => 'Webman配置',
        'process_status' => '进程状态',
        'auto_fix' => '自动修复',
        'startup_script' => '启动脚本',
        'api_function' => 'API功能'
    ];
    
    $passed = 0;
    $total = count($results);
    
    echo "📊 诊断结果详情:\n";
    foreach ($results as $test => $result) {
        $status = $result ? '✅ 正常' : '❌ 异常';
        $name = $testNames[$test] ?? $test;
        echo "   - {$name}: {$status}\n";
        if ($result) $passed++;
    }
    
    $healthScore = ($passed / $total) * 100;
    
    echo "\n📈 系统健康度:\n";
    echo "   - 健康评分: " . number_format($healthScore, 1) . "%\n";
    echo "   - 正常项: {$passed}/{$total}\n";
    
    if ($healthScore >= 90) {
        echo "   🎉 系统状态优秀，可以正常运行！\n";
        $grade = "A+";
    } elseif ($healthScore >= 75) {
        echo "   ✅ 系统状态良好，基本可以正常运行\n";
        $grade = "A";
    } elseif ($healthScore >= 60) {
        echo "   ⚠️  系统存在一些问题，建议修复后使用\n";
        $grade = "B";
    } else {
        echo "   ❌ 系统存在严重问题，需要修复后才能使用\n";
        $grade = "C";
    }
    
    echo "   - 系统评级: {$grade}\n";
    
    echo "\n🔧 修复建议:\n";
    if (!$results['php_env']) {
        echo "   - 检查PHP版本和扩展安装\n";
    }
    if (!$results['file_permissions']) {
        echo "   - 设置正确的文件和目录权限\n";
    }
    if (!$results['process_status']) {
        echo "   - 启动Webman服务器进程\n";
    }
    if (!$results['webman_config']) {
        echo "   - 检查Webman配置文件\n";
    }
    
    echo "\n🚀 启动建议:\n";
    if ($results['startup_script']) {
        $scriptName = PHP_OS_FAMILY === 'Windows' ? 'start_server.bat' : 'start_server.sh';
        echo "   - 使用生成的启动脚本: {$scriptName}\n";
    }
    echo "   - 手动启动: php windows.php (Windows) 或 php start.php start (Linux/Mac)\n";
    echo "   - 访问地址: http://localhost:8787/admin/system/apidoc\n";
    
    return $healthScore;
}

// 执行诊断
try {
    echo "开始执行EasyAdmin8-webman服务器诊断...\n\n";
    
    $results = [];
    $results['php_env'] = checkPhpEnvironment();
    $results['file_permissions'] = checkFilePermissions();
    $results['port_usage'] = checkPortUsage();
    $results['webman_config'] = checkWebmanConfig();
    $results['process_status'] = checkProcessStatus();
    $results['auto_fix'] = autoFixCommonIssues();
    $results['startup_script'] = generateStartupScript();
    $results['api_function'] = testApiDocFunction();
    
    $healthScore = generateDiagnosisReport($results);
    
    echo "\n=== 诊断完成 ===\n";
    
    if ($healthScore >= 75) {
        echo "🎊 恭喜！系统诊断通过，可以正常使用API文档功能！\n";
    } else {
        echo "⚠️  系统需要进一步修复，请按照建议进行调整\n";
    }
    
} catch (Exception $e) {
    echo "❌ 诊断过程中发生错误: " . $e->getMessage() . "\n";
    echo "📍 错误位置: " . $e->getFile() . ":" . $e->getLine() . "\n";
}

echo "\n🎯 EasyAdmin8-webman服务器诊断完成！\n";
