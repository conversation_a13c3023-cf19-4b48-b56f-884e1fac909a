/**
 * CURD 模板管理组件
 * 支持配置保存、加载、复用等功能
 */
define(['jquery', 'layui'], function($, layui) {
    'use strict';
    
    var TemplateManager = function(options) {
        this.options = $.extend({
            apiUrl: '/admin/system/curd_generate_v2',
            container: '#template-manager-container',
            enableAutoSave: true,
            autoSaveInterval: 30000, // 30秒自动保存
            maxRecentTemplates: 10
        }, options);
        
        this.container = $(this.options.container);
        this.currentConfig = {};
        this.autoSaveTimer = null;
        this.recentTemplates = [];
        
        this.init();
    };
    
    TemplateManager.prototype = {
        
        /**
         * 初始化模板管理器
         */
        init: function() {
            this.loadRecentTemplates();
            this.bindEvents();
            this.initAutoSave();
        },
        
        /**
         * 绑定事件
         */
        bindEvents: function() {
            var self = this;
            
            // 保存模板
            $(document).on('click', '.save-template-btn', function() {
                self.showSaveDialog();
            });
            
            // 加载模板
            $(document).on('click', '.load-template-btn', function() {
                self.showLoadDialog();
            });
            
            // 模板推荐
            $(document).on('click', '.recommend-template-btn', function() {
                self.showRecommendations();
            });
            
            // 管理模板
            $(document).on('click', '.manage-template-btn', function() {
                self.showManageDialog();
            });
        },
        
        /**
         * 显示保存对话框
         */
        showSaveDialog: function() {
            var self = this;
            
            var html = `
                <form class="layui-form" id="save-template-form">
                    <div class="layui-form-item">
                        <label class="layui-form-label">模板名称</label>
                        <div class="layui-input-block">
                            <input type="text" name="name" required lay-verify="required" 
                                   placeholder="请输入模板名称" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">模板描述</label>
                        <div class="layui-input-block">
                            <textarea name="description" placeholder="请输入模板描述" 
                                      class="layui-textarea"></textarea>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">适用表名</label>
                        <div class="layui-input-block">
                            <input type="text" name="table_pattern" 
                                   placeholder="如: user%, %_config, 支持通配符" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">标签</label>
                        <div class="layui-input-block">
                            <input type="text" name="tags" 
                                   placeholder="多个标签用逗号分隔" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <div class="layui-input-block">
                            <input type="checkbox" name="is_public" title="公开模板" lay-skin="primary">
                        </div>
                    </div>
                </form>
            `;
            
            layer.open({
                type: 1,
                title: '保存配置模板',
                content: html,
                area: ['500px', '400px'],
                btn: ['保存', '取消'],
                yes: function(index) {
                    self.saveTemplate(index);
                }
            });
        },
        
        /**
         * 保存模板
         */
        saveTemplate: function(layerIndex) {
            var self = this;
            var formData = this.getFormData('#save-template-form');
            
            if (!formData.name) {
                layer.msg('请输入模板名称');
                return;
            }
            
            var templateData = {
                name: formData.name,
                description: formData.description,
                table_pattern: formData.table_pattern,
                tags: formData.tags,
                is_public: formData.is_public ? 1 : 0,
                config: this.currentConfig
            };
            
            $.ajax({
                url: this.options.apiUrl,
                type: 'POST',
                data: {
                    action: 'save_template',
                    template: templateData
                },
                success: function(response) {
                    if (response.code === 0) {
                        layer.close(layerIndex);
                        layer.msg('模板保存成功');
                        self.addToRecentTemplates(templateData);
                    } else {
                        layer.msg(response.msg || '保存失败');
                    }
                },
                error: function() {
                    layer.msg('网络错误');
                }
            });
        },
        
        /**
         * 显示加载对话框
         */
        showLoadDialog: function() {
            var self = this;
            
            // 获取模板列表
            this.getTemplateList(function(templates) {
                var html = self.buildTemplateListHtml(templates);
                
                layer.open({
                    type: 1,
                    title: '加载配置模板',
                    content: html,
                    area: ['700px', '500px'],
                    btn: ['加载', '取消'],
                    yes: function(index) {
                        var selectedId = $('.template-item.selected').data('id');
                        if (selectedId) {
                            self.loadTemplate(selectedId, index);
                        } else {
                            layer.msg('请选择一个模板');
                        }
                    }
                });
            });
        },
        
        /**
         * 构建模板列表HTML
         */
        buildTemplateListHtml: function(templates) {
            var html = `
                <div class="template-list-container">
                    <div class="template-tabs">
                        <div class="tab-item active" data-tab="my">我的模板</div>
                        <div class="tab-item" data-tab="public">公开模板</div>
                        <div class="tab-item" data-tab="recent">最近使用</div>
                    </div>
                    <div class="template-search">
                        <input type="text" placeholder="搜索模板..." class="layui-input" id="template-search">
                    </div>
                    <div class="template-list" id="template-list">
            `;
            
            templates.forEach(function(template) {
                html += `
                    <div class="template-item" data-id="${template.id}">
                        <div class="template-header">
                            <h4>${template.name}</h4>
                            <span class="template-meta">${template.created_at}</span>
                        </div>
                        <div class="template-description">${template.description || '暂无描述'}</div>
                        <div class="template-tags">
                            ${template.tags ? template.tags.split(',').map(tag => `<span class="tag">${tag.trim()}</span>`).join('') : ''}
                        </div>
                        <div class="template-stats">
                            <span>使用 ${template.use_count || 0} 次</span>
                            ${template.is_public ? '<span class="public-badge">公开</span>' : ''}
                        </div>
                    </div>
                `;
            });
            
            html += '</div></div>';
            
            // 绑定选择事件
            setTimeout(function() {
                $('.template-item').on('click', function() {
                    $('.template-item').removeClass('selected');
                    $(this).addClass('selected');
                });
                
                $('#template-search').on('input', function() {
                    var keyword = $(this).val().toLowerCase();
                    $('.template-item').each(function() {
                        var text = $(this).text().toLowerCase();
                        $(this).toggle(text.indexOf(keyword) !== -1);
                    });
                });
            }, 100);
            
            return html;
        },
        
        /**
         * 加载模板
         */
        loadTemplate: function(templateId, layerIndex) {
            var self = this;
            
            $.ajax({
                url: this.options.apiUrl,
                type: 'POST',
                data: {
                    action: 'load_template',
                    template_id: templateId
                },
                success: function(response) {
                    if (response.code === 0) {
                        layer.close(layerIndex);
                        self.applyTemplate(response.data);
                        layer.msg('模板加载成功');
                    } else {
                        layer.msg(response.msg || '加载失败');
                    }
                },
                error: function() {
                    layer.msg('网络错误');
                }
            });
        },
        
        /**
         * 应用模板
         */
        applyTemplate: function(templateConfig) {
            // 触发配置应用事件
            if (window.applyTemplateConfig) {
                window.applyTemplateConfig(templateConfig);
            }
            
            // 更新当前配置
            this.currentConfig = templateConfig;
            
            // 触发预览更新
            if (window.triggerPreview) {
                window.triggerPreview();
            }
        },
        
        /**
         * 显示推荐模板
         */
        showRecommendations: function() {
            var self = this;
            var tableName = window.tableInfo ? window.tableInfo.name : '';
            
            if (!tableName) {
                layer.msg('请先选择数据表');
                return;
            }
            
            $.ajax({
                url: this.options.apiUrl,
                type: 'POST',
                data: {
                    action: 'recommend_templates',
                    table_name: tableName
                },
                success: function(response) {
                    if (response.code === 0) {
                        self.showRecommendationDialog(response.data);
                    } else {
                        layer.msg(response.msg || '获取推荐失败');
                    }
                },
                error: function() {
                    layer.msg('网络错误');
                }
            });
        },
        
        /**
         * 显示推荐对话框
         */
        showRecommendationDialog: function(recommendations) {
            var self = this;
            
            if (recommendations.length === 0) {
                layer.msg('暂无推荐模板');
                return;
            }
            
            var html = '<div class="recommendation-list">';
            
            recommendations.forEach(function(template) {
                html += `
                    <div class="recommendation-item" data-id="${template.id}">
                        <div class="recommendation-header">
                            <h4>${template.name}</h4>
                            <span class="match-score">匹配度: ${template.match_score}%</span>
                        </div>
                        <div class="recommendation-description">${template.description}</div>
                        <div class="recommendation-reason">
                            推荐理由: ${this.getRecommendationReason(template)}
                        </div>
                    </div>
                `;
            });
            
            html += '</div>';
            
            layer.open({
                type: 1,
                title: '推荐模板',
                content: html,
                area: ['600px', '400px'],
                btn: ['使用推荐', '取消'],
                yes: function(index) {
                    var selectedId = $('.recommendation-item.selected').data('id');
                    if (selectedId) {
                        self.loadTemplate(selectedId, index);
                    } else {
                        // 使用第一个推荐
                        self.loadTemplate(recommendations[0].id, index);
                    }
                }
            });
            
            // 绑定选择事件
            setTimeout(function() {
                $('.recommendation-item').on('click', function() {
                    $('.recommendation-item').removeClass('selected');
                    $(this).addClass('selected');
                });
            }, 100);
        },
        
        /**
         * 获取推荐理由
         */
        getRecommendationReason: function(template) {
            var reasons = [];
            
            if (template.match_score >= 50) {
                reasons.push('表名模式匹配');
            }
            if (template.use_count > 10) {
                reasons.push('使用频率高');
            }
            if (template.is_public) {
                reasons.push('官方推荐');
            }
            
            return reasons.length > 0 ? reasons.join(', ') : '综合评分较高';
        },
        
        /**
         * 获取模板列表
         */
        getTemplateList: function(callback) {
            $.ajax({
                url: this.options.apiUrl,
                type: 'POST',
                data: {
                    action: 'get_templates'
                },
                success: function(response) {
                    if (response.code === 0) {
                        callback(response.data);
                    } else {
                        layer.msg(response.msg || '获取模板列表失败');
                        callback([]);
                    }
                },
                error: function() {
                    layer.msg('网络错误');
                    callback([]);
                }
            });
        },
        
        /**
         * 更新当前配置
         */
        updateCurrentConfig: function(config) {
            this.currentConfig = config;
            
            if (this.options.enableAutoSave) {
                this.scheduleAutoSave();
            }
        },
        
        /**
         * 初始化自动保存
         */
        initAutoSave: function() {
            if (this.options.enableAutoSave) {
                this.scheduleAutoSave();
            }
        },
        
        /**
         * 计划自动保存
         */
        scheduleAutoSave: function() {
            var self = this;
            
            clearTimeout(this.autoSaveTimer);
            this.autoSaveTimer = setTimeout(function() {
                self.autoSave();
            }, this.options.autoSaveInterval);
        },
        
        /**
         * 自动保存
         */
        autoSave: function() {
            if (Object.keys(this.currentConfig).length === 0) {
                return;
            }
            
            var autoSaveData = {
                name: '自动保存_' + new Date().toLocaleString(),
                description: '系统自动保存的配置',
                config: this.currentConfig,
                is_auto_save: true
            };
            
            // 保存到本地存储
            this.saveToLocalStorage('auto_save_config', autoSaveData);
        },
        
        /**
         * 加载最近使用的模板
         */
        loadRecentTemplates: function() {
            var recent = this.getFromLocalStorage('recent_templates');
            this.recentTemplates = recent || [];
        },
        
        /**
         * 添加到最近使用
         */
        addToRecentTemplates: function(template) {
            // 移除重复项
            this.recentTemplates = this.recentTemplates.filter(t => t.name !== template.name);
            
            // 添加到开头
            this.recentTemplates.unshift(template);
            
            // 限制数量
            if (this.recentTemplates.length > this.options.maxRecentTemplates) {
                this.recentTemplates = this.recentTemplates.slice(0, this.options.maxRecentTemplates);
            }
            
            // 保存到本地存储
            this.saveToLocalStorage('recent_templates', this.recentTemplates);
        },
        
        /**
         * 获取表单数据
         */
        getFormData: function(formSelector) {
            var data = {};
            $(formSelector).find('input, textarea, select').each(function() {
                var $this = $(this);
                var name = $this.attr('name');
                var type = $this.attr('type');
                
                if (name) {
                    if (type === 'checkbox') {
                        data[name] = $this.prop('checked');
                    } else {
                        data[name] = $this.val();
                    }
                }
            });
            return data;
        },
        
        /**
         * 保存到本地存储
         */
        saveToLocalStorage: function(key, data) {
            try {
                localStorage.setItem('curd_template_' + key, JSON.stringify(data));
            } catch (e) {
                console.warn('无法保存到本地存储:', e);
            }
        },
        
        /**
         * 从本地存储获取
         */
        getFromLocalStorage: function(key) {
            try {
                var data = localStorage.getItem('curd_template_' + key);
                return data ? JSON.parse(data) : null;
            } catch (e) {
                console.warn('无法从本地存储读取:', e);
                return null;
            }
        },
        
        /**
         * 销毁组件
         */
        destroy: function() {
            clearTimeout(this.autoSaveTimer);
            $(document).off('.template-manager');
        }
    };
    
    return TemplateManager;
});
